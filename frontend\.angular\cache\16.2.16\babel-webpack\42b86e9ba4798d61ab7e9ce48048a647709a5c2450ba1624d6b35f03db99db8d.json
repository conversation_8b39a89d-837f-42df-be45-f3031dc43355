{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { CallType } from '../../../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../services/message.service\";\nimport * as i4 from \"../../../../services/call.service\";\nimport * as i5 from \"../../../../services/toast.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../call-interface/call-interface.component\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 50);\n  }\n}\nfunction MessageChatComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"span\");\n    i0.ɵɵtext(2, \"En train d'\\u00E9crire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52);\n    i0.ɵɵelement(4, \"div\", 53)(5, \"div\", 54)(6, \"div\", 55);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.isOnline) ? \"En ligne\" : ctx_r2.formatLastActive(ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_23_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      ctx_r17.toggleSearch();\n      return i0.ɵɵresetView(ctx_r17.showMainMenu = false);\n    });\n    i0.ɵɵelement(3, \"i\", 59);\n    i0.ɵɵelementStart(4, \"span\", 60);\n    i0.ɵɵtext(5, \"Rechercher\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 61);\n    i0.ɵɵelement(7, \"i\", 62);\n    i0.ɵɵelementStart(8, \"span\", 60);\n    i0.ɵɵtext(9, \"Voir le profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"hr\", 63);\n    i0.ɵɵelementStart(11, \"button\", 61);\n    i0.ɵɵelement(12, \"i\", 64);\n    i0.ɵɵelementStart(13, \"span\", 60);\n    i0.ɵɵtext(14, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction MessageChatComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵelement(2, \"i\", 67);\n    i0.ɵɵelementStart(3, \"p\", 68);\n    i0.ɵɵtext(4, \" D\\u00E9posez vos fichiers ici \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 69);\n    i0.ɵɵtext(6, \" Images, vid\\u00E9os, documents... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵelement(1, \"div\", 71);\n    i0.ɵɵelementStart(2, \"span\", 72);\n    i0.ɵɵtext(3, \"Chargement des messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 74);\n    i0.ɵɵelement(2, \"i\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 76);\n    i0.ɵɵtext(4, \" Aucun message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 77);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Commencez votre conversation avec \", ctx_r7.otherParticipant == null ? null : ctx_r7.otherParticipant.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"div\", 92)(2, \"span\", 93);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.formatDateSeparator(message_r21.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"img\", 95);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const message_r21 = i0.ɵɵnextContext().$implicit;\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.openUserProfile(message_r21.sender == null ? null : message_r21.sender.id));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r21.sender == null ? null : message_r21.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r21.sender == null ? null : message_r21.sender.username);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r25.getUserColor(message_r21.sender == null ? null : message_r21.sender.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r21.sender == null ? null : message_r21.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelement(1, \"div\", 98);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r26.formatMessageContent(message_r21.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 102);\n  }\n  if (rf & 2) {\n    const message_r21 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", (message_r21.sender == null ? null : message_r21.sender.id) === ctx_r37.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵproperty(\"innerHTML\", ctx_r37.formatMessageContent(message_r21.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"img\", 100);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const message_r21 = i0.ɵɵnextContext().$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.openImageViewer(message_r21));\n    })(\"load\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_img_load_1_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const message_r21 = i0.ɵɵnextContext().$implicit;\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r42.onImageLoad($event, message_r21));\n    })(\"error\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const message_r21 = i0.ɵɵnextContext().$implicit;\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.onImageError($event, message_r21));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_ng_container_1_div_7_div_2_Template, 1, 3, \"div\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r27.getImageUrl(message_r21), i0.ɵɵsanitizeUrl)(\"alt\", message_r21.content || \"Image\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r21.content);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 111);\n  }\n  if (rf & 2) {\n    const wave_r49 = ctx.$implicit;\n    const i_r50 = ctx.index;\n    const message_r21 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r47 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", ctx_r47.isVoicePlaying(message_r21.id) ? wave_r49 : 8, \"px\")(\"animation\", ctx_r47.isVoicePlaying(message_r21.id) ? \"pulse 1s infinite\" : \"none\")(\"animation-delay\", i_r50 * 0.1 + \"s\");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_8_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 112);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_8_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const message_r21 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r52 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r52.changeVoiceSpeed(message_r21));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r21 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r48 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r48.getVoiceSpeed(message_r21), \"x \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const message_r21 = i0.ɵɵnextContext().$implicit;\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r56.toggleVoicePlayback(message_r21));\n    });\n    i0.ɵɵelement(2, \"i\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 106);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_29_ng_container_1_div_8_div_4_Template, 1, 6, \"div\", 107);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 108)(6, \"div\", 109);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, MessageChatComponent_div_29_ng_container_1_div_8_button_8_Template, 2, 1, \"button\", 110);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r28.isVoicePlaying(message_r21.id) ? \"fas fa-pause\" : \"fas fa-play\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r28.voiceWaves);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r28.getVoiceDuration(message_r21), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.isVoicePlaying(message_r21.id));\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_12_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 118);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_12_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 119);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_12_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 120);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_12_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 121);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_div_12_i_1_Template, 1, 0, \"i\", 114);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_ng_container_1_div_12_i_2_Template, 1, 0, \"i\", 115);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_29_ng_container_1_div_12_i_3_Template, 1, 0, \"i\", 116);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_29_ng_container_1_div_12_i_4_Template, 1, 0, \"i\", 117);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r21.status === \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r21.status === \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r21.status === \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r21.status === \"READ\");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r66 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_div_1_Template, 4, 1, \"div\", 81);\n    i0.ɵɵelementStart(2, \"div\", 82);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_Template_div_click_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r66);\n      const message_r21 = restoredCtx.$implicit;\n      const ctx_r65 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r65.onMessageClick(message_r21, $event));\n    })(\"contextmenu\", function MessageChatComponent_div_29_ng_container_1_Template_div_contextmenu_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r66);\n      const message_r21 = restoredCtx.$implicit;\n      const ctx_r67 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r67.onMessageContextMenu(message_r21, $event));\n    });\n    i0.ɵɵtemplate(3, MessageChatComponent_div_29_ng_container_1_div_3_Template, 2, 2, \"div\", 83);\n    i0.ɵɵelementStart(4, \"div\", 84);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_29_ng_container_1_div_5_Template, 2, 3, \"div\", 85);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_29_ng_container_1_div_6_Template, 2, 1, \"div\", 86);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_29_ng_container_1_div_7_Template, 3, 3, \"div\", 87);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_29_ng_container_1_div_8_Template, 9, 5, \"div\", 88);\n    i0.ɵɵelementStart(9, \"div\", 89)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, MessageChatComponent_div_29_ng_container_1_div_12_Template, 5, 4, \"div\", 90);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r21 = ctx.$implicit;\n    const i_r22 = ctx.index;\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.shouldShowDateSeparator(i_r22));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"justify-content\", (message_r21.sender == null ? null : message_r21.sender.id) === ctx_r19.currentUserId ? \"flex-end\" : \"flex-start\");\n    i0.ɵɵproperty(\"id\", \"message-\" + message_r21.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r21.sender == null ? null : message_r21.sender.id) !== ctx_r19.currentUserId && ctx_r19.shouldShowAvatar(i_r22));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", (message_r21.sender == null ? null : message_r21.sender.id) === ctx_r19.currentUserId ? \"#3b82f6\" : \"#ffffff\")(\"color\", (message_r21.sender == null ? null : message_r21.sender.id) === ctx_r19.currentUserId ? \"#ffffff\" : \"#111827\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.isGroupConversation() && (message_r21.sender == null ? null : message_r21.sender.id) !== ctx_r19.currentUserId && ctx_r19.shouldShowSenderName(i_r22));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.getMessageType(message_r21) === \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.hasImage(message_r21));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.getMessageType(message_r21) === \"audio\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r19.formatMessageTime(message_r21.timestamp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r21.sender == null ? null : message_r21.sender.id) === ctx_r19.currentUserId);\n  }\n}\nfunction MessageChatComponent_div_29_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵelement(1, \"img\", 123);\n    i0.ɵɵelementStart(2, \"div\", 124)(3, \"div\", 125);\n    i0.ɵɵelement(4, \"div\", 126)(5, \"div\", 127)(6, \"div\", 128);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (ctx_r20.otherParticipant == null ? null : ctx_r20.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r20.otherParticipant == null ? null : ctx_r20.otherParticipant.username);\n  }\n}\nfunction MessageChatComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_Template, 13, 15, \"ng-container\", 79);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_div_2_Template, 7, 2, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.messages)(\"ngForTrackBy\", ctx_r8.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.otherUserIsTyping);\n  }\n}\nfunction MessageChatComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 129);\n  }\n}\nfunction MessageChatComponent_i_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 130);\n  }\n}\nfunction MessageChatComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 131);\n  }\n}\nfunction MessageChatComponent_div_45_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 137);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_button_5_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r71);\n      const emoji_r69 = restoredCtx.$implicit;\n      const ctx_r70 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r70.insertEmoji(emoji_r69));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r69 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r69.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r69.emoji, \" \");\n  }\n}\nfunction MessageChatComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"div\", 133)(2, \"h4\", 134);\n    i0.ɵɵtext(3, \" \\u00C9mojis \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 135);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_45_button_5_Template, 2, 2, \"button\", 136);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.getEmojisForCategory(ctx_r12.selectedEmojiCategory));\n  }\n}\nfunction MessageChatComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r73 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 138)(1, \"div\", 133)(2, \"h4\", 134);\n    i0.ɵɵtext(3, \" Pi\\u00E8ces jointes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 139)(5, \"button\", 140);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_46_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelementStart(6, \"div\", 141);\n    i0.ɵɵelement(7, \"i\", 142);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 143);\n    i0.ɵɵtext(9, \"Images\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 140);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_46_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelementStart(11, \"div\", 144);\n    i0.ɵɵelement(12, \"i\", 145);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 143);\n    i0.ɵɵtext(14, \"Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 140);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_46_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.openCamera());\n    });\n    i0.ɵɵelementStart(16, \"div\", 146);\n    i0.ɵɵelement(17, \"i\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 143);\n    i0.ɵɵtext(19, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r77 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 148);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_49_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.closeAllMenus());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_50_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 161);\n  }\n  if (rf & 2) {\n    const wave_r79 = ctx.$implicit;\n    const i_r80 = ctx.index;\n    i0.ɵɵstyleProp(\"height\", wave_r79, \"px\")(\"animation\", \"bounce 1s infinite\")(\"animation-delay\", i_r80 * 0.1 + \"s\");\n  }\n}\nfunction MessageChatComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 149)(1, \"div\", 150);\n    i0.ɵɵelement(2, \"i\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 152)(4, \"div\", 153);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 154);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_50_div_7_Template, 1, 6, \"div\", 155);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 156);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 31)(11, \"button\", 157);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_50_Template_button_click_11_listener($event) {\n      i0.ɵɵrestoreView(_r82);\n      const ctx_r81 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r81.onRecordCancel($event));\n    });\n    i0.ɵɵelement(12, \"i\", 158);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 159);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_50_Template_button_click_13_listener($event) {\n      i0.ɵɵrestoreView(_r82);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.onRecordEnd($event));\n    });\n    i0.ɵɵelement(14, \"i\", 160);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.formatRecordingDuration(ctx_r16.voiceRecordingDuration), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.voiceWaves);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Format: \", ctx_r16.getRecordingFormat(), \" \");\n  }\n}\nexport class MessageChatComponent {\n  constructor(fb, route, router, MessageService, callService, toastService, cdr) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.MessageService = MessageService;\n    this.callService = callService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    // === DONNÉES PRINCIPALES ===\n    this.conversation = null;\n    this.messages = [];\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    // === ÉTATS DE L'INTERFACE ===\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.searchMode = false;\n    this.isSendingMessage = false;\n    this.otherUserIsTyping = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n    this.contextMenuPosition = {\n      x: 0,\n      y: 0\n    };\n    this.showReactionPicker = false;\n    this.reactionPickerMessage = null;\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    this.uploadProgress = 0;\n    this.isUploading = false;\n    this.isDragOver = false;\n    // === GESTION VOCALE OPTIMISÉE ===\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    this.voiceWaves = [4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8];\n    // Lecture des messages vocaux\n    this.currentAudio = null;\n    this.playingMessageId = null;\n    this.voicePlayback = {};\n    // === APPELS WEBRTC ===\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // État de l'appel WebRTC\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    // === ÉMOJIS ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    // === PAGINATION ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    // === AUTRES ÉTATS ===\n    this.isTyping = false;\n    this.isUserTyping = false;\n    this.typingTimeout = null;\n    this.subscriptions = new Subscription();\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled() {\n    return !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage;\n  }\n  // Méthode pour gérer l'état du contrôle de saisie\n  updateInputState() {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n  ngOnInit() {\n    console.log('🚀 MessageChatComponent initialized');\n    this.initializeComponent();\n  }\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n  setupCallSubscriptions() {\n    // S'abonner aux appels entrants\n    this.subscriptions.add(this.callService.incomingCall$.subscribe({\n      next: incomingCall => {\n        if (incomingCall) {\n          console.log('📞 Incoming call received:', incomingCall);\n          this.handleIncomingCall(incomingCall);\n        }\n      },\n      error: error => {\n        console.error('❌ Error in incoming call subscription:', error);\n      }\n    }));\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(this.callService.activeCall$.subscribe({\n      next: call => {\n        if (call) {\n          console.log('📞 Active call updated:', call);\n          this.activeCall = call;\n        }\n      },\n      error: error => {\n        console.error('❌ Error in active call subscription:', error);\n      }\n    }));\n  }\n  handleIncomingCall(incomingCall) {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log('🔔 Handling incoming call from:', incomingCall.caller.username);\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  loadCurrentUser() {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId\n      });\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  loadConversation() {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: conversation => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        this.isLoading = false;\n      }\n    });\n  }\n  setOtherParticipant() {\n    if (!this.conversation?.participants || this.conversation.participants.length === 0) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        console.log('Comparing participant ID:', participantId, 'with current user ID:', this.currentUserId);\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId = this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log('First participant is current user, using second participant');\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline\n      });\n      // Log très visible pour debug\n      console.log('🎯 FINAL RESULT: otherParticipant =', this.otherParticipant.username);\n      console.log('🎯 Should display in sidebar:', this.otherParticipant.username);\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n  loadMessages() {\n    if (!this.conversation?.id) return;\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a, b) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content\n    });\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n    this.MessageService.getMessages(this.currentUserId,\n    // senderId\n    this.otherParticipant?.id || this.otherParticipant?._id,\n    // receiverId\n    this.conversation.id, this.currentPage, this.MAX_MESSAGES_TO_LOAD).subscribe({\n      next: newMessages => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages = newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      }\n    });\n  }\n\n  setupSubscriptions() {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n    console.log('🔄 Setting up real-time subscriptions for conversation:', this.conversation.id);\n    // Subscription pour les nouveaux messages\n    console.log('📨 Setting up message subscription...');\n    this.subscriptions.add(this.MessageService.subscribeToNewMessages(this.conversation.id).subscribe({\n      next: newMessage => {\n        console.log('📨 New message received via subscription:', newMessage);\n        console.log('📨 Message structure:', {\n          id: newMessage.id,\n          type: newMessage.type,\n          content: newMessage.content,\n          sender: newMessage.sender,\n          senderId: newMessage.senderId,\n          receiverId: newMessage.receiverId,\n          attachments: newMessage.attachments\n        });\n        // Debug des attachments\n        console.log('📨 [Debug] Message type detected:', this.getMessageType(newMessage));\n        console.log('📨 [Debug] Has image:', this.hasImage(newMessage));\n        console.log('📨 [Debug] Has file:', this.hasFile(newMessage));\n        console.log('📨 [Debug] Image URL:', this.getImageUrl(newMessage));\n        if (newMessage.attachments) {\n          newMessage.attachments.forEach((att, index) => {\n            console.log(`📨 [Debug] Attachment ${index}:`, {\n              type: att.type,\n              url: att.url,\n              path: att.path,\n              name: att.name,\n              size: att.size\n            });\n          });\n        }\n        // Ajouter le message à la liste s'il n'existe pas déjà\n        const messageExists = this.messages.some(msg => msg.id === newMessage.id);\n        if (!messageExists) {\n          // Ajouter le nouveau message à la fin (en bas)\n          this.messages.push(newMessage);\n          console.log('✅ Message added to list, total messages:', this.messages.length);\n          // Forcer la détection de changements\n          this.cdr.detectChanges();\n          // Scroll vers le bas après un court délai\n          setTimeout(() => {\n            this.scrollToBottom();\n          }, 50);\n          // Marquer comme lu si ce n'est pas notre message\n          const senderId = newMessage.sender?.id || newMessage.senderId;\n          console.log('📨 Checking if message should be marked as read:', {\n            senderId,\n            currentUserId: this.currentUserId,\n            shouldMarkAsRead: senderId !== this.currentUserId\n          });\n          if (senderId && senderId !== this.currentUserId) {\n            this.markMessageAsRead(newMessage.id);\n          }\n        }\n      },\n      error: error => {\n        console.error('❌ Error in message subscription:', error);\n      }\n    }));\n    // Subscription pour les indicateurs de frappe\n    console.log('📝 Setting up typing indicator subscription...');\n    this.subscriptions.add(this.MessageService.subscribeToTypingIndicator(this.conversation.id).subscribe({\n      next: typingData => {\n        console.log('📝 Typing indicator received:', typingData);\n        // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n        if (typingData.userId !== this.currentUserId) {\n          this.otherUserIsTyping = typingData.isTyping;\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in typing subscription:', error);\n      }\n    }));\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(this.MessageService.subscribeToConversationUpdates(this.conversation.id).subscribe({\n      next: conversationUpdate => {\n        console.log('📋 Conversation update:', conversationUpdate);\n        // Mettre à jour la conversation si nécessaire\n        if (conversationUpdate.id === this.conversation.id) {\n          this.conversation = {\n            ...this.conversation,\n            ...conversationUpdate\n          };\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in conversation subscription:', error);\n      }\n    }));\n  }\n  markMessageAsRead(messageId) {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {\n        console.log('✅ Message marked as read:', messageId);\n      },\n      error: error => {\n        console.error('❌ Error marking message as read:', error);\n      }\n    });\n  }\n  // === ENVOI DE MESSAGES ===\n  sendMessage() {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id\n    });\n    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n      next: message => {\n        console.log('✅ Message sent successfully:', message);\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(msg => msg.id === message.id);\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log('📋 Message added to local list, total:', this.messages.length);\n        }\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: error => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      }\n    });\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const diffMins = Math.floor((Date.now() - new Date(lastActive).getTime()) / 60000);\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId) {\n    return this.voicePlayback[messageId] || {\n      progress: 0,\n      duration: 0,\n      currentTime: 0,\n      speed: 1\n    };\n  }\n  setVoicePlaybackData(messageId, data) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data\n    };\n  }\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // isVoicePlaying, playVoiceMessage, toggleVoicePlayback - définies plus loin\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n  startVideoCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    console.log('📹 Starting video call with:', this.otherParticipant.username);\n    this.initiateCall(CallType.VIDEO);\n  }\n  startVoiceCall() {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n    console.log('📞 Starting voice call with:', this.otherParticipant.username);\n    this.initiateCall(CallType.AUDIO);\n  }\n  endCall() {\n    this.isInCall = false;\n    this.callType = null;\n    this.activeCall = null;\n    console.log('📞 Call ended');\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n  formatFileSize(bytes) {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  downloadFile(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n  toggleSearch() {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n  toggleMainMenu() {\n    this.showMainMenu = !this.showMainMenu;\n  }\n  goBackToConversations() {\n    console.log('🔙 Going back to conversations');\n    // Naviguer vers la liste des conversations\n    this.router.navigate(['/front/messages/conversations']).then(() => {\n      console.log('✅ Navigation to conversations successful');\n    }).catch(error => {\n      console.error('❌ Navigation error:', error);\n      // Fallback: essayer la route parent\n      this.router.navigate(['/front/messages']).catch(() => {\n        // Dernier recours: recharger la page\n        window.location.href = '/front/messages/conversations';\n      });\n    });\n  }\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showMessageContextMenu = true;\n  }\n  showQuickReactions(message, event) {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    this.showReactionPicker = true;\n  }\n  quickReact(emoji) {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n  toggleReaction(messageId, emoji) {\n    console.log('👍 Toggling reaction:', emoji, 'for message:', messageId);\n    // Implémentation de la réaction\n  }\n\n  hasUserReacted(reaction, userId) {\n    return reaction.userId === userId;\n  }\n  replyToMessage(message) {\n    console.log('↩️ Replying to message:', message.id);\n    this.closeAllMenus();\n  }\n  forwardMessage(message) {\n    console.log('➡️ Forwarding message:', message.id);\n    this.closeAllMenus();\n  }\n  deleteMessage(message) {\n    console.log('🗑️ Deleting message:', message.id);\n    this.closeAllMenus();\n  }\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.showEmojiPicker = false;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODES POUR LA GESTION DE LA FRAPPE ===\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // handleTypingIndicator - définie plus loin\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n  trackByMessageId(index, message) {\n    return message.id || message._id || index.toString();\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  testAddMessage() {\n    console.log('🧪 Test: Adding message');\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image: this.otherParticipant?.image || 'assets/images/default-avatar.png'\n      },\n      type: 'TEXT',\n      isRead: false\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n  isGroupConversation() {\n    return this.conversation?.isGroup || this.conversation?.participants?.length > 2 || false;\n  }\n  openCamera() {\n    console.log('📷 Opening camera');\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor) {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1');\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n  resetZoom() {\n    const imageElement = document.querySelector('.image-viewer-zoom');\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  triggerFileInput(type) {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n  formatMessageContent(content) {\n    if (!content) return '';\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(urlRegex, '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>');\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  shouldShowAvatar(index) {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    if (!nextMessage) return true;\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n  shouldShowSenderName(index) {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!previousMessage) return true;\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n  getMessageType(message) {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n    return 'text';\n  }\n  hasImage(message) {\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      return true;\n    }\n    // Vérifier les attachments\n    const hasImageAttachment = message.attachments?.some(att => {\n      return att.type?.startsWith('image/') || att.type === 'IMAGE';\n    }) || false;\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n    return hasImageAttachment || hasImageUrl;\n  }\n  hasFile(message) {\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      return true;\n    }\n    // Vérifier les attachments non-image\n    const hasFileAttachment = message.attachments?.some(att => {\n      return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n    }) || false;\n    return hasFileAttachment;\n  }\n  getImageUrl(message) {\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      return message.imageUrl;\n    }\n    if (message.image) {\n      return message.image;\n    }\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/') || att.type === 'IMAGE');\n    if (imageAttachment) {\n      return imageAttachment.url || imageAttachment.path || '';\n    }\n    return '';\n  }\n  getFileName(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    return fileAttachment?.name || 'Fichier';\n  }\n  getFileSize(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.size) return '';\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  getFileIcon(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.type) return 'fas fa-file';\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n  getUserColor(userId) {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message, event) {\n    console.log('Message clicked:', message);\n  }\n  onInputChange(event) {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInputFocus() {\n    // Gérer le focus sur le champ de saisie\n  }\n  onInputBlur() {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n  onScroll(event) {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {\n      this.loadMoreMessages();\n    }\n  }\n  openUserProfile(userId) {\n    console.log('Opening user profile for:', userId);\n  }\n  onImageLoad(event, message) {\n    console.log('🖼️ [Debug] Image loaded successfully for message:', message.id, event.target.src);\n  }\n  onImageError(event, message) {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n  openImageViewer(message) {\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message\n      };\n      this.showImageViewer = true;\n      console.log('🖼️ [ImageViewer] Opening image:', this.selectedImage);\n    }\n  }\n  closeImageViewer() {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    console.log('🖼️ [ImageViewer] Closed');\n  }\n  downloadImage() {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log('🖼️ [ImageViewer] Download started:', this.selectedImage.name);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  searchMessages() {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n    this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n  onSearchQueryChange() {\n    this.searchMessages();\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n  jumpToMessage(messageId) {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  closeContextMenu() {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n  initiateCall(callType) {\n    console.log('🚀 [MessageChat] Starting call initiation process...');\n    console.log('📋 [MessageChat] Call details:', {\n      callType,\n      otherParticipant: this.otherParticipant,\n      conversation: this.conversation?.id,\n      currentUserId: this.currentUserId\n    });\n    if (!this.otherParticipant) {\n      console.error('❌ [MessageChat] No recipient selected');\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      console.error('❌ [MessageChat] Recipient ID not found');\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n    console.log(`📞 [MessageChat] Initiating ${callType} call to user:`, {\n      recipientId,\n      recipientName: this.otherParticipant.username || this.otherParticipant.name,\n      conversationId: this.conversation?.id\n    });\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n    console.log('🔄 [MessageChat] Calling CallService.initiateCall...');\n    // Utiliser le CallService\n    this.callService.initiateCall(recipientId, callType, this.conversation?.id).subscribe({\n      next: call => {\n        console.log('✅ [MessageChat] Call initiated successfully:', {\n          callId: call.id,\n          callType: call.type,\n          callStatus: call.status,\n          caller: call.caller?.username,\n          recipient: call.recipient?.username,\n          conversationId: call.conversationId\n        });\n        this.activeCall = call;\n        this.isCallConnected = false;\n        this.toastService.showSuccess(`Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`);\n        console.log('📡 [MessageChat] Call should now be sent to recipient via WebSocket');\n      },\n      error: error => {\n        console.error('❌ [MessageChat] Error initiating call:', {\n          error: error.message || error,\n          recipientId,\n          callType,\n          conversationId: this.conversation?.id\n        });\n        this.endCall();\n        this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n      }\n    });\n  }\n  acceptCall(incomingCall) {\n    console.log('🔄 Accepting incoming call:', incomingCall);\n    this.callService.acceptCall(incomingCall).subscribe({\n      next: call => {\n        console.log('✅ Call accepted successfully:', call);\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: error => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      }\n    });\n  }\n  rejectCall(incomingCall) {\n    console.log('🔄 Rejecting incoming call:', incomingCall);\n    this.callService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        console.log('✅ Call rejected successfully');\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: error => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      }\n    });\n  }\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // endCall - Cette méthode est déjà définie plus loin dans le fichier\n  startCallTimer() {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n  resetCallState() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n  // === CONTRÔLES D'APPEL ===\n  toggleMute() {\n    if (!this.activeCall) return;\n    this.isMuted = !this.isMuted;\n    // Utiliser la méthode toggleMedia du CallService\n    this.callService.toggleMedia(this.activeCall.id, undefined,\n    // video unchanged\n    !this.isMuted // audio state\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isMuted ? 'Micro coupé' : 'Micro activé');\n      },\n      error: error => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        this.toastService.showError('Erreur lors du changement du micro');\n      }\n    });\n  }\n  toggleVideo() {\n    if (!this.activeCall) return;\n    this.isVideoEnabled = !this.isVideoEnabled;\n    // Utiliser la méthode toggleMedia du CallService\n    this.callService.toggleMedia(this.activeCall.id, this.isVideoEnabled,\n    // video state\n    undefined // audio unchanged\n    ).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      error: error => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.toastService.showError('Erreur lors du changement de la caméra');\n      }\n    });\n  }\n  formatCallDuration(duration) {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor(duration % 3600 / 60);\n    const seconds = duration % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎤 [Voice] Starting voice recording...');\n      try {\n        // Vérifier le support du navigateur\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n          throw new Error(\"Votre navigateur ne supporte pas l'enregistrement audio\");\n        }\n        // Vérifier si MediaRecorder est supporté\n        if (!window.MediaRecorder) {\n          throw new Error(\"MediaRecorder n'est pas supporté par votre navigateur\");\n        }\n        console.log('🎤 [Voice] Requesting microphone access...');\n        // Demander l'accès au microphone avec des contraintes optimisées\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            sampleRate: 44100,\n            channelCount: 1\n          }\n        });\n        console.log('🎤 [Voice] Microphone access granted');\n        // Vérifier les types MIME supportés\n        let mimeType = 'audio/webm;codecs=opus';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/webm';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = 'audio/mp4';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n              mimeType = ''; // Laisser le navigateur choisir\n            }\n          }\n        }\n\n        console.log('🎤 [Voice] Using MIME type:', mimeType);\n        // Créer le MediaRecorder\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: mimeType || undefined\n        });\n        // Initialiser les variables\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        console.log('🎤 [Voice] MediaRecorder created, starting timer...');\n        // Démarrer le timer\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          // Animer les waves\n          _this.animateVoiceWaves();\n          _this.cdr.detectChanges();\n        }, 1000);\n        // Gérer les événements du MediaRecorder\n        _this.mediaRecorder.ondataavailable = event => {\n          console.log('🎤 [Voice] Data available:', event.data.size, 'bytes');\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          console.log('🎤 [Voice] MediaRecorder stopped, processing audio...');\n          _this.processRecordedAudio();\n        };\n        _this.mediaRecorder.onerror = event => {\n          console.error('🎤 [Voice] MediaRecorder error:', event.error);\n          _this.toastService.showError(\"Erreur lors de l'enregistrement\");\n          _this.cancelVoiceRecording();\n        };\n        // Démarrer l'enregistrement\n        _this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n        console.log('🎤 [Voice] Recording started successfully');\n        _this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n      } catch (error) {\n        console.error('🎤 [Voice] Error starting recording:', error);\n        let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n        if (error.name === 'NotAllowedError') {\n          errorMessage = \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n        } else if (error.name === 'NotFoundError') {\n          errorMessage = 'Aucun microphone détecté. Veuillez connecter un microphone.';\n        } else if (error.name === 'NotSupportedError') {\n          errorMessage = \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n        _this.toastService.showError(errorMessage);\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🎤 [Voice] Processing recorded audio...');\n      try {\n        // Vérifier qu'on a des données audio\n        if (_this2.audioChunks.length === 0) {\n          console.error('🎤 [Voice] No audio chunks available');\n          _this2.toastService.showError('Aucun audio enregistré');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        console.log('🎤 [Voice] Audio chunks:', _this2.audioChunks.length, 'Duration:', _this2.voiceRecordingDuration);\n        // Vérifier la durée minimale\n        if (_this2.voiceRecordingDuration < 1) {\n          console.error('🎤 [Voice] Recording too short:', _this2.voiceRecordingDuration);\n          _this2.toastService.showError('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        // Déterminer le type MIME du blob\n        let mimeType = 'audio/webm;codecs=opus';\n        if (_this2.mediaRecorder?.mimeType) {\n          mimeType = _this2.mediaRecorder.mimeType;\n        }\n        console.log('🎤 [Voice] Creating audio blob with MIME type:', mimeType);\n        // Créer le blob audio\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio blob created:', {\n          size: audioBlob.size,\n          type: audioBlob.type\n        });\n        // Déterminer l'extension du fichier\n        let extension = '.webm';\n        if (mimeType.includes('mp4')) {\n          extension = '.mp4';\n        } else if (mimeType.includes('wav')) {\n          extension = '.wav';\n        } else if (mimeType.includes('ogg')) {\n          extension = '.ogg';\n        }\n        // Créer le fichier\n        const audioFile = new File([audioBlob], `voice_${Date.now()}${extension}`, {\n          type: mimeType\n        });\n        console.log('🎤 [Voice] Audio file created:', {\n          name: audioFile.name,\n          size: audioFile.size,\n          type: audioFile.type\n        });\n        // Envoyer le message vocal\n        _this2.voiceRecordingState = 'processing';\n        yield _this2.sendVoiceMessage(audioFile);\n        console.log('🎤 [Voice] Voice message sent successfully');\n        _this2.toastService.showSuccess('🎤 Message vocal envoyé');\n      } catch (error) {\n        console.error('🎤 [Voice] Error processing audio:', error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal: \" + (error.message || 'Erreur inconnue'));\n      } finally {\n        // Nettoyer l'état\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n        _this2.isRecordingVoice = false;\n        console.log('🎤 [Voice] Audio processing completed, state reset');\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const receiverId = _this3.otherParticipant?.id || _this3.otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this3.MessageService.sendMessage(receiverId, '', audioFile, 'AUDIO', _this3.conversation.id).subscribe({\n          next: message => {\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n  onRecordStart(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record start triggered');\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder\n    });\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      console.log('🎤 [Voice] Already processing, ignoring start');\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n    if (this.isRecordingVoice) {\n      console.log('🎤 [Voice] Already recording, ignoring start');\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch(error => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\"Impossible de démarrer l'enregistrement vocal: \" + (error.message || 'Erreur inconnue'));\n    });\n  }\n  onRecordEnd(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record end triggered');\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring end');\n      return;\n    }\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n  onRecordCancel(event) {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record cancel triggered');\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring cancel');\n      return;\n    }\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n  getRecordingFormat() {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n  // === ANIMATION DES WAVES VOCALES ===\n  animateVoiceWaves() {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event) {\n    console.log('📁 [Upload] File selection triggered');\n    const files = event.target.files;\n    if (!files || files.length === 0) {\n      console.log('📁 [Upload] No files selected');\n      return;\n    }\n    console.log(`📁 [Upload] ${files.length} file(s) selected:`, files);\n    for (let file of files) {\n      console.log(`📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`);\n      this.uploadFile(file);\n    }\n  }\n  uploadFile(file) {\n    console.log(`📁 [Upload] Starting upload for file: ${file.name}`);\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    console.log(`📁 [Upload] Receiver ID: ${receiverId}`);\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log('🖼️ [Compression] Compressing image:', file.name, 'Original size:', file.size);\n      this.compressImage(file).then(compressedFile => {\n        console.log('🖼️ [Compression] ✅ Image compressed successfully. New size:', compressedFile.size);\n        this.sendFileToServer(compressedFile, receiverId);\n      }).catch(error => {\n        console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n        // Envoyer le fichier original en cas d'erreur\n        this.sendFileToServer(file, receiverId);\n      });\n      return;\n    }\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n  sendFileToServer(file, receiverId) {\n    const messageType = this.getFileMessageType(file);\n    console.log(`📁 [Upload] Message type determined: ${messageType}`);\n    console.log(`📁 [Upload] Conversation ID: ${this.conversation.id}`);\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    console.log('📁 [Upload] Calling MessageService.sendMessage...');\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n    this.MessageService.sendMessage(receiverId, '', file, messageType, this.conversation.id).subscribe({\n      next: message => {\n        console.log('📁 [Upload] ✅ File sent successfully:', message);\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message)\n        });\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: error => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      }\n    });\n  }\n  getFileMessageType(file) {\n    if (file.type.startsWith('image/')) return 'IMAGE';\n    if (file.type.startsWith('video/')) return 'VIDEO';\n    if (file.type.startsWith('audio/')) return 'AUDIO';\n    return 'FILE';\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  resetUploadState() {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n  // === DRAG & DROP ===\n  onDragOver(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n  onDragLeave(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = event.currentTarget.getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n  onDrop(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      console.log('📁 [Drag&Drop] Files dropped:', files.length);\n      // Traiter chaque fichier\n      Array.from(files).forEach(file => {\n        console.log('📁 [Drag&Drop] Processing file:', file.name, file.type, file.size);\n        this.uploadFile(file);\n      });\n      this.toastService.showSuccess(`${files.length} fichier(s) en cours d'envoi`);\n    }\n  }\n  // === COMPRESSION D'IMAGES ===\n  compressImage(file, quality = 0.8) {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let {\n          width,\n          height\n        } = img;\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n        canvas.width = width;\n        canvas.height = height;\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n        // Convertir en blob avec compression\n        canvas.toBlob(blob => {\n          if (blob) {\n            const compressedFile = new File([blob], file.name, {\n              type: file.type,\n              lastModified: Date.now()\n            });\n            resolve(compressedFile);\n          } else {\n            reject(new Error('Failed to compress image'));\n          }\n        }, file.type, quality);\n      };\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n  handleTypingIndicator() {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n  sendTypingIndicator(isTyping) {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(`📝 Sending typing indicator: ${isTyping} to user ${receiverId}`);\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n  onCallAccepted(call) {\n    console.log('🔄 Call accepted from interface:', call);\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n  onCallRejected() {\n    console.log('🔄 Call rejected from interface');\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n  playVoiceMessage(message) {\n    console.log('🎵 [Voice] Playing voice message:', message.id);\n    this.toggleVoicePlayback(message);\n  }\n  isVoicePlaying(messageId) {\n    return this.playingMessageId === messageId;\n  }\n  toggleVoicePlayback(message) {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n  startVoicePlayback(message, audioUrl) {\n    const messageId = message.id;\n    try {\n      console.log('🎵 [Voice] Starting playback for:', messageId, 'URL:', audioUrl);\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0\n      });\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration\n          });\n          console.log('🎵 [Voice] Audio loaded, duration:', this.currentAudio.duration);\n        }\n      });\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = currentTime / this.currentAudio.duration * 100;\n          this.setVoicePlaybackData(messageId, {\n            currentTime,\n            progress\n          });\n          this.cdr.detectChanges();\n        }\n      });\n      this.currentAudio.addEventListener('ended', () => {\n        console.log('🎵 [Voice] Playback ended for:', messageId);\n        this.stopVoicePlayback();\n      });\n      this.currentAudio.addEventListener('error', error => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n      // Démarrer la lecture\n      this.currentAudio.play().then(() => {\n        console.log('🎵 [Voice] Playback started successfully');\n        this.toastService.showSuccess('🎵 Lecture du message vocal');\n      }).catch(error => {\n        console.error('🎵 [Voice] Error starting playback:', error);\n        this.toastService.showError('Impossible de lire le message vocal');\n        this.stopVoicePlayback();\n      });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n  stopVoicePlayback() {\n    if (this.currentAudio) {\n      console.log('🎵 [Voice] Stopping playback for:', this.playingMessageId);\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n  getVoiceUrl(message) {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(att => att.type?.startsWith('audio/') || att.type === 'AUDIO');\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n    return '';\n  }\n  getVoiceWaves(message) {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);\n    const waves = [];\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + (seed + i * 7) % 20;\n      waves.push(height);\n    }\n    return waves;\n  }\n  getVoiceProgress(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor(data.progress / 100 * totalWaves);\n  }\n  getVoiceCurrentTime(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n  getVoiceDuration(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n  formatAudioTime(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  seekVoiceMessage(message, waveIndex) {\n    const messageId = message.id;\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n    const totalWaves = 16;\n    const seekPercentage = waveIndex / totalWaves * 100;\n    const seekTime = seekPercentage / 100 * this.currentAudio.duration;\n    this.currentAudio.currentTime = seekTime;\n    console.log('🎵 [Voice] Seeking to:', seekTime, 'seconds');\n  }\n  toggleVoiceSpeed(message) {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n    this.setVoicePlaybackData(messageId, {\n      speed: newSpeed\n    });\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  changeVoiceSpeed(message) {\n    this.toggleVoiceSpeed(message);\n  }\n  getVoiceSpeed(message) {\n    const data = this.getVoicePlaybackData(message.id);\n    return data.speed || 1;\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach(track => track.stop());\n    }\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.CallService), i0.ɵɵdirectiveInject(i5.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 52,\n      vars: 58,\n      consts: [[2, \"display\", \"flex\", \"flex-direction\", \"column\", \"height\", \"100vh\", \"background\", \"linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)\", \"color\", \"#1f2937\", \"font-family\", \"'Inter', -apple-system, BlinkMacSystemFont, sans-serif\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"padding\", \"12px 16px\", \"background\", \"#ffffff\", \"border-bottom\", \"1px solid #e5e7eb\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"z-index\", \"10\"], [\"onmouseover\", \"this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.background='transparent'; this.style.transform='scale(1)'\", \"title\", \"Retour aux conversations\", 2, \"padding\", \"10px\", \"margin-right\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"40px\", \"min-height\", \"40px\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", 2, \"color\", \"#374151\", \"font-size\", \"18px\", \"font-weight\", \"bold\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"flex\", \"1\", \"min-width\", \"0\"], [2, \"position\", \"relative\", \"margin-right\", \"12px\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", \"title\", \"Voir le profil\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"border\", \"2px solid transparent\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s ease\", 3, \"src\", \"alt\", \"click\"], [\"style\", \"\\n            position: absolute;\\n            bottom: 0;\\n            right: 0;\\n            width: 12px;\\n            height: 12px;\\n            background: #10b981;\\n            border: 2px solid transparent;\\n            border-radius: 50%;\\n            animation: pulse 2s infinite;\\n          \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"min-width\", \"0\"], [2, \"font-weight\", \"600\", \"color\", \"#111827\", \"margin\", \"0\", \"font-size\", \"16px\", \"white-space\", \"nowrap\", \"overflow\", \"hidden\", \"text-overflow\", \"ellipsis\"], [2, \"font-size\", \"14px\", \"color\", \"#6b7280\", \"margin-top\", \"2px\"], [\"style\", \"display: flex; align-items: center; gap: 4px; color: #10b981\", 4, \"ngIf\"], [4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"title\", \"Appel vid\\u00E9o\", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [\"title\", \"Appel vocal\", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Rechercher\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Menu\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"position\", \"relative\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [\"style\", \"\\n        position: absolute;\\n        top: 64px;\\n        right: 16px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        min-width: 192px;\\n      \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"overflow-y\", \"auto\", \"padding\", \"16px\", \"position\", \"relative\", 3, \"scroll\", \"dragover\", \"dragleave\", \"drop\"], [\"messagesContainer\", \"\"], [\"style\", \"\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        right: 0;\\n        bottom: 0;\\n        background: rgba(34, 197, 94, 0.2);\\n        border: 2px dashed transparent;\\n        border-radius: 8px;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        z-index: 50;\\n        backdrop-filter: blur(2px);\\n        animation: pulse 2s infinite;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 32px 0;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        padding: 64px 0;\\n      \", 4, \"ngIf\"], [\"style\", \"display: flex; flex-direction: column; gap: 8px\", 4, \"ngIf\"], [2, \"background\", \"#ffffff\", \"border-top\", \"1px solid #e5e7eb\", \"padding\", \"16px\"], [2, \"display\", \"flex\", \"align-items\", \"end\", \"gap\", \"12px\", 3, \"formGroup\", \"ngSubmit\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"type\", \"button\", \"title\", \"Pi\\u00E8ces jointes\", \"onmouseover\", \"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\", \"onmouseout\", \"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer un message vocal\", \"onmouseover\", \"if(!this.style.background || this.style.background === 'transparent') this.style.background='#f3f4f6'\", \"onmouseout\", \"if(this.style.background === '#f3f4f6') this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"transparent\", \"color\", \"#6b7280\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"position\", \"relative\", 3, \"mousedown\", \"mouseup\", \"mouseleave\", \"touchstart\", \"touchend\", \"touchcancel\"], [\"style\", \"\\n              position: absolute;\\n              top: -2px;\\n              right: -2px;\\n              width: 8px;\\n              height: 8px;\\n              background: #ef4444;\\n              border-radius: 50%;\\n              animation: ping 1s infinite;\\n            \", 4, \"ngIf\"], [2, \"flex\", \"1\", \"position\", \"relative\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", 2, \"width\", \"100%\", \"min-height\", \"44px\", \"max-height\", \"120px\", \"padding\", \"12px 16px\", \"border\", \"1px solid #e5e7eb\", \"border-radius\", \"22px\", \"resize\", \"none\", \"outline\", \"none\", \"font-family\", \"inherit\", \"font-size\", \"14px\", \"line-height\", \"1.4\", \"background\", \"#ffffff\", \"color\", \"#111827\", \"transition\", \"all 0.2s\", 3, \"disabled\", \"keydown\", \"input\", \"focus\"], [\"type\", \"submit\", \"title\", \"Envoyer\", \"onmouseover\", \"if(!this.disabled) this.style.background='#2563eb'\", \"onmouseout\", \"if(!this.disabled) this.style.background='#3b82f6'\", 2, \"padding\", \"12px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"#3b82f6\", \"color\", \"#ffffff\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"44px\", \"min-height\", \"44px\", 3, \"disabled\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"style\", \"\\n            width: 16px;\\n            height: 16px;\\n            border: 2px solid #ffffff;\\n            border-top-color: transparent;\\n            border-radius: 50%;\\n            animation: spin 1s linear infinite;\\n          \", 4, \"ngIf\"], [\"style\", \"\\n        position: absolute;\\n        bottom: 80px;\\n        left: 16px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        width: 320px;\\n        max-height: 300px;\\n        overflow-y: auto;\\n      \", 4, \"ngIf\"], [\"style\", \"\\n        position: absolute;\\n        bottom: 80px;\\n        left: 60px;\\n        background: #ffffff;\\n        border-radius: 16px;\\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\\n        border: 1px solid #e5e7eb;\\n        z-index: 50;\\n        min-width: 200px;\\n      \", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"style\", \"\\n      position: fixed;\\n      top: 0;\\n      left: 0;\\n      right: 0;\\n      bottom: 0;\\n      background: rgba(0, 0, 0, 0.25);\\n      z-index: 40;\\n    \", 3, \"click\", 4, \"ngIf\"], [\"style\", \"\\n      position: fixed;\\n      bottom: 100px;\\n      left: 50%;\\n      transform: translateX(-50%);\\n      background: linear-gradient(135deg, #f59e0b, #d97706);\\n      color: white;\\n      padding: 20px 24px;\\n      border-radius: 20px;\\n      box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);\\n      z-index: 60;\\n      display: flex;\\n      align-items: center;\\n      gap: 16px;\\n      min-width: 280px;\\n      animation: slideInUp 0.3s ease-out;\\n    \", 4, \"ngIf\"], [3, \"isVisible\", \"activeCall\", \"callType\", \"otherParticipant\", \"callEnded\", \"callAccepted\", \"callRejected\"], [2, \"position\", \"absolute\", \"bottom\", \"0\", \"right\", \"0\", \"width\", \"12px\", \"height\", \"12px\", \"background\", \"#10b981\", \"border\", \"2px solid transparent\", \"border-radius\", \"50%\", \"animation\", \"pulse 2s infinite\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"gap\", \"2px\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"4px\", \"height\", \"4px\", \"background\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [2, \"position\", \"absolute\", \"top\", \"64px\", \"right\", \"16px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"min-width\", \"192px\"], [2, \"padding\", \"8px\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"8px 12px\", \"border-radius\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"text-align\", \"left\", 3, \"click\"], [1, \"fas\", \"fa-search\", 2, \"color\", \"#3b82f6\"], [2, \"color\", \"#374151\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"8px 12px\", \"border-radius\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", \"text-align\", \"left\"], [1, \"fas\", \"fa-user\", 2, \"color\", \"#10b981\"], [2, \"margin\", \"8px 0\", \"border-color\", \"#e5e7eb\"], [1, \"fas\", \"fa-cog\", 2, \"color\", \"#6b7280\"], [2, \"position\", \"absolute\", \"top\", \"0\", \"left\", \"0\", \"right\", \"0\", \"bottom\", \"0\", \"background\", \"rgba(34, 197, 94, 0.2)\", \"border\", \"2px dashed transparent\", \"border-radius\", \"8px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"z-index\", \"50\", \"backdrop-filter\", \"blur(2px)\", \"animation\", \"pulse 2s infinite\"], [2, \"text-align\", \"center\", \"background\", \"#ffffff\", \"padding\", \"24px\", \"border-radius\", \"12px\", \"box-shadow\", \"0 10px 15px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid transparent\"], [1, \"fas\", \"fa-cloud-upload-alt\", 2, \"font-size\", \"48px\", \"color\", \"#10b981\", \"margin-bottom\", \"12px\", \"animation\", \"bounce 1s infinite\"], [2, \"font-size\", \"20px\", \"font-weight\", \"bold\", \"color\", \"#047857\", \"margin-bottom\", \"8px\"], [2, \"font-size\", \"14px\", \"color\", \"#10b981\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"32px 0\"], [2, \"width\", \"32px\", \"height\", \"32px\", \"border\", \"2px solid #e5e7eb\", \"border-bottom-color\", \"#10b981\", \"border-radius\", \"50%\", \"animation\", \"spin 1s linear infinite\", \"margin-bottom\", \"16px\"], [2, \"color\", \"#6b7280\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"64px 0\"], [2, \"font-size\", \"64px\", \"color\", \"#d1d5db\", \"margin-bottom\", \"16px\"], [1, \"fas\", \"fa-comments\"], [2, \"font-size\", \"20px\", \"font-weight\", \"600\", \"color\", \"#374151\", \"margin-bottom\", \"8px\"], [2, \"color\", \"#6b7280\", \"text-align\", \"center\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\", \"gap\", \"8px\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"style\", \"display: flex; align-items: start; gap: 8px\", 4, \"ngIf\"], [\"style\", \"display: flex; justify-content: center; margin: 16px 0\", 4, \"ngIf\"], [2, \"display\", \"flex\", 3, \"id\", \"click\", \"contextmenu\"], [\"style\", \"margin-right: 8px; flex-shrink: 0\", 4, \"ngIf\"], [2, \"max-width\", \"320px\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\", \"position\", \"relative\", \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\", \"border\", \"none\"], [\"style\", \"\\n                font-size: 12px;\\n                font-weight: 600;\\n                margin-bottom: 4px;\\n                opacity: 0.75;\\n              \", 3, \"color\", 4, \"ngIf\"], [\"style\", \"word-wrap: break-word; overflow-wrap: break-word\", 4, \"ngIf\"], [\"style\", \"margin: 8px 0\", 4, \"ngIf\"], [\"style\", \"\\n                display: flex;\\n                align-items: center;\\n                gap: 12px;\\n                padding: 12px;\\n                background: rgba(255, 255, 255, 0.1);\\n                border-radius: 12px;\\n                margin: 8px 0;\\n                min-width: 200px;\\n                max-width: 280px;\\n              \", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"flex-end\", \"gap\", \"4px\", \"margin-top\", \"4px\", \"font-size\", \"12px\", \"opacity\", \"0.75\"], [\"style\", \"display: flex; align-items: center\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"justify-content\", \"center\", \"margin\", \"16px 0\"], [2, \"background\", \"#ffffff\", \"padding\", \"4px 12px\", \"border-radius\", \"20px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [2, \"font-size\", \"12px\", \"color\", \"#6b7280\"], [2, \"margin-right\", \"8px\", \"flex-shrink\", \"0\"], [\"onmouseover\", \"this.style.transform='scale(1.05)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"width\", \"32px\", \"height\", \"32px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\"], [2, \"font-size\", \"12px\", \"font-weight\", \"600\", \"margin-bottom\", \"4px\", \"opacity\", \"0.75\"], [2, \"word-wrap\", \"break-word\", \"overflow-wrap\", \"break-word\"], [3, \"innerHTML\"], [2, \"margin\", \"8px 0\"], [\"onmouseover\", \"this.style.transform='scale(1.02)'\", \"onmouseout\", \"this.style.transform='scale(1)'\", 2, \"max-width\", \"280px\", \"height\", \"auto\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"transform 0.2s\", 3, \"src\", \"alt\", \"click\", \"load\", \"error\"], [\"style\", \"font-size: 14px; margin-top: 8px; line-height: 1.4\", 3, \"color\", \"innerHTML\", 4, \"ngIf\"], [2, \"font-size\", \"14px\", \"margin-top\", \"8px\", \"line-height\", \"1.4\", 3, \"innerHTML\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"12px\", \"padding\", \"12px\", \"background\", \"rgba(255, 255, 255, 0.1)\", \"border-radius\", \"12px\", \"margin\", \"8px 0\", \"min-width\", \"200px\", \"max-width\", \"280px\"], [\"onmouseover\", \"this.style.background='rgba(255, 255, 255, 0.3)'\", \"onmouseout\", \"this.style.background='rgba(255, 255, 255, 0.2)'\", \"title\", \"Lire/Pause\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"rgba(255, 255, 255, 0.2)\", \"color\", \"inherit\", \"cursor\", \"pointer\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"transition\", \"all 0.2s\", \"flex-shrink\", \"0\", 3, \"click\"], [2, \"font-size\", \"14px\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"2px\", \"height\", \"24px\", \"overflow\", \"hidden\"], [\"style\", \"\\n                    width: 3px;\\n                    background: currentColor;\\n                    border-radius: 2px;\\n                    opacity: 0.7;\\n                    transition: height 0.3s ease;\\n                  \", 3, \"height\", \"animation\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\", \"flex-shrink\", \"0\"], [2, \"font-size\", \"12px\", \"opacity\", \"0.8\", \"min-width\", \"40px\", \"text-align\", \"right\"], [\"style\", \"\\n                    padding: 4px 8px;\\n                    border-radius: 12px;\\n                    border: none;\\n                    background: rgba(255, 255, 255, 0.2);\\n                    color: inherit;\\n                    cursor: pointer;\\n                    font-size: 11px;\\n                    transition: all 0.2s;\\n                  \", \"onmouseover\", \"this.style.background='rgba(255, 255, 255, 0.3)'\", \"onmouseout\", \"this.style.background='rgba(255, 255, 255, 0.2)'\", \"title\", \"Changer la vitesse\", 3, \"click\", 4, \"ngIf\"], [2, \"width\", \"3px\", \"background\", \"currentColor\", \"border-radius\", \"2px\", \"opacity\", \"0.7\", \"transition\", \"height 0.3s ease\"], [\"onmouseover\", \"this.style.background='rgba(255, 255, 255, 0.3)'\", \"onmouseout\", \"this.style.background='rgba(255, 255, 255, 0.2)'\", \"title\", \"Changer la vitesse\", 2, \"padding\", \"4px 8px\", \"border-radius\", \"12px\", \"border\", \"none\", \"background\", \"rgba(255, 255, 255, 0.2)\", \"color\", \"inherit\", \"cursor\", \"pointer\", \"font-size\", \"11px\", \"transition\", \"all 0.2s\", 3, \"click\"], [2, \"display\", \"flex\", \"align-items\", \"center\"], [\"class\", \"fas fa-clock\", \"title\", \"Envoi en cours\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", \"title\", \"Envoy\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"title\", \"Livr\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"style\", \"color: #3b82f6\", \"title\", \"Lu\", 4, \"ngIf\"], [\"title\", \"Envoi en cours\", 1, \"fas\", \"fa-clock\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", 2, \"color\", \"#3b82f6\"], [2, \"display\", \"flex\", \"align-items\", \"start\", \"gap\", \"8px\"], [2, \"width\", \"32px\", \"height\", \"32px\", \"border-radius\", \"50%\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [2, \"background\", \"#ffffff\", \"padding\", \"12px 16px\", \"border-radius\", \"18px\", \"box-shadow\", \"0 1px 3px rgba(0, 0, 0, 0.1)\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.1s\"], [2, \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#6b7280\", \"border-radius\", \"50%\", \"animation\", \"bounce 1s infinite 0.2s\"], [2, \"position\", \"absolute\", \"top\", \"-2px\", \"right\", \"-2px\", \"width\", \"8px\", \"height\", \"8px\", \"background\", \"#ef4444\", \"border-radius\", \"50%\", \"animation\", \"ping 1s infinite\"], [1, \"fas\", \"fa-paper-plane\"], [2, \"width\", \"16px\", \"height\", \"16px\", \"border\", \"2px solid #ffffff\", \"border-top-color\", \"transparent\", \"border-radius\", \"50%\", \"animation\", \"spin 1s linear infinite\"], [2, \"position\", \"absolute\", \"bottom\", \"80px\", \"left\", \"16px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"width\", \"320px\", \"max-height\", \"300px\", \"overflow-y\", \"auto\"], [2, \"padding\", \"16px\"], [2, \"margin\", \"0 0 12px 0\", \"font-size\", \"14px\", \"font-weight\", \"600\", \"color\", \"#374151\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(8, 1fr)\", \"gap\", \"8px\"], [\"style\", \"\\n              padding: 8px;\\n              border: none;\\n              background: transparent;\\n              border-radius: 8px;\\n              cursor: pointer;\\n              font-size: 20px;\\n              transition: all 0.2s;\\n            \", \"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"padding\", \"8px\", \"border\", \"none\", \"background\", \"transparent\", \"border-radius\", \"8px\", \"cursor\", \"pointer\", \"font-size\", \"20px\", \"transition\", \"all 0.2s\", 3, \"title\", \"click\"], [2, \"position\", \"absolute\", \"bottom\", \"80px\", \"left\", \"60px\", \"background\", \"#ffffff\", \"border-radius\", \"16px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.1)\", \"border\", \"1px solid #e5e7eb\", \"z-index\", \"50\", \"min-width\", \"200px\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(2, 1fr)\", \"gap\", \"12px\"], [\"onmouseover\", \"this.style.background='#f3f4f6'\", \"onmouseout\", \"this.style.background='transparent'\", 2, \"display\", \"flex\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"gap\", \"8px\", \"padding\", \"16px\", \"border\", \"none\", \"background\", \"transparent\", \"border-radius\", \"12px\", \"cursor\", \"pointer\", \"transition\", \"all 0.2s\", 3, \"click\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#dbeafe\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-image\", 2, \"color\", \"#3b82f6\", \"font-size\", \"20px\"], [2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#374151\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#fef3c7\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-file-alt\", 2, \"color\", \"#f59e0b\", \"font-size\", \"20px\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"#dcfce7\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [1, \"fas\", \"fa-camera\", 2, \"color\", \"#10b981\", \"font-size\", \"20px\"], [2, \"position\", \"fixed\", \"top\", \"0\", \"left\", \"0\", \"right\", \"0\", \"bottom\", \"0\", \"background\", \"rgba(0, 0, 0, 0.25)\", \"z-index\", \"40\", 3, \"click\"], [2, \"position\", \"fixed\", \"bottom\", \"100px\", \"left\", \"50%\", \"transform\", \"translateX(-50%)\", \"background\", \"linear-gradient(135deg, #f59e0b, #d97706)\", \"color\", \"white\", \"padding\", \"20px 24px\", \"border-radius\", \"20px\", \"box-shadow\", \"0 20px 25px rgba(0, 0, 0, 0.2)\", \"z-index\", \"60\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"min-width\", \"280px\", \"animation\", \"slideInUp 0.3s ease-out\"], [2, \"width\", \"48px\", \"height\", \"48px\", \"background\", \"rgba(255, 255, 255, 0.2)\", \"border-radius\", \"50%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"animation\", \"pulse 1s infinite\"], [1, \"fas\", \"fa-microphone\", 2, \"font-size\", \"20px\"], [2, \"flex\", \"1\"], [2, \"font-size\", \"18px\", \"font-weight\", \"bold\", \"margin-bottom\", \"4px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"2px\", \"height\", \"20px\"], [\"style\", \"\\n            width: 3px;\\n            background: rgba(255, 255, 255, 0.8);\\n            border-radius: 2px;\\n            transition: height 0.3s ease;\\n          \", 3, \"height\", \"animation\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [2, \"font-size\", \"12px\", \"opacity\", \"0.8\", \"margin-top\", \"4px\"], [\"onmouseover\", \"this.style.background='rgba(239, 68, 68, 1)'\", \"onmouseout\", \"this.style.background='rgba(239, 68, 68, 0.8)'\", \"title\", \"Annuler l'enregistrement\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"rgba(239, 68, 68, 0.8)\", \"color\", \"white\", \"cursor\", \"pointer\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-times\", 2, \"font-size\", \"16px\"], [\"onmouseover\", \"this.style.background='rgba(34, 197, 94, 1)'\", \"onmouseout\", \"this.style.background='rgba(34, 197, 94, 0.8)'\", \"title\", \"Envoyer le message vocal\", 2, \"width\", \"40px\", \"height\", \"40px\", \"border-radius\", \"50%\", \"border\", \"none\", \"background\", \"rgba(34, 197, 94, 0.8)\", \"color\", \"white\", \"cursor\", \"pointer\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"transition\", \"all 0.2s\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\", 2, \"font-size\", \"16px\"], [2, \"width\", \"3px\", \"background\", \"rgba(255, 255, 255, 0.8)\", \"border-radius\", \"2px\", \"transition\", \"height 0.3s ease\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_2_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"img\", 6);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_img_click_6_listener() {\n            return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, MessageChatComponent_div_7_Template, 1, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"h3\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10);\n          i0.ɵɵtemplate(12, MessageChatComponent_div_12_Template, 7, 0, \"div\", 11);\n          i0.ɵɵtemplate(13, MessageChatComponent_span_13_Template, 2, 1, \"span\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_15_listener() {\n            return ctx.startVideoCall();\n          });\n          i0.ɵɵelement(16, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_17_listener() {\n            return ctx.startVoiceCall();\n          });\n          i0.ɵɵelement(18, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_19_listener() {\n            return ctx.toggleSearch();\n          });\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(22, \"i\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, MessageChatComponent_div_23_Template, 15, 0, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"main\", 23, 24);\n          i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_main_scroll_24_listener($event) {\n            return ctx.onScroll($event);\n          })(\"dragover\", function MessageChatComponent_Template_main_dragover_24_listener($event) {\n            return ctx.onDragOver($event);\n          })(\"dragleave\", function MessageChatComponent_Template_main_dragleave_24_listener($event) {\n            return ctx.onDragLeave($event);\n          })(\"drop\", function MessageChatComponent_Template_main_drop_24_listener($event) {\n            return ctx.onDrop($event);\n          });\n          i0.ɵɵtemplate(26, MessageChatComponent_div_26_Template, 7, 0, \"div\", 25);\n          i0.ɵɵtemplate(27, MessageChatComponent_div_27_Template, 4, 0, \"div\", 26);\n          i0.ɵɵtemplate(28, MessageChatComponent_div_28_Template, 7, 1, \"div\", 27);\n          i0.ɵɵtemplate(29, MessageChatComponent_div_29_Template, 3, 3, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"footer\", 29)(31, \"form\", 30);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_31_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(32, \"div\", 31)(33, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_33_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(34, \"i\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_35_listener() {\n            return ctx.toggleAttachmentMenu();\n          });\n          i0.ɵɵelement(36, \"i\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"button\", 36);\n          i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_Template_button_mousedown_37_listener($event) {\n            return ctx.onRecordStart($event);\n          })(\"mouseup\", function MessageChatComponent_Template_button_mouseup_37_listener($event) {\n            return ctx.onRecordEnd($event);\n          })(\"mouseleave\", function MessageChatComponent_Template_button_mouseleave_37_listener($event) {\n            return ctx.onRecordCancel($event);\n          })(\"touchstart\", function MessageChatComponent_Template_button_touchstart_37_listener($event) {\n            return ctx.onRecordStart($event);\n          })(\"touchend\", function MessageChatComponent_Template_button_touchend_37_listener($event) {\n            return ctx.onRecordEnd($event);\n          })(\"touchcancel\", function MessageChatComponent_Template_button_touchcancel_37_listener($event) {\n            return ctx.onRecordCancel($event);\n          });\n          i0.ɵɵelement(38, \"i\");\n          i0.ɵɵtemplate(39, MessageChatComponent_div_39_Template, 1, 0, \"div\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 38)(41, \"textarea\", 39);\n          i0.ɵɵlistener(\"keydown\", function MessageChatComponent_Template_textarea_keydown_41_listener($event) {\n            return ctx.onInputKeyDown($event);\n          })(\"input\", function MessageChatComponent_Template_textarea_input_41_listener($event) {\n            return ctx.onInputChange($event);\n          })(\"focus\", function MessageChatComponent_Template_textarea_focus_41_listener() {\n            return ctx.onInputFocus();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"button\", 40);\n          i0.ɵɵtemplate(43, MessageChatComponent_i_43_Template, 1, 0, \"i\", 41);\n          i0.ɵɵtemplate(44, MessageChatComponent_div_44_Template, 1, 0, \"div\", 42);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(45, MessageChatComponent_div_45_Template, 6, 1, \"div\", 43);\n          i0.ɵɵtemplate(46, MessageChatComponent_div_46_Template, 20, 0, \"div\", 44);\n          i0.ɵɵelementStart(47, \"input\", 45, 46);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_47_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(49, MessageChatComponent_div_49_Template, 1, 0, \"div\", 47);\n          i0.ɵɵtemplate(50, MessageChatComponent_div_50_Template, 15, 3, \"div\", 48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"app-call-interface\", 49);\n          i0.ɵɵlistener(\"callEnded\", function MessageChatComponent_Template_app_call_interface_callEnded_51_listener() {\n            return ctx.endCall();\n          })(\"callAccepted\", function MessageChatComponent_Template_app_call_interface_callAccepted_51_listener($event) {\n            return ctx.onCallAccepted($event);\n          })(\"callRejected\", function MessageChatComponent_Template_app_call_interface_callRejected_51_listener() {\n            return ctx.onCallRejected();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || \"Utilisateur\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUserTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserTyping);\n          i0.ɵɵadvance(6);\n          i0.ɵɵstyleProp(\"background\", ctx.searchMode ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.searchMode ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showMainMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showMainMenu ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMainMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background\", ctx.isDragOver ? \"rgba(34, 197, 94, 0.1)\" : \"transparent\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDragOver);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showEmojiPicker ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showEmojiPicker ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.showAttachmentMenu ? \"#dcfce7\" : \"transparent\")(\"color\", ctx.showAttachmentMenu ? \"#16a34a\" : \"#6b7280\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.isRecordingVoice ? \"#fef3c7\" : \"transparent\")(\"color\", ctx.isRecordingVoice ? \"#f59e0b\" : \"#6b7280\")(\"transform\", ctx.isRecordingVoice ? \"scale(1.1)\" : \"scale(1)\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.isRecordingVoice ? \"fas fa-stop\" : \"fas fa-microphone\");\n          i0.ɵɵstyleProp(\"animation\", ctx.isRecordingVoice ? \"pulse 1s infinite\" : \"none\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isInputDisabled());\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background\", !ctx.messageForm.valid || ctx.isSendingMessage ? \"#9ca3af\" : \"#3b82f6\")(\"cursor\", !ctx.messageForm.valid || ctx.isSendingMessage ? \"not-allowed\" : \"pointer\");\n          i0.ɵɵproperty(\"disabled\", !ctx.messageForm.valid || ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSendingMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"accept\", ctx.getFileAcceptTypes());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker || ctx.showAttachmentMenu || ctx.showMainMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"isVisible\", ctx.isInCall)(\"activeCall\", ctx.activeCall)(\"callType\", ctx.callType)(\"otherParticipant\", ctx.otherParticipant);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.CallInterfaceComponent],\n      styles: [\"@keyframes _ngcontent-%COMP%_pulse {\\n      0%,\\n      100% {\\n        opacity: 1;\\n      }\\n      50% {\\n        opacity: 0.5;\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_bounce {\\n      0%,\\n      20%,\\n      53%,\\n      80%,\\n      100% {\\n        transform: translateY(0);\\n      }\\n      40%,\\n      43% {\\n        transform: translateY(-8px);\\n      }\\n      70% {\\n        transform: translateY(-4px);\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_spin {\\n      from {\\n        transform: rotate(0deg);\\n      }\\n      to {\\n        transform: rotate(360deg);\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_ping {\\n      75%,\\n      100% {\\n        transform: scale(2);\\n        opacity: 0;\\n      }\\n    }\\n    @keyframes _ngcontent-%COMP%_slideInUp {\\n      from {\\n        transform: translateX(-50%) translateY(20px);\\n        opacity: 0;\\n      }\\n      to {\\n        transform: translateX(-50%) translateY(0);\\n        opacity: 1;\\n      }\\n    }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "CallType", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "otherParticipant", "isOnline", "formatLastActive", "lastActive", "ɵɵlistener", "MessageChatComponent_div_23_Template_button_click_2_listener", "ɵɵrestoreView", "_r18", "ctx_r17", "ɵɵnextContext", "toggleSearch", "ɵɵresetView", "showMainMenu", "ctx_r7", "username", "ctx_r23", "formatDateSeparator", "message_r21", "timestamp", "MessageChatComponent_div_29_ng_container_1_div_3_Template_img_click_1_listener", "_r33", "$implicit", "ctx_r31", "openUserProfile", "sender", "id", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "ɵɵstyleProp", "ctx_r25", "getUserColor", "ctx_r26", "formatMessageContent", "content", "ɵɵsanitizeHtml", "ctx_r37", "currentUserId", "MessageChatComponent_div_29_ng_container_1_div_7_Template_img_click_1_listener", "_r41", "ctx_r39", "openImageViewer", "MessageChatComponent_div_29_ng_container_1_div_7_Template_img_load_1_listener", "$event", "ctx_r42", "onImageLoad", "MessageChatComponent_div_29_ng_container_1_div_7_Template_img_error_1_listener", "ctx_r44", "onImageError", "ɵɵtemplate", "MessageChatComponent_div_29_ng_container_1_div_7_div_2_Template", "ctx_r27", "getImageUrl", "ctx_r47", "isVoicePlaying", "wave_r49", "i_r50", "MessageChatComponent_div_29_ng_container_1_div_8_button_8_Template_button_click_0_listener", "_r54", "ctx_r52", "changeVoiceSpeed", "ctx_r48", "getVoiceSpeed", "MessageChatComponent_div_29_ng_container_1_div_8_Template_button_click_1_listener", "_r58", "ctx_r56", "toggleVoicePlayback", "MessageChatComponent_div_29_ng_container_1_div_8_div_4_Template", "MessageChatComponent_div_29_ng_container_1_div_8_button_8_Template", "ɵɵclassMap", "ctx_r28", "voiceWaves", "getVoiceDuration", "MessageChatComponent_div_29_ng_container_1_div_12_i_1_Template", "MessageChatComponent_div_29_ng_container_1_div_12_i_2_Template", "MessageChatComponent_div_29_ng_container_1_div_12_i_3_Template", "MessageChatComponent_div_29_ng_container_1_div_12_i_4_Template", "status", "ɵɵelementContainerStart", "MessageChatComponent_div_29_ng_container_1_div_1_Template", "MessageChatComponent_div_29_ng_container_1_Template_div_click_2_listener", "restoredCtx", "_r66", "ctx_r65", "onMessageClick", "MessageChatComponent_div_29_ng_container_1_Template_div_contextmenu_2_listener", "ctx_r67", "onMessageContextMenu", "MessageChatComponent_div_29_ng_container_1_div_3_Template", "MessageChatComponent_div_29_ng_container_1_div_5_Template", "MessageChatComponent_div_29_ng_container_1_div_6_Template", "MessageChatComponent_div_29_ng_container_1_div_7_Template", "MessageChatComponent_div_29_ng_container_1_div_8_Template", "MessageChatComponent_div_29_ng_container_1_div_12_Template", "ɵɵelementContainerEnd", "ctx_r19", "shouldShowDateSeparator", "i_r22", "shouldShowAvatar", "isGroupConversation", "shouldShowSenderName", "getMessageType", "hasImage", "ɵɵtextInterpolate", "formatMessageTime", "ctx_r20", "MessageChatComponent_div_29_ng_container_1_Template", "MessageChatComponent_div_29_div_2_Template", "ctx_r8", "messages", "trackByMessageId", "otherUserIsTyping", "MessageChatComponent_div_45_button_5_Template_button_click_0_listener", "_r71", "emoji_r69", "ctx_r70", "insert<PERSON><PERSON><PERSON>", "name", "emoji", "MessageChatComponent_div_45_button_5_Template", "ctx_r12", "getEmojisForCategory", "selectedEmojiCategory", "MessageChatComponent_div_46_Template_button_click_5_listener", "_r73", "ctx_r72", "triggerFileInput", "MessageChatComponent_div_46_Template_button_click_10_listener", "ctx_r74", "MessageChatComponent_div_46_Template_button_click_15_listener", "ctx_r75", "openCamera", "MessageChatComponent_div_49_Template_div_click_0_listener", "_r77", "ctx_r76", "closeAllMenus", "wave_r79", "i_r80", "MessageChatComponent_div_50_div_7_Template", "MessageChatComponent_div_50_Template_button_click_11_listener", "_r82", "ctx_r81", "onRecordCancel", "MessageChatComponent_div_50_Template_button_click_13_listener", "ctx_r83", "onRecordEnd", "ctx_r16", "formatRecordingDuration", "voiceRecordingDuration", "getRecordingFormat", "MessageChatComponent", "constructor", "fb", "route", "router", "MessageService", "callService", "toastService", "cdr", "conversation", "currentUsername", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "searchMode", "isSendingMessage", "showMessageContextMenu", "selectedMessage", "contextMenuPosition", "x", "y", "showReactionPicker", "reactionPickerMessage", "showImageViewer", "selectedImage", "uploadProgress", "isUploading", "isDragOver", "isRecordingVoice", "voiceRecordingState", "mediaRecorder", "audioChunks", "recordingTimer", "currentAudio", "playingMessageId", "voicePlayback", "isInCall", "callType", "callDuration", "callTimer", "activeCall", "isCallConnected", "isMuted", "isVideoEnabled", "localVideoElement", "remoteVideoElement", "emojiCategories", "icon", "emojis", "MAX_MESSAGES_TO_LOAD", "currentPage", "isTyping", "isUserTyping", "typingTimeout", "subscriptions", "messageForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "isInputDisabled", "updateInputState", "contentControl", "get", "disable", "enable", "ngOnInit", "console", "log", "initializeComponent", "loadCurrentUser", "loadConversation", "setupCallSubscriptions", "add", "incomingCall$", "subscribe", "next", "incomingCall", "handleIncomingCall", "error", "activeCall$", "call", "caller", "play", "userString", "localStorage", "getItem", "user", "JSON", "parse", "userId", "_id", "extracted", "conversationId", "snapshot", "paramMap", "showError", "getConversation", "participants", "participantsCount", "length", "isGroup", "messagesCount", "setOtherParticipant", "loadMessages", "setupSubscriptions", "warn", "find", "p", "participantId", "String", "firstParticipantId", "sort", "a", "b", "dateA", "Date", "createdAt", "getTime", "dateB", "total", "first", "last", "scrollToBottom", "loadMoreMessages", "offset", "getMessages", "newMessages", "reverse", "subscribeToNewMessages", "newMessage", "type", "senderId", "receiverId", "attachments", "hasFile", "for<PERSON>ach", "att", "index", "url", "path", "size", "messageExists", "some", "msg", "push", "detectChanges", "setTimeout", "shouldMarkAsRead", "markMessageAsRead", "subscribeToTypingIndicator", "typingData", "subscribeToConversationUpdates", "conversationUpdate", "messageId", "sendMessage", "valid", "value", "trim", "undefined", "message", "reset", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "diffMins", "Math", "floor", "now", "getVoicePlaybackData", "progress", "duration", "currentTime", "speed", "setVoicePlaybackData", "data", "startVideoCall", "initiateCall", "VIDEO", "startVoiceCall", "AUDIO", "endCall", "formatFileSize", "bytes", "round", "downloadFile", "fileAttachment", "startsWith", "link", "document", "createElement", "href", "download", "target", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "showSuccess", "toggleMainMenu", "goBackToConversations", "navigate", "then", "catch", "window", "location", "event", "preventDefault", "clientX", "clientY", "showQuickReactions", "stopPropagation", "quickReact", "toggleReaction", "hasUserReacted", "reaction", "replyToMessage", "forwardMessage", "deleteMessage", "toggleEmojiPicker", "selectEmojiCategory", "category", "currentC<PERSON>nt", "newContent", "patchValue", "toggleAttachmentMenu", "toString", "testAddMessage", "testMessage", "toLocaleTimeString", "toISOString", "isRead", "zoomImage", "factor", "imageElement", "querySelector", "currentTransform", "style", "transform", "currentScale", "parseFloat", "match", "newScale", "max", "min", "classList", "remove", "resetZoom", "input", "fileInput", "accept", "date", "hour", "minute", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "urlRegex", "replace", "currentMessage", "previousMessage", "currentDate", "previousDate", "nextMessage", "attachment", "voiceUrl", "audioUrl", "voice", "hasImageAttachment", "hasImageUrl", "imageUrl", "hasFileAttachment", "imageAttachment", "getFileName", "getFileSize", "getFileIcon", "includes", "colors", "charCodeAt", "onInputChange", "handleTypingIndicator", "onInputKeyDown", "key", "shift<PERSON>ey", "onInputFocus", "onInputBlur", "onScroll", "src", "closeImageViewer", "downloadImage", "searchMessages", "filter", "toLowerCase", "onSearchQueryChange", "clearSearch", "jumpToMessage", "messageElement", "getElementById", "scrollIntoView", "behavior", "block", "closeContextMenu", "recipientId", "<PERSON><PERSON><PERSON>", "startCallTimer", "callId", "callStatus", "recipient", "acceptCall", "rejectCall", "setInterval", "resetCallState", "clearInterval", "toggleMute", "toggleMedia", "toggleVideo", "formatCallDuration", "hours", "minutes", "seconds", "padStart", "startVoiceRecording", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "Error", "MediaRecorder", "stream", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "channelCount", "mimeType", "isTypeSupported", "animateVoice<PERSON>aves", "ondataavailable", "onstop", "processRecordedAudio", "onerror", "cancelVoiceRecording", "start", "errorMessage", "stopVoiceRecording", "state", "stop", "getTracks", "track", "_this2", "audioBlob", "Blob", "extension", "audioFile", "File", "sendVoiceMessage", "_this3", "Promise", "resolve", "reject", "onRecordStart", "showWarning", "showInfo", "map", "random", "onFileSelected", "files", "file", "uploadFile", "maxSize", "compressImage", "compressedFile", "sendFileToServer", "messageType", "getFileMessageType", "progressInterval", "resetUploadState", "getFileAcceptTypes", "onDragOver", "onDragLeave", "rect", "currentTarget", "getBoundingClientRect", "left", "right", "top", "bottom", "onDrop", "dataTransfer", "Array", "from", "quality", "canvas", "ctx", "getContext", "img", "Image", "onload", "max<PERSON><PERSON><PERSON>", "maxHeight", "width", "height", "ratio", "drawImage", "toBlob", "blob", "lastModified", "URL", "createObjectURL", "sendTypingIndicator", "clearTimeout", "onCallAccepted", "onCallRejected", "playVoiceMessage", "getVoiceUrl", "stopVoicePlayback", "startVoicePlayback", "Audio", "currentData", "playbackRate", "addEventListener", "pause", "audioAttachment", "getVoiceWaves", "seed", "split", "reduce", "acc", "char", "waves", "i", "getVoiceProgress", "totalWaves", "getVoiceCurrentTime", "formatAudioTime", "metadata", "remainingSeconds", "seekVoiceMessage", "waveIndex", "seekPercentage", "seekTime", "toggleVoiceSpeed", "newSpeed", "ngOnDestroy", "unsubscribe", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "i4", "CallService", "i5", "ToastService", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "MessageChatComponent_Template_button_click_2_listener", "MessageChatComponent_Template_img_click_6_listener", "MessageChatComponent_div_7_Template", "MessageChatComponent_div_12_Template", "MessageChatComponent_span_13_Template", "MessageChatComponent_Template_button_click_15_listener", "MessageChatComponent_Template_button_click_17_listener", "MessageChatComponent_Template_button_click_19_listener", "MessageChatComponent_Template_button_click_21_listener", "MessageChatComponent_div_23_Template", "MessageChatComponent_Template_main_scroll_24_listener", "MessageChatComponent_Template_main_dragover_24_listener", "MessageChatComponent_Template_main_dragleave_24_listener", "MessageChatComponent_Template_main_drop_24_listener", "MessageChatComponent_div_26_Template", "MessageChatComponent_div_27_Template", "MessageChatComponent_div_28_Template", "MessageChatComponent_div_29_Template", "MessageChatComponent_Template_form_ngSubmit_31_listener", "MessageChatComponent_Template_button_click_33_listener", "MessageChatComponent_Template_button_click_35_listener", "MessageChatComponent_Template_button_mousedown_37_listener", "MessageChatComponent_Template_button_mouseup_37_listener", "MessageChatComponent_Template_button_mouseleave_37_listener", "MessageChatComponent_Template_button_touchstart_37_listener", "MessageChatComponent_Template_button_touchend_37_listener", "MessageChatComponent_Template_button_touchcancel_37_listener", "MessageChatComponent_div_39_Template", "MessageChatComponent_Template_textarea_keydown_41_listener", "MessageChatComponent_Template_textarea_input_41_listener", "MessageChatComponent_Template_textarea_focus_41_listener", "MessageChatComponent_i_43_Template", "MessageChatComponent_div_44_Template", "MessageChatComponent_div_45_Template", "MessageChatComponent_div_46_Template", "MessageChatComponent_Template_input_change_47_listener", "MessageChatComponent_div_49_Template", "MessageChatComponent_div_50_Template", "MessageChatComponent_Template_app_call_interface_callEnded_51_listener", "MessageChatComponent_Template_app_call_interface_callAccepted_51_listener", "MessageChatComponent_Template_app_call_interface_callRejected_51_listener"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  On<PERSON><PERSON>t,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ViewChild,\n  ElementRef,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { MessageService } from '../../../../services/message.service';\nimport { CallService } from '../../../../services/call.service';\nimport { ToastService } from '../../../../services/toast.service';\nimport { CallType, Call, IncomingCall } from '../../../../models/message.model';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html',\n})\nexport class MessageChatComponent implements OnInit, OnDestroy {\n  // === RÉFÉRENCES DOM ===\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  // === DONNÉES PRINCIPALES ===\n  conversation: any = null;\n  messages: any[] = [];\n  currentUserId: string | null = null;\n  currentUsername = 'You';\n  messageForm: FormGroup;\n  otherParticipant: any = null;\n\n  // === ÉTATS DE L'INTERFACE ===\n  isLoading = false;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  showEmojiPicker = false;\n  showAttachmentMenu = false;\n  showSearch = false;\n  searchQuery = '';\n  searchResults: any[] = [];\n  searchMode = false;\n  isSendingMessage = false;\n  otherUserIsTyping = false;\n  showMainMenu = false;\n  showMessageContextMenu = false;\n  selectedMessage: any = null;\n  contextMenuPosition = { x: 0, y: 0 };\n  showReactionPicker = false;\n  reactionPickerMessage: any = null;\n\n  showImageViewer = false;\n  selectedImage: any = null;\n  uploadProgress = 0;\n  isUploading = false;\n  isDragOver = false;\n\n  // === GESTION VOCALE OPTIMISÉE ===\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioChunks: Blob[] = [];\n  private recordingTimer: any = null;\n  voiceWaves: number[] = [\n    4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8,\n  ];\n\n  // Lecture des messages vocaux\n  private currentAudio: HTMLAudioElement | null = null;\n  private playingMessageId: string | null = null;\n  private voicePlayback: {\n    [messageId: string]: {\n      progress: number;\n      duration: number;\n      currentTime: number;\n      speed: number;\n    };\n  } = {};\n\n  // === APPELS WEBRTC ===\n  isInCall = false;\n  callType: 'VIDEO' | 'AUDIO' | null = null;\n  callDuration = 0;\n  private callTimer: any = null;\n\n  // État de l'appel WebRTC\n  activeCall: any = null;\n  isCallConnected = false;\n  isMuted = false;\n  isVideoEnabled = true;\n  localVideoElement: HTMLVideoElement | null = null;\n  remoteVideoElement: HTMLVideoElement | null = null;\n\n  // === ÉMOJIS ===\n  emojiCategories: any[] = [\n    {\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [\n        { emoji: '😀', name: 'grinning face' },\n        { emoji: '😃', name: 'grinning face with big eyes' },\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\n        { emoji: '😆', name: 'grinning squinting face' },\n        { emoji: '😅', name: 'grinning face with sweat' },\n        { emoji: '😂', name: 'face with tears of joy' },\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\n        { emoji: '😇', name: 'smiling face with halo' },\n      ],\n    },\n    {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [\n        { emoji: '👶', name: 'baby' },\n        { emoji: '🧒', name: 'child' },\n        { emoji: '👦', name: 'boy' },\n        { emoji: '👧', name: 'girl' },\n        { emoji: '🧑', name: 'person' },\n        { emoji: '👨', name: 'man' },\n        { emoji: '👩', name: 'woman' },\n        { emoji: '👴', name: 'old man' },\n        { emoji: '👵', name: 'old woman' },\n      ],\n    },\n    {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [\n        { emoji: '🐶', name: 'dog face' },\n        { emoji: '🐱', name: 'cat face' },\n        { emoji: '🐭', name: 'mouse face' },\n        { emoji: '🐹', name: 'hamster' },\n        { emoji: '🐰', name: 'rabbit face' },\n        { emoji: '🦊', name: 'fox' },\n        { emoji: '🐻', name: 'bear' },\n        { emoji: '🐼', name: 'panda' },\n      ],\n    },\n  ];\n  selectedEmojiCategory = this.emojiCategories[0];\n\n  // === PAGINATION ===\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private currentPage = 1;\n\n  // === AUTRES ÉTATS ===\n  isTyping = false;\n  isUserTyping = false;\n  private typingTimeout: any = null;\n  private subscriptions = new Subscription();\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private MessageService: MessageService,\n    private callService: CallService,\n    private toastService: ToastService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]],\n    });\n  }\n\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\n  isInputDisabled(): boolean {\n    return (\n      !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage\n    );\n  }\n\n  // Méthode pour gérer l'état du contrôle de saisie\n  private updateInputState(): void {\n    const contentControl = this.messageForm.get('content');\n    if (this.isInputDisabled()) {\n      contentControl?.disable();\n    } else {\n      contentControl?.enable();\n    }\n  }\n\n  ngOnInit(): void {\n    console.log('🚀 MessageChatComponent initialized');\n    this.initializeComponent();\n  }\n\n  private initializeComponent(): void {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupCallSubscriptions();\n  }\n\n  private setupCallSubscriptions(): void {\n    // S'abonner aux appels entrants\n    this.subscriptions.add(\n      this.callService.incomingCall$.subscribe({\n        next: (incomingCall) => {\n          if (incomingCall) {\n            console.log('📞 Incoming call received:', incomingCall);\n            this.handleIncomingCall(incomingCall);\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in incoming call subscription:', error);\n        },\n      })\n    );\n\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(\n      this.callService.activeCall$.subscribe({\n        next: (call) => {\n          if (call) {\n            console.log('📞 Active call updated:', call);\n            this.activeCall = call;\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in active call subscription:', error);\n        },\n      })\n    );\n  }\n\n  private handleIncomingCall(incomingCall: IncomingCall): void {\n    // Afficher une notification ou modal d'appel entrant\n    // Pour l'instant, on log juste\n    console.log(\n      '🔔 Handling incoming call from:',\n      incomingCall.caller.username\n    );\n\n    // Jouer la sonnerie\n    this.MessageService.play('ringtone');\n\n    // Ici on pourrait afficher une modal ou notification\n    // Pour l'instant, on accepte automatiquement pour tester\n    // this.acceptCall(incomingCall);\n  }\n\n  private loadCurrentUser(): void {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId,\n      });\n\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername,\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n\n  private loadConversation(): void {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: (conversation) => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length,\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n\n        // Configurer les subscriptions temps réel après le chargement de la conversation\n        this.setupSubscriptions();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError(\n          'Erreur lors du chargement de la conversation'\n        );\n        this.isLoading = false;\n      },\n    });\n  }\n\n  private setOtherParticipant(): void {\n    if (\n      !this.conversation?.participants ||\n      this.conversation.participants.length === 0\n    ) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        console.log(\n          'Comparing participant ID:',\n          participantId,\n          'with current user ID:',\n          this.currentUserId\n        );\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId =\n          this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log(\n            'First participant is current user, using second participant'\n          );\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline,\n      });\n\n      // Log très visible pour debug\n      console.log(\n        '🎯 FINAL RESULT: otherParticipant =',\n        this.otherParticipant.username\n      );\n      console.log(\n        '🎯 Should display in sidebar:',\n        this.otherParticipant.username\n      );\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n\n    // Mettre à jour l'état du champ de saisie\n    this.updateInputState();\n  }\n\n  private loadMessages(): void {\n    if (!this.conversation?.id) return;\n\n    // Les messages sont déjà chargés avec la conversation\n    let messages = this.conversation.messages || [];\n\n    // Trier les messages par timestamp (plus anciens en premier)\n    this.messages = messages.sort((a: any, b: any) => {\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\n    });\n\n    console.log('📋 Messages loaded and sorted:', {\n      total: this.messages.length,\n      first: this.messages[0]?.content,\n      last: this.messages[this.messages.length - 1]?.content,\n    });\n\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id)\n      return;\n\n    this.isLoadingMore = true;\n    this.currentPage++;\n\n    // Calculer l'offset basé sur les messages déjà chargés\n    const offset = this.messages.length;\n\n    this.MessageService.getMessages(\n      this.currentUserId!, // senderId\n      this.otherParticipant?.id || this.otherParticipant?._id!, // receiverId\n      this.conversation.id,\n      this.currentPage,\n      this.MAX_MESSAGES_TO_LOAD\n    ).subscribe({\n      next: (newMessages: any[]) => {\n        if (newMessages && newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste\n          this.messages = [...newMessages.reverse(), ...this.messages];\n          this.hasMoreMessages =\n            newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      },\n    });\n  }\n\n  private setupSubscriptions(): void {\n    if (!this.conversation?.id) {\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\n      return;\n    }\n\n    console.log(\n      '🔄 Setting up real-time subscriptions for conversation:',\n      this.conversation.id\n    );\n\n    // Subscription pour les nouveaux messages\n    console.log('📨 Setting up message subscription...');\n    this.subscriptions.add(\n      this.MessageService.subscribeToNewMessages(\n        this.conversation.id\n      ).subscribe({\n        next: (newMessage: any) => {\n          console.log('📨 New message received via subscription:', newMessage);\n          console.log('📨 Message structure:', {\n            id: newMessage.id,\n            type: newMessage.type,\n            content: newMessage.content,\n            sender: newMessage.sender,\n            senderId: newMessage.senderId,\n            receiverId: newMessage.receiverId,\n            attachments: newMessage.attachments,\n          });\n\n          // Debug des attachments\n          console.log(\n            '📨 [Debug] Message type detected:',\n            this.getMessageType(newMessage)\n          );\n          console.log('📨 [Debug] Has image:', this.hasImage(newMessage));\n          console.log('📨 [Debug] Has file:', this.hasFile(newMessage));\n          console.log('📨 [Debug] Image URL:', this.getImageUrl(newMessage));\n          if (newMessage.attachments) {\n            newMessage.attachments.forEach((att: any, index: number) => {\n              console.log(`📨 [Debug] Attachment ${index}:`, {\n                type: att.type,\n                url: att.url,\n                path: att.path,\n                name: att.name,\n                size: att.size,\n              });\n            });\n          }\n\n          // Ajouter le message à la liste s'il n'existe pas déjà\n          const messageExists = this.messages.some(\n            (msg) => msg.id === newMessage.id\n          );\n          if (!messageExists) {\n            // Ajouter le nouveau message à la fin (en bas)\n            this.messages.push(newMessage);\n            console.log(\n              '✅ Message added to list, total messages:',\n              this.messages.length\n            );\n\n            // Forcer la détection de changements\n            this.cdr.detectChanges();\n\n            // Scroll vers le bas après un court délai\n            setTimeout(() => {\n              this.scrollToBottom();\n            }, 50);\n\n            // Marquer comme lu si ce n'est pas notre message\n            const senderId = newMessage.sender?.id || newMessage.senderId;\n            console.log('📨 Checking if message should be marked as read:', {\n              senderId,\n              currentUserId: this.currentUserId,\n              shouldMarkAsRead: senderId !== this.currentUserId,\n            });\n\n            if (senderId && senderId !== this.currentUserId) {\n              this.markMessageAsRead(newMessage.id);\n            }\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in message subscription:', error);\n        },\n      })\n    );\n\n    // Subscription pour les indicateurs de frappe\n    console.log('📝 Setting up typing indicator subscription...');\n    this.subscriptions.add(\n      this.MessageService.subscribeToTypingIndicator(\n        this.conversation.id\n      ).subscribe({\n        next: (typingData: any) => {\n          console.log('📝 Typing indicator received:', typingData);\n\n          // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\n          if (typingData.userId !== this.currentUserId) {\n            this.otherUserIsTyping = typingData.isTyping;\n            this.cdr.detectChanges();\n          }\n        },\n        error: (error: any) => {\n          console.error('❌ Error in typing subscription:', error);\n        },\n      })\n    );\n\n    // Subscription pour les mises à jour de conversation\n    this.subscriptions.add(\n      this.MessageService.subscribeToConversationUpdates(\n        this.conversation.id\n      ).subscribe({\n        next: (conversationUpdate: any) => {\n          console.log('📋 Conversation update:', conversationUpdate);\n\n          // Mettre à jour la conversation si nécessaire\n          if (conversationUpdate.id === this.conversation.id) {\n            this.conversation = { ...this.conversation, ...conversationUpdate };\n            this.cdr.detectChanges();\n          }\n        },\n        error: (error: any) => {\n          console.error('❌ Error in conversation subscription:', error);\n        },\n      })\n    );\n  }\n\n  private markMessageAsRead(messageId: string): void {\n    this.MessageService.markMessageAsRead(messageId).subscribe({\n      next: () => {\n        console.log('✅ Message marked as read:', messageId);\n      },\n      error: (error) => {\n        console.error('❌ Error marking message as read:', error);\n      },\n    });\n  }\n\n  // === ENVOI DE MESSAGES ===\n  sendMessage(): void {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    // Désactiver le bouton d'envoi\n    this.isSendingMessage = true;\n    this.updateInputState();\n\n    console.log('📤 Sending message:', {\n      content,\n      receiverId,\n      conversationId: this.conversation.id,\n    });\n\n    this.MessageService.sendMessage(\n      receiverId,\n      content,\n      undefined,\n      'TEXT' as any,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        console.log('✅ Message sent successfully:', message);\n\n        // Ajouter le message à la liste s'il n'y est pas déjà\n        const messageExists = this.messages.some(\n          (msg) => msg.id === message.id\n        );\n        if (!messageExists) {\n          this.messages.push(message);\n          console.log(\n            '📋 Message added to local list, total:',\n            this.messages.length\n          );\n        }\n\n        // Réinitialiser le formulaire\n        this.messageForm.reset();\n        this.isSendingMessage = false;\n        this.updateInputState();\n\n        // Forcer la détection de changements et scroll\n        this.cdr.detectChanges();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 50);\n      },\n      error: (error: any) => {\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        this.isSendingMessage = false;\n        this.updateInputState();\n      },\n    });\n  }\n\n  scrollToBottom(): void {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\n  formatLastActive(lastActive: string | Date | null): string {\n    if (!lastActive) return 'Hors ligne';\n\n    const diffMins = Math.floor(\n      (Date.now() - new Date(lastActive).getTime()) / 60000\n    );\n\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\n  }\n\n  // Méthodes utilitaires pour les messages vocaux\n  getVoicePlaybackData(messageId: string) {\n    return (\n      this.voicePlayback[messageId] || {\n        progress: 0,\n        duration: 0,\n        currentTime: 0,\n        speed: 1,\n      }\n    );\n  }\n\n  private setVoicePlaybackData(\n    messageId: string,\n    data: Partial<(typeof this.voicePlayback)[string]>\n  ) {\n    this.voicePlayback[messageId] = {\n      ...this.getVoicePlaybackData(messageId),\n      ...data,\n    };\n  }\n\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // isVoicePlaying, playVoiceMessage, toggleVoicePlayback - définies plus loin\n\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\n\n  startVideoCall(): void {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n\n    console.log('📹 Starting video call with:', this.otherParticipant.username);\n    this.initiateCall(CallType.VIDEO);\n  }\n\n  startVoiceCall(): void {\n    if (!this.otherParticipant?.id) {\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\n      return;\n    }\n\n    console.log('📞 Starting voice call with:', this.otherParticipant.username);\n    this.initiateCall(CallType.AUDIO);\n  }\n\n  endCall(): void {\n    this.isInCall = false;\n    this.callType = null;\n    this.activeCall = null;\n    console.log('📞 Call ended');\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // onCallAccepted, onCallRejected - définies plus loin\n\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\n\n  formatFileSize(bytes: number): string {\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n\n  downloadFile(message: any): void {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (fileAttachment?.url) {\n      const link = document.createElement('a');\n      link.href = fileAttachment.url;\n      link.download = fileAttachment.name || 'file';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n    }\n  }\n\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\n\n  toggleSearch(): void {\n    this.searchMode = !this.searchMode;\n    this.showSearch = this.searchMode;\n  }\n\n  toggleMainMenu(): void {\n    this.showMainMenu = !this.showMainMenu;\n  }\n\n  goBackToConversations(): void {\n    console.log('🔙 Going back to conversations');\n    // Naviguer vers la liste des conversations\n    this.router\n      .navigate(['/front/messages/conversations'])\n      .then(() => {\n        console.log('✅ Navigation to conversations successful');\n      })\n      .catch((error) => {\n        console.error('❌ Navigation error:', error);\n        // Fallback: essayer la route parent\n        this.router.navigate(['/front/messages']).catch(() => {\n          // Dernier recours: recharger la page\n          window.location.href = '/front/messages/conversations';\n        });\n      });\n  }\n\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\n\n  closeAllMenus(): void {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showMainMenu = false;\n    this.showMessageContextMenu = false;\n    this.showReactionPicker = false;\n  }\n\n  onMessageContextMenu(message: any, event: MouseEvent): void {\n    event.preventDefault();\n    this.selectedMessage = message;\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\n    this.showMessageContextMenu = true;\n  }\n\n  showQuickReactions(message: any, event: MouseEvent): void {\n    event.stopPropagation();\n    this.reactionPickerMessage = message;\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\n    this.showReactionPicker = true;\n  }\n\n  quickReact(emoji: string): void {\n    if (this.reactionPickerMessage) {\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\n    }\n    this.showReactionPicker = false;\n  }\n\n  toggleReaction(messageId: string, emoji: string): void {\n    console.log('👍 Toggling reaction:', emoji, 'for message:', messageId);\n    // Implémentation de la réaction\n  }\n\n  hasUserReacted(reaction: any, userId: string): boolean {\n    return reaction.userId === userId;\n  }\n\n  replyToMessage(message: any): void {\n    console.log('↩️ Replying to message:', message.id);\n    this.closeAllMenus();\n  }\n\n  forwardMessage(message: any): void {\n    console.log('➡️ Forwarding message:', message.id);\n    this.closeAllMenus();\n  }\n\n  deleteMessage(message: any): void {\n    console.log('🗑️ Deleting message:', message.id);\n    this.closeAllMenus();\n  }\n\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\n\n  toggleEmojiPicker(): void {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n\n  selectEmojiCategory(category: any): void {\n    this.selectedEmojiCategory = category;\n  }\n\n  getEmojisForCategory(category: any): any[] {\n    return category?.emojis || [];\n  }\n\n  insertEmoji(emoji: any): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({ content: newContent });\n    this.showEmojiPicker = false;\n  }\n\n  toggleAttachmentMenu(): void {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  // === MÉTHODES POUR LA GESTION DE LA FRAPPE ===\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // handleTypingIndicator - définie plus loin\n\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n\n  trackByMessageId(index: number, message: any): string {\n    return message.id || message._id || index.toString();\n  }\n\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n\n  testAddMessage(): void {\n    console.log('🧪 Test: Adding message');\n    const testMessage = {\n      id: `test-${Date.now()}`,\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\n      timestamp: new Date().toISOString(),\n      sender: {\n        id: this.otherParticipant?.id || 'test-user',\n        username: this.otherParticipant?.username || 'Test User',\n        image:\n          this.otherParticipant?.image || 'assets/images/default-avatar.png',\n      },\n      type: 'TEXT',\n      isRead: false,\n    };\n    this.messages.push(testMessage);\n    this.cdr.detectChanges();\n    setTimeout(() => this.scrollToBottom(), 50);\n  }\n\n  isGroupConversation(): boolean {\n    return (\n      this.conversation?.isGroup ||\n      this.conversation?.participants?.length > 2 ||\n      false\n    );\n  }\n\n  openCamera(): void {\n    console.log('📷 Opening camera');\n    this.showAttachmentMenu = false;\n    // TODO: Implémenter l'ouverture de la caméra\n  }\n\n  zoomImage(factor: number): void {\n    const imageElement = document.querySelector(\n      '.image-viewer-zoom'\n    ) as HTMLElement;\n    if (imageElement) {\n      const currentTransform = imageElement.style.transform || 'scale(1)';\n      const currentScale = parseFloat(\n        currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1'\n      );\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\n      imageElement.style.transform = `scale(${newScale})`;\n      if (newScale > 1) {\n        imageElement.classList.add('zoomed');\n      } else {\n        imageElement.classList.remove('zoomed');\n      }\n    }\n  }\n\n  resetZoom(): void {\n    const imageElement = document.querySelector(\n      '.image-viewer-zoom'\n    ) as HTMLElement;\n    if (imageElement) {\n      imageElement.style.transform = 'scale(1)';\n      imageElement.classList.remove('zoomed');\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  triggerFileInput(type?: string): void {\n    const input = this.fileInput?.nativeElement;\n    if (!input) {\n      console.error('File input element not found');\n      return;\n    }\n\n    // Configurer le type de fichier accepté\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    } else {\n      input.accept = '*/*';\n    }\n\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\n    input.value = '';\n\n    // Déclencher la sélection de fichier\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n\n  formatMessageTime(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  }\n\n  formatDateSeparator(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n\n  formatMessageContent(content: string): string {\n    if (!content) return '';\n\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(\n      urlRegex,\n      '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>'\n    );\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n\n    return currentDate !== previousDate;\n  }\n\n  shouldShowAvatar(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n\n    if (!nextMessage) return true;\n\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n\n  shouldShowSenderName(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!previousMessage) return true;\n\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n\n  getMessageType(message: any): string {\n    // Vérifier d'abord le type de message explicite\n    if (message.type) {\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\n    }\n\n    // Ensuite vérifier les attachments\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n\n    // Vérifier si c'est un message vocal basé sur les propriétés\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\n\n    return 'text';\n  }\n\n  hasImage(message: any): boolean {\n    // Vérifier le type de message\n    if (message.type === 'IMAGE' || message.type === 'image') {\n      return true;\n    }\n\n    // Vérifier les attachments\n    const hasImageAttachment =\n      message.attachments?.some((att: any) => {\n        return att.type?.startsWith('image/') || att.type === 'IMAGE';\n      }) || false;\n\n    // Vérifier les propriétés directes d'image\n    const hasImageUrl = !!(message.imageUrl || message.image);\n\n    return hasImageAttachment || hasImageUrl;\n  }\n\n  hasFile(message: any): boolean {\n    // Vérifier le type de message\n    if (message.type === 'FILE' || message.type === 'file') {\n      return true;\n    }\n\n    // Vérifier les attachments non-image\n    const hasFileAttachment =\n      message.attachments?.some((att: any) => {\n        return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\n      }) || false;\n\n    return hasFileAttachment;\n  }\n\n  getImageUrl(message: any): string {\n    // Vérifier les propriétés directes d'image\n    if (message.imageUrl) {\n      return message.imageUrl;\n    }\n    if (message.image) {\n      return message.image;\n    }\n\n    // Vérifier les attachments\n    const imageAttachment = message.attachments?.find(\n      (att: any) => att.type?.startsWith('image/') || att.type === 'IMAGE'\n    );\n\n    if (imageAttachment) {\n      return imageAttachment.url || imageAttachment.path || '';\n    }\n\n    return '';\n  }\n\n  getFileName(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    return fileAttachment?.name || 'Fichier';\n  }\n\n  getFileSize(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.size) return '';\n\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n\n  getFileIcon(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.type) return 'fas fa-file';\n\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n\n  getUserColor(userId: string): string {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = [\n      '#FF6B6B',\n      '#4ECDC4',\n      '#45B7D1',\n      '#96CEB4',\n      '#FFEAA7',\n      '#DDA0DD',\n      '#98D8C8',\n    ];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message: any, event: any): void {\n    console.log('Message clicked:', message);\n  }\n\n  onInputChange(event: any): void {\n    // Gérer les changements dans le champ de saisie\n    this.handleTypingIndicator();\n  }\n\n  onInputKeyDown(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  onInputFocus(): void {\n    // Gérer le focus sur le champ de saisie\n  }\n\n  onInputBlur(): void {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n\n  onScroll(event: any): void {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (\n      element.scrollTop === 0 &&\n      this.hasMoreMessages &&\n      !this.isLoadingMore\n    ) {\n      this.loadMoreMessages();\n    }\n  }\n\n  openUserProfile(userId: string): void {\n    console.log('Opening user profile for:', userId);\n  }\n\n  onImageLoad(event: any, message: any): void {\n    console.log(\n      '🖼️ [Debug] Image loaded successfully for message:',\n      message.id,\n      event.target.src\n    );\n  }\n\n  onImageError(event: any, message: any): void {\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\n      src: event.target.src,\n      error: event,\n    });\n    // Optionnel : afficher une image de remplacement\n    event.target.src =\n      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\n  }\n\n  openImageViewer(message: any): void {\n    const imageAttachment = message.attachments?.find((att: any) =>\n      att.type?.startsWith('image/')\n    );\n    if (imageAttachment?.url) {\n      this.selectedImage = {\n        url: imageAttachment.url,\n        name: imageAttachment.name || 'Image',\n        size: this.formatFileSize(imageAttachment.size || 0),\n        message: message,\n      };\n      this.showImageViewer = true;\n      console.log('🖼️ [ImageViewer] Opening image:', this.selectedImage);\n    }\n  }\n\n  closeImageViewer(): void {\n    this.showImageViewer = false;\n    this.selectedImage = null;\n    console.log('🖼️ [ImageViewer] Closed');\n  }\n\n  downloadImage(): void {\n    if (this.selectedImage?.url) {\n      const link = document.createElement('a');\n      link.href = this.selectedImage.url;\n      link.download = this.selectedImage.name || 'image';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.toastService.showSuccess('Téléchargement démarré');\n      console.log(\n        '🖼️ [ImageViewer] Download started:',\n        this.selectedImage.name\n      );\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  searchMessages(): void {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n\n    this.searchResults = this.messages.filter(\n      (message) =>\n        message.content\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase()) ||\n        message.sender?.username\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase())\n    );\n  }\n\n  onSearchQueryChange(): void {\n    this.searchMessages();\n  }\n\n  clearSearch(): void {\n    this.searchQuery = '';\n    this.searchResults = [];\n  }\n\n  jumpToMessage(messageId: string): void {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\n      // Highlight temporairement le message\n      messageElement.classList.add('highlight');\n      setTimeout(() => {\n        messageElement.classList.remove('highlight');\n      }, 2000);\n    }\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  closeContextMenu(): void {\n    this.showMessageContextMenu = false;\n    this.selectedMessage = null;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // triggerFileInput - définie plus loin\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\n  // goBackToConversations, startVideoCall, startVoiceCall\n  // Ces méthodes sont déjà définies plus loin dans le fichier\n\n  private initiateCall(callType: CallType): void {\n    console.log('🚀 [MessageChat] Starting call initiation process...');\n    console.log('📋 [MessageChat] Call details:', {\n      callType,\n      otherParticipant: this.otherParticipant,\n      conversation: this.conversation?.id,\n      currentUserId: this.currentUserId,\n    });\n\n    if (!this.otherParticipant) {\n      console.error('❌ [MessageChat] No recipient selected');\n      this.toastService.showError('Aucun destinataire sélectionné');\n      return;\n    }\n\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\n    if (!recipientId) {\n      console.error('❌ [MessageChat] Recipient ID not found');\n      this.toastService.showError('ID du destinataire introuvable');\n      return;\n    }\n\n    console.log(`📞 [MessageChat] Initiating ${callType} call to user:`, {\n      recipientId,\n      recipientName:\n        this.otherParticipant.username || this.otherParticipant.name,\n      conversationId: this.conversation?.id,\n    });\n\n    this.isInCall = true;\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n    this.callDuration = 0;\n\n    // Démarrer le timer d'appel\n    this.startCallTimer();\n\n    console.log('🔄 [MessageChat] Calling CallService.initiateCall...');\n\n    // Utiliser le CallService\n    this.callService\n      .initiateCall(recipientId, callType, this.conversation?.id)\n      .subscribe({\n        next: (call: Call) => {\n          console.log('✅ [MessageChat] Call initiated successfully:', {\n            callId: call.id,\n            callType: call.type,\n            callStatus: call.status,\n            caller: call.caller?.username,\n            recipient: call.recipient?.username,\n            conversationId: call.conversationId,\n          });\n\n          this.activeCall = call;\n          this.isCallConnected = false;\n          this.toastService.showSuccess(\n            `Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`\n          );\n\n          console.log(\n            '📡 [MessageChat] Call should now be sent to recipient via WebSocket'\n          );\n        },\n        error: (error) => {\n          console.error('❌ [MessageChat] Error initiating call:', {\n            error: error.message || error,\n            recipientId,\n            callType,\n            conversationId: this.conversation?.id,\n          });\n\n          this.endCall();\n          this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\n        },\n      });\n  }\n\n  acceptCall(incomingCall: IncomingCall): void {\n    console.log('🔄 Accepting incoming call:', incomingCall);\n\n    this.callService.acceptCall(incomingCall).subscribe({\n      next: (call: Call) => {\n        console.log('✅ Call accepted successfully:', call);\n        this.activeCall = call;\n        this.isInCall = true;\n        this.isCallConnected = true;\n        this.callType = call.type === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\n        this.startCallTimer();\n        this.toastService.showSuccess('Appel accepté');\n      },\n      error: (error) => {\n        console.error('❌ Error accepting call:', error);\n        this.toastService.showError(\"Erreur lors de l'acceptation de l'appel\");\n      },\n    });\n  }\n\n  rejectCall(incomingCall: IncomingCall): void {\n    console.log('🔄 Rejecting incoming call:', incomingCall);\n\n    this.callService.rejectCall(incomingCall.id, 'User rejected').subscribe({\n      next: () => {\n        console.log('✅ Call rejected successfully');\n        this.toastService.showSuccess('Appel rejeté');\n      },\n      error: (error) => {\n        console.error('❌ Error rejecting call:', error);\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\n      },\n    });\n  }\n\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\n  // endCall - Cette méthode est déjà définie plus loin dans le fichier\n\n  private startCallTimer(): void {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n\n  private resetCallState(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.activeCall = null;\n    this.isCallConnected = false;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n  }\n\n  // === CONTRÔLES D'APPEL ===\n  toggleMute(): void {\n    if (!this.activeCall) return;\n\n    this.isMuted = !this.isMuted;\n\n    // Utiliser la méthode toggleMedia du CallService\n    this.callService\n      .toggleMedia(\n        this.activeCall.id,\n        undefined, // video unchanged\n        !this.isMuted // audio state\n      )\n      .subscribe({\n        next: () => {\n          this.toastService.showSuccess(\n            this.isMuted ? 'Micro coupé' : 'Micro activé'\n          );\n        },\n        error: (error) => {\n          console.error('❌ Error toggling mute:', error);\n          // Revert state on error\n          this.isMuted = !this.isMuted;\n          this.toastService.showError('Erreur lors du changement du micro');\n        },\n      });\n  }\n\n  toggleVideo(): void {\n    if (!this.activeCall) return;\n\n    this.isVideoEnabled = !this.isVideoEnabled;\n\n    // Utiliser la méthode toggleMedia du CallService\n    this.callService\n      .toggleMedia(\n        this.activeCall.id,\n        this.isVideoEnabled, // video state\n        undefined // audio unchanged\n      )\n      .subscribe({\n        next: () => {\n          this.toastService.showSuccess(\n            this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\n          );\n        },\n        error: (error) => {\n          console.error('❌ Error toggling video:', error);\n          // Revert state on error\n          this.isVideoEnabled = !this.isVideoEnabled;\n          this.toastService.showError('Erreur lors du changement de la caméra');\n        },\n      });\n  }\n\n  formatCallDuration(duration: number): string {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor((duration % 3600) / 60);\n    const seconds = duration % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds\n        .toString()\n        .padStart(2, '0')}`;\n    }\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\n\n  async startVoiceRecording(): Promise<void> {\n    console.log('🎤 [Voice] Starting voice recording...');\n\n    try {\n      // Vérifier le support du navigateur\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n        throw new Error(\n          \"Votre navigateur ne supporte pas l'enregistrement audio\"\n        );\n      }\n\n      // Vérifier si MediaRecorder est supporté\n      if (!window.MediaRecorder) {\n        throw new Error(\n          \"MediaRecorder n'est pas supporté par votre navigateur\"\n        );\n      }\n\n      console.log('🎤 [Voice] Requesting microphone access...');\n\n      // Demander l'accès au microphone avec des contraintes optimisées\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n          sampleRate: 44100,\n          channelCount: 1,\n        },\n      });\n\n      console.log('🎤 [Voice] Microphone access granted');\n\n      // Vérifier les types MIME supportés\n      let mimeType = 'audio/webm;codecs=opus';\n      if (!MediaRecorder.isTypeSupported(mimeType)) {\n        mimeType = 'audio/webm';\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\n          mimeType = 'audio/mp4';\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\n            mimeType = ''; // Laisser le navigateur choisir\n          }\n        }\n      }\n\n      console.log('🎤 [Voice] Using MIME type:', mimeType);\n\n      // Créer le MediaRecorder\n      this.mediaRecorder = new MediaRecorder(stream, {\n        mimeType: mimeType || undefined,\n      });\n\n      // Initialiser les variables\n      this.audioChunks = [];\n      this.isRecordingVoice = true;\n      this.voiceRecordingDuration = 0;\n      this.voiceRecordingState = 'recording';\n\n      console.log('🎤 [Voice] MediaRecorder created, starting timer...');\n\n      // Démarrer le timer\n      this.recordingTimer = setInterval(() => {\n        this.voiceRecordingDuration++;\n        // Animer les waves\n        this.animateVoiceWaves();\n        this.cdr.detectChanges();\n      }, 1000);\n\n      // Gérer les événements du MediaRecorder\n      this.mediaRecorder.ondataavailable = (event) => {\n        console.log('🎤 [Voice] Data available:', event.data.size, 'bytes');\n        if (event.data.size > 0) {\n          this.audioChunks.push(event.data);\n        }\n      };\n\n      this.mediaRecorder.onstop = () => {\n        console.log('🎤 [Voice] MediaRecorder stopped, processing audio...');\n        this.processRecordedAudio();\n      };\n\n      this.mediaRecorder.onerror = (event: any) => {\n        console.error('🎤 [Voice] MediaRecorder error:', event.error);\n        this.toastService.showError(\"Erreur lors de l'enregistrement\");\n        this.cancelVoiceRecording();\n      };\n\n      // Démarrer l'enregistrement\n      this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n      console.log('🎤 [Voice] Recording started successfully');\n\n      this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\n    } catch (error: any) {\n      console.error('🎤 [Voice] Error starting recording:', error);\n\n      let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\n\n      if (error.name === 'NotAllowedError') {\n        errorMessage =\n          \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\n      } else if (error.name === 'NotFoundError') {\n        errorMessage =\n          'Aucun microphone détecté. Veuillez connecter un microphone.';\n      } else if (error.name === 'NotSupportedError') {\n        errorMessage =\n          \"Votre navigateur ne supporte pas l'enregistrement audio.\";\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      this.toastService.showError(errorMessage);\n      this.cancelVoiceRecording();\n    }\n  }\n\n  stopVoiceRecording(): void {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n\n  cancelVoiceRecording(): void {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n      this.mediaRecorder = null;\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n\n  private async processRecordedAudio(): Promise<void> {\n    console.log('🎤 [Voice] Processing recorded audio...');\n\n    try {\n      // Vérifier qu'on a des données audio\n      if (this.audioChunks.length === 0) {\n        console.error('🎤 [Voice] No audio chunks available');\n        this.toastService.showError('Aucun audio enregistré');\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      console.log(\n        '🎤 [Voice] Audio chunks:',\n        this.audioChunks.length,\n        'Duration:',\n        this.voiceRecordingDuration\n      );\n\n      // Vérifier la durée minimale\n      if (this.voiceRecordingDuration < 1) {\n        console.error(\n          '🎤 [Voice] Recording too short:',\n          this.voiceRecordingDuration\n        );\n        this.toastService.showError(\n          'Enregistrement trop court (minimum 1 seconde)'\n        );\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      // Déterminer le type MIME du blob\n      let mimeType = 'audio/webm;codecs=opus';\n      if (this.mediaRecorder?.mimeType) {\n        mimeType = this.mediaRecorder.mimeType;\n      }\n\n      console.log('🎤 [Voice] Creating audio blob with MIME type:', mimeType);\n\n      // Créer le blob audio\n      const audioBlob = new Blob(this.audioChunks, {\n        type: mimeType,\n      });\n\n      console.log('🎤 [Voice] Audio blob created:', {\n        size: audioBlob.size,\n        type: audioBlob.type,\n      });\n\n      // Déterminer l'extension du fichier\n      let extension = '.webm';\n      if (mimeType.includes('mp4')) {\n        extension = '.mp4';\n      } else if (mimeType.includes('wav')) {\n        extension = '.wav';\n      } else if (mimeType.includes('ogg')) {\n        extension = '.ogg';\n      }\n\n      // Créer le fichier\n      const audioFile = new File(\n        [audioBlob],\n        `voice_${Date.now()}${extension}`,\n        {\n          type: mimeType,\n        }\n      );\n\n      console.log('🎤 [Voice] Audio file created:', {\n        name: audioFile.name,\n        size: audioFile.size,\n        type: audioFile.type,\n      });\n\n      // Envoyer le message vocal\n      this.voiceRecordingState = 'processing';\n      await this.sendVoiceMessage(audioFile);\n\n      console.log('🎤 [Voice] Voice message sent successfully');\n      this.toastService.showSuccess('🎤 Message vocal envoyé');\n    } catch (error: any) {\n      console.error('🎤 [Voice] Error processing audio:', error);\n      this.toastService.showError(\n        \"Erreur lors de l'envoi du message vocal: \" +\n          (error.message || 'Erreur inconnue')\n      );\n    } finally {\n      // Nettoyer l'état\n      this.voiceRecordingState = 'idle';\n      this.voiceRecordingDuration = 0;\n      this.audioChunks = [];\n      this.isRecordingVoice = false;\n\n      console.log('🎤 [Voice] Audio processing completed, state reset');\n    }\n  }\n\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        '',\n        audioFile,\n        'AUDIO' as any,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n          reject(error);\n        },\n      });\n    });\n  }\n\n  formatRecordingDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\n\n  onRecordStart(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record start triggered');\n    console.log('🎤 [Voice] Current state:', {\n      isRecordingVoice: this.isRecordingVoice,\n      voiceRecordingState: this.voiceRecordingState,\n      voiceRecordingDuration: this.voiceRecordingDuration,\n      mediaRecorder: !!this.mediaRecorder,\n    });\n\n    // Vérifier si on peut enregistrer\n    if (this.voiceRecordingState === 'processing') {\n      console.log('🎤 [Voice] Already processing, ignoring start');\n      this.toastService.showWarning('Traitement en cours...');\n      return;\n    }\n\n    if (this.isRecordingVoice) {\n      console.log('🎤 [Voice] Already recording, ignoring start');\n      this.toastService.showWarning('Enregistrement déjà en cours...');\n      return;\n    }\n\n    // Afficher un message de début\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\n\n    // Démarrer l'enregistrement\n    this.startVoiceRecording().catch((error) => {\n      console.error('🎤 [Voice] Failed to start recording:', error);\n      this.toastService.showError(\n        \"Impossible de démarrer l'enregistrement vocal: \" +\n          (error.message || 'Erreur inconnue')\n      );\n    });\n  }\n\n  onRecordEnd(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record end triggered');\n\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring end');\n      return;\n    }\n\n    // Arrêter l'enregistrement et envoyer\n    this.stopVoiceRecording();\n  }\n\n  onRecordCancel(event: Event): void {\n    event.preventDefault();\n    console.log('🎤 [Voice] Record cancel triggered');\n\n    if (!this.isRecordingVoice) {\n      console.log('🎤 [Voice] Not recording, ignoring cancel');\n      return;\n    }\n\n    // Annuler l'enregistrement\n    this.cancelVoiceRecording();\n  }\n\n  getRecordingFormat(): string {\n    if (this.mediaRecorder?.mimeType) {\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\n    }\n    return 'Auto';\n  }\n\n  // === ANIMATION DES WAVES VOCALES ===\n\n  private animateVoiceWaves(): void {\n    // Animer les waves pendant l'enregistrement\n    this.voiceWaves = this.voiceWaves.map(() => {\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\n    });\n  }\n\n  onFileSelected(event: any): void {\n    console.log('📁 [Upload] File selection triggered');\n    const files = event.target.files;\n\n    if (!files || files.length === 0) {\n      console.log('📁 [Upload] No files selected');\n      return;\n    }\n\n    console.log(`📁 [Upload] ${files.length} file(s) selected:`, files);\n\n    for (let file of files) {\n      console.log(\n        `📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`\n      );\n      this.uploadFile(file);\n    }\n  }\n\n  private uploadFile(file: File): void {\n    console.log(`📁 [Upload] Starting upload for file: ${file.name}`);\n\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      console.error('📁 [Upload] No receiver ID found');\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    console.log(`📁 [Upload] Receiver ID: ${receiverId}`);\n\n    // Vérifier la taille du fichier (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\n      return;\n    }\n\n    // 🖼️ Compression d'image si nécessaire\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\n      // > 1MB\n      console.log(\n        '🖼️ [Compression] Compressing image:',\n        file.name,\n        'Original size:',\n        file.size\n      );\n      this.compressImage(file)\n        .then((compressedFile) => {\n          console.log(\n            '🖼️ [Compression] ✅ Image compressed successfully. New size:',\n            compressedFile.size\n          );\n          this.sendFileToServer(compressedFile, receiverId);\n        })\n        .catch((error) => {\n          console.error('🖼️ [Compression] ❌ Error compressing image:', error);\n          // Envoyer le fichier original en cas d'erreur\n          this.sendFileToServer(file, receiverId);\n        });\n      return;\n    }\n\n    // Envoyer le fichier sans compression\n    this.sendFileToServer(file, receiverId);\n  }\n\n  private sendFileToServer(file: File, receiverId: string): void {\n    const messageType = this.getFileMessageType(file);\n    console.log(`📁 [Upload] Message type determined: ${messageType}`);\n    console.log(`📁 [Upload] Conversation ID: ${this.conversation.id}`);\n\n    this.isSendingMessage = true;\n    this.isUploading = true;\n    this.uploadProgress = 0;\n    console.log('📁 [Upload] Calling MessageService.sendMessage...');\n\n    // Simuler la progression d'upload\n    const progressInterval = setInterval(() => {\n      this.uploadProgress += Math.random() * 15;\n      if (this.uploadProgress >= 90) {\n        clearInterval(progressInterval);\n      }\n      this.cdr.detectChanges();\n    }, 300);\n\n    this.MessageService.sendMessage(\n      receiverId,\n      '',\n      file,\n      messageType,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        console.log('📁 [Upload] ✅ File sent successfully:', message);\n        console.log('📁 [Debug] Sent message structure:', {\n          id: message.id,\n          type: message.type,\n          attachments: message.attachments,\n          hasImage: this.hasImage(message),\n          hasFile: this.hasFile(message),\n          imageUrl: this.getImageUrl(message),\n        });\n\n        clearInterval(progressInterval);\n        this.uploadProgress = 100;\n\n        setTimeout(() => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Fichier envoyé avec succès');\n          this.resetUploadState();\n        }, 500);\n      },\n      error: (error: any) => {\n        console.error('📁 [Upload] ❌ Error sending file:', error);\n        clearInterval(progressInterval);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n        this.resetUploadState();\n      },\n    });\n  }\n\n  private getFileMessageType(file: File): any {\n    if (file.type.startsWith('image/')) return 'IMAGE' as any;\n    if (file.type.startsWith('video/')) return 'VIDEO' as any;\n    if (file.type.startsWith('audio/')) return 'AUDIO' as any;\n    return 'FILE' as any;\n  }\n\n  getFileAcceptTypes(): string {\n    return '*/*';\n  }\n\n  resetUploadState(): void {\n    this.isSendingMessage = false;\n    this.isUploading = false;\n    this.uploadProgress = 0;\n  }\n\n  // === DRAG & DROP ===\n\n  onDragOver(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n\n  onDragLeave(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\n    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\n    const x = event.clientX;\n    const y = event.clientY;\n\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n      this.isDragOver = false;\n    }\n  }\n\n  onDrop(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      console.log('📁 [Drag&Drop] Files dropped:', files.length);\n\n      // Traiter chaque fichier\n      Array.from(files).forEach((file) => {\n        console.log(\n          '📁 [Drag&Drop] Processing file:',\n          file.name,\n          file.type,\n          file.size\n        );\n        this.uploadFile(file);\n      });\n\n      this.toastService.showSuccess(\n        `${files.length} fichier(s) en cours d'envoi`\n      );\n    }\n  }\n\n  // === COMPRESSION D'IMAGES ===\n\n  private compressImage(file: File, quality: number = 0.8): Promise<File> {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n\n      img.onload = () => {\n        // Calculer les nouvelles dimensions (max 1920x1080)\n        const maxWidth = 1920;\n        const maxHeight = 1080;\n        let { width, height } = img;\n\n        if (width > maxWidth || height > maxHeight) {\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\n          width *= ratio;\n          height *= ratio;\n        }\n\n        canvas.width = width;\n        canvas.height = height;\n\n        // Dessiner l'image redimensionnée\n        ctx?.drawImage(img, 0, 0, width, height);\n\n        // Convertir en blob avec compression\n        canvas.toBlob(\n          (blob) => {\n            if (blob) {\n              const compressedFile = new File([blob], file.name, {\n                type: file.type,\n                lastModified: Date.now(),\n              });\n              resolve(compressedFile);\n            } else {\n              reject(new Error('Failed to compress image'));\n            }\n          },\n          file.type,\n          quality\n        );\n      };\n\n      img.onerror = () => reject(new Error('Failed to load image'));\n      img.src = URL.createObjectURL(file);\n    });\n  }\n\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\n\n  private handleTypingIndicator(): void {\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\n      this.sendTypingIndicator(true);\n    }\n\n    // Reset le timer\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Arrêter l'indicateur de frappe\n      this.sendTypingIndicator(false);\n    }, 2000);\n  }\n\n  private sendTypingIndicator(isTyping: boolean): void {\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n    if (receiverId && this.conversation?.id) {\n      console.log(\n        `📝 Sending typing indicator: ${isTyping} to user ${receiverId}`\n      );\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\n    }\n  }\n\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\n\n  onCallAccepted(call: Call): void {\n    console.log('🔄 Call accepted from interface:', call);\n    this.activeCall = call;\n    this.isInCall = true;\n    this.isCallConnected = true;\n    this.startCallTimer();\n    this.toastService.showSuccess('Appel accepté');\n  }\n\n  onCallRejected(): void {\n    console.log('🔄 Call rejected from interface');\n    this.endCall();\n    this.toastService.showInfo('Appel rejeté');\n  }\n\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\n\n  playVoiceMessage(message: any): void {\n    console.log('🎵 [Voice] Playing voice message:', message.id);\n    this.toggleVoicePlayback(message);\n  }\n\n  isVoicePlaying(messageId: string): boolean {\n    return this.playingMessageId === messageId;\n  }\n\n  toggleVoicePlayback(message: any): void {\n    const messageId = message.id;\n    const audioUrl = this.getVoiceUrl(message);\n\n    if (!audioUrl) {\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\n      this.toastService.showError('Fichier audio introuvable');\n      return;\n    }\n\n    // Si c'est déjà en cours de lecture, arrêter\n    if (this.isVoicePlaying(messageId)) {\n      this.stopVoicePlayback();\n      return;\n    }\n\n    // Arrêter toute autre lecture en cours\n    this.stopVoicePlayback();\n\n    // Démarrer la nouvelle lecture\n    this.startVoicePlayback(message, audioUrl);\n  }\n\n  private startVoicePlayback(message: any, audioUrl: string): void {\n    const messageId = message.id;\n\n    try {\n      console.log(\n        '🎵 [Voice] Starting playback for:',\n        messageId,\n        'URL:',\n        audioUrl\n      );\n\n      this.currentAudio = new Audio(audioUrl);\n      this.playingMessageId = messageId;\n\n      // Initialiser les valeurs par défaut avec la nouvelle structure\n      const currentData = this.getVoicePlaybackData(messageId);\n      this.setVoicePlaybackData(messageId, {\n        progress: 0,\n        currentTime: 0,\n        speed: currentData.speed || 1,\n        duration: currentData.duration || 0,\n      });\n\n      // Configurer la vitesse de lecture\n      this.currentAudio.playbackRate = currentData.speed || 1;\n\n      // Événements audio\n      this.currentAudio.addEventListener('loadedmetadata', () => {\n        if (this.currentAudio) {\n          this.setVoicePlaybackData(messageId, {\n            duration: this.currentAudio.duration,\n          });\n          console.log(\n            '🎵 [Voice] Audio loaded, duration:',\n            this.currentAudio.duration\n          );\n        }\n      });\n\n      this.currentAudio.addEventListener('timeupdate', () => {\n        if (this.currentAudio && this.playingMessageId === messageId) {\n          const currentTime = this.currentAudio.currentTime;\n          const progress = (currentTime / this.currentAudio.duration) * 100;\n          this.setVoicePlaybackData(messageId, { currentTime, progress });\n          this.cdr.detectChanges();\n        }\n      });\n\n      this.currentAudio.addEventListener('ended', () => {\n        console.log('🎵 [Voice] Playback ended for:', messageId);\n        this.stopVoicePlayback();\n      });\n\n      this.currentAudio.addEventListener('error', (error) => {\n        console.error('🎵 [Voice] Audio error:', error);\n        this.toastService.showError('Erreur lors de la lecture audio');\n        this.stopVoicePlayback();\n      });\n\n      // Démarrer la lecture\n      this.currentAudio\n        .play()\n        .then(() => {\n          console.log('🎵 [Voice] Playback started successfully');\n          this.toastService.showSuccess('🎵 Lecture du message vocal');\n        })\n        .catch((error) => {\n          console.error('🎵 [Voice] Error starting playback:', error);\n          this.toastService.showError('Impossible de lire le message vocal');\n          this.stopVoicePlayback();\n        });\n    } catch (error) {\n      console.error('🎵 [Voice] Error creating audio:', error);\n      this.toastService.showError('Erreur lors de la lecture audio');\n      this.stopVoicePlayback();\n    }\n  }\n\n  private stopVoicePlayback(): void {\n    if (this.currentAudio) {\n      console.log('🎵 [Voice] Stopping playback for:', this.playingMessageId);\n      this.currentAudio.pause();\n      this.currentAudio.currentTime = 0;\n      this.currentAudio = null;\n    }\n    this.playingMessageId = null;\n    this.cdr.detectChanges();\n  }\n\n  getVoiceUrl(message: any): string {\n    // Vérifier les propriétés directes d'audio\n    if (message.voiceUrl) return message.voiceUrl;\n    if (message.audioUrl) return message.audioUrl;\n    if (message.voice) return message.voice;\n\n    // Vérifier les attachments audio\n    const audioAttachment = message.attachments?.find(\n      (att: any) => att.type?.startsWith('audio/') || att.type === 'AUDIO'\n    );\n\n    if (audioAttachment) {\n      return audioAttachment.url || audioAttachment.path || '';\n    }\n\n    return '';\n  }\n\n  getVoiceWaves(message: any): number[] {\n    // Générer des waves basées sur l'ID du message pour la cohérence\n    const messageId = message.id || '';\n    const seed = messageId\n      .split('')\n      .reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);\n    const waves: number[] = [];\n\n    for (let i = 0; i < 16; i++) {\n      const height = 4 + ((seed + i * 7) % 20);\n      waves.push(height);\n    }\n\n    return waves;\n  }\n\n  getVoiceProgress(message: any): number {\n    const data = this.getVoicePlaybackData(message.id);\n    const totalWaves = 16;\n    return Math.floor((data.progress / 100) * totalWaves);\n  }\n\n  getVoiceCurrentTime(message: any): string {\n    const data = this.getVoicePlaybackData(message.id);\n    return this.formatAudioTime(data.currentTime);\n  }\n\n  getVoiceDuration(message: any): string {\n    const data = this.getVoicePlaybackData(message.id);\n    const duration = data.duration || message.metadata?.duration || 0;\n\n    if (typeof duration === 'string') {\n      return duration; // Déjà formaté\n    }\n\n    return this.formatAudioTime(duration);\n  }\n\n  private formatAudioTime(seconds: number): string {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  seekVoiceMessage(message: any, waveIndex: number): void {\n    const messageId = message.id;\n\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\n      return;\n    }\n\n    const totalWaves = 16;\n    const seekPercentage = (waveIndex / totalWaves) * 100;\n    const seekTime = (seekPercentage / 100) * this.currentAudio.duration;\n\n    this.currentAudio.currentTime = seekTime;\n    console.log('🎵 [Voice] Seeking to:', seekTime, 'seconds');\n  }\n\n  toggleVoiceSpeed(message: any): void {\n    const messageId = message.id;\n    const data = this.getVoicePlaybackData(messageId);\n\n    // Cycle entre 1x, 1.5x, 2x\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\n\n    this.setVoicePlaybackData(messageId, { speed: newSpeed });\n\n    if (this.currentAudio && this.playingMessageId === messageId) {\n      this.currentAudio.playbackRate = newSpeed;\n    }\n\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\n  }\n\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n\n  changeVoiceSpeed(message: any): void {\n    this.toggleVoiceSpeed(message);\n  }\n\n  getVoiceSpeed(message: any): number {\n    const data = this.getVoicePlaybackData(message.id);\n    return data.speed || 1;\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n\n    // Nettoyer les timers\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    // Nettoyer les ressources audio\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream?.getTracks().forEach((track) => track.stop());\n    }\n\n    // Nettoyer la lecture audio\n    this.stopVoicePlayback();\n  }\n}\n", "<!-- ===== MESSAGE CHAT COMPONENT - REORGANIZED & OPTIMIZED ===== -->\n<div\n  style=\"\n    display: flex;\n    flex-direction: column;\n    height: 100vh;\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n    color: #1f2937;\n    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  \"\n>\n  <!-- ===== ANIMATIONS CSS ===== -->\n  <style>\n    @keyframes pulse {\n      0%,\n      100% {\n        opacity: 1;\n      }\n      50% {\n        opacity: 0.5;\n      }\n    }\n    @keyframes bounce {\n      0%,\n      20%,\n      53%,\n      80%,\n      100% {\n        transform: translateY(0);\n      }\n      40%,\n      43% {\n        transform: translateY(-8px);\n      }\n      70% {\n        transform: translateY(-4px);\n      }\n    }\n    @keyframes spin {\n      from {\n        transform: rotate(0deg);\n      }\n      to {\n        transform: rotate(360deg);\n      }\n    }\n    @keyframes ping {\n      75%,\n      100% {\n        transform: scale(2);\n        opacity: 0;\n      }\n    }\n    @keyframes slideInUp {\n      from {\n        transform: translateX(-50%) translateY(20px);\n        opacity: 0;\n      }\n      to {\n        transform: translateX(-50%) translateY(0);\n        opacity: 1;\n      }\n    }\n  </style>\n\n  <!-- ===== HEADER SECTION ===== -->\n  <header\n    style=\"\n      display: flex;\n      align-items: center;\n      padding: 12px 16px;\n      background: #ffffff;\n      border-bottom: 1px solid #e5e7eb;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n      position: relative;\n      z-index: 10;\n    \"\n  >\n    <!-- Bouton retour -->\n    <button\n      (click)=\"goBackToConversations()\"\n      style=\"\n        padding: 10px;\n        margin-right: 12px;\n        border-radius: 50%;\n        border: none;\n        background: transparent;\n        cursor: pointer;\n        transition: all 0.2s ease;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        min-width: 40px;\n        min-height: 40px;\n      \"\n      onmouseover=\"this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'\"\n      onmouseout=\"this.style.background='transparent'; this.style.transform='scale(1)'\"\n      title=\"Retour aux conversations\"\n    >\n      <i\n        class=\"fas fa-arrow-left\"\n        style=\"color: #374151; font-size: 18px; font-weight: bold\"\n      ></i>\n    </button>\n\n    <!-- Info utilisateur -->\n    <div style=\"display: flex; align-items: center; flex: 1; min-width: 0\">\n      <!-- Avatar avec statut -->\n      <div style=\"position: relative; margin-right: 12px\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          style=\"\n            width: 40px;\n            height: 40px;\n            border-radius: 50%;\n            object-fit: cover;\n            border: 2px solid transparent;\n            cursor: pointer;\n            transition: transform 0.2s ease;\n          \"\n          (click)=\"openUserProfile(otherParticipant?.id!)\"\n          onmouseover=\"this.style.transform='scale(1.05)'\"\n          onmouseout=\"this.style.transform='scale(1)'\"\n          title=\"Voir le profil\"\n        />\n        <!-- Indicateur en ligne -->\n        <div\n          *ngIf=\"otherParticipant?.isOnline\"\n          style=\"\n            position: absolute;\n            bottom: 0;\n            right: 0;\n            width: 12px;\n            height: 12px;\n            background: #10b981;\n            border: 2px solid transparent;\n            border-radius: 50%;\n            animation: pulse 2s infinite;\n          \"\n        ></div>\n      </div>\n\n      <!-- Nom et statut -->\n      <div style=\"flex: 1; min-width: 0\">\n        <h3\n          style=\"\n            font-weight: 600;\n            color: #111827;\n            margin: 0;\n            font-size: 16px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n          \"\n        >\n          {{ otherParticipant?.username || \"Utilisateur\" }}\n        </h3>\n        <div style=\"font-size: 14px; color: #6b7280; margin-top: 2px\">\n          <!-- Indicateur de frappe -->\n          <div\n            *ngIf=\"isUserTyping\"\n            style=\"display: flex; align-items: center; gap: 4px; color: #10b981\"\n          >\n            <span>En train d'écrire</span>\n            <div style=\"display: flex; gap: 2px\">\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite;\n                \"\n              ></div>\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite 0.1s;\n                \"\n              ></div>\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite 0.2s;\n                \"\n              ></div>\n            </div>\n          </div>\n          <!-- Statut en ligne -->\n          <span *ngIf=\"!isUserTyping\">\n            {{\n              otherParticipant?.isOnline\n                ? \"En ligne\"\n                : formatLastActive(otherParticipant?.lastActive)\n            }}\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Actions -->\n    <div style=\"display: flex; align-items: center; gap: 8px\">\n      <!-- Appel vidéo -->\n      <button\n        (click)=\"startVideoCall()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n        \"\n        title=\"Appel vidéo\"\n        onmouseover=\"this.style.background='#f3f4f6'\"\n        onmouseout=\"this.style.background='transparent'\"\n      >\n        <i class=\"fas fa-video\"></i>\n      </button>\n\n      <!-- Appel vocal -->\n      <button\n        (click)=\"startVoiceCall()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n        \"\n        title=\"Appel vocal\"\n        onmouseover=\"this.style.background='#f3f4f6'\"\n        onmouseout=\"this.style.background='transparent'\"\n      >\n        <i class=\"fas fa-phone\"></i>\n      </button>\n\n      <!-- Recherche -->\n      <button\n        (click)=\"toggleSearch()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n        \"\n        [style.background]=\"searchMode ? '#dcfce7' : 'transparent'\"\n        [style.color]=\"searchMode ? '#16a34a' : '#6b7280'\"\n        title=\"Rechercher\"\n      >\n        <i class=\"fas fa-search\"></i>\n      </button>\n\n      <!-- Menu principal -->\n      <button\n        (click)=\"toggleMainMenu()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n          position: relative;\n        \"\n        [style.background]=\"showMainMenu ? '#dcfce7' : 'transparent'\"\n        [style.color]=\"showMainMenu ? '#16a34a' : '#6b7280'\"\n        title=\"Menu\"\n      >\n        <i class=\"fas fa-ellipsis-v\"></i>\n      </button>\n    </div>\n\n    <!-- Menu dropdown -->\n    <div\n      *ngIf=\"showMainMenu\"\n      style=\"\n        position: absolute;\n        top: 64px;\n        right: 16px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        min-width: 192px;\n      \"\n    >\n      <div style=\"padding: 8px\">\n        <button\n          (click)=\"toggleSearch(); showMainMenu = false\"\n          style=\"\n            width: 100%;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            padding: 8px 12px;\n            border-radius: 8px;\n            border: none;\n            background: transparent;\n            cursor: pointer;\n            transition: all 0.2s;\n            text-align: left;\n          \"\n          onmouseover=\"this.style.background='#f3f4f6'\"\n          onmouseout=\"this.style.background='transparent'\"\n        >\n          <i class=\"fas fa-search\" style=\"color: #3b82f6\"></i>\n          <span style=\"color: #374151\">Rechercher</span>\n        </button>\n        <button\n          style=\"\n            width: 100%;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            padding: 8px 12px;\n            border-radius: 8px;\n            border: none;\n            background: transparent;\n            cursor: pointer;\n            transition: all 0.2s;\n            text-align: left;\n          \"\n          onmouseover=\"this.style.background='#f3f4f6'\"\n          onmouseout=\"this.style.background='transparent'\"\n        >\n          <i class=\"fas fa-user\" style=\"color: #10b981\"></i>\n          <span style=\"color: #374151\">Voir le profil</span>\n        </button>\n        <hr style=\"margin: 8px 0; border-color: #e5e7eb\" />\n        <button\n          style=\"\n            width: 100%;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            padding: 8px 12px;\n            border-radius: 8px;\n            border: none;\n            background: transparent;\n            cursor: pointer;\n            transition: all 0.2s;\n            text-align: left;\n          \"\n          onmouseover=\"this.style.background='#f3f4f6'\"\n          onmouseout=\"this.style.background='transparent'\"\n        >\n          <i class=\"fas fa-cog\" style=\"color: #6b7280\"></i>\n          <span style=\"color: #374151\">Paramètres</span>\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <!-- ===== MAIN MESSAGES SECTION ===== -->\n  <main\n    style=\"flex: 1; overflow-y: auto; padding: 16px; position: relative\"\n    #messagesContainer\n    (scroll)=\"onScroll($event)\"\n    (dragover)=\"onDragOver($event)\"\n    (dragleave)=\"onDragLeave($event)\"\n    (drop)=\"onDrop($event)\"\n    [style.background]=\"isDragOver ? 'rgba(34, 197, 94, 0.1)' : 'transparent'\"\n  >\n    <!-- Drag & Drop Overlay -->\n    <div\n      *ngIf=\"isDragOver\"\n      style=\"\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(34, 197, 94, 0.2);\n        border: 2px dashed transparent;\n        border-radius: 8px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 50;\n        backdrop-filter: blur(2px);\n        animation: pulse 2s infinite;\n      \"\n    >\n      <div\n        style=\"\n          text-align: center;\n          background: #ffffff;\n          padding: 24px;\n          border-radius: 12px;\n          box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);\n          border: 1px solid transparent;\n        \"\n      >\n        <i\n          class=\"fas fa-cloud-upload-alt\"\n          style=\"\n            font-size: 48px;\n            color: #10b981;\n            margin-bottom: 12px;\n            animation: bounce 1s infinite;\n          \"\n        ></i>\n        <p\n          style=\"\n            font-size: 20px;\n            font-weight: bold;\n            color: #047857;\n            margin-bottom: 8px;\n          \"\n        >\n          Déposez vos fichiers ici\n        </p>\n        <p style=\"font-size: 14px; color: #10b981\">\n          Images, vidéos, documents...\n        </p>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div\n      *ngIf=\"isLoading\"\n      style=\"\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 32px 0;\n      \"\n    >\n      <div\n        style=\"\n          width: 32px;\n          height: 32px;\n          border: 2px solid #e5e7eb;\n          border-bottom-color: #10b981;\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin-bottom: 16px;\n        \"\n      ></div>\n      <span style=\"color: #6b7280\">Chargement des messages...</span>\n    </div>\n\n    <!-- Empty State -->\n    <div\n      *ngIf=\"!isLoading && messages.length === 0\"\n      style=\"\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 64px 0;\n      \"\n    >\n      <div style=\"font-size: 64px; color: #d1d5db; margin-bottom: 16px\">\n        <i class=\"fas fa-comments\"></i>\n      </div>\n      <h3\n        style=\"\n          font-size: 20px;\n          font-weight: 600;\n          color: #374151;\n          margin-bottom: 8px;\n        \"\n      >\n        Aucun message\n      </h3>\n      <p style=\"color: #6b7280; text-align: center\">\n        Commencez votre conversation avec {{ otherParticipant?.username }}\n      </p>\n    </div>\n\n    <!-- Messages List -->\n    <div\n      *ngIf=\"!isLoading && messages.length > 0\"\n      style=\"display: flex; flex-direction: column; gap: 8px\"\n    >\n      <ng-container\n        *ngFor=\"\n          let message of messages;\n          let i = index;\n          trackBy: trackByMessageId\n        \"\n      >\n        <!-- Date Separator -->\n        <div\n          *ngIf=\"shouldShowDateSeparator(i)\"\n          style=\"display: flex; justify-content: center; margin: 16px 0\"\n        >\n          <div\n            style=\"\n              background: #ffffff;\n              padding: 4px 12px;\n              border-radius: 20px;\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n            \"\n          >\n            <span style=\"font-size: 12px; color: #6b7280\">\n              {{ formatDateSeparator(message.timestamp) }}\n            </span>\n          </div>\n        </div>\n\n        <!-- Message Container -->\n        <div\n          style=\"display: flex\"\n          [style.justify-content]=\"\n            message.sender?.id === currentUserId ? 'flex-end' : 'flex-start'\n          \"\n          [id]=\"'message-' + message.id\"\n          (click)=\"onMessageClick(message, $event)\"\n          (contextmenu)=\"onMessageContextMenu(message, $event)\"\n        >\n          <!-- Avatar for others -->\n          <div\n            *ngIf=\"message.sender?.id !== currentUserId && shouldShowAvatar(i)\"\n            style=\"margin-right: 8px; flex-shrink: 0\"\n          >\n            <img\n              [src]=\"\n                message.sender?.image || 'assets/images/default-avatar.png'\n              \"\n              [alt]=\"message.sender?.username\"\n              style=\"\n                width: 32px;\n                height: 32px;\n                border-radius: 50%;\n                object-fit: cover;\n                cursor: pointer;\n                transition: transform 0.2s;\n              \"\n              (click)=\"openUserProfile(message.sender?.id!)\"\n              onmouseover=\"this.style.transform='scale(1.05)'\"\n              onmouseout=\"this.style.transform='scale(1)'\"\n            />\n          </div>\n\n          <!-- Message Bubble -->\n          <div\n            [style.background-color]=\"\n              message.sender?.id === currentUserId ? '#3b82f6' : '#ffffff'\n            \"\n            [style.color]=\"\n              message.sender?.id === currentUserId ? '#ffffff' : '#111827'\n            \"\n            style=\"\n              max-width: 320px;\n              padding: 12px 16px;\n              border-radius: 18px;\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n              position: relative;\n              word-wrap: break-word;\n              overflow-wrap: break-word;\n              border: none;\n            \"\n          >\n            <!-- Sender Name (for groups) -->\n            <div\n              *ngIf=\"\n                isGroupConversation() &&\n                message.sender?.id !== currentUserId &&\n                shouldShowSenderName(i)\n              \"\n              style=\"\n                font-size: 12px;\n                font-weight: 600;\n                margin-bottom: 4px;\n                opacity: 0.75;\n              \"\n              [style.color]=\"getUserColor(message.sender?.id!)\"\n            >\n              {{ message.sender?.username }}\n            </div>\n\n            <!-- Text Content -->\n            <div\n              *ngIf=\"getMessageType(message) === 'text'\"\n              style=\"word-wrap: break-word; overflow-wrap: break-word\"\n            >\n              <div [innerHTML]=\"formatMessageContent(message.content)\"></div>\n            </div>\n\n            <!-- Image Content -->\n            <div *ngIf=\"hasImage(message)\" style=\"margin: 8px 0\">\n              <img\n                [src]=\"getImageUrl(message)\"\n                [alt]=\"message.content || 'Image'\"\n                (click)=\"openImageViewer(message)\"\n                (load)=\"onImageLoad($event, message)\"\n                (error)=\"onImageError($event, message)\"\n                style=\"\n                  max-width: 280px;\n                  height: auto;\n                  border-radius: 12px;\n                  cursor: pointer;\n                  transition: transform 0.2s;\n                \"\n                onmouseover=\"this.style.transform='scale(1.02)'\"\n                onmouseout=\"this.style.transform='scale(1)'\"\n              />\n              <!-- Image Caption -->\n              <div\n                *ngIf=\"message.content\"\n                [style.color]=\"\n                  message.sender?.id === currentUserId ? '#ffffff' : '#111827'\n                \"\n                style=\"font-size: 14px; margin-top: 8px; line-height: 1.4\"\n                [innerHTML]=\"formatMessageContent(message.content)\"\n              ></div>\n            </div>\n\n            <!-- Voice Message Content -->\n            <div\n              *ngIf=\"getMessageType(message) === 'audio'\"\n              style=\"\n                display: flex;\n                align-items: center;\n                gap: 12px;\n                padding: 12px;\n                background: rgba(255, 255, 255, 0.1);\n                border-radius: 12px;\n                margin: 8px 0;\n                min-width: 200px;\n                max-width: 280px;\n              \"\n            >\n              <!-- Bouton Play/Pause -->\n              <button\n                (click)=\"toggleVoicePlayback(message)\"\n                style=\"\n                  width: 40px;\n                  height: 40px;\n                  border-radius: 50%;\n                  border: none;\n                  background: rgba(255, 255, 255, 0.2);\n                  color: inherit;\n                  cursor: pointer;\n                  display: flex;\n                  align-items: center;\n                  justify-content: center;\n                  transition: all 0.2s;\n                  flex-shrink: 0;\n                \"\n                onmouseover=\"this.style.background='rgba(255, 255, 255, 0.3)'\"\n                onmouseout=\"this.style.background='rgba(255, 255, 255, 0.2)'\"\n                title=\"Lire/Pause\"\n              >\n                <i\n                  [class]=\"\n                    isVoicePlaying(message.id) ? 'fas fa-pause' : 'fas fa-play'\n                  \"\n                  style=\"font-size: 14px\"\n                ></i>\n              </button>\n\n              <!-- Visualisation des ondes sonores -->\n              <div\n                style=\"\n                  flex: 1;\n                  display: flex;\n                  align-items: center;\n                  gap: 2px;\n                  height: 24px;\n                  overflow: hidden;\n                \"\n              >\n                <div\n                  *ngFor=\"let wave of voiceWaves; let i = index\"\n                  style=\"\n                    width: 3px;\n                    background: currentColor;\n                    border-radius: 2px;\n                    opacity: 0.7;\n                    transition: height 0.3s ease;\n                  \"\n                  [style.height.px]=\"isVoicePlaying(message.id) ? wave : 8\"\n                  [style.animation]=\"\n                    isVoicePlaying(message.id) ? 'pulse 1s infinite' : 'none'\n                  \"\n                  [style.animation-delay]=\"i * 0.1 + 's'\"\n                ></div>\n              </div>\n\n              <!-- Durée et contrôles -->\n              <div\n                style=\"\n                  display: flex;\n                  align-items: center;\n                  gap: 8px;\n                  flex-shrink: 0;\n                \"\n              >\n                <!-- Durée -->\n                <div\n                  style=\"\n                    font-size: 12px;\n                    opacity: 0.8;\n                    min-width: 40px;\n                    text-align: right;\n                  \"\n                >\n                  {{ getVoiceDuration(message) }}\n                </div>\n\n                <!-- Vitesse de lecture (si en cours de lecture) -->\n                <button\n                  *ngIf=\"isVoicePlaying(message.id)\"\n                  (click)=\"changeVoiceSpeed(message)\"\n                  style=\"\n                    padding: 4px 8px;\n                    border-radius: 12px;\n                    border: none;\n                    background: rgba(255, 255, 255, 0.2);\n                    color: inherit;\n                    cursor: pointer;\n                    font-size: 11px;\n                    transition: all 0.2s;\n                  \"\n                  onmouseover=\"this.style.background='rgba(255, 255, 255, 0.3)'\"\n                  onmouseout=\"this.style.background='rgba(255, 255, 255, 0.2)'\"\n                  title=\"Changer la vitesse\"\n                >\n                  {{ getVoiceSpeed(message) }}x\n                </button>\n              </div>\n            </div>\n\n            <!-- Message Metadata -->\n            <div\n              style=\"\n                display: flex;\n                align-items: center;\n                justify-content: flex-end;\n                gap: 4px;\n                margin-top: 4px;\n                font-size: 12px;\n                opacity: 0.75;\n              \"\n            >\n              <span>{{ formatMessageTime(message.timestamp) }}</span>\n              <div\n                *ngIf=\"message.sender?.id === currentUserId\"\n                style=\"display: flex; align-items: center\"\n              >\n                <i\n                  class=\"fas fa-clock\"\n                  *ngIf=\"message.status === 'SENDING'\"\n                  title=\"Envoi en cours\"\n                ></i>\n                <i\n                  class=\"fas fa-check\"\n                  *ngIf=\"message.status === 'SENT'\"\n                  title=\"Envoyé\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  *ngIf=\"message.status === 'DELIVERED'\"\n                  title=\"Livré\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  style=\"color: #3b82f6\"\n                  *ngIf=\"message.status === 'READ'\"\n                  title=\"Lu\"\n                ></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </ng-container>\n\n      <!-- Typing Indicator -->\n      <div\n        *ngIf=\"otherUserIsTyping\"\n        style=\"display: flex; align-items: start; gap: 8px\"\n      >\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          style=\"\n            width: 32px;\n            height: 32px;\n            border-radius: 50%;\n            object-fit: cover;\n          \"\n        />\n        <div\n          style=\"\n            background: #ffffff;\n            padding: 12px 16px;\n            border-radius: 18px;\n            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          \"\n        >\n          <div style=\"display: flex; gap: 4px\">\n            <div\n              style=\"\n                width: 8px;\n                height: 8px;\n                background: #6b7280;\n                border-radius: 50%;\n                animation: bounce 1s infinite;\n              \"\n            ></div>\n            <div\n              style=\"\n                width: 8px;\n                height: 8px;\n                background: #6b7280;\n                border-radius: 50%;\n                animation: bounce 1s infinite 0.1s;\n              \"\n            ></div>\n            <div\n              style=\"\n                width: 8px;\n                height: 8px;\n                background: #6b7280;\n                border-radius: 50%;\n                animation: bounce 1s infinite 0.2s;\n              \"\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n\n  <!-- ===== FOOTER INPUT SECTION ===== -->\n  <footer\n    style=\"background: #ffffff; border-top: 1px solid #e5e7eb; padding: 16px\"\n  >\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      style=\"display: flex; align-items: end; gap: 12px\"\n    >\n      <!-- Left Actions -->\n      <div style=\"display: flex; gap: 8px\">\n        <!-- Emoji Button -->\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          style=\"\n            padding: 8px;\n            border-radius: 50%;\n            border: none;\n            background: transparent;\n            color: #6b7280;\n            cursor: pointer;\n            transition: all 0.2s;\n          \"\n          [style.background]=\"showEmojiPicker ? '#dcfce7' : 'transparent'\"\n          [style.color]=\"showEmojiPicker ? '#16a34a' : '#6b7280'\"\n          title=\"Émojis\"\n          onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\n          onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\n        >\n          <i class=\"fas fa-smile\"></i>\n        </button>\n\n        <!-- Attachment Button -->\n        <button\n          type=\"button\"\n          (click)=\"toggleAttachmentMenu()\"\n          style=\"\n            padding: 8px;\n            border-radius: 50%;\n            border: none;\n            background: transparent;\n            color: #6b7280;\n            cursor: pointer;\n            transition: all 0.2s;\n          \"\n          [style.background]=\"showAttachmentMenu ? '#dcfce7' : 'transparent'\"\n          [style.color]=\"showAttachmentMenu ? '#16a34a' : '#6b7280'\"\n          title=\"Pièces jointes\"\n          onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\n          onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\n        >\n          <i class=\"fas fa-paperclip\"></i>\n        </button>\n\n        <!-- Voice Recording Button -->\n        <button\n          type=\"button\"\n          (mousedown)=\"onRecordStart($event)\"\n          (mouseup)=\"onRecordEnd($event)\"\n          (mouseleave)=\"onRecordCancel($event)\"\n          (touchstart)=\"onRecordStart($event)\"\n          (touchend)=\"onRecordEnd($event)\"\n          (touchcancel)=\"onRecordCancel($event)\"\n          style=\"\n            padding: 8px;\n            border-radius: 50%;\n            border: none;\n            background: transparent;\n            color: #6b7280;\n            cursor: pointer;\n            transition: all 0.2s;\n            position: relative;\n          \"\n          [style.background]=\"isRecordingVoice ? '#fef3c7' : 'transparent'\"\n          [style.color]=\"isRecordingVoice ? '#f59e0b' : '#6b7280'\"\n          [style.transform]=\"isRecordingVoice ? 'scale(1.1)' : 'scale(1)'\"\n          title=\"Maintenir pour enregistrer un message vocal\"\n          onmouseover=\"if(!this.style.background || this.style.background === 'transparent') this.style.background='#f3f4f6'\"\n          onmouseout=\"if(this.style.background === '#f3f4f6') this.style.background='transparent'\"\n        >\n          <i\n            [class]=\"isRecordingVoice ? 'fas fa-stop' : 'fas fa-microphone'\"\n            [style.animation]=\"isRecordingVoice ? 'pulse 1s infinite' : 'none'\"\n          ></i>\n\n          <!-- Indicateur d'enregistrement -->\n          <div\n            *ngIf=\"isRecordingVoice\"\n            style=\"\n              position: absolute;\n              top: -2px;\n              right: -2px;\n              width: 8px;\n              height: 8px;\n              background: #ef4444;\n              border-radius: 50%;\n              animation: ping 1s infinite;\n            \"\n          ></div>\n        </button>\n      </div>\n\n      <!-- Message Input -->\n      <div style=\"flex: 1; position: relative\">\n        <textarea\n          formControlName=\"content\"\n          placeholder=\"Tapez votre message...\"\n          (keydown)=\"onInputKeyDown($event)\"\n          (input)=\"onInputChange($event)\"\n          (focus)=\"onInputFocus()\"\n          style=\"\n            width: 100%;\n            min-height: 44px;\n            max-height: 120px;\n            padding: 12px 16px;\n            border: 1px solid #e5e7eb;\n            border-radius: 22px;\n            resize: none;\n            outline: none;\n            font-family: inherit;\n            font-size: 14px;\n            line-height: 1.4;\n            background: #ffffff;\n            color: #111827;\n            transition: all 0.2s;\n          \"\n          [disabled]=\"isInputDisabled()\"\n        ></textarea>\n      </div>\n\n      <!-- Send Button -->\n      <button\n        type=\"submit\"\n        [disabled]=\"!messageForm.valid || isSendingMessage\"\n        style=\"\n          padding: 12px;\n          border-radius: 50%;\n          border: none;\n          background: #3b82f6;\n          color: #ffffff;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          min-width: 44px;\n          min-height: 44px;\n        \"\n        [style.background]=\"\n          !messageForm.valid || isSendingMessage ? '#9ca3af' : '#3b82f6'\n        \"\n        [style.cursor]=\"\n          !messageForm.valid || isSendingMessage ? 'not-allowed' : 'pointer'\n        \"\n        title=\"Envoyer\"\n        onmouseover=\"if(!this.disabled) this.style.background='#2563eb'\"\n        onmouseout=\"if(!this.disabled) this.style.background='#3b82f6'\"\n      >\n        <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\n        <div\n          *ngIf=\"isSendingMessage\"\n          style=\"\n            width: 16px;\n            height: 16px;\n            border: 2px solid #ffffff;\n            border-top-color: transparent;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n          \"\n        ></div>\n      </button>\n    </form>\n\n    <!-- Emoji Picker -->\n    <div\n      *ngIf=\"showEmojiPicker\"\n      style=\"\n        position: absolute;\n        bottom: 80px;\n        left: 16px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        width: 320px;\n        max-height: 300px;\n        overflow-y: auto;\n      \"\n    >\n      <div style=\"padding: 16px\">\n        <h4\n          style=\"\n            margin: 0 0 12px 0;\n            font-size: 14px;\n            font-weight: 600;\n            color: #374151;\n          \"\n        >\n          Émojis\n        </h4>\n        <div\n          style=\"display: grid; grid-template-columns: repeat(8, 1fr); gap: 8px\"\n        >\n          <button\n            *ngFor=\"let emoji of getEmojisForCategory(selectedEmojiCategory)\"\n            (click)=\"insertEmoji(emoji)\"\n            style=\"\n              padding: 8px;\n              border: none;\n              background: transparent;\n              border-radius: 8px;\n              cursor: pointer;\n              font-size: 20px;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n            [title]=\"emoji.name\"\n          >\n            {{ emoji.emoji }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Attachment Menu -->\n    <div\n      *ngIf=\"showAttachmentMenu\"\n      style=\"\n        position: absolute;\n        bottom: 80px;\n        left: 60px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        min-width: 200px;\n      \"\n    >\n      <div style=\"padding: 16px\">\n        <h4\n          style=\"\n            margin: 0 0 12px 0;\n            font-size: 14px;\n            font-weight: 600;\n            color: #374151;\n          \"\n        >\n          Pièces jointes\n        </h4>\n        <div\n          style=\"\n            display: grid;\n            grid-template-columns: repeat(2, 1fr);\n            gap: 12px;\n          \"\n        >\n          <!-- Images -->\n          <button\n            (click)=\"triggerFileInput('image')\"\n            style=\"\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              gap: 8px;\n              padding: 16px;\n              border: none;\n              background: transparent;\n              border-radius: 12px;\n              cursor: pointer;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n          >\n            <div\n              style=\"\n                width: 48px;\n                height: 48px;\n                background: #dbeafe;\n                border-radius: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <i\n                class=\"fas fa-image\"\n                style=\"color: #3b82f6; font-size: 20px\"\n              ></i>\n            </div>\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\n              >Images</span\n            >\n          </button>\n\n          <!-- Documents -->\n          <button\n            (click)=\"triggerFileInput('document')\"\n            style=\"\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              gap: 8px;\n              padding: 16px;\n              border: none;\n              background: transparent;\n              border-radius: 12px;\n              cursor: pointer;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n          >\n            <div\n              style=\"\n                width: 48px;\n                height: 48px;\n                background: #fef3c7;\n                border-radius: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <i\n                class=\"fas fa-file-alt\"\n                style=\"color: #f59e0b; font-size: 20px\"\n              ></i>\n            </div>\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\n              >Documents</span\n            >\n          </button>\n\n          <!-- Camera -->\n          <button\n            (click)=\"openCamera()\"\n            style=\"\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              gap: 8px;\n              padding: 16px;\n              border: none;\n              background: transparent;\n              border-radius: 12px;\n              cursor: pointer;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n          >\n            <div\n              style=\"\n                width: 48px;\n                height: 48px;\n                background: #dcfce7;\n                border-radius: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <i\n                class=\"fas fa-camera\"\n                style=\"color: #10b981; font-size: 20px\"\n              ></i>\n            </div>\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\n              >Caméra</span\n            >\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Hidden File Input -->\n    <input\n      #fileInput\n      type=\"file\"\n      style=\"display: none\"\n      (change)=\"onFileSelected($event)\"\n      [accept]=\"getFileAcceptTypes()\"\n      multiple\n    />\n  </footer>\n\n  <!-- Overlay to close menus -->\n  <div\n    *ngIf=\"showEmojiPicker || showAttachmentMenu || showMainMenu\"\n    style=\"\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.25);\n      z-index: 40;\n    \"\n    (click)=\"closeAllMenus()\"\n  ></div>\n\n  <!-- Interface d'enregistrement vocal -->\n  <div\n    *ngIf=\"isRecordingVoice\"\n    style=\"\n      position: fixed;\n      bottom: 100px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: linear-gradient(135deg, #f59e0b, #d97706);\n      color: white;\n      padding: 20px 24px;\n      border-radius: 20px;\n      box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);\n      z-index: 60;\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      min-width: 280px;\n      animation: slideInUp 0.3s ease-out;\n    \"\n  >\n    <!-- Icône microphone animée -->\n    <div\n      style=\"\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        animation: pulse 1s infinite;\n      \"\n    >\n      <i class=\"fas fa-microphone\" style=\"font-size: 20px\"></i>\n    </div>\n\n    <!-- Contenu central -->\n    <div style=\"flex: 1\">\n      <!-- Durée d'enregistrement -->\n      <div style=\"font-size: 18px; font-weight: bold; margin-bottom: 4px\">\n        {{ formatRecordingDuration(voiceRecordingDuration) }}\n      </div>\n\n      <!-- Visualisation des ondes sonores -->\n      <div style=\"display: flex; align-items: center; gap: 2px; height: 20px\">\n        <div\n          *ngFor=\"let wave of voiceWaves; let i = index\"\n          style=\"\n            width: 3px;\n            background: rgba(255, 255, 255, 0.8);\n            border-radius: 2px;\n            transition: height 0.3s ease;\n          \"\n          [style.height.px]=\"wave\"\n          [style.animation]=\"'bounce 1s infinite'\"\n          [style.animation-delay]=\"i * 0.1 + 's'\"\n        ></div>\n      </div>\n\n      <!-- Format d'enregistrement -->\n      <div style=\"font-size: 12px; opacity: 0.8; margin-top: 4px\">\n        Format: {{ getRecordingFormat() }}\n      </div>\n    </div>\n\n    <!-- Boutons d'action -->\n    <div style=\"display: flex; gap: 8px\">\n      <!-- Bouton Annuler -->\n      <button\n        (click)=\"onRecordCancel($event)\"\n        style=\"\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          border: none;\n          background: rgba(239, 68, 68, 0.8);\n          color: white;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.2s;\n        \"\n        onmouseover=\"this.style.background='rgba(239, 68, 68, 1)'\"\n        onmouseout=\"this.style.background='rgba(239, 68, 68, 0.8)'\"\n        title=\"Annuler l'enregistrement\"\n      >\n        <i class=\"fas fa-times\" style=\"font-size: 16px\"></i>\n      </button>\n\n      <!-- Bouton Envoyer -->\n      <button\n        (click)=\"onRecordEnd($event)\"\n        style=\"\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          border: none;\n          background: rgba(34, 197, 94, 0.8);\n          color: white;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.2s;\n        \"\n        onmouseover=\"this.style.background='rgba(34, 197, 94, 1)'\"\n        onmouseout=\"this.style.background='rgba(34, 197, 94, 0.8)'\"\n        title=\"Envoyer le message vocal\"\n      >\n        <i class=\"fas fa-paper-plane\" style=\"font-size: 16px\"></i>\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Call Interface Component -->\n<app-call-interface\n  [isVisible]=\"isInCall\"\n  [activeCall]=\"activeCall\"\n  [callType]=\"callType\"\n  [otherParticipant]=\"otherParticipant\"\n  (callEnded)=\"endCall()\"\n  (callAccepted)=\"onCallAccepted($event)\"\n  (callRejected)=\"onCallRejected()\"\n></app-call-interface>\n"], "mappings": ";AAQA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,MAAM;AAInC,SAASC,QAAQ,QAA4B,kCAAkC;;;;;;;;;;;;;ICiHvEC,EAAA,CAAAC,SAAA,cAaO;;;;;IAoBLD,EAAA,CAAAE,cAAA,cAGC;IACOF,EAAA,CAAAG,MAAA,6BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,cAAqC;IACnCF,EAAA,CAAAC,SAAA,cAQO;IAmBTD,EAAA,CAAAI,YAAA,EAAM;;;;;IAGRJ,EAAA,CAAAE,cAAA,WAA4B;IAC1BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IALLJ,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAC,QAAA,iBAAAF,MAAA,CAAAG,gBAAA,CAAAH,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,UAAA,OAKF;;;;;;IAsFNX,EAAA,CAAAE,cAAA,cAaC;IAGKF,EAAA,CAAAY,UAAA,mBAAAC,6DAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAASD,OAAA,CAAAE,YAAA,EAAc;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAH,OAAA,CAAAI,YAAA,GAAiB,KAAK;IAAA,EAAC;IAiB9CpB,EAAA,CAAAC,SAAA,YAAoD;IACpDD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEhDJ,EAAA,CAAAE,cAAA,iBAgBC;IACCF,EAAA,CAAAC,SAAA,YAAkD;IAClDD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpDJ,EAAA,CAAAC,SAAA,cAAmD;IACnDD,EAAA,CAAAE,cAAA,kBAgBC;IACCF,EAAA,CAAAC,SAAA,aAAiD;IACjDD,EAAA,CAAAE,cAAA,gBAA6B;IAAAF,EAAA,CAAAG,MAAA,uBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAiBpDJ,EAAA,CAAAE,cAAA,cAkBC;IAWGF,EAAA,CAAAC,SAAA,YAQK;IACLD,EAAA,CAAAE,cAAA,YAOC;IACCF,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,YAA2C;IACzCF,EAAA,CAAAG,MAAA,0CACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAKRJ,EAAA,CAAAE,cAAA,cASC;IACCF,EAAA,CAAAC,SAAA,cAUO;IACPD,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAIhEJ,EAAA,CAAAE,cAAA,cASC;IAEGF,EAAA,CAAAC,SAAA,YAA+B;IACjCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAOC;IACCF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAA8C;IAC5CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,wCAAAe,MAAA,CAAAb,gBAAA,kBAAAa,MAAA,CAAAb,gBAAA,CAAAc,QAAA,MACF;;;;;IAgBEtB,EAAA,CAAAE,cAAA,cAGC;IAUKF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAiB,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAAC,SAAA,OACF;;;;;;IAeF1B,EAAA,CAAAE,cAAA,cAGC;IAcGF,EAAA,CAAAY,UAAA,mBAAAe,+EAAA;MAAA3B,EAAA,CAAAc,aAAA,CAAAc,IAAA;MAAA,MAAAH,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAW,OAAA,CAAAC,eAAA,CAAAN,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,CAAoC;IAAA,EAAC;IAbhDjC,EAAA,CAAAI,YAAA,EAgBE;;;;IAfAJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAG,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAEC,QAAAX,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA;;;;;IAoCHtB,EAAA,CAAAE,cAAA,cAaC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHJJ,EAAA,CAAAqC,WAAA,UAAAC,OAAA,CAAAC,YAAA,CAAAd,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,EAAiD;IAEjDjC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA,MACF;;;;;IAGAtB,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,cAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IADCJ,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAkC,UAAA,cAAAM,OAAA,CAAAC,oBAAA,CAAAhB,WAAA,CAAAiB,OAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAmD;;;;;IAsBxD3C,EAAA,CAAAC,SAAA,eAOO;;;;;IALLD,EAAA,CAAAqC,WAAA,WAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAW,OAAA,CAAAC,aAAA,yBAEC;IAED7C,EAAA,CAAAkC,UAAA,cAAAU,OAAA,CAAAH,oBAAA,CAAAhB,WAAA,CAAAiB,OAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAmD;;;;;;IAxBvD3C,EAAA,CAAAE,cAAA,cAAqD;IAIjDF,EAAA,CAAAY,UAAA,mBAAAkC,+EAAA;MAAA9C,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAmB,OAAA,GAAAhD,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA6B,OAAA,CAAAC,eAAA,CAAAxB,WAAA,CAAwB;IAAA,EAAC,kBAAAyB,8EAAAC,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAuB,OAAA,GAAApD,EAAA,CAAAiB,aAAA;MAAA,OAC1BjB,EAAA,CAAAmB,WAAA,CAAAiC,OAAA,CAAAC,WAAA,CAAAF,MAAA,EAAA1B,WAAA,CAA4B;IAAA,EADF,mBAAA6B,+EAAAH,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAtB,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAA0B,OAAA,GAAAvD,EAAA,CAAAiB,aAAA;MAAA,OAEzBjB,EAAA,CAAAmB,WAAA,CAAAoC,OAAA,CAAAC,YAAA,CAAAL,MAAA,EAAA1B,WAAA,CAA6B;IAAA,EAFJ;IAHpCzB,EAAA,CAAAI,YAAA,EAeE;IAEFJ,EAAA,CAAAyD,UAAA,IAAAC,+DAAA,mBAOO;IACT1D,EAAA,CAAAI,YAAA,EAAM;;;;;IAxBFJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAkC,UAAA,QAAAyB,OAAA,CAAAC,WAAA,CAAAnC,WAAA,GAAAzB,EAAA,CAAAoC,aAAA,CAA4B,QAAAX,WAAA,CAAAiB,OAAA;IAiB3B1C,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAAiB,OAAA,CAAqB;;;;;IAgEtB1C,EAAA,CAAAC,SAAA,eAcO;;;;;;;IALLD,EAAA,CAAAqC,WAAA,WAAAwB,OAAA,CAAAC,cAAA,CAAArC,WAAA,CAAAQ,EAAA,IAAA8B,QAAA,WAAyD,cAAAF,OAAA,CAAAC,cAAA,CAAArC,WAAA,CAAAQ,EAAA,qDAAA+B,KAAA;;;;;;IA8B3DhE,EAAA,CAAAE,cAAA,kBAgBC;IAdCF,EAAA,CAAAY,UAAA,mBAAAqD,2FAAA;MAAAjE,EAAA,CAAAc,aAAA,CAAAoD,IAAA;MAAA,MAAAzC,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,IAAAY,SAAA;MAAA,MAAAsC,OAAA,GAAAnE,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAgD,OAAA,CAAAC,gBAAA,CAAA3C,WAAA,CAAyB;IAAA,EAAC;IAenCzB,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IADPJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+D,OAAA,CAAAC,aAAA,CAAA7C,WAAA,QACF;;;;;;IA/GJzB,EAAA,CAAAE,cAAA,eAaC;IAGGF,EAAA,CAAAY,UAAA,mBAAA2D,kFAAA;MAAAvE,EAAA,CAAAc,aAAA,CAAA0D,IAAA;MAAA,MAAA/C,WAAA,GAAAzB,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAA4C,OAAA,GAAAzE,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAsD,OAAA,CAAAC,mBAAA,CAAAjD,WAAA,CAA4B;IAAA,EAAC;IAmBtCzB,EAAA,CAAAC,SAAA,aAKK;IACPD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,eASC;IACCF,EAAA,CAAAyD,UAAA,IAAAkB,+DAAA,mBAcO;IACT3E,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAOC;IAUGF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAyD,UAAA,IAAAmB,kEAAA,sBAkBS;IACX5E,EAAA,CAAAI,YAAA,EAAM;;;;;IA5EFJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAA6E,UAAA,CAAAC,OAAA,CAAAhB,cAAA,CAAArC,WAAA,CAAAQ,EAAA,mCAEC;IAiBgBjC,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAkC,UAAA,YAAA4C,OAAA,CAAAC,UAAA,CAAe;IAkChC/E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAwE,OAAA,CAAAE,gBAAA,CAAAvD,WAAA,OACF;IAIGzB,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAkC,UAAA,SAAA4C,OAAA,CAAAhB,cAAA,CAAArC,WAAA,CAAAQ,EAAA,EAAgC;;;;;IAsCnCjC,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAKK;;;;;IAxBPD,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAyD,UAAA,IAAAwB,8DAAA,iBAIK;IACLjF,EAAA,CAAAyD,UAAA,IAAAyB,8DAAA,iBAIK;IACLlF,EAAA,CAAAyD,UAAA,IAAA0B,8DAAA,iBAIK;IACLnF,EAAA,CAAAyD,UAAA,IAAA2B,8DAAA,iBAKK;IACPpF,EAAA,CAAAI,YAAA,EAAM;;;;IAnBDJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA4D,MAAA,eAAkC;IAKlCrF,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA4D,MAAA,YAA+B;IAK/BrF,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA4D,MAAA,iBAAoC;IAMpCrF,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAkC,UAAA,SAAAT,WAAA,CAAA4D,MAAA,YAA+B;;;;;;IA7R5CrF,EAAA,CAAAsF,uBAAA,GAMC;IAECtF,EAAA,CAAAyD,UAAA,IAAA8B,yDAAA,kBAgBM;IAGNvF,EAAA,CAAAE,cAAA,cAQC;IAFCF,EAAA,CAAAY,UAAA,mBAAA4E,yEAAArC,MAAA;MAAA,MAAAsC,WAAA,GAAAzF,EAAA,CAAAc,aAAA,CAAA4E,IAAA;MAAA,MAAAjE,WAAA,GAAAgE,WAAA,CAAA5D,SAAA;MAAA,MAAA8D,OAAA,GAAA3F,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAwE,OAAA,CAAAC,cAAA,CAAAnE,WAAA,EAAA0B,MAAA,CAA+B;IAAA,EAAC,yBAAA0C,+EAAA1C,MAAA;MAAA,MAAAsC,WAAA,GAAAzF,EAAA,CAAAc,aAAA,CAAA4E,IAAA;MAAA,MAAAjE,WAAA,GAAAgE,WAAA,CAAA5D,SAAA;MAAA,MAAAiE,OAAA,GAAA9F,EAAA,CAAAiB,aAAA;MAAA,OAC1BjB,EAAA,CAAAmB,WAAA,CAAA2E,OAAA,CAAAC,oBAAA,CAAAtE,WAAA,EAAA0B,MAAA,CAAqC;IAAA,EADX;IAIzCnD,EAAA,CAAAyD,UAAA,IAAAuC,yDAAA,kBAqBM;IAGNhG,EAAA,CAAAE,cAAA,cAiBC;IAECF,EAAA,CAAAyD,UAAA,IAAAwC,yDAAA,kBAeM;IAGNjG,EAAA,CAAAyD,UAAA,IAAAyC,yDAAA,kBAKM;IAGNlG,EAAA,CAAAyD,UAAA,IAAA0C,yDAAA,kBA0BM;IAGNnG,EAAA,CAAAyD,UAAA,IAAA2C,yDAAA,kBAiHM;IAGNpG,EAAA,CAAAE,cAAA,cAUC;IACOF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAyD,UAAA,KAAA4C,0DAAA,kBAyBM;IACRrG,EAAA,CAAAI,YAAA,EAAM;IAGZJ,EAAA,CAAAsG,qBAAA,EAAe;;;;;;IA3RVtG,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAoBjCzG,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAqC,WAAA,qBAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,6BAEC;IACD7C,EAAA,CAAAkC,UAAA,oBAAAT,WAAA,CAAAQ,EAAA,CAA8B;IAM3BjC,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAkC,UAAA,UAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,IAAA0D,OAAA,CAAAG,gBAAA,CAAAD,KAAA,EAAiE;IAwBlEzG,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAqC,WAAA,sBAAAZ,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,yBAEC,WAAApB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA;IAiBE7C,EAAA,CAAAK,SAAA,GAKf;IALeL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAI,mBAAA,OAAAlF,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,IAAA0D,OAAA,CAAAK,oBAAA,CAAAH,KAAA,EAKf;IAaezG,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAM,cAAA,CAAApF,WAAA,aAAwC;IAOrCzB,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAO,QAAA,CAAArF,WAAA,EAAuB;IA8B1BzB,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAkC,UAAA,SAAAqE,OAAA,CAAAM,cAAA,CAAApF,WAAA,cAAyC;IA8HpCzB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA+G,iBAAA,CAAAR,OAAA,CAAAS,iBAAA,CAAAvF,WAAA,CAAAC,SAAA,EAA0C;IAE7C1B,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAkC,UAAA,UAAAT,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAC,EAAA,MAAAsE,OAAA,CAAA1D,aAAA,CAA0C;;;;;IA+BrD7C,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAC,SAAA,eASE;IACFD,EAAA,CAAAE,cAAA,eAOC;IAEGF,EAAA,CAAAC,SAAA,eAQO;IAmBTD,EAAA,CAAAI,YAAA,EAAM;;;;IA7CNJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAkC,UAAA,SAAA+E,OAAA,CAAAzG,gBAAA,kBAAAyG,OAAA,CAAAzG,gBAAA,CAAA2B,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAAqE,QAAA6E,OAAA,CAAAzG,gBAAA,kBAAAyG,OAAA,CAAAzG,gBAAA,CAAAc,QAAA;;;;;IAhT3EtB,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAyD,UAAA,IAAAyD,mDAAA,6BAoSe;IAGflH,EAAA,CAAAyD,UAAA,IAAA0D,0CAAA,kBAoDM;IACRnH,EAAA,CAAAI,YAAA,EAAM;;;;IA1VuBJ,EAAA,CAAAK,SAAA,GACZ;IADYL,EAAA,CAAAkC,UAAA,YAAAkF,MAAA,CAAAC,QAAA,CACZ,iBAAAD,MAAA,CAAAE,gBAAA;IAqSZtH,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAAkF,MAAA,CAAAG,iBAAA,CAAuB;;;;;IA8ItBvH,EAAA,CAAAC,SAAA,eAYO;;;;;IA4DTD,EAAA,CAAAC,SAAA,aAA4D;;;;;IAC5DD,EAAA,CAAAC,SAAA,eAUO;;;;;;IAmCLD,EAAA,CAAAE,cAAA,kBAeC;IAbCF,EAAA,CAAAY,UAAA,mBAAA4G,sEAAA;MAAA,MAAA/B,WAAA,GAAAzF,EAAA,CAAAc,aAAA,CAAA2G,IAAA;MAAA,MAAAC,SAAA,GAAAjC,WAAA,CAAA5D,SAAA;MAAA,MAAA8F,OAAA,GAAA3H,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAwG,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAc5B1H,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAHPJ,EAAA,CAAAkC,UAAA,UAAAwF,SAAA,CAAAG,IAAA,CAAoB;IAEpB7H,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoH,SAAA,CAAAI,KAAA,MACF;;;;;IA/CN9H,EAAA,CAAAE,cAAA,eAeC;IAUKF,EAAA,CAAAG,MAAA,oBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAyD,UAAA,IAAAsE,6CAAA,sBAiBS;IACX/H,EAAA,CAAAI,YAAA,EAAM;;;;IAjBgBJ,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAkC,UAAA,YAAA8F,OAAA,CAAAC,oBAAA,CAAAD,OAAA,CAAAE,qBAAA,EAA8C;;;;;;IAsBxElI,EAAA,CAAAE,cAAA,eAaC;IAUKF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,eAMC;IAGGF,EAAA,CAAAY,UAAA,mBAAAuH,6DAAA;MAAAnI,EAAA,CAAAc,aAAA,CAAAsH,IAAA;MAAA,MAAAC,OAAA,GAAArI,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAkH,OAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAgBnCtI,EAAA,CAAAE,cAAA,eAUC;IACCF,EAAA,CAAAC,SAAA,aAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBACG;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAIHJ,EAAA,CAAAE,cAAA,mBAgBC;IAfCF,EAAA,CAAAY,UAAA,mBAAA2H,8DAAA;MAAAvI,EAAA,CAAAc,aAAA,CAAAsH,IAAA;MAAA,MAAAI,OAAA,GAAAxI,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAqH,OAAA,CAAAF,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAgBtCtI,EAAA,CAAAE,cAAA,gBAUC;IACCF,EAAA,CAAAC,SAAA,cAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EACX;IAIHJ,EAAA,CAAAE,cAAA,mBAgBC;IAfCF,EAAA,CAAAY,UAAA,mBAAA6H,8DAAA;MAAAzI,EAAA,CAAAc,aAAA,CAAAsH,IAAA;MAAA,MAAAM,OAAA,GAAA1I,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAuH,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAgBtB3I,EAAA,CAAAE,cAAA,gBAUC;IACCF,EAAA,CAAAC,SAAA,cAGK;IACPD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;;;;;;IAkBXJ,EAAA,CAAAE,cAAA,eAYC;IADCF,EAAA,CAAAY,UAAA,mBAAAgI,0DAAA;MAAA5I,EAAA,CAAAc,aAAA,CAAA+H,IAAA;MAAA,MAAAC,OAAA,GAAA9I,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAA2H,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1B/I,EAAA,CAAAI,YAAA,EAAM;;;;;IAgDDJ,EAAA,CAAAC,SAAA,eAWO;;;;;IAHLD,EAAA,CAAAqC,WAAA,WAAA2G,QAAA,OAAwB,uDAAAC,KAAA;;;;;;IArDhCjJ,EAAA,CAAAE,cAAA,eAmBC;IAcGF,EAAA,CAAAC,SAAA,aAAyD;IAC3DD,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAAqB;IAGjBF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAAwE;IACtEF,EAAA,CAAAyD,UAAA,IAAAyF,0CAAA,mBAWO;IACTlJ,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAA4D;IAC1DF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAE,cAAA,eAAqC;IAGjCF,EAAA,CAAAY,UAAA,mBAAAuI,8DAAAhG,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsI,IAAA;MAAA,MAAAC,OAAA,GAAArJ,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAkI,OAAA,CAAAC,cAAA,CAAAnG,MAAA,CAAsB;IAAA,EAAC;IAkBhCnD,EAAA,CAAAC,SAAA,cAAoD;IACtDD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,mBAkBC;IAjBCF,EAAA,CAAAY,UAAA,mBAAA2I,8DAAApG,MAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsI,IAAA;MAAA,MAAAI,OAAA,GAAAxJ,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAmB,WAAA,CAAAqI,OAAA,CAAAC,WAAA,CAAAtG,MAAA,CAAmB;IAAA,EAAC;IAkB7BnD,EAAA,CAAAC,SAAA,cAA0D;IAC5DD,EAAA,CAAAI,YAAA,EAAS;;;;IAvEPJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoJ,OAAA,CAAAC,uBAAA,CAAAD,OAAA,CAAAE,sBAAA,OACF;IAKqB5J,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAkC,UAAA,YAAAwH,OAAA,CAAA3E,UAAA,CAAe;IAelC/E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,cAAAoJ,OAAA,CAAAG,kBAAA,QACF;;;AD7wCN,OAAM,MAAOC,oBAAoB;EA2I/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,YAA0B,EAC1BC,GAAsB;IANtB,KAAAN,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IA5Ib;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAlD,QAAQ,GAAU,EAAE;IACpB,KAAAxE,aAAa,GAAkB,IAAI;IACnC,KAAA2H,eAAe,GAAG,KAAK;IAEvB,KAAAhK,gBAAgB,GAAQ,IAAI;IAE5B;IACA,KAAAiK,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAA3D,iBAAiB,GAAG,KAAK;IACzB,KAAAnG,YAAY,GAAG,KAAK;IACpB,KAAA+J,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,qBAAqB,GAAQ,IAAI;IAEjC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAnC,sBAAsB,GAAG,CAAC;IAC1B,KAAAoC,mBAAmB,GAAwC,MAAM;IACzD,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAClC,KAAApH,UAAU,GAAa,CACrB,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CACzD;IAED;IACQ,KAAAqH,YAAY,GAA4B,IAAI;IAC5C,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,aAAa,GAOjB,EAAE;IAEN;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAElD;IACA,KAAAC,eAAe,GAAU,CACvB;MACEhL,EAAE,EAAE,SAAS;MACb4F,IAAI,EAAE,SAAS;MACfqF,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAErF,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAe,CAAE,EACtC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACE5F,EAAE,EAAE,QAAQ;MACZ4F,IAAI,EAAE,QAAQ;MACdqF,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAErF,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACE5F,EAAE,EAAE,QAAQ;MACZ4F,IAAI,EAAE,QAAQ;MACdqF,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAErF,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAY,CAAE,EACnC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAa,CAAE,EACpC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAAK,qBAAqB,GAAG,IAAI,CAAC+E,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAAG,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,KAAK;IACZ,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAI3N,YAAY,EAAE;IAWxC,IAAI,CAAC4N,WAAW,GAAG,IAAI,CAAC1D,EAAE,CAAC2D,KAAK,CAAC;MAC/BjL,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7C,UAAU,CAAC+N,QAAQ,EAAE/N,UAAU,CAACgO,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEA;EACAC,eAAeA,CAAA;IACb,OACE,CAAC,IAAI,CAACtN,gBAAgB,IAAI,IAAI,CAACuL,gBAAgB,IAAI,IAAI,CAACb,gBAAgB;EAE5E;EAEA;EACQ6C,gBAAgBA,CAAA;IACtB,MAAMC,cAAc,GAAG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC;IACtD,IAAI,IAAI,CAACH,eAAe,EAAE,EAAE;MAC1BE,cAAc,EAAEE,OAAO,EAAE;KAC1B,MAAM;MACLF,cAAc,EAAEG,MAAM,EAAE;;EAE5B;EAEAC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAACjB,aAAa,CAACkB,GAAG,CACpB,IAAI,CAACvE,WAAW,CAACwE,aAAa,CAACC,SAAS,CAAC;MACvCC,IAAI,EAAGC,YAAY,IAAI;QACrB,IAAIA,YAAY,EAAE;UAChBV,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAES,YAAY,CAAC;UACvD,IAAI,CAACC,kBAAkB,CAACD,YAAY,CAAC;;MAEzC,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACxB,aAAa,CAACkB,GAAG,CACpB,IAAI,CAACvE,WAAW,CAAC8E,WAAW,CAACL,SAAS,CAAC;MACrCC,IAAI,EAAGK,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACRd,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEa,IAAI,CAAC;UAC5C,IAAI,CAACxC,UAAU,GAAGwC,IAAI;;MAE1B,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC9D;KACD,CAAC,CACH;EACH;EAEQD,kBAAkBA,CAACD,YAA0B;IACnD;IACA;IACAV,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCS,YAAY,CAACK,MAAM,CAAC9N,QAAQ,CAC7B;IAED;IACA,IAAI,CAAC6I,cAAc,CAACkF,IAAI,CAAC,UAAU,CAAC;IAEpC;IACA;IACA;EACF;;EAEQb,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMc,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/CnB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEgB,UAAU,CAAC;MAEzD,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtEjB,OAAO,CAACY,KAAK,CAAC,gCAAgC,CAAC;QAC/C,IAAI,CAACpM,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC2H,eAAe,GAAG,KAAK;QAC5B;;MAGF,MAAMiF,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;MACnCjB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmB,IAAI,CAAC;MAE3C;MACA,MAAMG,MAAM,GAAGH,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAACxN,EAAE,IAAIwN,IAAI,CAACG,MAAM;MACjDvB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CuB,GAAG,EAAEJ,IAAI,CAACI,GAAG;QACb5N,EAAE,EAAEwN,IAAI,CAACxN,EAAE;QACX2N,MAAM,EAAEH,IAAI,CAACG,MAAM;QACnBE,SAAS,EAAEF;OACZ,CAAC;MAEF,IAAIA,MAAM,EAAE;QACV,IAAI,CAAC/M,aAAa,GAAG+M,MAAM;QAC3B,IAAI,CAACpF,eAAe,GAAGiF,IAAI,CAACnO,QAAQ,IAAImO,IAAI,CAAC5H,IAAI,IAAI,KAAK;QAC1DwG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UACjDrM,EAAE,EAAE,IAAI,CAACY,aAAa;UACtBvB,QAAQ,EAAE,IAAI,CAACkJ;SAChB,CAAC;OACH,MAAM;QACL6D,OAAO,CAACY,KAAK,CAAC,0CAA0C,EAAEQ,IAAI,CAAC;QAC/D,IAAI,CAAC5M,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC2H,eAAe,GAAG,KAAK;;KAE/B,CAAC,OAAOyE,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAI,CAACpM,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC2H,eAAe,GAAG,KAAK;;EAEhC;EAEQiE,gBAAgBA,CAAA;IACtB,MAAMsB,cAAc,GAAG,IAAI,CAAC9F,KAAK,CAAC+F,QAAQ,CAACC,QAAQ,CAAChC,GAAG,CAAC,IAAI,CAAC;IAC7DI,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEyB,cAAc,CAAC;IAE5D,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAAC1F,YAAY,CAAC6F,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAACzF,SAAS,GAAG,IAAI;IACrB,IAAI,CAACN,cAAc,CAACgG,eAAe,CAACJ,cAAc,CAAC,CAAClB,SAAS,CAAC;MAC5DC,IAAI,EAAGvE,YAAY,IAAI;QACrB8D,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE/D,YAAY,CAAC;QACjE8D,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;UACxCrM,EAAE,EAAEsI,YAAY,EAAEtI,EAAE;UACpBmO,YAAY,EAAE7F,YAAY,EAAE6F,YAAY;UACxCC,iBAAiB,EAAE9F,YAAY,EAAE6F,YAAY,EAAEE,MAAM;UACrDC,OAAO,EAAEhG,YAAY,EAAEgG,OAAO;UAC9BlJ,QAAQ,EAAEkD,YAAY,EAAElD,QAAQ;UAChCmJ,aAAa,EAAEjG,YAAY,EAAElD,QAAQ,EAAEiJ;SACxC,CAAC;QACF,IAAI,CAAC/F,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACkG,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;QAEnB;QACA,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC;MACD1B,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAAC5E,YAAY,CAAC6F,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAACzF,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQgG,mBAAmBA,CAAA;IACzB,IACE,CAAC,IAAI,CAAClG,YAAY,EAAE6F,YAAY,IAChC,IAAI,CAAC7F,YAAY,CAAC6F,YAAY,CAACE,MAAM,KAAK,CAAC,EAC3C;MACAjC,OAAO,CAACuC,IAAI,CAAC,uCAAuC,CAAC;MACrD,IAAI,CAACpQ,gBAAgB,GAAG,IAAI;MAC5B;;IAGF6N,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACzL,aAAa,CAAC;IACnDwL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC/D,YAAY,CAAC6F,YAAY,CAAC;IAEhE;IACA;IAEA,IAAI,IAAI,CAAC7F,YAAY,CAACgG,OAAO,EAAE;MAC7B;MACA;MACA,IAAI,CAAC/P,gBAAgB,GAAG,IAAI,CAAC+J,YAAY,CAAC6F,YAAY,CAACS,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAC7O,EAAE,IAAI6O,CAAC,CAACjB,GAAG;QACnC,OAAOmB,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAACnO,aAAa,CAAC;MAC7D,CAAC,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACrC,gBAAgB,GAAG,IAAI,CAAC+J,YAAY,CAAC6F,YAAY,CAACS,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAC7O,EAAE,IAAI6O,CAAC,CAACjB,GAAG;QACnCxB,OAAO,CAACC,GAAG,CACT,2BAA2B,EAC3ByC,aAAa,EACb,uBAAuB,EACvB,IAAI,CAAClO,aAAa,CACnB;QACD,OAAOmO,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAACnO,aAAa,CAAC;MAC7D,CAAC,CAAC;;IAGJ;IACA,IAAI,CAAC,IAAI,CAACrC,gBAAgB,IAAI,IAAI,CAAC+J,YAAY,CAAC6F,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;MACvEjC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,IAAI,CAAC9N,gBAAgB,GAAG,IAAI,CAAC+J,YAAY,CAAC6F,YAAY,CAAC,CAAC,CAAC;MAEzD;MACA,IAAI,IAAI,CAAC7F,YAAY,CAAC6F,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMW,kBAAkB,GACtB,IAAI,CAACzQ,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAACqP,GAAG;QACvD,IAAImB,MAAM,CAACC,kBAAkB,CAAC,KAAKD,MAAM,CAAC,IAAI,CAACnO,aAAa,CAAC,EAAE;UAC7DwL,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACD,IAAI,CAAC9N,gBAAgB,GAAG,IAAI,CAAC+J,YAAY,CAAC6F,YAAY,CAAC,CAAC,CAAC;;;;IAK/D;IACA,IAAI,IAAI,CAAC5P,gBAAgB,EAAE;MACzB6N,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QACnDrM,EAAE,EAAE,IAAI,CAACzB,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAACqP,GAAG;QACzDvO,QAAQ,EAAE,IAAI,CAACd,gBAAgB,CAACc,QAAQ;QACxCa,KAAK,EAAE,IAAI,CAAC3B,gBAAgB,CAAC2B,KAAK;QAClC1B,QAAQ,EAAE,IAAI,CAACD,gBAAgB,CAACC;OACjC,CAAC;MAEF;MACA4N,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC9N,gBAAgB,CAACc,QAAQ,CAC/B;MACD+M,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/B,IAAI,CAAC9N,gBAAgB,CAACc,QAAQ,CAC/B;KACF,MAAM;MACL+M,OAAO,CAACY,KAAK,CAAC,uDAAuD,CAAC;MACtEZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC/D,YAAY,CAAC6F,YAAY,CAAC;MACzE/B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACzL,aAAa,CAAC;MAEnD;MACAwL,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;IAGrD;IACA,IAAI,CAACP,gBAAgB,EAAE;EACzB;EAEQ2C,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACnG,YAAY,EAAEtI,EAAE,EAAE;IAE5B;IACA,IAAIoF,QAAQ,GAAG,IAAI,CAACkD,YAAY,CAAClD,QAAQ,IAAI,EAAE;IAE/C;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAC6J,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;MAC/C,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACzP,SAAS,IAAIyP,CAAC,CAACI,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,MAAMC,KAAK,GAAG,IAAIH,IAAI,CAACF,CAAC,CAAC1P,SAAS,IAAI0P,CAAC,CAACG,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,OAAOH,KAAK,GAAGI,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;;IAEFpD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CoD,KAAK,EAAE,IAAI,CAACrK,QAAQ,CAACiJ,MAAM;MAC3BqB,KAAK,EAAE,IAAI,CAACtK,QAAQ,CAAC,CAAC,CAAC,EAAE3E,OAAO;MAChCkP,IAAI,EAAE,IAAI,CAACvK,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACiJ,MAAM,GAAG,CAAC,CAAC,EAAE5N;KAChD,CAAC;IAEF,IAAI,CAACiI,eAAe,GAAG,IAAI,CAACtD,QAAQ,CAACiJ,MAAM,KAAK,IAAI,CAAClD,oBAAoB;IACzE,IAAI,CAAC3C,SAAS,GAAG,KAAK;IACtB,IAAI,CAACoH,cAAc,EAAE;EACvB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACpH,aAAa,IAAI,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAEtI,EAAE,EACvE;IAEF,IAAI,CAACyI,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC2C,WAAW,EAAE;IAElB;IACA,MAAM0E,MAAM,GAAG,IAAI,CAAC1K,QAAQ,CAACiJ,MAAM;IAEnC,IAAI,CAACnG,cAAc,CAAC6H,WAAW,CAC7B,IAAI,CAACnP,aAAc;IAAE;IACrB,IAAI,CAACrC,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAEqP,GAAI;IAAE;IAC1D,IAAI,CAACtF,YAAY,CAACtI,EAAE,EACpB,IAAI,CAACoL,WAAW,EAChB,IAAI,CAACD,oBAAoB,CAC1B,CAACyB,SAAS,CAAC;MACVC,IAAI,EAAGmD,WAAkB,IAAI;QAC3B,IAAIA,WAAW,IAAIA,WAAW,CAAC3B,MAAM,GAAG,CAAC,EAAE;UACzC;UACA,IAAI,CAACjJ,QAAQ,GAAG,CAAC,GAAG4K,WAAW,CAACC,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC7K,QAAQ,CAAC;UAC5D,IAAI,CAACsD,eAAe,GAClBsH,WAAW,CAAC3B,MAAM,KAAK,IAAI,CAAClD,oBAAoB;SACnD,MAAM;UACL,IAAI,CAACzC,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACDuE,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAAC5E,YAAY,CAAC6F,SAAS,CAAC,wCAAwC,CAAC;QACrE,IAAI,CAACxF,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC2C,WAAW,EAAE,CAAC,CAAC;MACtB;KACD,CAAC;EACJ;;EAEQsD,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACpG,YAAY,EAAEtI,EAAE,EAAE;MAC1BoM,OAAO,CAACuC,IAAI,CAAC,kDAAkD,CAAC;MAChE;;IAGFvC,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzD,IAAI,CAAC/D,YAAY,CAACtI,EAAE,CACrB;IAED;IACAoM,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpD,IAAI,CAACb,aAAa,CAACkB,GAAG,CACpB,IAAI,CAACxE,cAAc,CAACgI,sBAAsB,CACxC,IAAI,CAAC5H,YAAY,CAACtI,EAAE,CACrB,CAAC4M,SAAS,CAAC;MACVC,IAAI,EAAGsD,UAAe,IAAI;QACxB/D,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE8D,UAAU,CAAC;QACpE/D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;UACnCrM,EAAE,EAAEmQ,UAAU,CAACnQ,EAAE;UACjBoQ,IAAI,EAAED,UAAU,CAACC,IAAI;UACrB3P,OAAO,EAAE0P,UAAU,CAAC1P,OAAO;UAC3BV,MAAM,EAAEoQ,UAAU,CAACpQ,MAAM;UACzBsQ,QAAQ,EAAEF,UAAU,CAACE,QAAQ;UAC7BC,UAAU,EAAEH,UAAU,CAACG,UAAU;UACjCC,WAAW,EAAEJ,UAAU,CAACI;SACzB,CAAC;QAEF;QACAnE,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnC,IAAI,CAACzH,cAAc,CAACuL,UAAU,CAAC,CAChC;QACD/D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACxH,QAAQ,CAACsL,UAAU,CAAC,CAAC;QAC/D/D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACmE,OAAO,CAACL,UAAU,CAAC,CAAC;QAC7D/D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC1K,WAAW,CAACwO,UAAU,CAAC,CAAC;QAClE,IAAIA,UAAU,CAACI,WAAW,EAAE;UAC1BJ,UAAU,CAACI,WAAW,CAACE,OAAO,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAI;YACzDvE,OAAO,CAACC,GAAG,CAAC,yBAAyBsE,KAAK,GAAG,EAAE;cAC7CP,IAAI,EAAEM,GAAG,CAACN,IAAI;cACdQ,GAAG,EAAEF,GAAG,CAACE,GAAG;cACZC,IAAI,EAAEH,GAAG,CAACG,IAAI;cACdjL,IAAI,EAAE8K,GAAG,CAAC9K,IAAI;cACdkL,IAAI,EAAEJ,GAAG,CAACI;aACX,CAAC;UACJ,CAAC,CAAC;;QAGJ;QACA,MAAMC,aAAa,GAAG,IAAI,CAAC3L,QAAQ,CAAC4L,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAACjR,EAAE,KAAKmQ,UAAU,CAACnQ,EAAE,CAClC;QACD,IAAI,CAAC+Q,aAAa,EAAE;UAClB;UACA,IAAI,CAAC3L,QAAQ,CAAC8L,IAAI,CAACf,UAAU,CAAC;UAC9B/D,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAACjH,QAAQ,CAACiJ,MAAM,CACrB;UAED;UACA,IAAI,CAAChG,GAAG,CAAC8I,aAAa,EAAE;UAExB;UACAC,UAAU,CAAC,MAAK;YACd,IAAI,CAACxB,cAAc,EAAE;UACvB,CAAC,EAAE,EAAE,CAAC;UAEN;UACA,MAAMS,QAAQ,GAAGF,UAAU,CAACpQ,MAAM,EAAEC,EAAE,IAAImQ,UAAU,CAACE,QAAQ;UAC7DjE,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;YAC9DgE,QAAQ;YACRzP,aAAa,EAAE,IAAI,CAACA,aAAa;YACjCyQ,gBAAgB,EAAEhB,QAAQ,KAAK,IAAI,CAACzP;WACrC,CAAC;UAEF,IAAIyP,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAACzP,aAAa,EAAE;YAC/C,IAAI,CAAC0Q,iBAAiB,CAACnB,UAAU,CAACnQ,EAAE,CAAC;;;MAG3C,CAAC;MACDgN,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC,CACH;IAED;IACAZ,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,IAAI,CAACb,aAAa,CAACkB,GAAG,CACpB,IAAI,CAACxE,cAAc,CAACqJ,0BAA0B,CAC5C,IAAI,CAACjJ,YAAY,CAACtI,EAAE,CACrB,CAAC4M,SAAS,CAAC;MACVC,IAAI,EAAG2E,UAAe,IAAI;QACxBpF,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEmF,UAAU,CAAC;QAExD;QACA,IAAIA,UAAU,CAAC7D,MAAM,KAAK,IAAI,CAAC/M,aAAa,EAAE;UAC5C,IAAI,CAAC0E,iBAAiB,GAAGkM,UAAU,CAACnG,QAAQ;UAC5C,IAAI,CAAChD,GAAG,CAAC8I,aAAa,EAAE;;MAE5B,CAAC;MACDnE,KAAK,EAAGA,KAAU,IAAI;QACpBZ,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACxB,aAAa,CAACkB,GAAG,CACpB,IAAI,CAACxE,cAAc,CAACuJ,8BAA8B,CAChD,IAAI,CAACnJ,YAAY,CAACtI,EAAE,CACrB,CAAC4M,SAAS,CAAC;MACVC,IAAI,EAAG6E,kBAAuB,IAAI;QAChCtF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqF,kBAAkB,CAAC;QAE1D;QACA,IAAIA,kBAAkB,CAAC1R,EAAE,KAAK,IAAI,CAACsI,YAAY,CAACtI,EAAE,EAAE;UAClD,IAAI,CAACsI,YAAY,GAAG;YAAE,GAAG,IAAI,CAACA,YAAY;YAAE,GAAGoJ;UAAkB,CAAE;UACnE,IAAI,CAACrJ,GAAG,CAAC8I,aAAa,EAAE;;MAE5B,CAAC;MACDnE,KAAK,EAAGA,KAAU,IAAI;QACpBZ,OAAO,CAACY,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D;KACD,CAAC,CACH;EACH;EAEQsE,iBAAiBA,CAACK,SAAiB;IACzC,IAAI,CAACzJ,cAAc,CAACoJ,iBAAiB,CAACK,SAAS,CAAC,CAAC/E,SAAS,CAAC;MACzDC,IAAI,EAAEA,CAAA,KAAK;QACTT,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEsF,SAAS,CAAC;MACrD,CAAC;MACD3E,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEA;EACA4E,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACnG,WAAW,CAACoG,KAAK,IAAI,CAAC,IAAI,CAACvJ,YAAY,EAAEtI,EAAE,EAAE;IAEvD,MAAMS,OAAO,GAAG,IAAI,CAACgL,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAE8F,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAACtR,OAAO,EAAE;IAEd,MAAM6P,UAAU,GAAG,IAAI,CAAC/R,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAEqP,GAAG;IAE1E,IAAI,CAAC0C,UAAU,EAAE;MACf,IAAI,CAAClI,YAAY,CAAC6F,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF;IACA,IAAI,CAAChF,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC6C,gBAAgB,EAAE;IAEvBM,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjC5L,OAAO;MACP6P,UAAU;MACVxC,cAAc,EAAE,IAAI,CAACxF,YAAY,CAACtI;KACnC,CAAC;IAEF,IAAI,CAACkI,cAAc,CAAC0J,WAAW,CAC7BtB,UAAU,EACV7P,OAAO,EACPuR,SAAS,EACT,MAAa,EACb,IAAI,CAAC1J,YAAY,CAACtI,EAAE,CACrB,CAAC4M,SAAS,CAAC;MACVC,IAAI,EAAGoF,OAAY,IAAI;QACrB7F,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE4F,OAAO,CAAC;QAEpD;QACA,MAAMlB,aAAa,GAAG,IAAI,CAAC3L,QAAQ,CAAC4L,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAACjR,EAAE,KAAKiS,OAAO,CAACjS,EAAE,CAC/B;QACD,IAAI,CAAC+Q,aAAa,EAAE;UAClB,IAAI,CAAC3L,QAAQ,CAAC8L,IAAI,CAACe,OAAO,CAAC;UAC3B7F,OAAO,CAACC,GAAG,CACT,wCAAwC,EACxC,IAAI,CAACjH,QAAQ,CAACiJ,MAAM,CACrB;;QAGH;QACA,IAAI,CAAC5C,WAAW,CAACyG,KAAK,EAAE;QACxB,IAAI,CAACjJ,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC6C,gBAAgB,EAAE;QAEvB;QACA,IAAI,CAACzD,GAAG,CAAC8I,aAAa,EAAE;QACxBC,UAAU,CAAC,MAAK;UACd,IAAI,CAACxB,cAAc,EAAE;QACvB,CAAC,EAAE,EAAE,CAAC;MACR,CAAC;MACD5C,KAAK,EAAGA,KAAU,IAAI;QACpBZ,OAAO,CAACY,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D,IAAI,CAAC5E,YAAY,CAAC6F,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAAChF,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC6C,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEA8D,cAAcA,CAAA;IACZwB,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACe,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA9T,gBAAgBA,CAACC,UAAgC;IAC/C,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IAEpC,MAAM8T,QAAQ,GAAGC,IAAI,CAACC,KAAK,CACzB,CAACrD,IAAI,CAACsD,GAAG,EAAE,GAAG,IAAItD,IAAI,CAAC3Q,UAAU,CAAC,CAAC6Q,OAAO,EAAE,IAAI,KAAK,CACtD;IAED,IAAIiD,QAAQ,GAAG,CAAC,EAAE,OAAO,aAAa;IACtC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,UAAUA,QAAQ,MAAM;IAClD,IAAIA,QAAQ,GAAG,IAAI,EAAE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,GAAG;IAClE,OAAO,UAAUC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,IAAI,CAAC,GAAG;EACjD;EAEA;EACAI,oBAAoBA,CAACjB,SAAiB;IACpC,OACE,IAAI,CAACtH,aAAa,CAACsH,SAAS,CAAC,IAAI;MAC/BkB,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC;MACdC,KAAK,EAAE;KACR;EAEL;EAEQC,oBAAoBA,CAC1BtB,SAAiB,EACjBuB,IAAkD;IAElD,IAAI,CAAC7I,aAAa,CAACsH,SAAS,CAAC,GAAG;MAC9B,GAAG,IAAI,CAACiB,oBAAoB,CAACjB,SAAS,CAAC;MACvC,GAAGuB;KACJ;EACH;EAEA;EAEA;EACA;EAEA;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC5U,gBAAgB,EAAEyB,EAAE,EAAE;MAC9B,IAAI,CAACoI,YAAY,CAAC6F,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF7B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC9N,gBAAgB,CAACc,QAAQ,CAAC;IAC3E,IAAI,CAAC+T,YAAY,CAACtV,QAAQ,CAACuV,KAAK,CAAC;EACnC;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC/U,gBAAgB,EAAEyB,EAAE,EAAE;MAC9B,IAAI,CAACoI,YAAY,CAAC6F,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF7B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC9N,gBAAgB,CAACc,QAAQ,CAAC;IAC3E,IAAI,CAAC+T,YAAY,CAACtV,QAAQ,CAACyV,KAAK,CAAC;EACnC;EAEAC,OAAOA,CAAA;IACL,IAAI,CAAClJ,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACG,UAAU,GAAG,IAAI;IACtB0B,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;EAEA;EACA;EAEA;EAEAoH,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOjB,IAAI,CAACkB,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOjB,IAAI,CAACkB,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAE,YAAYA,CAAC3B,OAAY;IACvB,MAAM4B,cAAc,GAAG5B,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC7C8B,GAAQ,IAAK,CAACA,GAAG,CAACN,IAAI,EAAE0D,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAID,cAAc,EAAEjD,GAAG,EAAE;MACvB,MAAMmD,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGL,cAAc,CAACjD,GAAG;MAC9BmD,IAAI,CAACI,QAAQ,GAAGN,cAAc,CAACjO,IAAI,IAAI,MAAM;MAC7CmO,IAAI,CAACK,MAAM,GAAG,QAAQ;MACtBJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,EAAE;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/B,IAAI,CAAC3L,YAAY,CAACqM,WAAW,CAAC,wBAAwB,CAAC;;EAE3D;EAEA;EAEAxV,YAAYA,CAAA;IACV,IAAI,CAAC+J,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,UAAU,GAAG,IAAI,CAACG,UAAU;EACnC;EAEA0L,cAAcA,CAAA;IACZ,IAAI,CAACvV,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAwV,qBAAqBA,CAAA;IACnBvI,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C;IACA,IAAI,CAACpE,MAAM,CACR2M,QAAQ,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAC3CC,IAAI,CAAC,MAAK;MACTzI,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,CAAC,CACDyI,KAAK,CAAE9H,KAAK,IAAI;MACfZ,OAAO,CAACY,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C;MACA,IAAI,CAAC/E,MAAM,CAAC2M,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAACE,KAAK,CAAC,MAAK;QACnD;QACAC,MAAM,CAACC,QAAQ,CAACd,IAAI,GAAG,+BAA+B;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EAEA;EAEApN,aAAaA,CAAA;IACX,IAAI,CAAC6B,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACzJ,YAAY,GAAG,KAAK;IACzB,IAAI,CAAC+J,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACK,kBAAkB,GAAG,KAAK;EACjC;EAEAzF,oBAAoBA,CAACmO,OAAY,EAAEgD,KAAiB;IAClDA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAAC/L,eAAe,GAAG8I,OAAO;IAC9B,IAAI,CAAC7I,mBAAmB,GAAG;MAAEC,CAAC,EAAE4L,KAAK,CAACE,OAAO;MAAE7L,CAAC,EAAE2L,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAAClM,sBAAsB,GAAG,IAAI;EACpC;EAEAmM,kBAAkBA,CAACpD,OAAY,EAAEgD,KAAiB;IAChDA,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAAC9L,qBAAqB,GAAGyI,OAAO;IACpC,IAAI,CAAC7I,mBAAmB,GAAG;MAAEC,CAAC,EAAE4L,KAAK,CAACE,OAAO;MAAE7L,CAAC,EAAE2L,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAAC7L,kBAAkB,GAAG,IAAI;EAChC;EAEAgM,UAAUA,CAAC1P,KAAa;IACtB,IAAI,IAAI,CAAC2D,qBAAqB,EAAE;MAC9B,IAAI,CAACgM,cAAc,CAAC,IAAI,CAAChM,qBAAqB,CAACxJ,EAAE,EAAE6F,KAAK,CAAC;;IAE3D,IAAI,CAAC0D,kBAAkB,GAAG,KAAK;EACjC;EAEAiM,cAAcA,CAAC7D,SAAiB,EAAE9L,KAAa;IAC7CuG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAExG,KAAK,EAAE,cAAc,EAAE8L,SAAS,CAAC;IACtE;EACF;;EAEA8D,cAAcA,CAACC,QAAa,EAAE/H,MAAc;IAC1C,OAAO+H,QAAQ,CAAC/H,MAAM,KAAKA,MAAM;EACnC;EAEAgI,cAAcA,CAAC1D,OAAY;IACzB7F,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4F,OAAO,CAACjS,EAAE,CAAC;IAClD,IAAI,CAAC8G,aAAa,EAAE;EACtB;EAEA8O,cAAcA,CAAC3D,OAAY;IACzB7F,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE4F,OAAO,CAACjS,EAAE,CAAC;IACjD,IAAI,CAAC8G,aAAa,EAAE;EACtB;EAEA+O,aAAaA,CAAC5D,OAAY;IACxB7F,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE4F,OAAO,CAACjS,EAAE,CAAC;IAChD,IAAI,CAAC8G,aAAa,EAAE;EACtB;EAEA;EAEAgP,iBAAiBA,CAAA;IACf,IAAI,CAACnN,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAoN,mBAAmBA,CAACC,QAAa;IAC/B,IAAI,CAAC/P,qBAAqB,GAAG+P,QAAQ;EACvC;EAEAhQ,oBAAoBA,CAACgQ,QAAa;IAChC,OAAOA,QAAQ,EAAE9K,MAAM,IAAI,EAAE;EAC/B;EAEAvF,WAAWA,CAACE,KAAU;IACpB,MAAMoQ,cAAc,GAAG,IAAI,CAACxK,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAE8F,KAAK,IAAI,EAAE;IACnE,MAAMoE,UAAU,GAAGD,cAAc,GAAGpQ,KAAK,CAACA,KAAK;IAC/C,IAAI,CAAC4F,WAAW,CAAC0K,UAAU,CAAC;MAAE1V,OAAO,EAAEyV;IAAU,CAAE,CAAC;IACpD,IAAI,CAACvN,eAAe,GAAG,KAAK;EAC9B;EAEAyN,oBAAoBA,CAAA;IAClB,IAAI,CAACxN,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA;EACA;EAEA;EACA;EAEA;EAEA;EACA;EAEA;EAEAvD,gBAAgBA,CAACsL,KAAa,EAAEsB,OAAY;IAC1C,OAAOA,OAAO,CAACjS,EAAE,IAAIiS,OAAO,CAACrE,GAAG,IAAI+C,KAAK,CAAC0F,QAAQ,EAAE;EACtD;EAEA;EAEAC,cAAcA,CAAA;IACZlK,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC,MAAMkK,WAAW,GAAG;MAClBvW,EAAE,EAAE,QAAQqP,IAAI,CAACsD,GAAG,EAAE,EAAE;MACxBlS,OAAO,EAAE,mBAAmB,IAAI4O,IAAI,EAAE,CAACmH,kBAAkB,EAAE,EAAE;MAC7D/W,SAAS,EAAE,IAAI4P,IAAI,EAAE,CAACoH,WAAW,EAAE;MACnC1W,MAAM,EAAE;QACNC,EAAE,EAAE,IAAI,CAACzB,gBAAgB,EAAEyB,EAAE,IAAI,WAAW;QAC5CX,QAAQ,EAAE,IAAI,CAACd,gBAAgB,EAAEc,QAAQ,IAAI,WAAW;QACxDa,KAAK,EACH,IAAI,CAAC3B,gBAAgB,EAAE2B,KAAK,IAAI;OACnC;MACDkQ,IAAI,EAAE,MAAM;MACZsG,MAAM,EAAE;KACT;IACD,IAAI,CAACtR,QAAQ,CAAC8L,IAAI,CAACqF,WAAW,CAAC;IAC/B,IAAI,CAAClO,GAAG,CAAC8I,aAAa,EAAE;IACxBC,UAAU,CAAC,MAAM,IAAI,CAACxB,cAAc,EAAE,EAAE,EAAE,CAAC;EAC7C;EAEAlL,mBAAmBA,CAAA;IACjB,OACE,IAAI,CAAC4D,YAAY,EAAEgG,OAAO,IAC1B,IAAI,CAAChG,YAAY,EAAE6F,YAAY,EAAEE,MAAM,GAAG,CAAC,IAC3C,KAAK;EAET;EAEA3H,UAAUA,CAAA;IACR0F,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,IAAI,CAACzD,kBAAkB,GAAG,KAAK;IAC/B;EACF;;EAEA+N,SAASA,CAACC,MAAc;IACtB,MAAMC,YAAY,GAAG7C,QAAQ,CAAC8C,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChB,MAAME,gBAAgB,GAAGF,YAAY,CAACG,KAAK,CAACC,SAAS,IAAI,UAAU;MACnE,MAAMC,YAAY,GAAGC,UAAU,CAC7BJ,gBAAgB,CAACK,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CACvD;MACD,MAAMC,QAAQ,GAAG5E,IAAI,CAAC6E,GAAG,CAAC,GAAG,EAAE7E,IAAI,CAAC8E,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGN,MAAM,CAAC,CAAC;MAClEC,YAAY,CAACG,KAAK,CAACC,SAAS,GAAG,SAASI,QAAQ,GAAG;MACnD,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChBR,YAAY,CAACW,SAAS,CAAC9K,GAAG,CAAC,QAAQ,CAAC;OACrC,MAAM;QACLmK,YAAY,CAACW,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;;EAG7C;EAEAC,SAASA,CAAA;IACP,MAAMb,YAAY,GAAG7C,QAAQ,CAAC8C,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,CAACC,SAAS,GAAG,UAAU;MACzCJ,YAAY,CAACW,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;EAE3C;EAEA;EACA;EACA;EACA;EAEApR,gBAAgBA,CAAC+J,IAAa;IAC5B,MAAMuH,KAAK,GAAG,IAAI,CAACC,SAAS,EAAEvF,aAAa;IAC3C,IAAI,CAACsF,KAAK,EAAE;MACVvL,OAAO,CAACY,KAAK,CAAC,8BAA8B,CAAC;MAC7C;;IAGF;IACA,IAAIoD,IAAI,KAAK,OAAO,EAAE;MACpBuH,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAIzH,IAAI,KAAK,OAAO,EAAE;MAC3BuH,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAIzH,IAAI,KAAK,UAAU,EAAE;MAC9BuH,KAAK,CAACE,MAAM,GAAG,iCAAiC;KACjD,MAAM;MACLF,KAAK,CAACE,MAAM,GAAG,KAAK;;IAGtB;IACAF,KAAK,CAAC7F,KAAK,GAAG,EAAE;IAEhB;IACA6F,KAAK,CAACpD,KAAK,EAAE;IACb,IAAI,CAAC3L,kBAAkB,GAAG,KAAK;EACjC;EAEA7D,iBAAiBA,CAACtF,SAAwB;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMqY,IAAI,GAAG,IAAIzI,IAAI,CAAC5P,SAAS,CAAC;IAChC,OAAOqY,IAAI,CAACtB,kBAAkB,CAAC,OAAO,EAAE;MACtCuB,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAzY,mBAAmBA,CAACE,SAAwB;IAC1C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMqY,IAAI,GAAG,IAAIzI,IAAI,CAAC5P,SAAS,CAAC;IAChC,MAAMwY,KAAK,GAAG,IAAI5I,IAAI,EAAE;IACxB,MAAM6I,SAAS,GAAG,IAAI7I,IAAI,CAAC4I,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIN,IAAI,CAACO,YAAY,EAAE,KAAKJ,KAAK,CAACI,YAAY,EAAE,EAAE;MAChD,OAAO,aAAa;KACrB,MAAM,IAAIP,IAAI,CAACO,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MAC3D,OAAO,MAAM;KACd,MAAM;MACL,OAAOP,IAAI,CAACQ,kBAAkB,CAAC,OAAO,CAAC;;EAE3C;EAEA9X,oBAAoBA,CAACC,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,MAAM8X,QAAQ,GAAG,sBAAsB;IACvC,OAAO9X,OAAO,CAAC+X,OAAO,CACpBD,QAAQ,EACR,qEAAqE,CACtE;EACH;EAEAhU,uBAAuBA,CAACoM,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAM8H,cAAc,GAAG,IAAI,CAACrT,QAAQ,CAACuL,KAAK,CAAC;IAC3C,MAAM+H,eAAe,GAAG,IAAI,CAACtT,QAAQ,CAACuL,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAAC8H,cAAc,EAAEhZ,SAAS,IAAI,CAACiZ,eAAe,EAAEjZ,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAMkZ,WAAW,GAAG,IAAItJ,IAAI,CAACoJ,cAAc,CAAChZ,SAAS,CAAC,CAAC4Y,YAAY,EAAE;IACrE,MAAMO,YAAY,GAAG,IAAIvJ,IAAI,CAACqJ,eAAe,CAACjZ,SAAS,CAAC,CAAC4Y,YAAY,EAAE;IAEvE,OAAOM,WAAW,KAAKC,YAAY;EACrC;EAEAnU,gBAAgBA,CAACkM,KAAa;IAC5B,MAAM8H,cAAc,GAAG,IAAI,CAACrT,QAAQ,CAACuL,KAAK,CAAC;IAC3C,MAAMkI,WAAW,GAAG,IAAI,CAACzT,QAAQ,CAACuL,KAAK,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACkI,WAAW,EAAE,OAAO,IAAI;IAE7B,OAAOJ,cAAc,CAAC1Y,MAAM,EAAEC,EAAE,KAAK6Y,WAAW,CAAC9Y,MAAM,EAAEC,EAAE;EAC7D;EAEA2E,oBAAoBA,CAACgM,KAAa;IAChC,MAAM8H,cAAc,GAAG,IAAI,CAACrT,QAAQ,CAACuL,KAAK,CAAC;IAC3C,MAAM+H,eAAe,GAAG,IAAI,CAACtT,QAAQ,CAACuL,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAAC+H,eAAe,EAAE,OAAO,IAAI;IAEjC,OAAOD,cAAc,CAAC1Y,MAAM,EAAEC,EAAE,KAAK0Y,eAAe,CAAC3Y,MAAM,EAAEC,EAAE;EACjE;EAEA4E,cAAcA,CAACqN,OAAY;IACzB;IACA,IAAIA,OAAO,CAAC7B,IAAI,EAAE;MAChB,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,eAAe,EAAE,OAAO,OAAO;MACpD,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,MAAM,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,MAAM,EAAE,OAAO,MAAM;;IAGvE;IACA,IAAI6B,OAAO,CAAC1B,WAAW,IAAI0B,OAAO,CAAC1B,WAAW,CAAClC,MAAM,GAAG,CAAC,EAAE;MACzD,MAAMyK,UAAU,GAAG7G,OAAO,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACzC,IAAIuI,UAAU,CAAC1I,IAAI,EAAE0D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIgF,UAAU,CAAC1I,IAAI,EAAE0D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIgF,UAAU,CAAC1I,IAAI,EAAE0D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,OAAO,MAAM;;IAGf;IACA,IAAI7B,OAAO,CAAC8G,QAAQ,IAAI9G,OAAO,CAAC+G,QAAQ,IAAI/G,OAAO,CAACgH,KAAK,EAAE,OAAO,OAAO;IAEzE,OAAO,MAAM;EACf;EAEApU,QAAQA,CAACoN,OAAY;IACnB;IACA,IAAIA,OAAO,CAAC7B,IAAI,KAAK,OAAO,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,OAAO,EAAE;MACxD,OAAO,IAAI;;IAGb;IACA,MAAM8I,kBAAkB,GACtBjH,OAAO,CAAC1B,WAAW,EAAES,IAAI,CAAEN,GAAQ,IAAI;MACrC,OAAOA,GAAG,CAACN,IAAI,EAAE0D,UAAU,CAAC,QAAQ,CAAC,IAAIpD,GAAG,CAACN,IAAI,KAAK,OAAO;IAC/D,CAAC,CAAC,IAAI,KAAK;IAEb;IACA,MAAM+I,WAAW,GAAG,CAAC,EAAElH,OAAO,CAACmH,QAAQ,IAAInH,OAAO,CAAC/R,KAAK,CAAC;IAEzD,OAAOgZ,kBAAkB,IAAIC,WAAW;EAC1C;EAEA3I,OAAOA,CAACyB,OAAY;IAClB;IACA,IAAIA,OAAO,CAAC7B,IAAI,KAAK,MAAM,IAAI6B,OAAO,CAAC7B,IAAI,KAAK,MAAM,EAAE;MACtD,OAAO,IAAI;;IAGb;IACA,MAAMiJ,iBAAiB,GACrBpH,OAAO,CAAC1B,WAAW,EAAES,IAAI,CAAEN,GAAQ,IAAI;MACrC,OAAO,CAACA,GAAG,CAACN,IAAI,EAAE0D,UAAU,CAAC,QAAQ,CAAC,IAAIpD,GAAG,CAACN,IAAI,KAAK,OAAO;IAChE,CAAC,CAAC,IAAI,KAAK;IAEb,OAAOiJ,iBAAiB;EAC1B;EAEA1X,WAAWA,CAACsQ,OAAY;IACtB;IACA,IAAIA,OAAO,CAACmH,QAAQ,EAAE;MACpB,OAAOnH,OAAO,CAACmH,QAAQ;;IAEzB,IAAInH,OAAO,CAAC/R,KAAK,EAAE;MACjB,OAAO+R,OAAO,CAAC/R,KAAK;;IAGtB;IACA,MAAMoZ,eAAe,GAAGrH,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC9C8B,GAAQ,IAAKA,GAAG,CAACN,IAAI,EAAE0D,UAAU,CAAC,QAAQ,CAAC,IAAIpD,GAAG,CAACN,IAAI,KAAK,OAAO,CACrE;IAED,IAAIkJ,eAAe,EAAE;MACnB,OAAOA,eAAe,CAAC1I,GAAG,IAAI0I,eAAe,CAACzI,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEA0I,WAAWA,CAACtH,OAAY;IACtB,MAAM4B,cAAc,GAAG5B,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC7C8B,GAAQ,IAAK,CAACA,GAAG,CAACN,IAAI,EAAE0D,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,OAAOD,cAAc,EAAEjO,IAAI,IAAI,SAAS;EAC1C;EAEA4T,WAAWA,CAACvH,OAAY;IACtB,MAAM4B,cAAc,GAAG5B,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC7C8B,GAAQ,IAAK,CAACA,GAAG,CAACN,IAAI,EAAE0D,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAE/C,IAAI,EAAE,OAAO,EAAE;IAEpC,MAAM4C,KAAK,GAAGG,cAAc,CAAC/C,IAAI;IACjC,IAAI4C,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOjB,IAAI,CAACkB,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOjB,IAAI,CAACkB,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEA+F,WAAWA,CAACxH,OAAY;IACtB,MAAM4B,cAAc,GAAG5B,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC7C8B,GAAQ,IAAK,CAACA,GAAG,CAACN,IAAI,EAAE0D,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACD,cAAc,EAAEzD,IAAI,EAAE,OAAO,aAAa;IAE/C,IAAIyD,cAAc,CAACzD,IAAI,CAAC0D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAACzD,IAAI,CAAC0D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAID,cAAc,CAACzD,IAAI,CAACsJ,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IACjE,IAAI7F,cAAc,CAACzD,IAAI,CAACsJ,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACnE,IAAI7F,cAAc,CAACzD,IAAI,CAACsJ,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IACrE,OAAO,aAAa;EACtB;EAEApZ,YAAYA,CAACqN,MAAc;IACzB;IACA,MAAMgM,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAMhJ,KAAK,GAAGhD,MAAM,CAACiM,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAACtL,MAAM;IAClD,OAAOsL,MAAM,CAAChJ,KAAK,CAAC;EACtB;EAEA;EACAhN,cAAcA,CAACsO,OAAY,EAAEgD,KAAU;IACrC7I,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE4F,OAAO,CAAC;EAC1C;EAEA4H,aAAaA,CAAC5E,KAAU;IACtB;IACA,IAAI,CAAC6E,qBAAqB,EAAE;EAC9B;EAEAC,cAAcA,CAAC9E,KAAoB;IACjC,IAAIA,KAAK,CAAC+E,GAAG,KAAK,OAAO,IAAI,CAAC/E,KAAK,CAACgF,QAAQ,EAAE;MAC5ChF,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAACtD,WAAW,EAAE;;EAEtB;EAEAsI,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,QAAQA,CAACnF,KAAU;IACjB;IACA,MAAM7C,OAAO,GAAG6C,KAAK,CAACb,MAAM;IAC5B,IACEhC,OAAO,CAACE,SAAS,KAAK,CAAC,IACvB,IAAI,CAAC5J,eAAe,IACpB,CAAC,IAAI,CAACD,aAAa,EACnB;MACA,IAAI,CAACoH,gBAAgB,EAAE;;EAE3B;EAEA/P,eAAeA,CAAC6N,MAAc;IAC5BvB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEsB,MAAM,CAAC;EAClD;EAEAvM,WAAWA,CAAC6T,KAAU,EAAEhD,OAAY;IAClC7F,OAAO,CAACC,GAAG,CACT,oDAAoD,EACpD4F,OAAO,CAACjS,EAAE,EACViV,KAAK,CAACb,MAAM,CAACiG,GAAG,CACjB;EACH;EAEA9Y,YAAYA,CAAC0T,KAAU,EAAEhD,OAAY;IACnC7F,OAAO,CAACY,KAAK,CAAC,+CAA+C,EAAEiF,OAAO,CAACjS,EAAE,EAAE;MACzEqa,GAAG,EAAEpF,KAAK,CAACb,MAAM,CAACiG,GAAG;MACrBrN,KAAK,EAAEiI;KACR,CAAC;IACF;IACAA,KAAK,CAACb,MAAM,CAACiG,GAAG,GACd,4WAA4W;EAChX;EAEArZ,eAAeA,CAACiR,OAAY;IAC1B,MAAMqH,eAAe,GAAGrH,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAAE8B,GAAQ,IACzDA,GAAG,CAACN,IAAI,EAAE0D,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,IAAIwF,eAAe,EAAE1I,GAAG,EAAE;MACxB,IAAI,CAAClH,aAAa,GAAG;QACnBkH,GAAG,EAAE0I,eAAe,CAAC1I,GAAG;QACxBhL,IAAI,EAAE0T,eAAe,CAAC1T,IAAI,IAAI,OAAO;QACrCkL,IAAI,EAAE,IAAI,CAAC2C,cAAc,CAAC6F,eAAe,CAACxI,IAAI,IAAI,CAAC,CAAC;QACpDmB,OAAO,EAAEA;OACV;MACD,IAAI,CAACxI,eAAe,GAAG,IAAI;MAC3B2C,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC3C,aAAa,CAAC;;EAEvE;EAEA4Q,gBAAgBA,CAAA;IACd,IAAI,CAAC7Q,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB0C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC;EAEAkO,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC7Q,aAAa,EAAEkH,GAAG,EAAE;MAC3B,MAAMmD,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAG,IAAI,CAACxK,aAAa,CAACkH,GAAG;MAClCmD,IAAI,CAACI,QAAQ,GAAG,IAAI,CAACzK,aAAa,CAAC9D,IAAI,IAAI,OAAO;MAClDmO,IAAI,CAACK,MAAM,GAAG,QAAQ;MACtBJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,EAAE;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/B,IAAI,CAAC3L,YAAY,CAACqM,WAAW,CAAC,wBAAwB,CAAC;MACvDrI,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC3C,aAAa,CAAC9D,IAAI,CACxB;;EAEL;EAEA;EACA;EACA;EAEA4U,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC1R,WAAW,CAACiJ,IAAI,EAAE,EAAE;MAC5B,IAAI,CAAChJ,aAAa,GAAG,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC3D,QAAQ,CAACqV,MAAM,CACtCxI,OAAO,IACNA,OAAO,CAACxR,OAAO,EACXia,WAAW,EAAE,CACdhB,QAAQ,CAAC,IAAI,CAAC5Q,WAAW,CAAC4R,WAAW,EAAE,CAAC,IAC3CzI,OAAO,CAAClS,MAAM,EAAEV,QAAQ,EACpBqb,WAAW,EAAE,CACdhB,QAAQ,CAAC,IAAI,CAAC5Q,WAAW,CAAC4R,WAAW,EAAE,CAAC,CAC9C;EACH;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACH,cAAc,EAAE;EACvB;EAEAI,WAAWA,CAAA;IACT,IAAI,CAAC9R,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;EACzB;EAEA8R,aAAaA,CAAClJ,SAAiB;IAC7B,MAAMmJ,cAAc,GAAG9G,QAAQ,CAAC+G,cAAc,CAAC,WAAWpJ,SAAS,EAAE,CAAC;IACtE,IAAImJ,cAAc,EAAE;MAClBA,cAAc,CAACE,cAAc,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAQ,CAAE,CAAC;MACtE;MACAJ,cAAc,CAACtD,SAAS,CAAC9K,GAAG,CAAC,WAAW,CAAC;MACzC0E,UAAU,CAAC,MAAK;QACd0J,cAAc,CAACtD,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;MAC9C,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;EACA;EACA;EAEA0D,gBAAgBA,CAAA;IACd,IAAI,CAACjS,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,eAAe,GAAG,IAAI;EAC7B;EAEA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EAEQiK,YAAYA,CAAC7I,QAAkB;IACrC6B,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACnED,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5C9B,QAAQ;MACRhM,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvC+J,YAAY,EAAE,IAAI,CAACA,YAAY,EAAEtI,EAAE;MACnCY,aAAa,EAAE,IAAI,CAACA;KACrB,CAAC;IAEF,IAAI,CAAC,IAAI,CAACrC,gBAAgB,EAAE;MAC1B6N,OAAO,CAACY,KAAK,CAAC,uCAAuC,CAAC;MACtD,IAAI,CAAC5E,YAAY,CAAC6F,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,MAAMmN,WAAW,GAAG,IAAI,CAAC7c,gBAAgB,CAACyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,CAACqP,GAAG;IACzE,IAAI,CAACwN,WAAW,EAAE;MAChBhP,OAAO,CAACY,KAAK,CAAC,wCAAwC,CAAC;MACvD,IAAI,CAAC5E,YAAY,CAAC6F,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF7B,OAAO,CAACC,GAAG,CAAC,+BAA+B9B,QAAQ,gBAAgB,EAAE;MACnE6Q,WAAW;MACXC,aAAa,EACX,IAAI,CAAC9c,gBAAgB,CAACc,QAAQ,IAAI,IAAI,CAACd,gBAAgB,CAACqH,IAAI;MAC9DkI,cAAc,EAAE,IAAI,CAACxF,YAAY,EAAEtI;KACpC,CAAC;IAEF,IAAI,CAACsK,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,KAAKzM,QAAQ,CAACuV,KAAK,GAAG,OAAO,GAAG,OAAO;IAC/D,IAAI,CAAC7I,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,CAAC8Q,cAAc,EAAE;IAErBlP,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IAEnE;IACA,IAAI,CAAClE,WAAW,CACbiL,YAAY,CAACgI,WAAW,EAAE7Q,QAAQ,EAAE,IAAI,CAACjC,YAAY,EAAEtI,EAAE,CAAC,CAC1D4M,SAAS,CAAC;MACTC,IAAI,EAAGK,IAAU,IAAI;QACnBd,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;UAC1DkP,MAAM,EAAErO,IAAI,CAAClN,EAAE;UACfuK,QAAQ,EAAE2C,IAAI,CAACkD,IAAI;UACnBoL,UAAU,EAAEtO,IAAI,CAAC9J,MAAM;UACvB+J,MAAM,EAAED,IAAI,CAACC,MAAM,EAAE9N,QAAQ;UAC7Boc,SAAS,EAAEvO,IAAI,CAACuO,SAAS,EAAEpc,QAAQ;UACnCyO,cAAc,EAAEZ,IAAI,CAACY;SACtB,CAAC;QAEF,IAAI,CAACpD,UAAU,GAAGwC,IAAI;QACtB,IAAI,CAACvC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACvC,YAAY,CAACqM,WAAW,CAC3B,SAASlK,QAAQ,KAAKzM,QAAQ,CAACuV,KAAK,GAAG,OAAO,GAAG,OAAO,SAAS,CAClE;QAEDjH,OAAO,CAACC,GAAG,CACT,qEAAqE,CACtE;MACH,CAAC;MACDW,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,wCAAwC,EAAE;UACtDA,KAAK,EAAEA,KAAK,CAACiF,OAAO,IAAIjF,KAAK;UAC7BoO,WAAW;UACX7Q,QAAQ;UACRuD,cAAc,EAAE,IAAI,CAACxF,YAAY,EAAEtI;SACpC,CAAC;QAEF,IAAI,CAACwT,OAAO,EAAE;QACd,IAAI,CAACpL,YAAY,CAAC6F,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACN;EAEAyN,UAAUA,CAAC5O,YAA0B;IACnCV,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAES,YAAY,CAAC;IAExD,IAAI,CAAC3E,WAAW,CAACuT,UAAU,CAAC5O,YAAY,CAAC,CAACF,SAAS,CAAC;MAClDC,IAAI,EAAGK,IAAU,IAAI;QACnBd,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEa,IAAI,CAAC;QAClD,IAAI,CAACxC,UAAU,GAAGwC,IAAI;QACtB,IAAI,CAAC5C,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACK,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACJ,QAAQ,GAAG2C,IAAI,CAACkD,IAAI,KAAKtS,QAAQ,CAACuV,KAAK,GAAG,OAAO,GAAG,OAAO;QAChE,IAAI,CAACiI,cAAc,EAAE;QACrB,IAAI,CAAClT,YAAY,CAACqM,WAAW,CAAC,eAAe,CAAC;MAChD,CAAC;MACDzH,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC5E,YAAY,CAAC6F,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;EACJ;EAEA0N,UAAUA,CAAC7O,YAA0B;IACnCV,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAES,YAAY,CAAC;IAExD,IAAI,CAAC3E,WAAW,CAACwT,UAAU,CAAC7O,YAAY,CAAC9M,EAAE,EAAE,eAAe,CAAC,CAAC4M,SAAS,CAAC;MACtEC,IAAI,EAAEA,CAAA,KAAK;QACTT,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3C,IAAI,CAACjE,YAAY,CAACqM,WAAW,CAAC,cAAc,CAAC;MAC/C,CAAC;MACDzH,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC5E,YAAY,CAAC6F,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;EACJ;EAEA;EACA;EAEQqN,cAAcA,CAAA;IACpB,IAAI,CAAC9Q,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,SAAS,GAAGmR,WAAW,CAAC,MAAK;MAChC,IAAI,CAACpR,YAAY,EAAE;MACnB,IAAI,CAACnC,GAAG,CAAC8I,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV;EAEQ0K,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACpR,SAAS,EAAE;MAClBqR,aAAa,CAAC,IAAI,CAACrR,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EAEA;EACAkR,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACrR,UAAU,EAAE;IAEtB,IAAI,CAACE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,IAAI,CAACzC,WAAW,CACb6T,WAAW,CACV,IAAI,CAACtR,UAAU,CAAC1K,EAAE,EAClBgS,SAAS;IAAE;IACX,CAAC,IAAI,CAACpH,OAAO,CAAC;KACf,CACAgC,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACzE,YAAY,CAACqM,WAAW,CAC3B,IAAI,CAAC7J,OAAO,GAAG,aAAa,GAAG,cAAc,CAC9C;MACH,CAAC;MACDoC,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA,IAAI,CAACpC,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;QAC5B,IAAI,CAACxC,YAAY,CAAC6F,SAAS,CAAC,oCAAoC,CAAC;MACnE;KACD,CAAC;EACN;EAEAgO,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACvR,UAAU,EAAE;IAEtB,IAAI,CAACG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C;IACA,IAAI,CAAC1C,WAAW,CACb6T,WAAW,CACV,IAAI,CAACtR,UAAU,CAAC1K,EAAE,EAClB,IAAI,CAAC6K,cAAc;IAAE;IACrBmH,SAAS,CAAC;KACX,CACApF,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACzE,YAAY,CAACqM,WAAW,CAC3B,IAAI,CAAC5J,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDmC,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;QACA,IAAI,CAACnC,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAACzC,YAAY,CAAC6F,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACN;EAEAiO,kBAAkBA,CAACpJ,QAAgB;IACjC,MAAMqJ,KAAK,GAAG1J,IAAI,CAACC,KAAK,CAACI,QAAQ,GAAG,IAAI,CAAC;IACzC,MAAMsJ,OAAO,GAAG3J,IAAI,CAACC,KAAK,CAAEI,QAAQ,GAAG,IAAI,GAAI,EAAE,CAAC;IAClD,MAAMuJ,OAAO,GAAGvJ,QAAQ,GAAG,EAAE;IAE7B,IAAIqJ,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIC,OAAO,CAAC/F,QAAQ,EAAE,CAACiG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CAC9DhG,QAAQ,EAAE,CACViG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAEvB,OAAO,GAAGF,OAAO,IAAIC,OAAO,CAAChG,QAAQ,EAAE,CAACiG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACA;EAEMC,mBAAmBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACvBrQ,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MAErD,IAAI;QACF;QACA,IAAI,CAACqQ,SAAS,CAACC,YAAY,IAAI,CAACD,SAAS,CAACC,YAAY,CAACC,YAAY,EAAE;UACnE,MAAM,IAAIC,KAAK,CACb,yDAAyD,CAC1D;;QAGH;QACA,IAAI,CAAC9H,MAAM,CAAC+H,aAAa,EAAE;UACzB,MAAM,IAAID,KAAK,CACb,uDAAuD,CACxD;;QAGHzQ,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QAEzD;QACA,MAAM0Q,MAAM,SAASL,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDI,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE,IAAI;YACrBC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE;;SAEjB,CAAC;QAEFjR,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QAEnD;QACA,IAAIiR,QAAQ,GAAG,wBAAwB;QACvC,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;UAC5CA,QAAQ,GAAG,YAAY;UACvB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;YAC5CA,QAAQ,GAAG,WAAW;YACtB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;cAC5CA,QAAQ,GAAG,EAAE,CAAC,CAAC;;;;;QAKrBlR,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEiR,QAAQ,CAAC;QAEpD;QACAd,KAAI,CAACxS,aAAa,GAAG,IAAI8S,aAAa,CAACC,MAAM,EAAE;UAC7CO,QAAQ,EAAEA,QAAQ,IAAItL;SACvB,CAAC;QAEF;QACAwK,KAAI,CAACvS,WAAW,GAAG,EAAE;QACrBuS,KAAI,CAAC1S,gBAAgB,GAAG,IAAI;QAC5B0S,KAAI,CAAC7U,sBAAsB,GAAG,CAAC;QAC/B6U,KAAI,CAACzS,mBAAmB,GAAG,WAAW;QAEtCqC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QAElE;QACAmQ,KAAI,CAACtS,cAAc,GAAG0R,WAAW,CAAC,MAAK;UACrCY,KAAI,CAAC7U,sBAAsB,EAAE;UAC7B;UACA6U,KAAI,CAACgB,iBAAiB,EAAE;UACxBhB,KAAI,CAACnU,GAAG,CAAC8I,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACAqL,KAAI,CAACxS,aAAa,CAACyT,eAAe,GAAIxI,KAAK,IAAI;UAC7C7I,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4I,KAAK,CAAC/B,IAAI,CAACpC,IAAI,EAAE,OAAO,CAAC;UACnE,IAAImE,KAAK,CAAC/B,IAAI,CAACpC,IAAI,GAAG,CAAC,EAAE;YACvB0L,KAAI,CAACvS,WAAW,CAACiH,IAAI,CAAC+D,KAAK,CAAC/B,IAAI,CAAC;;QAErC,CAAC;QAEDsJ,KAAI,CAACxS,aAAa,CAAC0T,MAAM,GAAG,MAAK;UAC/BtR,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;UACpEmQ,KAAI,CAACmB,oBAAoB,EAAE;QAC7B,CAAC;QAEDnB,KAAI,CAACxS,aAAa,CAAC4T,OAAO,GAAI3I,KAAU,IAAI;UAC1C7I,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEiI,KAAK,CAACjI,KAAK,CAAC;UAC7DwP,KAAI,CAACpU,YAAY,CAAC6F,SAAS,CAAC,iCAAiC,CAAC;UAC9DuO,KAAI,CAACqB,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACArB,KAAI,CAACxS,aAAa,CAAC8T,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/B1R,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAExDmQ,KAAI,CAACpU,YAAY,CAACqM,WAAW,CAAC,iCAAiC,CAAC;OACjE,CAAC,OAAOzH,KAAU,EAAE;QACnBZ,OAAO,CAACY,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAE5D,IAAI+Q,YAAY,GAAG,+CAA+C;QAElE,IAAI/Q,KAAK,CAACpH,IAAI,KAAK,iBAAiB,EAAE;UACpCmY,YAAY,GACV,iGAAiG;SACpG,MAAM,IAAI/Q,KAAK,CAACpH,IAAI,KAAK,eAAe,EAAE;UACzCmY,YAAY,GACV,6DAA6D;SAChE,MAAM,IAAI/Q,KAAK,CAACpH,IAAI,KAAK,mBAAmB,EAAE;UAC7CmY,YAAY,GACV,0DAA0D;SAC7D,MAAM,IAAI/Q,KAAK,CAACiF,OAAO,EAAE;UACxB8L,YAAY,GAAG/Q,KAAK,CAACiF,OAAO;;QAG9BuK,KAAI,CAACpU,YAAY,CAAC6F,SAAS,CAAC8P,YAAY,CAAC;QACzCvB,KAAI,CAACqB,oBAAoB,EAAE;;IAC5B;EACH;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAChU,aAAa,IAAI,IAAI,CAACA,aAAa,CAACiU,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAACjU,aAAa,CAACkU,IAAI,EAAE;MACzB,IAAI,CAAClU,aAAa,CAAC+S,MAAM,CAACoB,SAAS,EAAE,CAAC1N,OAAO,CAAE2N,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAAChU,cAAc,EAAE;MACvB4R,aAAa,CAAC,IAAI,CAAC5R,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACJ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,YAAY;EACzC;EAEA8T,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC7T,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACiU,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAACjU,aAAa,CAACkU,IAAI,EAAE;;MAE3B,IAAI,CAAClU,aAAa,CAAC+S,MAAM,CAACoB,SAAS,EAAE,CAAC1N,OAAO,CAAE2N,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;MACtE,IAAI,CAAClU,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvB4R,aAAa,CAAC,IAAI,CAAC5R,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACJ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACnC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACoC,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;EAEc0T,oBAAoBA,CAAA;IAAA,IAAAU,MAAA;IAAA,OAAA5B,iBAAA;MAChCrQ,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAEtD,IAAI;QACF;QACA,IAAIgS,MAAI,CAACpU,WAAW,CAACoE,MAAM,KAAK,CAAC,EAAE;UACjCjC,OAAO,CAACY,KAAK,CAAC,sCAAsC,CAAC;UACrDqR,MAAI,CAACjW,YAAY,CAAC6F,SAAS,CAAC,wBAAwB,CAAC;UACrDoQ,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGFzR,OAAO,CAACC,GAAG,CACT,0BAA0B,EAC1BgS,MAAI,CAACpU,WAAW,CAACoE,MAAM,EACvB,WAAW,EACXgQ,MAAI,CAAC1W,sBAAsB,CAC5B;QAED;QACA,IAAI0W,MAAI,CAAC1W,sBAAsB,GAAG,CAAC,EAAE;UACnCyE,OAAO,CAACY,KAAK,CACX,iCAAiC,EACjCqR,MAAI,CAAC1W,sBAAsB,CAC5B;UACD0W,MAAI,CAACjW,YAAY,CAAC6F,SAAS,CACzB,+CAA+C,CAChD;UACDoQ,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGF;QACA,IAAIP,QAAQ,GAAG,wBAAwB;QACvC,IAAIe,MAAI,CAACrU,aAAa,EAAEsT,QAAQ,EAAE;UAChCA,QAAQ,GAAGe,MAAI,CAACrU,aAAa,CAACsT,QAAQ;;QAGxClR,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEiR,QAAQ,CAAC;QAEvE;QACA,MAAMgB,SAAS,GAAG,IAAIC,IAAI,CAACF,MAAI,CAACpU,WAAW,EAAE;UAC3CmG,IAAI,EAAEkN;SACP,CAAC;QAEFlR,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CyE,IAAI,EAAEwN,SAAS,CAACxN,IAAI;UACpBV,IAAI,EAAEkO,SAAS,CAAClO;SACjB,CAAC;QAEF;QACA,IAAIoO,SAAS,GAAG,OAAO;QACvB,IAAIlB,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE;UAC5B8E,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIlB,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnC8E,SAAS,GAAG,MAAM;SACnB,MAAM,IAAIlB,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnC8E,SAAS,GAAG,MAAM;;QAGpB;QACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CACxB,CAACJ,SAAS,CAAC,EACX,SAASjP,IAAI,CAACsD,GAAG,EAAE,GAAG6L,SAAS,EAAE,EACjC;UACEpO,IAAI,EAAEkN;SACP,CACF;QAEDlR,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CzG,IAAI,EAAE6Y,SAAS,CAAC7Y,IAAI;UACpBkL,IAAI,EAAE2N,SAAS,CAAC3N,IAAI;UACpBV,IAAI,EAAEqO,SAAS,CAACrO;SACjB,CAAC;QAEF;QACAiO,MAAI,CAACtU,mBAAmB,GAAG,YAAY;QACvC,MAAMsU,MAAI,CAACM,gBAAgB,CAACF,SAAS,CAAC;QAEtCrS,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzDgS,MAAI,CAACjW,YAAY,CAACqM,WAAW,CAAC,yBAAyB,CAAC;OACzD,CAAC,OAAOzH,KAAU,EAAE;QACnBZ,OAAO,CAACY,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DqR,MAAI,CAACjW,YAAY,CAAC6F,SAAS,CACzB,2CAA2C,IACxCjB,KAAK,CAACiF,OAAO,IAAI,iBAAiB,CAAC,CACvC;OACF,SAAS;QACR;QACAoM,MAAI,CAACtU,mBAAmB,GAAG,MAAM;QACjCsU,MAAI,CAAC1W,sBAAsB,GAAG,CAAC;QAC/B0W,MAAI,CAACpU,WAAW,GAAG,EAAE;QACrBoU,MAAI,CAACvU,gBAAgB,GAAG,KAAK;QAE7BsC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;IAClE;EACH;EAEcsS,gBAAgBA,CAACF,SAAe;IAAA,IAAAG,MAAA;IAAA,OAAAnC,iBAAA;MAC5C,MAAMnM,UAAU,GAAGsO,MAAI,CAACrgB,gBAAgB,EAAEyB,EAAE,IAAI4e,MAAI,CAACrgB,gBAAgB,EAAEqP,GAAG;MAE1E,IAAI,CAAC0C,UAAU,EAAE;QACf,MAAM,IAAIuM,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIgC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCH,MAAI,CAAC1W,cAAc,CAAC0J,WAAW,CAC7BtB,UAAU,EACV,EAAE,EACFmO,SAAS,EACT,OAAc,EACdG,MAAI,CAACtW,YAAY,CAACtI,EAAE,CACrB,CAAC4M,SAAS,CAAC;UACVC,IAAI,EAAGoF,OAAY,IAAI;YACrB2M,MAAI,CAACxZ,QAAQ,CAAC8L,IAAI,CAACe,OAAO,CAAC;YAC3B2M,MAAI,CAAChP,cAAc,EAAE;YACrBkP,OAAO,EAAE;UACX,CAAC;UACD9R,KAAK,EAAGA,KAAU,IAAI;YACpBZ,OAAO,CAACY,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChE+R,MAAM,CAAC/R,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEAtF,uBAAuBA,CAACoL,QAAgB;IACtC,MAAMsJ,OAAO,GAAG3J,IAAI,CAACC,KAAK,CAACI,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMuJ,OAAO,GAAGvJ,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGsJ,OAAO,IAAIC,OAAO,CAAChG,QAAQ,EAAE,CAACiG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EAEA0C,aAAaA,CAAC/J,KAAY;IACxBA,KAAK,CAACC,cAAc,EAAE;IACtB9I,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvCvC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CpC,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;MACnDqC,aAAa,EAAE,CAAC,CAAC,IAAI,CAACA;KACvB,CAAC;IAEF;IACA,IAAI,IAAI,CAACD,mBAAmB,KAAK,YAAY,EAAE;MAC7CqC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,IAAI,CAACjE,YAAY,CAAC6W,WAAW,CAAC,wBAAwB,CAAC;MACvD;;IAGF,IAAI,IAAI,CAACnV,gBAAgB,EAAE;MACzBsC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,IAAI,CAACjE,YAAY,CAAC6W,WAAW,CAAC,iCAAiC,CAAC;MAChE;;IAGF;IACA,IAAI,CAAC7W,YAAY,CAAC8W,QAAQ,CAAC,2CAA2C,CAAC;IAEvE;IACA,IAAI,CAAC3C,mBAAmB,EAAE,CAACzH,KAAK,CAAE9H,KAAK,IAAI;MACzCZ,OAAO,CAACY,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAAC5E,YAAY,CAAC6F,SAAS,CACzB,iDAAiD,IAC9CjB,KAAK,CAACiF,OAAO,IAAI,iBAAiB,CAAC,CACvC;IACH,CAAC,CAAC;EACJ;EAEAzK,WAAWA,CAACyN,KAAY;IACtBA,KAAK,CAACC,cAAc,EAAE;IACtB9I,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,IAAI,CAAC,IAAI,CAACvC,gBAAgB,EAAE;MAC1BsC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD;;IAGF;IACA,IAAI,CAAC2R,kBAAkB,EAAE;EAC3B;EAEA3W,cAAcA,CAAC4N,KAAY;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtB9I,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD,IAAI,CAAC,IAAI,CAACvC,gBAAgB,EAAE;MAC1BsC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD;;IAGF;IACA,IAAI,CAACwR,oBAAoB,EAAE;EAC7B;EAEAjW,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACoC,aAAa,EAAEsT,QAAQ,EAAE;MAChC,IAAI,IAAI,CAACtT,aAAa,CAACsT,QAAQ,CAAC5D,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,MAAM;MAC/D,IAAI,IAAI,CAAC1P,aAAa,CAACsT,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAAC1P,aAAa,CAACsT,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAAC1P,aAAa,CAACsT,QAAQ,CAAC5D,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;;IAE/D,OAAO,MAAM;EACf;EAEA;EAEQ8D,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAAC1a,UAAU,GAAG,IAAI,CAACA,UAAU,CAACqc,GAAG,CAAC,MAAK;MACzC,OAAO1M,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC2M,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ;;EAEAC,cAAcA,CAACpK,KAAU;IACvB7I,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,MAAMiT,KAAK,GAAGrK,KAAK,CAACb,MAAM,CAACkL,KAAK;IAEhC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACjR,MAAM,KAAK,CAAC,EAAE;MAChCjC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C;;IAGFD,OAAO,CAACC,GAAG,CAAC,eAAeiT,KAAK,CAACjR,MAAM,oBAAoB,EAAEiR,KAAK,CAAC;IAEnE,KAAK,IAAIC,IAAI,IAAID,KAAK,EAAE;MACtBlT,OAAO,CAACC,GAAG,CACT,gCAAgCkT,IAAI,CAAC3Z,IAAI,WAAW2Z,IAAI,CAACzO,IAAI,WAAWyO,IAAI,CAACnP,IAAI,EAAE,CACpF;MACD,IAAI,CAACoP,UAAU,CAACD,IAAI,CAAC;;EAEzB;EAEQC,UAAUA,CAACD,IAAU;IAC3BnT,OAAO,CAACC,GAAG,CAAC,yCAAyCkT,IAAI,CAAC3Z,IAAI,EAAE,CAAC;IAEjE,MAAM0K,UAAU,GAAG,IAAI,CAAC/R,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAEqP,GAAG;IAE1E,IAAI,CAAC0C,UAAU,EAAE;MACflE,OAAO,CAACY,KAAK,CAAC,kCAAkC,CAAC;MACjD,IAAI,CAAC5E,YAAY,CAAC6F,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF7B,OAAO,CAACC,GAAG,CAAC,4BAA4BiE,UAAU,EAAE,CAAC;IAErD;IACA,MAAMmP,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,IAAIF,IAAI,CAACzO,IAAI,GAAG2O,OAAO,EAAE;MACvBrT,OAAO,CAACY,KAAK,CAAC,+BAA+BuS,IAAI,CAACzO,IAAI,QAAQ,CAAC;MAC/D,IAAI,CAAC1I,YAAY,CAAC6F,SAAS,CAAC,oCAAoC,CAAC;MACjE;;IAGF;IACA,IAAIsR,IAAI,CAACnP,IAAI,CAAC0D,UAAU,CAAC,QAAQ,CAAC,IAAIyL,IAAI,CAACzO,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;MAC7D;MACA1E,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtCkT,IAAI,CAAC3Z,IAAI,EACT,gBAAgB,EAChB2Z,IAAI,CAACzO,IAAI,CACV;MACD,IAAI,CAAC4O,aAAa,CAACH,IAAI,CAAC,CACrB1K,IAAI,CAAE8K,cAAc,IAAI;QACvBvT,OAAO,CAACC,GAAG,CACT,8DAA8D,EAC9DsT,cAAc,CAAC7O,IAAI,CACpB;QACD,IAAI,CAAC8O,gBAAgB,CAACD,cAAc,EAAErP,UAAU,CAAC;MACnD,CAAC,CAAC,CACDwE,KAAK,CAAE9H,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE;QACA,IAAI,CAAC4S,gBAAgB,CAACL,IAAI,EAAEjP,UAAU,CAAC;MACzC,CAAC,CAAC;MACJ;;IAGF;IACA,IAAI,CAACsP,gBAAgB,CAACL,IAAI,EAAEjP,UAAU,CAAC;EACzC;EAEQsP,gBAAgBA,CAACL,IAAU,EAAEjP,UAAkB;IACrD,MAAMuP,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACP,IAAI,CAAC;IACjDnT,OAAO,CAACC,GAAG,CAAC,wCAAwCwT,WAAW,EAAE,CAAC;IAClEzT,OAAO,CAACC,GAAG,CAAC,gCAAgC,IAAI,CAAC/D,YAAY,CAACtI,EAAE,EAAE,CAAC;IAEnE,IAAI,CAACiJ,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACW,WAAW,GAAG,IAAI;IACvB,IAAI,CAACD,cAAc,GAAG,CAAC;IACvByC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,MAAM0T,gBAAgB,GAAGnE,WAAW,CAAC,MAAK;MACxC,IAAI,CAACjS,cAAc,IAAI8I,IAAI,CAAC2M,MAAM,EAAE,GAAG,EAAE;MACzC,IAAI,IAAI,CAACzV,cAAc,IAAI,EAAE,EAAE;QAC7BmS,aAAa,CAACiE,gBAAgB,CAAC;;MAEjC,IAAI,CAAC1X,GAAG,CAAC8I,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAACjJ,cAAc,CAAC0J,WAAW,CAC7BtB,UAAU,EACV,EAAE,EACFiP,IAAI,EACJM,WAAW,EACX,IAAI,CAACvX,YAAY,CAACtI,EAAE,CACrB,CAAC4M,SAAS,CAAC;MACVC,IAAI,EAAGoF,OAAY,IAAI;QACrB7F,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE4F,OAAO,CAAC;QAC7D7F,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;UAChDrM,EAAE,EAAEiS,OAAO,CAACjS,EAAE;UACdoQ,IAAI,EAAE6B,OAAO,CAAC7B,IAAI;UAClBG,WAAW,EAAE0B,OAAO,CAAC1B,WAAW;UAChC1L,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACoN,OAAO,CAAC;UAChCzB,OAAO,EAAE,IAAI,CAACA,OAAO,CAACyB,OAAO,CAAC;UAC9BmH,QAAQ,EAAE,IAAI,CAACzX,WAAW,CAACsQ,OAAO;SACnC,CAAC;QAEF6J,aAAa,CAACiE,gBAAgB,CAAC;QAC/B,IAAI,CAACpW,cAAc,GAAG,GAAG;QAEzByH,UAAU,CAAC,MAAK;UACd,IAAI,CAAChM,QAAQ,CAAC8L,IAAI,CAACe,OAAO,CAAC;UAC3B,IAAI,CAACrC,cAAc,EAAE;UACrB,IAAI,CAACxH,YAAY,CAACqM,WAAW,CAAC,4BAA4B,CAAC;UAC3D,IAAI,CAACuL,gBAAgB,EAAE;QACzB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDhT,KAAK,EAAGA,KAAU,IAAI;QACpBZ,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD8O,aAAa,CAACiE,gBAAgB,CAAC;QAC/B,IAAI,CAAC3X,YAAY,CAAC6F,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAAC+R,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEQF,kBAAkBA,CAACP,IAAU;IACnC,IAAIA,IAAI,CAACnP,IAAI,CAAC0D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAIyL,IAAI,CAACnP,IAAI,CAAC0D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAIyL,IAAI,CAACnP,IAAI,CAAC0D,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,OAAO,MAAa;EACtB;EAEAmM,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAD,gBAAgBA,CAAA;IACd,IAAI,CAAC/W,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACW,WAAW,GAAG,KAAK;IACxB,IAAI,CAACD,cAAc,GAAG,CAAC;EACzB;EAEA;EAEAuW,UAAUA,CAACjL,KAAgB;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACzL,UAAU,GAAG,IAAI;EACxB;EAEAsW,WAAWA,CAAClL,KAAgB;IAC1BA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB;IACA,MAAM8K,IAAI,GAAInL,KAAK,CAACoL,aAA6B,CAACC,qBAAqB,EAAE;IACzE,MAAMjX,CAAC,GAAG4L,KAAK,CAACE,OAAO;IACvB,MAAM7L,CAAC,GAAG2L,KAAK,CAACG,OAAO;IAEvB,IAAI/L,CAAC,GAAG+W,IAAI,CAACG,IAAI,IAAIlX,CAAC,GAAG+W,IAAI,CAACI,KAAK,IAAIlX,CAAC,GAAG8W,IAAI,CAACK,GAAG,IAAInX,CAAC,GAAG8W,IAAI,CAACM,MAAM,EAAE;MACtE,IAAI,CAAC7W,UAAU,GAAG,KAAK;;EAE3B;EAEA8W,MAAMA,CAAC1L,KAAgB;IACrBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACzL,UAAU,GAAG,KAAK;IAEvB,MAAMyV,KAAK,GAAGrK,KAAK,CAAC2L,YAAY,EAAEtB,KAAK;IACvC,IAAIA,KAAK,IAAIA,KAAK,CAACjR,MAAM,GAAG,CAAC,EAAE;MAC7BjC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEiT,KAAK,CAACjR,MAAM,CAAC;MAE1D;MACAwS,KAAK,CAACC,IAAI,CAACxB,KAAK,CAAC,CAAC7O,OAAO,CAAE8O,IAAI,IAAI;QACjCnT,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCkT,IAAI,CAAC3Z,IAAI,EACT2Z,IAAI,CAACnP,IAAI,EACTmP,IAAI,CAACzO,IAAI,CACV;QACD,IAAI,CAAC0O,UAAU,CAACD,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,IAAI,CAACnX,YAAY,CAACqM,WAAW,CAC3B,GAAG6K,KAAK,CAACjR,MAAM,8BAA8B,CAC9C;;EAEL;EAEA;EAEQqR,aAAaA,CAACH,IAAU,EAAEwB,OAAA,GAAkB,GAAG;IACrD,OAAO,IAAIlC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMiC,MAAM,GAAGhN,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMgN,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;MAEvBD,GAAG,CAACE,MAAM,GAAG,MAAK;QAChB;QACA,MAAMC,QAAQ,GAAG,IAAI;QACrB,MAAMC,SAAS,GAAG,IAAI;QACtB,IAAI;UAAEC,KAAK;UAAEC;QAAM,CAAE,GAAGN,GAAG;QAE3B,IAAIK,KAAK,GAAGF,QAAQ,IAAIG,MAAM,GAAGF,SAAS,EAAE;UAC1C,MAAMG,KAAK,GAAGjP,IAAI,CAAC8E,GAAG,CAAC+J,QAAQ,GAAGE,KAAK,EAAED,SAAS,GAAGE,MAAM,CAAC;UAC5DD,KAAK,IAAIE,KAAK;UACdD,MAAM,IAAIC,KAAK;;QAGjBV,MAAM,CAACQ,KAAK,GAAGA,KAAK;QACpBR,MAAM,CAACS,MAAM,GAAGA,MAAM;QAEtB;QACAR,GAAG,EAAEU,SAAS,CAACR,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEK,KAAK,EAAEC,MAAM,CAAC;QAExC;QACAT,MAAM,CAACY,MAAM,CACVC,IAAI,IAAI;UACP,IAAIA,IAAI,EAAE;YACR,MAAMlC,cAAc,GAAG,IAAIjB,IAAI,CAAC,CAACmD,IAAI,CAAC,EAAEtC,IAAI,CAAC3Z,IAAI,EAAE;cACjDwK,IAAI,EAAEmP,IAAI,CAACnP,IAAI;cACf0R,YAAY,EAAEzS,IAAI,CAACsD,GAAG;aACvB,CAAC;YACFmM,OAAO,CAACa,cAAc,CAAC;WACxB,MAAM;YACLZ,MAAM,CAAC,IAAIlC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;QAEjD,CAAC,EACD0C,IAAI,CAACnP,IAAI,EACT2Q,OAAO,CACR;MACH,CAAC;MAEDI,GAAG,CAACvD,OAAO,GAAG,MAAMmB,MAAM,CAAC,IAAIlC,KAAK,CAAC,sBAAsB,CAAC,CAAC;MAC7DsE,GAAG,CAAC9G,GAAG,GAAG0H,GAAG,CAACC,eAAe,CAACzC,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA;EAEQzF,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACzO,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;MACA,IAAI,CAAC4W,mBAAmB,CAAC,IAAI,CAAC;;IAGhC;IACA,IAAI,IAAI,CAAC1W,aAAa,EAAE;MACtB2W,YAAY,CAAC,IAAI,CAAC3W,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAG6F,UAAU,CAAC,MAAK;MACnC,IAAI,CAAC/F,QAAQ,GAAG,KAAK;MACrB;MACA,IAAI,CAAC4W,mBAAmB,CAAC,KAAK,CAAC;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;EAEQA,mBAAmBA,CAAC5W,QAAiB;IAC3C;IACA,MAAMiF,UAAU,GAAG,IAAI,CAAC/R,gBAAgB,EAAEyB,EAAE,IAAI,IAAI,CAACzB,gBAAgB,EAAEqP,GAAG;IAC1E,IAAI0C,UAAU,IAAI,IAAI,CAAChI,YAAY,EAAEtI,EAAE,EAAE;MACvCoM,OAAO,CAACC,GAAG,CACT,gCAAgChB,QAAQ,YAAYiF,UAAU,EAAE,CACjE;MACD;MACA;;EAEJ;EAEA;EAEA6R,cAAcA,CAACjV,IAAU;IACvBd,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEa,IAAI,CAAC;IACrD,IAAI,CAACxC,UAAU,GAAGwC,IAAI;IACtB,IAAI,CAAC5C,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACK,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC2Q,cAAc,EAAE;IACrB,IAAI,CAAClT,YAAY,CAACqM,WAAW,CAAC,eAAe,CAAC;EAChD;EAEA2N,cAAcA,CAAA;IACZhW,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAACmH,OAAO,EAAE;IACd,IAAI,CAACpL,YAAY,CAAC8W,QAAQ,CAAC,cAAc,CAAC;EAC5C;EAEA;EAEAmD,gBAAgBA,CAACpQ,OAAY;IAC3B7F,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE4F,OAAO,CAACjS,EAAE,CAAC;IAC5D,IAAI,CAACyC,mBAAmB,CAACwP,OAAO,CAAC;EACnC;EAEApQ,cAAcA,CAAC8P,SAAiB;IAC9B,OAAO,IAAI,CAACvH,gBAAgB,KAAKuH,SAAS;EAC5C;EAEAlP,mBAAmBA,CAACwP,OAAY;IAC9B,MAAMN,SAAS,GAAGM,OAAO,CAACjS,EAAE;IAC5B,MAAMgZ,QAAQ,GAAG,IAAI,CAACsJ,WAAW,CAACrQ,OAAO,CAAC;IAE1C,IAAI,CAAC+G,QAAQ,EAAE;MACb5M,OAAO,CAACY,KAAK,CAAC,4CAA4C,EAAE2E,SAAS,CAAC;MACtE,IAAI,CAACvJ,YAAY,CAAC6F,SAAS,CAAC,2BAA2B,CAAC;MACxD;;IAGF;IACA,IAAI,IAAI,CAACpM,cAAc,CAAC8P,SAAS,CAAC,EAAE;MAClC,IAAI,CAAC4Q,iBAAiB,EAAE;MACxB;;IAGF;IACA,IAAI,CAACA,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,kBAAkB,CAACvQ,OAAO,EAAE+G,QAAQ,CAAC;EAC5C;EAEQwJ,kBAAkBA,CAACvQ,OAAY,EAAE+G,QAAgB;IACvD,MAAMrH,SAAS,GAAGM,OAAO,CAACjS,EAAE;IAE5B,IAAI;MACFoM,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnCsF,SAAS,EACT,MAAM,EACNqH,QAAQ,CACT;MAED,IAAI,CAAC7O,YAAY,GAAG,IAAIsY,KAAK,CAACzJ,QAAQ,CAAC;MACvC,IAAI,CAAC5O,gBAAgB,GAAGuH,SAAS;MAEjC;MACA,MAAM+Q,WAAW,GAAG,IAAI,CAAC9P,oBAAoB,CAACjB,SAAS,CAAC;MACxD,IAAI,CAACsB,oBAAoB,CAACtB,SAAS,EAAE;QACnCkB,QAAQ,EAAE,CAAC;QACXE,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE0P,WAAW,CAAC1P,KAAK,IAAI,CAAC;QAC7BF,QAAQ,EAAE4P,WAAW,CAAC5P,QAAQ,IAAI;OACnC,CAAC;MAEF;MACA,IAAI,CAAC3I,YAAY,CAACwY,YAAY,GAAGD,WAAW,CAAC1P,KAAK,IAAI,CAAC;MAEvD;MACA,IAAI,CAAC7I,YAAY,CAACyY,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QACxD,IAAI,IAAI,CAACzY,YAAY,EAAE;UACrB,IAAI,CAAC8I,oBAAoB,CAACtB,SAAS,EAAE;YACnCmB,QAAQ,EAAE,IAAI,CAAC3I,YAAY,CAAC2I;WAC7B,CAAC;UACF1G,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAAClC,YAAY,CAAC2I,QAAQ,CAC3B;;MAEL,CAAC,CAAC;MAEF,IAAI,CAAC3I,YAAY,CAACyY,gBAAgB,CAAC,YAAY,EAAE,MAAK;QACpD,IAAI,IAAI,CAACzY,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKuH,SAAS,EAAE;UAC5D,MAAMoB,WAAW,GAAG,IAAI,CAAC5I,YAAY,CAAC4I,WAAW;UACjD,MAAMF,QAAQ,GAAIE,WAAW,GAAG,IAAI,CAAC5I,YAAY,CAAC2I,QAAQ,GAAI,GAAG;UACjE,IAAI,CAACG,oBAAoB,CAACtB,SAAS,EAAE;YAAEoB,WAAW;YAAEF;UAAQ,CAAE,CAAC;UAC/D,IAAI,CAACxK,GAAG,CAAC8I,aAAa,EAAE;;MAE5B,CAAC,CAAC;MAEF,IAAI,CAAChH,YAAY,CAACyY,gBAAgB,CAAC,OAAO,EAAE,MAAK;QAC/CxW,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEsF,SAAS,CAAC;QACxD,IAAI,CAAC4Q,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF,IAAI,CAACpY,YAAY,CAACyY,gBAAgB,CAAC,OAAO,EAAG5V,KAAK,IAAI;QACpDZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC5E,YAAY,CAAC6F,SAAS,CAAC,iCAAiC,CAAC;QAC9D,IAAI,CAACsU,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF;MACA,IAAI,CAACpY,YAAY,CACdiD,IAAI,EAAE,CACNyH,IAAI,CAAC,MAAK;QACTzI,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,IAAI,CAACjE,YAAY,CAACqM,WAAW,CAAC,6BAA6B,CAAC;MAC9D,CAAC,CAAC,CACDK,KAAK,CAAE9H,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAC5E,YAAY,CAAC6F,SAAS,CAAC,qCAAqC,CAAC;QAClE,IAAI,CAACsU,iBAAiB,EAAE;MAC1B,CAAC,CAAC;KACL,CAAC,OAAOvV,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,CAAC5E,YAAY,CAAC6F,SAAS,CAAC,iCAAiC,CAAC;MAC9D,IAAI,CAACsU,iBAAiB,EAAE;;EAE5B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACpY,YAAY,EAAE;MACrBiC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACjC,gBAAgB,CAAC;MACvE,IAAI,CAACD,YAAY,CAAC0Y,KAAK,EAAE;MACzB,IAAI,CAAC1Y,YAAY,CAAC4I,WAAW,GAAG,CAAC;MACjC,IAAI,CAAC5I,YAAY,GAAG,IAAI;;IAE1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC/B,GAAG,CAAC8I,aAAa,EAAE;EAC1B;EAEAmR,WAAWA,CAACrQ,OAAY;IACtB;IACA,IAAIA,OAAO,CAAC8G,QAAQ,EAAE,OAAO9G,OAAO,CAAC8G,QAAQ;IAC7C,IAAI9G,OAAO,CAAC+G,QAAQ,EAAE,OAAO/G,OAAO,CAAC+G,QAAQ;IAC7C,IAAI/G,OAAO,CAACgH,KAAK,EAAE,OAAOhH,OAAO,CAACgH,KAAK;IAEvC;IACA,MAAM6J,eAAe,GAAG7Q,OAAO,CAAC1B,WAAW,EAAE3B,IAAI,CAC9C8B,GAAQ,IAAKA,GAAG,CAACN,IAAI,EAAE0D,UAAU,CAAC,QAAQ,CAAC,IAAIpD,GAAG,CAACN,IAAI,KAAK,OAAO,CACrE;IAED,IAAI0S,eAAe,EAAE;MACnB,OAAOA,eAAe,CAAClS,GAAG,IAAIkS,eAAe,CAACjS,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEAkS,aAAaA,CAAC9Q,OAAY;IACxB;IACA,MAAMN,SAAS,GAAGM,OAAO,CAACjS,EAAE,IAAI,EAAE;IAClC,MAAMgjB,IAAI,GAAGrR,SAAS,CACnBsR,KAAK,CAAC,EAAE,CAAC,CACTC,MAAM,CAAC,CAACC,GAAW,EAAEC,IAAY,KAAKD,GAAG,GAAGC,IAAI,CAACxJ,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,MAAMyJ,KAAK,GAAa,EAAE;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAM7B,MAAM,GAAG,CAAC,GAAI,CAACuB,IAAI,GAAGM,CAAC,GAAG,CAAC,IAAI,EAAG;MACxCD,KAAK,CAACnS,IAAI,CAACuQ,MAAM,CAAC;;IAGpB,OAAO4B,KAAK;EACd;EAEAE,gBAAgBA,CAACtR,OAAY;IAC3B,MAAMiB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACX,OAAO,CAACjS,EAAE,CAAC;IAClD,MAAMwjB,UAAU,GAAG,EAAE;IACrB,OAAO/Q,IAAI,CAACC,KAAK,CAAEQ,IAAI,CAACL,QAAQ,GAAG,GAAG,GAAI2Q,UAAU,CAAC;EACvD;EAEAC,mBAAmBA,CAACxR,OAAY;IAC9B,MAAMiB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACX,OAAO,CAACjS,EAAE,CAAC;IAClD,OAAO,IAAI,CAAC0jB,eAAe,CAACxQ,IAAI,CAACH,WAAW,CAAC;EAC/C;EAEAhQ,gBAAgBA,CAACkP,OAAY;IAC3B,MAAMiB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACX,OAAO,CAACjS,EAAE,CAAC;IAClD,MAAM8S,QAAQ,GAAGI,IAAI,CAACJ,QAAQ,IAAIb,OAAO,CAAC0R,QAAQ,EAAE7Q,QAAQ,IAAI,CAAC;IAEjE,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAOA,QAAQ,CAAC,CAAC;;;IAGnB,OAAO,IAAI,CAAC4Q,eAAe,CAAC5Q,QAAQ,CAAC;EACvC;EAEQ4Q,eAAeA,CAACrH,OAAe;IACrC,MAAMD,OAAO,GAAG3J,IAAI,CAACC,KAAK,CAAC2J,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMuH,gBAAgB,GAAGnR,IAAI,CAACC,KAAK,CAAC2J,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGD,OAAO,IAAIwH,gBAAgB,CAACvN,QAAQ,EAAE,CAACiG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEAuH,gBAAgBA,CAAC5R,OAAY,EAAE6R,SAAiB;IAC9C,MAAMnS,SAAS,GAAGM,OAAO,CAACjS,EAAE;IAE5B,IAAI,CAAC,IAAI,CAACmK,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKuH,SAAS,EAAE;MAC7D;;IAGF,MAAM6R,UAAU,GAAG,EAAE;IACrB,MAAMO,cAAc,GAAID,SAAS,GAAGN,UAAU,GAAI,GAAG;IACrD,MAAMQ,QAAQ,GAAID,cAAc,GAAG,GAAG,GAAI,IAAI,CAAC5Z,YAAY,CAAC2I,QAAQ;IAEpE,IAAI,CAAC3I,YAAY,CAAC4I,WAAW,GAAGiR,QAAQ;IACxC5X,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE2X,QAAQ,EAAE,SAAS,CAAC;EAC5D;EAEAC,gBAAgBA,CAAChS,OAAY;IAC3B,MAAMN,SAAS,GAAGM,OAAO,CAACjS,EAAE;IAC5B,MAAMkT,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACjB,SAAS,CAAC;IAEjD;IACA,MAAMuS,QAAQ,GAAGhR,IAAI,CAACF,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGE,IAAI,CAACF,KAAK,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;IAEpE,IAAI,CAACC,oBAAoB,CAACtB,SAAS,EAAE;MAAEqB,KAAK,EAAEkR;IAAQ,CAAE,CAAC;IAEzD,IAAI,IAAI,CAAC/Z,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKuH,SAAS,EAAE;MAC5D,IAAI,CAACxH,YAAY,CAACwY,YAAY,GAAGuB,QAAQ;;IAG3C,IAAI,CAAC9b,YAAY,CAACqM,WAAW,CAAC,YAAYyP,QAAQ,GAAG,CAAC;EACxD;EAEA;EAEA/hB,gBAAgBA,CAAC8P,OAAY;IAC3B,IAAI,CAACgS,gBAAgB,CAAChS,OAAO,CAAC;EAChC;EAEA5P,aAAaA,CAAC4P,OAAY;IACxB,MAAMiB,IAAI,GAAG,IAAI,CAACN,oBAAoB,CAACX,OAAO,CAACjS,EAAE,CAAC;IAClD,OAAOkT,IAAI,CAACF,KAAK,IAAI,CAAC;EACxB;EAEAmR,WAAWA,CAAA;IACT,IAAI,CAAC3Y,aAAa,CAAC4Y,WAAW,EAAE;IAEhC;IACA,IAAI,IAAI,CAAC3Z,SAAS,EAAE;MAClBqR,aAAa,CAAC,IAAI,CAACrR,SAAS,CAAC;;IAE/B,IAAI,IAAI,CAACP,cAAc,EAAE;MACvB4R,aAAa,CAAC,IAAI,CAAC5R,cAAc,CAAC;;IAEpC,IAAI,IAAI,CAACqB,aAAa,EAAE;MACtB2W,YAAY,CAAC,IAAI,CAAC3W,aAAa,CAAC;;IAGlC;IACA,IAAI,IAAI,CAACvB,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACiU,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAACjU,aAAa,CAACkU,IAAI,EAAE;;MAE3B,IAAI,CAAClU,aAAa,CAAC+S,MAAM,EAAEoB,SAAS,EAAE,CAAC1N,OAAO,CAAE2N,KAAK,IAAKA,KAAK,CAACF,IAAI,EAAE,CAAC;;IAGzE;IACA,IAAI,CAACqE,iBAAiB,EAAE;EAC1B;;;uBAh6EW1a,oBAAoB,EAAA9J,EAAA,CAAAsmB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxmB,EAAA,CAAAsmB,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1mB,EAAA,CAAAsmB,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA3mB,EAAA,CAAAsmB,iBAAA,CAAAM,EAAA,CAAAzc,cAAA,GAAAnK,EAAA,CAAAsmB,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA9mB,EAAA,CAAAsmB,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAhnB,EAAA,CAAAsmB,iBAAA,CAAAtmB,EAAA,CAAAinB,iBAAA;IAAA;EAAA;;;YAApBnd,oBAAoB;MAAAod,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAnE,GAAA;QAAA,IAAAmE,EAAA;;;;;;;;;;;;;;;UCnBjCrnB,EAAA,CAAAE,cAAA,aASC;UAsEKF,EAAA,CAAAY,UAAA,mBAAA0mB,sDAAA;YAAA,OAASpE,GAAA,CAAAtM,qBAAA,EAAuB;UAAA,EAAC;UAmBjC5W,EAAA,CAAAC,SAAA,WAGK;UACPD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,aAAuE;UAejEF,EAAA,CAAAY,UAAA,mBAAA2mB,mDAAA;YAAA,OAASrE,GAAA,CAAAnhB,eAAA,CAAAmhB,GAAA,CAAA1iB,gBAAA,kBAAA0iB,GAAA,CAAA1iB,gBAAA,CAAAyB,EAAA,CAAsC;UAAA,EAAC;UAZlDjC,EAAA,CAAAI,YAAA,EAgBE;UAEFJ,EAAA,CAAAyD,UAAA,IAAA+jB,mCAAA,iBAaO;UACTxnB,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAE,cAAA,aAAmC;UAY/BF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,eAA8D;UAE5DF,EAAA,CAAAyD,UAAA,KAAAgkB,oCAAA,kBAkCM;UAENznB,EAAA,CAAAyD,UAAA,KAAAikB,qCAAA,mBAMO;UACT1nB,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAE,cAAA,eAA0D;UAGtDF,EAAA,CAAAY,UAAA,mBAAA+mB,uDAAA;YAAA,OAASzE,GAAA,CAAA9N,cAAA,EAAgB;UAAA,EAAC;UAc1BpV,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAcC;UAbCF,EAAA,CAAAY,UAAA,mBAAAgnB,uDAAA;YAAA,OAAS1E,GAAA,CAAA3N,cAAA,EAAgB;UAAA,EAAC;UAc1BvV,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAcC;UAbCF,EAAA,CAAAY,UAAA,mBAAAinB,uDAAA;YAAA,OAAS3E,GAAA,CAAAhiB,YAAA,EAAc;UAAA,EAAC;UAcxBlB,EAAA,CAAAC,SAAA,aAA6B;UAC/BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAeC;UAdCF,EAAA,CAAAY,UAAA,mBAAAknB,uDAAA;YAAA,OAAS5E,GAAA,CAAAvM,cAAA,EAAgB;UAAA,EAAC;UAe1B3W,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAyD,UAAA,KAAAskB,oCAAA,mBA8EM;UACR/nB,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,oBAQC;UALCF,EAAA,CAAAY,UAAA,oBAAAonB,sDAAA7kB,MAAA;YAAA,OAAU+f,GAAA,CAAA7G,QAAA,CAAAlZ,MAAA,CAAgB;UAAA,EAAC,sBAAA8kB,wDAAA9kB,MAAA;YAAA,OACf+f,GAAA,CAAAf,UAAA,CAAAhf,MAAA,CAAkB;UAAA,EADH,uBAAA+kB,yDAAA/kB,MAAA;YAAA,OAEd+f,GAAA,CAAAd,WAAA,CAAAjf,MAAA,CAAmB;UAAA,EAFL,kBAAAglB,oDAAAhlB,MAAA;YAAA,OAGnB+f,GAAA,CAAAN,MAAA,CAAAzf,MAAA,CAAc;UAAA,EAHK;UAO3BnD,EAAA,CAAAyD,UAAA,KAAA2kB,oCAAA,kBAoDM;UAGNpoB,EAAA,CAAAyD,UAAA,KAAA4kB,oCAAA,kBAsBM;UAGNroB,EAAA,CAAAyD,UAAA,KAAA6kB,oCAAA,kBA0BM;UAGNtoB,EAAA,CAAAyD,UAAA,KAAA8kB,oCAAA,kBAgWM;UACRvoB,EAAA,CAAAI,YAAA,EAAO;UAGPJ,EAAA,CAAAE,cAAA,kBAEC;UAGGF,EAAA,CAAAY,UAAA,sBAAA4nB,wDAAA;YAAA,OAAYtF,GAAA,CAAArP,WAAA,EAAa;UAAA,EAAC;UAI1B7T,EAAA,CAAAE,cAAA,eAAqC;UAIjCF,EAAA,CAAAY,UAAA,mBAAA6nB,uDAAA;YAAA,OAASvF,GAAA,CAAAnL,iBAAA,EAAmB;UAAA,EAAC;UAgB7B/X,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAiBC;UAfCF,EAAA,CAAAY,UAAA,mBAAA8nB,uDAAA;YAAA,OAASxF,GAAA,CAAA7K,oBAAA,EAAsB;UAAA,EAAC;UAgBhCrY,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAwBC;UAtBCF,EAAA,CAAAY,UAAA,uBAAA+nB,2DAAAxlB,MAAA;YAAA,OAAa+f,GAAA,CAAAjC,aAAA,CAAA9d,MAAA,CAAqB;UAAA,EAAC,qBAAAylB,yDAAAzlB,MAAA;YAAA,OACxB+f,GAAA,CAAAzZ,WAAA,CAAAtG,MAAA,CAAmB;UAAA,EADK,wBAAA0lB,4DAAA1lB,MAAA;YAAA,OAErB+f,GAAA,CAAA5Z,cAAA,CAAAnG,MAAA,CAAsB;UAAA,EAFD,wBAAA2lB,4DAAA3lB,MAAA;YAAA,OAGrB+f,GAAA,CAAAjC,aAAA,CAAA9d,MAAA,CAAqB;UAAA,EAHA,sBAAA4lB,0DAAA5lB,MAAA;YAAA,OAIvB+f,GAAA,CAAAzZ,WAAA,CAAAtG,MAAA,CAAmB;UAAA,EAJI,yBAAA6lB,6DAAA7lB,MAAA;YAAA,OAKpB+f,GAAA,CAAA5Z,cAAA,CAAAnG,MAAA,CAAsB;UAAA,EALF;UAuBnCnD,EAAA,CAAAC,SAAA,SAGK;UAGLD,EAAA,CAAAyD,UAAA,KAAAwlB,oCAAA,kBAYO;UACTjpB,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAE,cAAA,eAAyC;UAIrCF,EAAA,CAAAY,UAAA,qBAAAsoB,2DAAA/lB,MAAA;YAAA,OAAW+f,GAAA,CAAAlH,cAAA,CAAA7Y,MAAA,CAAsB;UAAA,EAAC,mBAAAgmB,yDAAAhmB,MAAA;YAAA,OACzB+f,GAAA,CAAApH,aAAA,CAAA3Y,MAAA,CAAqB;UAAA,EADI,mBAAAimB,yDAAA;YAAA,OAEzBlG,GAAA,CAAA/G,YAAA,EAAc;UAAA,EAFW;UAoBnCnc,EAAA,CAAAI,YAAA,EAAW;UAIdJ,EAAA,CAAAE,cAAA,kBA0BC;UACCF,EAAA,CAAAyD,UAAA,KAAA4lB,kCAAA,gBAA4D;UAC5DrpB,EAAA,CAAAyD,UAAA,KAAA6lB,oCAAA,kBAUO;UACTtpB,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAyD,UAAA,KAAA8lB,oCAAA,kBAkDM;UAGNvpB,EAAA,CAAAyD,UAAA,KAAA+lB,oCAAA,mBAsJM;UAGNxpB,EAAA,CAAAE,cAAA,qBAOE;UAHAF,EAAA,CAAAY,UAAA,oBAAA6oB,uDAAAtmB,MAAA;YAAA,OAAU+f,GAAA,CAAA5B,cAAA,CAAAne,MAAA,CAAsB;UAAA,EAAC;UAJnCnD,EAAA,CAAAI,YAAA,EAOE;UAIJJ,EAAA,CAAAyD,UAAA,KAAAimB,oCAAA,kBAYO;UAGP1pB,EAAA,CAAAyD,UAAA,KAAAkmB,oCAAA,mBAiHM;UACR3pB,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAE,cAAA,8BAQC;UAHCF,EAAA,CAAAY,UAAA,uBAAAgpB,uEAAA;YAAA,OAAa1G,GAAA,CAAAzN,OAAA,EAAS;UAAA,EAAC,0BAAAoU,0EAAA1mB,MAAA;YAAA,OACP+f,GAAA,CAAAkB,cAAA,CAAAjhB,MAAA,CAAsB;UAAA,EADf,0BAAA2mB,0EAAA;YAAA,OAEP5G,GAAA,CAAAmB,cAAA,EAAgB;UAAA,EAFT;UAGxBrkB,EAAA,CAAAI,YAAA,EAAqB;;;UAlvCZJ,EAAA,CAAAK,SAAA,GAAqE;UAArEL,EAAA,CAAAkC,UAAA,SAAAghB,GAAA,CAAA1iB,gBAAA,kBAAA0iB,GAAA,CAAA1iB,gBAAA,CAAA2B,KAAA,yCAAAnC,EAAA,CAAAoC,aAAA,CAAqE,QAAA8gB,GAAA,CAAA1iB,gBAAA,kBAAA0iB,GAAA,CAAA1iB,gBAAA,CAAAc,QAAA;UAkBpEtB,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAAkC,UAAA,SAAAghB,GAAA,CAAA1iB,gBAAA,kBAAA0iB,GAAA,CAAA1iB,gBAAA,CAAAC,QAAA,CAAgC;UA4BjCT,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,OAAA4iB,GAAA,CAAA1iB,gBAAA,kBAAA0iB,GAAA,CAAA1iB,gBAAA,CAAAc,QAAA,wBACF;UAIKtB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAkC,UAAA,SAAAghB,GAAA,CAAA3V,YAAA,CAAkB;UAmCdvN,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAkC,UAAA,UAAAghB,GAAA,CAAA3V,YAAA,CAAmB;UA+D5BvN,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAqC,WAAA,eAAA6gB,GAAA,CAAAjY,UAAA,6BAA2D,UAAAiY,GAAA,CAAAjY,UAAA;UAoB3DjL,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAqC,WAAA,eAAA6gB,GAAA,CAAA9hB,YAAA,6BAA6D,UAAA8hB,GAAA,CAAA9hB,YAAA;UAU9DpB,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAkC,UAAA,SAAAghB,GAAA,CAAA9hB,YAAA,CAAkB;UAwFrBpB,EAAA,CAAAK,SAAA,GAA0E;UAA1EL,EAAA,CAAAqC,WAAA,eAAA6gB,GAAA,CAAApX,UAAA,4CAA0E;UAIvE9L,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAkC,UAAA,SAAAghB,GAAA,CAAApX,UAAA,CAAgB;UAuDhB9L,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAkC,UAAA,SAAAghB,GAAA,CAAAzY,SAAA,CAAe;UAyBfzK,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAkC,UAAA,UAAAghB,GAAA,CAAAzY,SAAA,IAAAyY,GAAA,CAAA7b,QAAA,CAAAiJ,MAAA,OAAyC;UA6BzCtQ,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAkC,UAAA,UAAAghB,GAAA,CAAAzY,SAAA,IAAAyY,GAAA,CAAA7b,QAAA,CAAAiJ,MAAA,KAAuC;UAuWxCtQ,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAkC,UAAA,cAAAghB,GAAA,CAAAxV,WAAA,CAAyB;UAmBrB1N,EAAA,CAAAK,SAAA,GAAgE;UAAhEL,EAAA,CAAAqC,WAAA,eAAA6gB,GAAA,CAAAtY,eAAA,6BAAgE,UAAAsY,GAAA,CAAAtY,eAAA;UAsBhE5K,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAqC,WAAA,eAAA6gB,GAAA,CAAArY,kBAAA,6BAAmE,UAAAqY,GAAA,CAAArY,kBAAA;UA4BnE7K,EAAA,CAAAK,SAAA,GAAiE;UAAjEL,EAAA,CAAAqC,WAAA,eAAA6gB,GAAA,CAAAnX,gBAAA,6BAAiE,UAAAmX,GAAA,CAAAnX,gBAAA,uCAAAmX,GAAA,CAAAnX,gBAAA;UAQ/D/L,EAAA,CAAAK,SAAA,GAAgE;UAAhEL,EAAA,CAAA6E,UAAA,CAAAqe,GAAA,CAAAnX,gBAAA,uCAAgE;UAChE/L,EAAA,CAAAqC,WAAA,cAAA6gB,GAAA,CAAAnX,gBAAA,gCAAmE;UAKlE/L,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAAghB,GAAA,CAAAnX,gBAAA,CAAsB;UAuCzB/L,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAkC,UAAA,aAAAghB,GAAA,CAAApV,eAAA,GAA8B;UAsBhC9N,EAAA,CAAAK,SAAA,GAEC;UAFDL,EAAA,CAAAqC,WAAA,gBAAA6gB,GAAA,CAAAxV,WAAA,CAAAoG,KAAA,IAAAoP,GAAA,CAAAhY,gBAAA,yBAEC,YAAAgY,GAAA,CAAAxV,WAAA,CAAAoG,KAAA,IAAAoP,GAAA,CAAAhY,gBAAA;UAjBDlL,EAAA,CAAAkC,UAAA,cAAAghB,GAAA,CAAAxV,WAAA,CAAAoG,KAAA,IAAAoP,GAAA,CAAAhY,gBAAA,CAAmD;UAyBpBlL,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAkC,UAAA,UAAAghB,GAAA,CAAAhY,gBAAA,CAAuB;UAEnDlL,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAAghB,GAAA,CAAAhY,gBAAA,CAAsB;UAe1BlL,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAkC,UAAA,SAAAghB,GAAA,CAAAtY,eAAA,CAAqB;UAqDrB5K,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAkC,UAAA,SAAAghB,GAAA,CAAArY,kBAAA,CAAwB;UA6JzB7K,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAkC,UAAA,WAAAghB,GAAA,CAAAhB,kBAAA,GAA+B;UAOhCliB,EAAA,CAAAK,SAAA,GAA2D;UAA3DL,EAAA,CAAAkC,UAAA,SAAAghB,GAAA,CAAAtY,eAAA,IAAAsY,GAAA,CAAArY,kBAAA,IAAAqY,GAAA,CAAA9hB,YAAA,CAA2D;UAe3DpB,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAAghB,GAAA,CAAAnX,gBAAA,CAAsB;UAqHzB/L,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,cAAAghB,GAAA,CAAA3W,QAAA,CAAsB,eAAA2W,GAAA,CAAAvW,UAAA,cAAAuW,GAAA,CAAA1W,QAAA,sBAAA0W,GAAA,CAAA1iB,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}