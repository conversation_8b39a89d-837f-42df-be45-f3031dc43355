import { Component, Input, Output, EventEmitter, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Call, CallType } from '../../../models/message.model';

@Component({
  selector: 'app-call-interface',
  templateUrl: './call-interface.component.html',
  styleUrls: ['./call-interface.component.css']
})
export class CallInterfaceComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() activeCall: Call | null = null;
  @Input() callType: 'VIDEO' | 'AUDIO' | null = null;
  @Input() otherParticipant: any = null;

  @Output() callEnded = new EventEmitter<void>();
  @Output() callAccepted = new EventEmitter<Call>();
  @Output() callRejected = new EventEmitter<void>();

  isMuted = false;
  isVideoEnabled = true;
  callDuration = 0;
  private callTimer: any = null;

  ngOnInit(): void {
    if (this.isVisible && this.activeCall) {
      this.startCallTimer();
    }
  }

  ngOnDestroy(): void {
    this.stopCallTimer();
  }

  endCall(): void {
    console.log('🔚 Ending call from interface');
    this.stopCallTimer();
    this.callEnded.emit();
  }

  acceptCall(): void {
    if (this.activeCall) {
      console.log('✅ Accepting call from interface');
      this.startCallTimer();
      this.callAccepted.emit(this.activeCall);
    }
  }

  rejectCall(): void {
    console.log('❌ Rejecting call from interface');
    this.callRejected.emit();
  }

  toggleMute(): void {
    this.isMuted = !this.isMuted;
    console.log('🎤 Mute toggled:', this.isMuted);
  }

  toggleVideo(): void {
    if (this.callType === 'VIDEO') {
      this.isVideoEnabled = !this.isVideoEnabled;
      console.log('📹 Video toggled:', this.isVideoEnabled);
    }
  }

  getCallTypeIcon(): string {
    return this.callType === 'VIDEO' ? 'fas fa-video' : 'fas fa-phone';
  }

  formatCallDuration(): string {
    const minutes = Math.floor(this.callDuration / 60);
    const seconds = this.callDuration % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  private startCallTimer(): void {
    this.callDuration = 0;
    this.callTimer = setInterval(() => {
      this.callDuration++;
    }, 1000);
  }

  private stopCallTimer(): void {
    if (this.callTimer) {
      clearInterval(this.callTimer);
      this.callTimer = null;
    }
  }
}
