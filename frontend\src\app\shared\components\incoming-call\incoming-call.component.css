/* Styles pour le composant d'appel entrant */

.incoming-call-modal {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.call-avatar {
  position: relative;
  display: inline-block;
}

.call-avatar::before {
  content: '';
  position: absolute;
  inset: -4px;
  border-radius: 50%;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  animation: rotate 2s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.call-button {
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.call-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.call-button:active {
  transform: translateY(0);
}

.ringtone-indicator {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .incoming-call-modal .max-w-sm {
    max-width: calc(100vw - 2rem);
  }
  
  .call-button {
    width: 3.5rem;
    height: 3.5rem;
  }
  
  .call-button i {
    font-size: 1.125rem;
  }
}
