import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { MessageService } from '../../../services/message.service';
import { ToastService } from '../../../services/toast.service';
import { IncomingCall, CallType } from '../../../models/message.model';

@Component({
  selector: 'app-incoming-call',
  templateUrl: './incoming-call.component.html',
  styleUrls: ['./incoming-call.component.css']
})
export class IncomingCallComponent implements OnInit, OnDestroy {
  incomingCall: IncomingCall | null = null;
  isVisible = false;
  private subscriptions = new Subscription();

  constructor(
    private messageService: MessageService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    // S'abonner aux appels entrants
    this.subscriptions.add(
      this.messageService.incomingCall$.subscribe({
        next: (call) => {
          if (call) {
            this.incomingCall = call;
            this.isVisible = true;
            this.playRingtone();
          } else {
            this.isVisible = false;
            this.stopRingtone();
          }
        },
        error: (error) => {
          console.error('❌ Error in incoming call subscription:', error);
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.stopRingtone();
  }

  acceptCall(): void {
    if (this.incomingCall) {
      console.log('✅ Accepting incoming call:', this.incomingCall.id);
      this.messageService.acceptCall(this.incomingCall).subscribe({
        next: () => {
          this.toastService.showSuccess('Appel accepté');
          this.closeModal();
        },
        error: (error) => {
          console.error('❌ Error accepting call:', error);
          this.toastService.showError('Erreur lors de l\'acceptation de l\'appel');
        }
      });
    }
  }

  rejectCall(): void {
    if (this.incomingCall) {
      console.log('❌ Rejecting incoming call:', this.incomingCall.id);
      this.messageService.rejectCall(this.incomingCall.id, 'User rejected').subscribe({
        next: () => {
          this.toastService.showInfo('Appel rejeté');
          this.closeModal();
        },
        error: (error) => {
          console.error('❌ Error rejecting call:', error);
          this.toastService.showError('Erreur lors du rejet de l\'appel');
        }
      });
    }
  }

  getCallTypeIcon(): string {
    if (!this.incomingCall) return 'fas fa-phone';
    return this.incomingCall.type === CallType.VIDEO ? 'fas fa-video' : 'fas fa-phone';
  }

  getCallTypeText(): string {
    if (!this.incomingCall) return 'Appel';
    return this.incomingCall.type === CallType.VIDEO ? 'Appel vidéo' : 'Appel vocal';
  }

  private playRingtone(): void {
    this.messageService.play('ringtone');
  }

  private stopRingtone(): void {
    this.messageService.stop('ringtone');
  }

  private closeModal(): void {
    this.isVisible = false;
    this.incomingCall = null;
    this.stopRingtone();
  }
}
