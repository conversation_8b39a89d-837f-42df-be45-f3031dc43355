{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { CallType } from '../../../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/message.service\";\nimport * as i2 from \"../../../../services/toast.service\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = [\"localVideo\"];\nconst _c1 = [\"remoteVideo\"];\nfunction CallInterfaceComponent_div_1_div_3_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36)(2, \"div\", 37)(3, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CallInterfaceComponent_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵelement(2, \"img\", 30)(3, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\", 32);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 33);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, CallInterfaceComponent_div_1_div_3_div_8_Template, 4, 0, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (ctx_r7.otherParticipant == null ? null : ctx_r7.otherParticipant.image) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r7.otherParticipant == null ? null : ctx_r7.otherParticipant.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.otherParticipant == null ? null : ctx_r7.otherParticipant.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r7.callStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.isConnected);\n  }\n}\nfunction CallInterfaceComponent_div_1_video_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 39, 40);\n  }\n}\nfunction CallInterfaceComponent_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"i\", 6);\n    i0.ɵɵtext(2, \"videocam_off\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CallInterfaceComponent_div_1_p_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 42);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.formatDuration(ctx_r10.callDuration), \" \");\n  }\n}\nfunction CallInterfaceComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"video\", 13, 14);\n    i0.ɵɵlistener(\"click\", function CallInterfaceComponent_div_1_Template_video_click_1_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.toggleFullscreen());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CallInterfaceComponent_div_1_div_3_Template, 9, 5, \"div\", 15);\n    i0.ɵɵelementStart(4, \"div\", 16);\n    i0.ɵɵtemplate(5, CallInterfaceComponent_div_1_video_5_Template, 2, 0, \"video\", 17);\n    i0.ɵɵtemplate(6, CallInterfaceComponent_div_1_div_6_Template, 3, 0, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 19)(8, \"div\", 20)(9, \"div\", 21);\n    i0.ɵɵelement(10, \"img\", 22);\n    i0.ɵɵelementStart(11, \"div\")(12, \"p\", 23);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 24);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, CallInterfaceComponent_div_1_p_16_Template, 2, 1, \"p\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function CallInterfaceComponent_div_1_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.toggleFullscreen());\n    });\n    i0.ɵɵelementStart(18, \"i\", 27);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"cursor-pointer\", ctx_r0.remoteStream);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.remoteStream);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoEnabled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isVideoEnabled);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", (ctx_r0.otherParticipant == null ? null : ctx_r0.otherParticipant.image) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.otherParticipant == null ? null : ctx_r0.otherParticipant.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.otherParticipant == null ? null : ctx_r0.otherParticipant.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.callStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isConnected && ctx_r0.callDuration > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.isFullscreen ? \"fullscreen_exit\" : \"fullscreen\");\n  }\n}\nfunction CallInterfaceComponent_div_2_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.formatDuration(ctx_r16.callDuration), \" \");\n  }\n}\nfunction CallInterfaceComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 29);\n    i0.ɵɵelement(2, \"img\", 44)(3, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\", 46);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 47);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, CallInterfaceComponent_div_2_p_8_Template, 2, 1, \"p\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (ctx_r1.otherParticipant == null ? null : ctx_r1.otherParticipant.image) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.otherParticipant == null ? null : ctx_r1.otherParticipant.username);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"animate-pulse\", !ctx_r1.isConnected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otherParticipant == null ? null : ctx_r1.otherParticipant.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.callStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isConnected && ctx_r1.callDuration > 0);\n  }\n}\nfunction CallInterfaceComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function CallInterfaceComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.toggleVideo());\n    });\n    i0.ɵɵelementStart(1, \"i\", 6);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(!ctx_r2.isVideoEnabled ? \"bg-red-500 hover:bg-red-600\" : \"bg-white/20 hover:bg-white/30\");\n    i0.ɵɵproperty(\"title\", ctx_r2.isVideoEnabled ? \"D\\u00E9sactiver la cam\\u00E9ra\" : \"Activer la cam\\u00E9ra\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.isVideoEnabled ? \"videocam\" : \"videocam_off\");\n  }\n}\nfunction CallInterfaceComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"div\", 52)(3, \"div\", 53);\n    i0.ɵɵelement(4, \"img\", 54)(5, \"div\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h2\", 56);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 57);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 58)(11, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function CallInterfaceComponent_div_15_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.rejectCall());\n    });\n    i0.ɵɵelementStart(12, \"i\", 6);\n    i0.ɵɵtext(13, \"call_end\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function CallInterfaceComponent_div_15_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.acceptCall());\n    });\n    i0.ɵɵelementStart(15, \"i\", 6);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", (ctx_r3.otherParticipant == null ? null : ctx_r3.otherParticipant.image) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r3.otherParticipant == null ? null : ctx_r3.otherParticipant.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.otherParticipant == null ? null : ctx_r3.otherParticipant.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getCallTypeLabel(), \" entrant...\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.getCallTypeIcon());\n  }\n}\nfunction CallInterfaceComponent_audio_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"audio\", 61, 62);\n  }\n}\nfunction CallInterfaceComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵelement(2, \"div\", 65);\n    i0.ɵɵelementStart(3, \"p\", 66);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r5.callStatus);\n  }\n}\nexport class CallInterfaceComponent {\n  constructor(messageService, toastService, cdr) {\n    this.messageService = messageService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    this.activeCall = null;\n    this.isVisible = false;\n    this.callType = null;\n    this.otherParticipant = null;\n    this.callEnded = new EventEmitter();\n    this.callAccepted = new EventEmitter();\n    this.callRejected = new EventEmitter();\n    // État de l'appel\n    this.isConnected = false;\n    this.isIncoming = false;\n    this.isOutgoing = false;\n    this.callDuration = 0;\n    this.callStatus = 'Connexion...';\n    // Contrôles\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.isSpeakerOn = false;\n    this.isFullscreen = false;\n    // Streams\n    this.localStream = null;\n    this.remoteStream = null;\n    // Timer\n    this.callTimer = null;\n    this.subscriptions = new Subscription();\n    // Types pour le template\n    this.CallType = CallType;\n  }\n  ngOnInit() {\n    this.setupCallSubscriptions();\n    this.initializeMediaDevices();\n  }\n  ngOnDestroy() {\n    this.cleanup();\n  }\n  setupCallSubscriptions() {\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(this.messageService.activeCall$.subscribe({\n      next: call => {\n        if (call) {\n          this.activeCall = call;\n          this.updateCallStatus();\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in active call subscription:', error);\n      }\n    }));\n    // S'abonner aux streams\n    this.subscriptions.add(this.messageService.localStream$.subscribe({\n      next: stream => {\n        this.localStream = stream;\n        this.attachLocalStream();\n      }\n    }));\n    this.subscriptions.add(this.messageService.remoteStream$.subscribe({\n      next: stream => {\n        this.remoteStream = stream;\n        this.attachRemoteStream();\n      }\n    }));\n  }\n  initializeMediaDevices() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.activeCall) return;\n      try {\n        const constraints = {\n          audio: true,\n          video: _this.callType === 'VIDEO'\n        };\n        _this.localStream = yield navigator.mediaDevices.getUserMedia(constraints);\n        _this.attachLocalStream();\n        console.log('✅ Media devices initialized');\n      } catch (error) {\n        console.error('❌ Error accessing media devices:', error);\n        _this.toastService.showError(\"Erreur d'accès à la caméra/micro\");\n      }\n    })();\n  }\n  attachLocalStream() {\n    if (this.localStream && this.localVideoRef?.nativeElement) {\n      this.localVideoRef.nativeElement.srcObject = this.localStream;\n      this.localVideoRef.nativeElement.muted = true; // Éviter l'écho\n    }\n  }\n\n  attachRemoteStream() {\n    if (this.remoteStream && this.remoteVideoRef?.nativeElement) {\n      this.remoteVideoRef.nativeElement.srcObject = this.remoteStream;\n    }\n  }\n  updateCallStatus() {\n    if (!this.activeCall) return;\n    switch (this.activeCall.status) {\n      case 'ringing':\n        this.callStatus = this.isIncoming ? 'Appel entrant...' : 'Appel en cours...';\n        break;\n      case 'accepted':\n        this.callStatus = 'Connexion...';\n        break;\n      case 'connected':\n        this.callStatus = 'Connecté';\n        this.isConnected = true;\n        this.startCallTimer();\n        break;\n      case 'ended':\n        this.callStatus = 'Appel terminé';\n        this.endCall();\n        break;\n      case 'rejected':\n        this.callStatus = 'Appel rejeté';\n        this.endCall();\n        break;\n      default:\n        this.callStatus = 'En cours...';\n    }\n  }\n  startCallTimer() {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n  // === CONTRÔLES D'APPEL ===\n  toggleMute() {\n    if (!this.activeCall) return;\n    this.isMuted = !this.isMuted;\n    // Couper/activer le micro dans le stream local\n    if (this.localStream) {\n      const audioTracks = this.localStream.getAudioTracks();\n      audioTracks.forEach(track => {\n        track.enabled = !this.isMuted;\n      });\n    }\n    // Notifier via le service\n    this.messageService.toggleMedia(this.activeCall.id, undefined, !this.isMuted).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isMuted ? 'Micro coupé' : 'Micro activé');\n      },\n      error: error => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        if (this.localStream) {\n          const audioTracks = this.localStream.getAudioTracks();\n          audioTracks.forEach(track => {\n            track.enabled = !this.isMuted;\n          });\n        }\n      }\n    });\n  }\n  toggleVideo() {\n    if (!this.activeCall || this.callType !== 'VIDEO') return;\n    this.isVideoEnabled = !this.isVideoEnabled;\n    // Couper/activer la vidéo dans le stream local\n    if (this.localStream) {\n      const videoTracks = this.localStream.getVideoTracks();\n      videoTracks.forEach(track => {\n        track.enabled = this.isVideoEnabled;\n      });\n    }\n    // Notifier via le service\n    this.messageService.toggleMedia(this.activeCall.id, this.isVideoEnabled, undefined).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      error: error => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        if (this.localStream) {\n          const videoTracks = this.localStream.getVideoTracks();\n          videoTracks.forEach(track => {\n            track.enabled = this.isVideoEnabled;\n          });\n        }\n      }\n    });\n  }\n  toggleSpeaker() {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    if (this.remoteVideoRef?.nativeElement) {\n      // Changer la sortie audio (si supporté par le navigateur)\n      this.remoteVideoRef.nativeElement.volume = this.isSpeakerOn ? 1 : 0.7;\n    }\n    this.toastService.showSuccess(this.isSpeakerOn ? 'Haut-parleur activé' : 'Haut-parleur désactivé');\n  }\n  toggleFullscreen() {\n    this.isFullscreen = !this.isFullscreen;\n    if (this.isFullscreen && this.remoteVideoRef?.nativeElement) {\n      this.remoteVideoRef.nativeElement.requestFullscreen?.();\n    } else if (document.fullscreenElement) {\n      document.exitFullscreen?.();\n    }\n  }\n  acceptCall() {\n    if (!this.activeCall) return;\n    this.callAccepted.emit(this.activeCall);\n  }\n  rejectCall() {\n    this.callRejected.emit();\n  }\n  endCall() {\n    this.cleanup();\n    this.callEnded.emit();\n  }\n  cleanup() {\n    // Arrêter le timer\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    // Arrêter les streams\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n    }\n    if (this.remoteStream) {\n      this.remoteStream.getTracks().forEach(track => track.stop());\n      this.remoteStream = null;\n    }\n    // Nettoyer les subscriptions\n    this.subscriptions.unsubscribe();\n    // Réinitialiser l'état\n    this.isConnected = false;\n    this.callDuration = 0;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.isSpeakerOn = false;\n    this.isFullscreen = false;\n  }\n  // === UTILITAIRES ===\n  formatDuration(seconds) {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  }\n  getCallTypeIcon() {\n    return this.callType === 'VIDEO' ? 'videocam' : 'call';\n  }\n  getCallTypeLabel() {\n    return this.callType === 'VIDEO' ? 'Appel vidéo' : 'Appel audio';\n  }\n  static {\n    this.ɵfac = function CallInterfaceComponent_Factory(t) {\n      return new (t || CallInterfaceComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CallInterfaceComponent,\n      selectors: [[\"app-call-interface\"]],\n      viewQuery: function CallInterfaceComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localVideoRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.remoteVideoRef = _t.first);\n        }\n      },\n      inputs: {\n        activeCall: \"activeCall\",\n        isVisible: \"isVisible\",\n        callType: \"callType\",\n        otherParticipant: \"otherParticipant\"\n      },\n      outputs: {\n        callEnded: \"callEnded\",\n        callAccepted: \"callAccepted\",\n        callRejected: \"callRejected\"\n      },\n      decls: 18,\n      vars: 20,\n      consts: [[1, \"fixed\", \"inset-0\", \"z-50\", \"transition-all\", \"duration-300\", \"bg-gray-900\", \"dark:bg-gray-800\", 2, \"background\", \"linear-gradient(135deg, #111827 0%, #1f2937 50%, #374151 100%)\"], [\"class\", \"relative w-full h-full\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center h-full p-8\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-0\", \"left-0\", \"right-0\", \"bg-gradient-to-t\", \"from-black/70\", \"to-transparent\", \"p-6\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-6\"], [1, \"w-16\", \"h-16\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-200\", \"shadow-lg\", 3, \"title\", \"click\"], [1, \"material-icons\", \"text-white\", \"text-2xl\"], [\"class\", \"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg\", 3, \"class\", \"title\", \"click\", 4, \"ngIf\"], [\"title\", \"Raccrocher\", 1, \"w-16\", \"h-16\", \"rounded-full\", \"bg-red-500\", \"hover:bg-red-600\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-200\", \"shadow-lg\", 3, \"click\"], [\"class\", \"absolute inset-0 bg-black/80 flex items-center justify-center p-8\", 4, \"ngIf\"], [\"autoplay\", \"\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 z-60 bg-black/50 flex items-center justify-center\", 4, \"ngIf\"], [1, \"relative\", \"w-full\", \"h-full\"], [\"autoplay\", \"\", \"playsinline\", \"\", 1, \"w-full\", \"h-full\", \"object-cover\", \"bg-gray-800\", \"dark:bg-gray-900\", 3, \"click\"], [\"remoteVideo\", \"\"], [\"class\", \"absolute inset-0 flex flex-col items-center justify-center\", \"style\", \"\\n        background: linear-gradient(\\n          135deg,\\n          #1e40af 0%,\\n          #3b82f6 50%,\\n          #06b6d4 100%\\n        );\\n      \", 4, \"ngIf\"], [1, \"absolute\", \"top-4\", \"right-4\", \"w-32\", \"h-24\", \"bg-gray-800\", \"rounded-lg\", \"overflow-hidden\", \"border-2\", \"border-white/20\", \"shadow-lg\"], [\"class\", \"w-full h-full object-cover\", \"autoplay\", \"\", \"playsinline\", \"\", \"muted\", \"\", 4, \"ngIf\"], [\"class\", \"w-full h-full bg-gray-700 flex items-center justify-center\", 4, \"ngIf\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"bg-gradient-to-b\", \"from-black/50\", \"to-transparent\", \"p-4\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"w-10\", \"h-10\", \"rounded-full\", \"border-2\", \"border-white/30\", 3, \"src\", \"alt\"], [1, \"text-white\", \"font-medium\"], [1, \"text-cyan-300\", \"text-sm\"], [\"class\", \"text-green-400 text-sm\", 4, \"ngIf\"], [1, \"p-2\", \"rounded-full\", \"bg-white/10\", \"hover:bg-white/20\", \"transition-colors\", 3, \"click\"], [1, \"material-icons\", \"text-white\"], [1, \"absolute\", \"inset-0\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", 2, \"background\", \"linear-gradient(\\n          135deg,\\n          #1e40af 0%,\\n          #3b82f6 50%,\\n          #06b6d4 100%\\n        )\"], [1, \"relative\", \"mb-8\"], [1, \"w-32\", \"h-32\", \"rounded-full\", \"border-4\", \"border-white/30\", \"shadow-2xl\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border-4\", \"border-cyan-400\", \"animate-pulse\", \"opacity-70\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\", \"drop-shadow-lg\"], [1, \"text-cyan-200\", \"text-lg\", \"font-medium\"], [\"class\", \"mt-4 flex items-center space-x-2\", 4, \"ngIf\"], [1, \"mt-4\", \"flex\", \"items-center\", \"space-x-2\"], [1, \"w-3\", \"h-3\", \"bg-cyan-400\", \"rounded-full\", \"animate-bounce\"], [1, \"w-3\", \"h-3\", \"bg-cyan-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-3\", \"h-3\", \"bg-cyan-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [\"autoplay\", \"\", \"playsinline\", \"\", \"muted\", \"\", 1, \"w-full\", \"h-full\", \"object-cover\"], [\"localVideo\", \"\"], [1, \"w-full\", \"h-full\", \"bg-gray-700\", \"flex\", \"items-center\", \"justify-center\"], [1, \"text-green-400\", \"text-sm\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"h-full\", \"p-8\"], [1, \"w-48\", \"h-48\", \"rounded-full\", \"border-4\", \"border-white/20\", \"shadow-2xl\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border-4\", \"border-cyan-400\"], [1, \"text-4xl\", \"font-bold\", \"text-white\", \"mb-4\"], [1, \"text-cyan-300\", \"text-xl\", \"mb-2\"], [\"class\", \"text-green-400 text-2xl font-mono\", 4, \"ngIf\"], [1, \"text-green-400\", \"text-2xl\", \"font-mono\"], [1, \"absolute\", \"inset-0\", \"bg-black/80\", \"flex\", \"items-center\", \"justify-center\", \"p-8\"], [1, \"bg-gray-800\", \"rounded-2xl\", \"p-8\", \"max-w-sm\", \"w-full\", \"text-center\", \"shadow-2xl\", \"border\", \"border-gray-700\"], [1, \"mb-8\"], [1, \"relative\", \"inline-block\", \"mb-4\"], [1, \"w-24\", \"h-24\", \"rounded-full\", \"border-4\", \"border-white/20\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border-4\", \"border-cyan-400\", \"animate-pulse\"], [1, \"text-2xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-cyan-300\", \"text-lg\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-8\"], [\"title\", \"Rejeter l'appel\", 1, \"w-16\", \"h-16\", \"rounded-full\", \"bg-red-500\", \"hover:bg-red-600\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-200\", \"shadow-lg\", 3, \"click\"], [\"title\", \"Accepter l'appel\", 1, \"w-16\", \"h-16\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-200\", \"shadow-lg\", 3, \"click\"], [\"autoplay\", \"\"], [\"remoteAudio\", \"\"], [1, \"fixed\", \"inset-0\", \"z-60\", \"bg-black/50\", \"flex\", \"items-center\", \"justify-center\"], [1, \"bg-gray-800\", \"rounded-xl\", \"p-8\", \"text-center\", \"shadow-2xl\", \"border\", \"border-gray-700\"], [1, \"w-12\", \"h-12\", \"border-4\", \"border-cyan-400\", \"border-t-transparent\", \"rounded-full\", \"animate-spin\", \"mx-auto\", \"mb-4\"], [1, \"text-white\", \"text-lg\"]],\n      template: function CallInterfaceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, CallInterfaceComponent_div_1_Template, 20, 11, \"div\", 1);\n          i0.ɵɵtemplate(2, CallInterfaceComponent_div_2_Template, 9, 7, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function CallInterfaceComponent_Template_button_click_5_listener() {\n            return ctx.toggleMute();\n          });\n          i0.ɵɵelementStart(6, \"i\", 6);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, CallInterfaceComponent_button_8_Template, 3, 4, \"button\", 7);\n          i0.ɵɵelementStart(9, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function CallInterfaceComponent_Template_button_click_9_listener() {\n            return ctx.toggleSpeaker();\n          });\n          i0.ɵɵelementStart(10, \"i\", 6);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function CallInterfaceComponent_Template_button_click_12_listener() {\n            return ctx.endCall();\n          });\n          i0.ɵɵelementStart(13, \"i\", 6);\n          i0.ɵɵtext(14, \"call_end\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(15, CallInterfaceComponent_div_15_Template, 17, 5, \"div\", 9);\n          i0.ɵɵtemplate(16, CallInterfaceComponent_audio_16_Template, 2, 0, \"audio\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, CallInterfaceComponent_div_17_Template, 5, 1, \"div\", 11);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"opacity-100\", ctx.isVisible)(\"opacity-0\", !ctx.isVisible)(\"pointer-events-none\", !ctx.isVisible);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.callType === \"VIDEO\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.callType === \"AUDIO\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassMap(ctx.isMuted ? \"bg-red-500 hover:bg-red-600\" : \"bg-white/20 hover:bg-white/30\");\n          i0.ɵɵproperty(\"title\", ctx.isMuted ? \"Activer le micro\" : \"Couper le micro\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isMuted ? \"mic_off\" : \"mic\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.callType === \"VIDEO\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.isSpeakerOn ? \"bg-blue-500 hover:bg-blue-600\" : \"bg-white/20 hover:bg-white/30\");\n          i0.ɵɵproperty(\"title\", ctx.isSpeakerOn ? \"D\\u00E9sactiver haut-parleur\" : \"Activer haut-parleur\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isSpeakerOn ? \"volume_up\" : \"volume_down\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isIncoming && !ctx.isConnected);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.callType === \"AUDIO\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isVisible && !ctx.isConnected && !ctx.isIncoming);\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\"\\n\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImNhbGwtaW50ZXJmYWNlLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsc0ZBQXNGIiwiZmlsZSI6ImNhbGwtaW50ZXJmYWNlLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBDU1MgcG91ciBsZSBjb21wb3NhbnQgZCdpbnRlcmZhY2UgZCdhcHBlbCAtIFV0aWxpc2UgVGFpbHdpbmQgQ1NTIGRhbnMgbGUgdGVtcGxhdGUgKi9cbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbWVzc2FnZXMvY2FsbC1pbnRlcmZhY2UvY2FsbC1pbnRlcmZhY2UuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxzRkFBc0Y7O0FBRXRGLHdYQUF3WCIsInNvdXJjZXNDb250ZW50IjpbIi8qIENTUyBwb3VyIGxlIGNvbXBvc2FudCBkJ2ludGVyZmFjZSBkJ2FwcGVsIC0gVXRpbGlzZSBUYWlsd2luZCBDU1MgZGFucyBsZSB0ZW1wbGF0ZSAqL1xuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Subscription", "CallType", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵtemplate", "CallInterfaceComponent_div_1_div_3_div_8_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r7", "otherParticipant", "image", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "callStatus", "isConnected", "ctx_r10", "formatDuration", "callDuration", "ɵɵlistener", "CallInterfaceComponent_div_1_Template_video_click_1_listener", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "toggleFullscreen", "CallInterfaceComponent_div_1_div_3_Template", "CallInterfaceComponent_div_1_video_5_Template", "CallInterfaceComponent_div_1_div_6_Template", "CallInterfaceComponent_div_1_p_16_Template", "CallInterfaceComponent_div_1_Template_button_click_17_listener", "ctx_r15", "ɵɵclassProp", "ctx_r0", "remoteStream", "isVideoEnabled", "isFullscreen", "ctx_r16", "CallInterfaceComponent_div_2_p_8_Template", "ctx_r1", "CallInterfaceComponent_button_8_Template_button_click_0_listener", "_r18", "ctx_r17", "toggleVideo", "ɵɵclassMap", "ctx_r2", "CallInterfaceComponent_div_15_Template_button_click_11_listener", "_r20", "ctx_r19", "rejectCall", "CallInterfaceComponent_div_15_Template_button_click_14_listener", "ctx_r21", "acceptCall", "ctx_r3", "getCallTypeLabel", "getCallTypeIcon", "ctx_r5", "CallInterfaceComponent", "constructor", "messageService", "toastService", "cdr", "activeCall", "isVisible", "callType", "callEnded", "callAccepted", "callRejected", "isIncoming", "isOutgoing", "isMuted", "isSpeakerOn", "localStream", "callTimer", "subscriptions", "ngOnInit", "setupCallSubscriptions", "initializeMediaDevices", "ngOnDestroy", "cleanup", "add", "activeCall$", "subscribe", "next", "call", "updateCallStatus", "detectChanges", "error", "console", "localStream$", "stream", "attachLocalStream", "remoteStream$", "attachRemoteStream", "_this", "_asyncToGenerator", "constraints", "audio", "video", "navigator", "mediaDevices", "getUserMedia", "log", "showError", "localVideoRef", "nativeElement", "srcObject", "muted", "remoteVideoRef", "status", "startCallTimer", "endCall", "setInterval", "toggleMute", "audioTracks", "getAudioTracks", "for<PERSON>ach", "track", "enabled", "toggleMedia", "id", "undefined", "showSuccess", "videoTracks", "getVideoTracks", "toggleSpeaker", "volume", "requestFullscreen", "document", "fullscreenElement", "exitFullscreen", "emit", "clearInterval", "getTracks", "stop", "unsubscribe", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "ToastService", "ChangeDetectorRef", "selectors", "viewQuery", "CallInterfaceComponent_Query", "rf", "ctx", "CallInterfaceComponent_div_1_Template", "CallInterfaceComponent_div_2_Template", "CallInterfaceComponent_Template_button_click_5_listener", "CallInterfaceComponent_button_8_Template", "CallInterfaceComponent_Template_button_click_9_listener", "CallInterfaceComponent_Template_button_click_12_listener", "CallInterfaceComponent_div_15_Template", "CallInterfaceComponent_audio_16_Template", "CallInterfaceComponent_div_17_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\call-interface\\call-interface.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\call-interface\\call-interface.component.html"], "sourcesContent": ["import {\n  Component,\n  OnInit,\n  OnDestroy,\n  Input,\n  Output,\n  EventEmitter,\n  ViewChild,\n  ElementRef,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { MessageService } from '../../../../services/message.service';\nimport { ToastService } from '../../../../services/toast.service';\nimport { Call, CallType, User } from '../../../../models/message.model';\n\n@Component({\n  selector: 'app-call-interface',\n  templateUrl: './call-interface.component.html',\n  styleUrls: ['./call-interface.component.css'],\n})\nexport class CallInterfaceComponent implements OnInit, OnDestroy {\n  @Input() activeCall: Call | null = null;\n  @Input() isVisible: boolean = false;\n  @Input() callType: 'VIDEO' | 'AUDIO' | null = null;\n  @Input() otherParticipant: User | null = null;\n\n  @Output() callEnded = new EventEmitter<void>();\n  @Output() callAccepted = new EventEmitter<Call>();\n  @Output() callRejected = new EventEmitter<void>();\n\n  // Références aux éléments vidéo\n  @ViewChild('localVideo', { static: false })\n  localVideoRef!: ElementRef<HTMLVideoElement>;\n  @ViewChild('remoteVideo', { static: false })\n  remoteVideoRef!: ElementRef<HTMLVideoElement>;\n\n  // État de l'appel\n  isConnected = false;\n  isIncoming = false;\n  isOutgoing = false;\n  callDuration = 0;\n  callStatus = 'Connexion...';\n\n  // Contrôles\n  isMuted = false;\n  isVideoEnabled = true;\n  isSpeakerOn = false;\n  isFullscreen = false;\n\n  // Streams\n  localStream: MediaStream | null = null;\n  remoteStream: MediaStream | null = null;\n\n  // Timer\n  private callTimer: any = null;\n  private subscriptions = new Subscription();\n\n  // Types pour le template\n  CallType = CallType;\n\n  constructor(\n    private messageService: MessageService,\n    private toastService: ToastService,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnInit(): void {\n    this.setupCallSubscriptions();\n    this.initializeMediaDevices();\n  }\n\n  ngOnDestroy(): void {\n    this.cleanup();\n  }\n\n  private setupCallSubscriptions(): void {\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(\n      this.messageService.activeCall$.subscribe({\n        next: (call) => {\n          if (call) {\n            this.activeCall = call;\n            this.updateCallStatus();\n            this.cdr.detectChanges();\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in active call subscription:', error);\n        },\n      })\n    );\n\n    // S'abonner aux streams\n    this.subscriptions.add(\n      this.messageService.localStream$.subscribe({\n        next: (stream) => {\n          this.localStream = stream;\n          this.attachLocalStream();\n        },\n      })\n    );\n\n    this.subscriptions.add(\n      this.messageService.remoteStream$.subscribe({\n        next: (stream) => {\n          this.remoteStream = stream;\n          this.attachRemoteStream();\n        },\n      })\n    );\n  }\n\n  private async initializeMediaDevices(): Promise<void> {\n    if (!this.activeCall) return;\n\n    try {\n      const constraints = {\n        audio: true,\n        video: this.callType === 'VIDEO',\n      };\n\n      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);\n      this.attachLocalStream();\n\n      console.log('✅ Media devices initialized');\n    } catch (error) {\n      console.error('❌ Error accessing media devices:', error);\n      this.toastService.showError(\"Erreur d'accès à la caméra/micro\");\n    }\n  }\n\n  private attachLocalStream(): void {\n    if (this.localStream && this.localVideoRef?.nativeElement) {\n      this.localVideoRef.nativeElement.srcObject = this.localStream;\n      this.localVideoRef.nativeElement.muted = true; // Éviter l'écho\n    }\n  }\n\n  private attachRemoteStream(): void {\n    if (this.remoteStream && this.remoteVideoRef?.nativeElement) {\n      this.remoteVideoRef.nativeElement.srcObject = this.remoteStream;\n    }\n  }\n\n  private updateCallStatus(): void {\n    if (!this.activeCall) return;\n\n    switch (this.activeCall.status as string) {\n      case 'ringing':\n        this.callStatus = this.isIncoming\n          ? 'Appel entrant...'\n          : 'Appel en cours...';\n        break;\n      case 'accepted':\n        this.callStatus = 'Connexion...';\n        break;\n      case 'connected':\n        this.callStatus = 'Connecté';\n        this.isConnected = true;\n        this.startCallTimer();\n        break;\n      case 'ended':\n        this.callStatus = 'Appel terminé';\n        this.endCall();\n        break;\n      case 'rejected':\n        this.callStatus = 'Appel rejeté';\n        this.endCall();\n        break;\n      default:\n        this.callStatus = 'En cours...';\n    }\n  }\n\n  private startCallTimer(): void {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n\n  // === CONTRÔLES D'APPEL ===\n  toggleMute(): void {\n    if (!this.activeCall) return;\n\n    this.isMuted = !this.isMuted;\n\n    // Couper/activer le micro dans le stream local\n    if (this.localStream) {\n      const audioTracks = this.localStream.getAudioTracks();\n      audioTracks.forEach((track) => {\n        track.enabled = !this.isMuted;\n      });\n    }\n\n    // Notifier via le service\n    this.messageService\n      .toggleMedia(this.activeCall.id, undefined, !this.isMuted)\n      .subscribe({\n        next: () => {\n          this.toastService.showSuccess(\n            this.isMuted ? 'Micro coupé' : 'Micro activé'\n          );\n        },\n        error: (error) => {\n          console.error('❌ Error toggling mute:', error);\n          // Revert state on error\n          this.isMuted = !this.isMuted;\n          if (this.localStream) {\n            const audioTracks = this.localStream.getAudioTracks();\n            audioTracks.forEach((track) => {\n              track.enabled = !this.isMuted;\n            });\n          }\n        },\n      });\n  }\n\n  toggleVideo(): void {\n    if (!this.activeCall || this.callType !== 'VIDEO') return;\n\n    this.isVideoEnabled = !this.isVideoEnabled;\n\n    // Couper/activer la vidéo dans le stream local\n    if (this.localStream) {\n      const videoTracks = this.localStream.getVideoTracks();\n      videoTracks.forEach((track) => {\n        track.enabled = this.isVideoEnabled;\n      });\n    }\n\n    // Notifier via le service\n    this.messageService\n      .toggleMedia(this.activeCall.id, this.isVideoEnabled, undefined)\n      .subscribe({\n        next: () => {\n          this.toastService.showSuccess(\n            this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\n          );\n        },\n        error: (error) => {\n          console.error('❌ Error toggling video:', error);\n          // Revert state on error\n          this.isVideoEnabled = !this.isVideoEnabled;\n          if (this.localStream) {\n            const videoTracks = this.localStream.getVideoTracks();\n            videoTracks.forEach((track) => {\n              track.enabled = this.isVideoEnabled;\n            });\n          }\n        },\n      });\n  }\n\n  toggleSpeaker(): void {\n    this.isSpeakerOn = !this.isSpeakerOn;\n\n    if (this.remoteVideoRef?.nativeElement) {\n      // Changer la sortie audio (si supporté par le navigateur)\n      this.remoteVideoRef.nativeElement.volume = this.isSpeakerOn ? 1 : 0.7;\n    }\n\n    this.toastService.showSuccess(\n      this.isSpeakerOn ? 'Haut-parleur activé' : 'Haut-parleur désactivé'\n    );\n  }\n\n  toggleFullscreen(): void {\n    this.isFullscreen = !this.isFullscreen;\n\n    if (this.isFullscreen && this.remoteVideoRef?.nativeElement) {\n      this.remoteVideoRef.nativeElement.requestFullscreen?.();\n    } else if (document.fullscreenElement) {\n      document.exitFullscreen?.();\n    }\n  }\n\n  acceptCall(): void {\n    if (!this.activeCall) return;\n\n    this.callAccepted.emit(this.activeCall);\n  }\n\n  rejectCall(): void {\n    this.callRejected.emit();\n  }\n\n  endCall(): void {\n    this.cleanup();\n    this.callEnded.emit();\n  }\n\n  private cleanup(): void {\n    // Arrêter le timer\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n\n    // Arrêter les streams\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n    }\n\n    if (this.remoteStream) {\n      this.remoteStream.getTracks().forEach((track) => track.stop());\n      this.remoteStream = null;\n    }\n\n    // Nettoyer les subscriptions\n    this.subscriptions.unsubscribe();\n\n    // Réinitialiser l'état\n    this.isConnected = false;\n    this.callDuration = 0;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.isSpeakerOn = false;\n    this.isFullscreen = false;\n  }\n\n  // === UTILITAIRES ===\n  formatDuration(seconds: number): string {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs\n      .toString()\n      .padStart(2, '0')}`;\n  }\n\n  getCallTypeIcon(): string {\n    return this.callType === 'VIDEO' ? 'videocam' : 'call';\n  }\n\n  getCallTypeLabel(): string {\n    return this.callType === 'VIDEO' ? 'Appel vidéo' : 'Appel audio';\n  }\n}\n", "<!-- Interface d'appel WebRTC avec Tailwind CSS -->\n<div\n  class=\"fixed inset-0 z-50 transition-all duration-300 bg-gray-900 dark:bg-gray-800\"\n  [class.opacity-100]=\"isVisible\"\n  [class.opacity-0]=\"!isVisible\"\n  [class.pointer-events-none]=\"!isVisible\"\n  style=\"\n    background: linear-gradient(135deg, #111827 0%, #1f2937 50%, #374151 100%);\n  \"\n>\n  <!-- === APPEL VIDÉO === -->\n  <div *ngIf=\"callType === 'VIDEO'\" class=\"relative w-full h-full\">\n    <!-- Vidéo distante (plein écran) -->\n    <video\n      #remoteVideo\n      class=\"w-full h-full object-cover bg-gray-800 dark:bg-gray-900\"\n      [class.cursor-pointer]=\"remoteStream\"\n      autoplay\n      playsinline\n      (click)=\"toggleFullscreen()\"\n    ></video>\n\n    <!-- Placeholder si pas de vidéo distante -->\n    <div\n      *ngIf=\"!remoteStream\"\n      class=\"absolute inset-0 flex flex-col items-center justify-center\"\n      style=\"\n        background: linear-gradient(\n          135deg,\n          #1e40af 0%,\n          #3b82f6 50%,\n          #06b6d4 100%\n        );\n      \"\n    >\n      <div class=\"relative mb-8\">\n        <img\n          [src]=\"otherParticipant?.image || '/assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-32 h-32 rounded-full border-4 border-white/30 shadow-2xl\"\n        />\n        <div\n          class=\"absolute inset-0 rounded-full border-4 border-cyan-400 animate-pulse opacity-70\"\n        ></div>\n      </div>\n      <h2 class=\"text-3xl font-bold text-white mb-2 drop-shadow-lg\">\n        {{ otherParticipant?.username }}\n      </h2>\n      <p class=\"text-cyan-200 text-lg font-medium\">{{ callStatus }}</p>\n\n      <!-- Indicateur de connexion -->\n      <div *ngIf=\"!isConnected\" class=\"mt-4 flex items-center space-x-2\">\n        <div class=\"w-3 h-3 bg-cyan-400 rounded-full animate-bounce\"></div>\n        <div\n          class=\"w-3 h-3 bg-cyan-400 rounded-full animate-bounce\"\n          style=\"animation-delay: 0.1s\"\n        ></div>\n        <div\n          class=\"w-3 h-3 bg-cyan-400 rounded-full animate-bounce\"\n          style=\"animation-delay: 0.2s\"\n        ></div>\n      </div>\n    </div>\n\n    <!-- Vidéo locale (picture-in-picture) -->\n    <div\n      class=\"absolute top-4 right-4 w-32 h-24 bg-gray-800 rounded-lg overflow-hidden border-2 border-white/20 shadow-lg\"\n    >\n      <video\n        #localVideo\n        *ngIf=\"isVideoEnabled\"\n        class=\"w-full h-full object-cover\"\n        autoplay\n        playsinline\n        muted\n      ></video>\n\n      <!-- Placeholder vidéo locale désactivée -->\n      <div\n        *ngIf=\"!isVideoEnabled\"\n        class=\"w-full h-full bg-gray-700 flex items-center justify-center\"\n      >\n        <i class=\"material-icons text-white text-2xl\">videocam_off</i>\n      </div>\n    </div>\n\n    <!-- En-tête d'appel vidéo -->\n    <div\n      class=\"absolute top-0 left-0 right-0 bg-gradient-to-b from-black/50 to-transparent p-4\"\n    >\n      <div class=\"flex items-center justify-between\">\n        <div class=\"flex items-center space-x-3\">\n          <img\n            [src]=\"\n              otherParticipant?.image || '/assets/images/default-avatar.png'\n            \"\n            [alt]=\"otherParticipant?.username\"\n            class=\"w-10 h-10 rounded-full border-2 border-white/30\"\n          />\n          <div>\n            <p class=\"text-white font-medium\">\n              {{ otherParticipant?.username }}\n            </p>\n            <p class=\"text-cyan-300 text-sm\">{{ callStatus }}</p>\n            <p\n              *ngIf=\"isConnected && callDuration > 0\"\n              class=\"text-green-400 text-sm\"\n            >\n              {{ formatDuration(callDuration) }}\n            </p>\n          </div>\n        </div>\n\n        <button\n          (click)=\"toggleFullscreen()\"\n          class=\"p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors\"\n        >\n          <i class=\"material-icons text-white\">{{\n            isFullscreen ? \"fullscreen_exit\" : \"fullscreen\"\n          }}</i>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- === APPEL AUDIO === -->\n  <div\n    *ngIf=\"callType === 'AUDIO'\"\n    class=\"flex flex-col items-center justify-center h-full p-8\"\n  >\n    <!-- Avatar principal -->\n    <div class=\"relative mb-8\">\n      <img\n        [src]=\"otherParticipant?.image || '/assets/images/default-avatar.png'\"\n        [alt]=\"otherParticipant?.username\"\n        class=\"w-48 h-48 rounded-full border-4 border-white/20 shadow-2xl\"\n      />\n      <div\n        class=\"absolute inset-0 rounded-full border-4 border-cyan-400\"\n        [class.animate-pulse]=\"!isConnected\"\n      ></div>\n    </div>\n\n    <!-- Informations utilisateur -->\n    <h2 class=\"text-4xl font-bold text-white mb-4\">\n      {{ otherParticipant?.username }}\n    </h2>\n    <p class=\"text-cyan-300 text-xl mb-2\">{{ callStatus }}</p>\n    <p\n      *ngIf=\"isConnected && callDuration > 0\"\n      class=\"text-green-400 text-2xl font-mono\"\n    >\n      {{ formatDuration(callDuration) }}\n    </p>\n  </div>\n\n  <!-- === CONTRÔLES D'APPEL === -->\n  <div\n    class=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6\"\n  >\n    <div class=\"flex items-center justify-center space-x-6\">\n      <!-- Bouton Micro -->\n      <button\n        (click)=\"toggleMute()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg\"\n        [class]=\"\n          isMuted\n            ? 'bg-red-500 hover:bg-red-600'\n            : 'bg-white/20 hover:bg-white/30'\n        \"\n        [title]=\"isMuted ? 'Activer le micro' : 'Couper le micro'\"\n      >\n        <i class=\"material-icons text-white text-2xl\">{{\n          isMuted ? \"mic_off\" : \"mic\"\n        }}</i>\n      </button>\n\n      <!-- Bouton Vidéo (seulement pour appels vidéo) -->\n      <button\n        *ngIf=\"callType === 'VIDEO'\"\n        (click)=\"toggleVideo()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg\"\n        [class]=\"\n          !isVideoEnabled\n            ? 'bg-red-500 hover:bg-red-600'\n            : 'bg-white/20 hover:bg-white/30'\n        \"\n        [title]=\"isVideoEnabled ? 'Désactiver la caméra' : 'Activer la caméra'\"\n      >\n        <i class=\"material-icons text-white text-2xl\">{{\n          isVideoEnabled ? \"videocam\" : \"videocam_off\"\n        }}</i>\n      </button>\n\n      <!-- Bouton Haut-parleur -->\n      <button\n        (click)=\"toggleSpeaker()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg\"\n        [class]=\"\n          isSpeakerOn\n            ? 'bg-blue-500 hover:bg-blue-600'\n            : 'bg-white/20 hover:bg-white/30'\n        \"\n        [title]=\"\n          isSpeakerOn ? 'Désactiver haut-parleur' : 'Activer haut-parleur'\n        \"\n      >\n        <i class=\"material-icons text-white text-2xl\">{{\n          isSpeakerOn ? \"volume_up\" : \"volume_down\"\n        }}</i>\n      </button>\n\n      <!-- Bouton Raccrocher -->\n      <button\n        (click)=\"endCall()\"\n        class=\"w-16 h-16 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all duration-200 shadow-lg\"\n        title=\"Raccrocher\"\n      >\n        <i class=\"material-icons text-white text-2xl\">call_end</i>\n      </button>\n    </div>\n  </div>\n\n  <!-- === MODAL APPEL ENTRANT === -->\n  <div\n    *ngIf=\"isIncoming && !isConnected\"\n    class=\"absolute inset-0 bg-black/80 flex items-center justify-center p-8\"\n  >\n    <div\n      class=\"bg-gray-800 rounded-2xl p-8 max-w-sm w-full text-center shadow-2xl border border-gray-700\"\n    >\n      <!-- Avatar et info -->\n      <div class=\"mb-8\">\n        <div class=\"relative inline-block mb-4\">\n          <img\n            [src]=\"\n              otherParticipant?.image || '/assets/images/default-avatar.png'\n            \"\n            [alt]=\"otherParticipant?.username\"\n            class=\"w-24 h-24 rounded-full border-4 border-white/20\"\n          />\n          <div\n            class=\"absolute inset-0 rounded-full border-4 border-cyan-400 animate-pulse\"\n          ></div>\n        </div>\n\n        <h2 class=\"text-2xl font-bold text-white mb-2\">\n          {{ otherParticipant?.username }}\n        </h2>\n        <p class=\"text-cyan-300 text-lg\">{{ getCallTypeLabel() }} entrant...</p>\n      </div>\n\n      <!-- Boutons d'action -->\n      <div class=\"flex items-center justify-center space-x-8\">\n        <button\n          (click)=\"rejectCall()\"\n          class=\"w-16 h-16 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all duration-200 shadow-lg\"\n          title=\"Rejeter l'appel\"\n        >\n          <i class=\"material-icons text-white text-2xl\">call_end</i>\n        </button>\n\n        <button\n          (click)=\"acceptCall()\"\n          class=\"w-16 h-16 rounded-full bg-green-500 hover:bg-green-600 flex items-center justify-center transition-all duration-200 shadow-lg\"\n          title=\"Accepter l'appel\"\n        >\n          <i class=\"material-icons text-white text-2xl\">{{\n            getCallTypeIcon()\n          }}</i>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Audio éléments (cachés) -->\n  <audio #remoteAudio autoplay *ngIf=\"callType === 'AUDIO'\"></audio>\n</div>\n\n<!-- Overlay de chargement -->\n<div\n  *ngIf=\"isVisible && !isConnected && !isIncoming\"\n  class=\"fixed inset-0 z-60 bg-black/50 flex items-center justify-center\"\n>\n  <div\n    class=\"bg-gray-800 rounded-xl p-8 text-center shadow-2xl border border-gray-700\"\n  >\n    <div\n      class=\"w-12 h-12 border-4 border-cyan-400 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n    ></div>\n    <p class=\"text-white text-lg\">{{ callStatus }}</p>\n  </div>\n</div>\n"], "mappings": ";AAAA,SAMEA,YAAY,QAIP,eAAe;AACtB,SAASC,YAAY,QAAQ,MAAM;AAGnC,SAAeC,QAAQ,QAAc,kCAAkC;;;;;;;;;ICqCjEC,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAE,SAAA,cAAmE;IASrEF,EAAA,CAAAG,YAAA,EAAM;;;;;IAtCRH,EAAA,CAAAC,cAAA,cAWC;IAEGD,EAAA,CAAAE,SAAA,cAIE;IAIJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA8D;IAC5DD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAA6C;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGjEH,EAAA,CAAAK,UAAA,IAAAC,iDAAA,kBAUM;IACRN,EAAA,CAAAG,YAAA,EAAM;;;;IAzBAH,EAAA,CAAAO,SAAA,GAAsE;IAAtEP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAC,KAAA,0CAAAX,EAAA,CAAAY,aAAA,CAAsE,QAAAH,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,QAAA;IASxEb,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,QAAA,MACF;IAC6Cb,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAe,iBAAA,CAAAN,MAAA,CAAAO,UAAA,CAAgB;IAGvDhB,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,UAAA,UAAAC,MAAA,CAAAQ,WAAA,CAAkB;;;;;IAiBxBjB,EAAA,CAAAE,SAAA,oBAOS;;;;;IAGTF,EAAA,CAAAC,cAAA,cAGC;IAC+CD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAsB1DH,EAAA,CAAAC,cAAA,YAGC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAI,OAAA,CAAAC,cAAA,CAAAD,OAAA,CAAAE,YAAA,OACF;;;;;;IAlGVpB,EAAA,CAAAC,cAAA,cAAiE;IAQ7DD,EAAA,CAAAqB,UAAA,mBAAAC,6DAAA;MAAAtB,EAAA,CAAAuB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAF,OAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAC7B5B,EAAA,CAAAG,YAAA,EAAQ;IAGTH,EAAA,CAAAK,UAAA,IAAAwB,2CAAA,kBAuCM;IAGN7B,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAK,UAAA,IAAAyB,6CAAA,oBAOS;IAGT9B,EAAA,CAAAK,UAAA,IAAA0B,2CAAA,kBAKM;IACR/B,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAEC;IAGKD,EAAA,CAAAE,SAAA,eAME;IACFF,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAI,MAAA,IAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACrDH,EAAA,CAAAK,UAAA,KAAA2B,0CAAA,gBAKI;IACNhC,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAqB,UAAA,mBAAAY,+DAAA;MAAAjC,EAAA,CAAAuB,aAAA,CAAAC,IAAA;MAAA,MAAAU,OAAA,GAAAlC,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAO,OAAA,CAAAN,gBAAA,EAAkB;IAAA,EAAC;IAG5B5B,EAAA,CAAAC,cAAA,aAAqC;IAAAD,EAAA,CAAAI,MAAA,IAEnC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAvGVH,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAmC,WAAA,mBAAAC,MAAA,CAAAC,YAAA,CAAqC;IAQpCrC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAQ,UAAA,UAAA4B,MAAA,CAAAC,YAAA,CAAmB;IA8CjBrC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,UAAA,SAAA4B,MAAA,CAAAE,cAAA,CAAoB;IASpBtC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,UAAA,UAAA4B,MAAA,CAAAE,cAAA,CAAqB;IAclBtC,EAAA,CAAAO,SAAA,GAEC;IAFDP,EAAA,CAAAQ,UAAA,SAAA4B,MAAA,CAAA1B,gBAAA,kBAAA0B,MAAA,CAAA1B,gBAAA,CAAAC,KAAA,0CAAAX,EAAA,CAAAY,aAAA,CAEC,QAAAwB,MAAA,CAAA1B,gBAAA,kBAAA0B,MAAA,CAAA1B,gBAAA,CAAAG,QAAA;IAMCb,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAsB,MAAA,CAAA1B,gBAAA,kBAAA0B,MAAA,CAAA1B,gBAAA,CAAAG,QAAA,MACF;IACiCb,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAe,iBAAA,CAAAqB,MAAA,CAAApB,UAAA,CAAgB;IAE9ChB,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAQ,UAAA,SAAA4B,MAAA,CAAAnB,WAAA,IAAAmB,MAAA,CAAAhB,YAAA,KAAqC;IAYLpB,EAAA,CAAAO,SAAA,GAEnC;IAFmCP,EAAA,CAAAe,iBAAA,CAAAqB,MAAA,CAAAG,YAAA,oCAEnC;;;;;IA6BRvC,EAAA,CAAAC,cAAA,YAGC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAA0B,OAAA,CAAArB,cAAA,CAAAqB,OAAA,CAAApB,YAAA,OACF;;;;;IA3BFpB,EAAA,CAAAC,cAAA,cAGC;IAGGD,EAAA,CAAAE,SAAA,cAIE;IAKJF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsC;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC1DH,EAAA,CAAAK,UAAA,IAAAoC,yCAAA,gBAKI;IACNzC,EAAA,CAAAG,YAAA,EAAM;;;;IArBAH,EAAA,CAAAO,SAAA,GAAsE;IAAtEP,EAAA,CAAAQ,UAAA,SAAAkC,MAAA,CAAAhC,gBAAA,kBAAAgC,MAAA,CAAAhC,gBAAA,CAAAC,KAAA,0CAAAX,EAAA,CAAAY,aAAA,CAAsE,QAAA8B,MAAA,CAAAhC,gBAAA,kBAAAgC,MAAA,CAAAhC,gBAAA,CAAAG,QAAA;IAMtEb,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAmC,WAAA,mBAAAO,MAAA,CAAAzB,WAAA,CAAoC;IAMtCjB,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAA4B,MAAA,CAAAhC,gBAAA,kBAAAgC,MAAA,CAAAhC,gBAAA,CAAAG,QAAA,MACF;IACsCb,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAe,iBAAA,CAAA2B,MAAA,CAAA1B,UAAA,CAAgB;IAEnDhB,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAQ,UAAA,SAAAkC,MAAA,CAAAzB,WAAA,IAAAyB,MAAA,CAAAtB,YAAA,KAAqC;;;;;;IA6BtCpB,EAAA,CAAAC,cAAA,gBAUC;IARCD,EAAA,CAAAqB,UAAA,mBAAAsB,iEAAA;MAAA3C,EAAA,CAAAuB,aAAA,CAAAqB,IAAA;MAAA,MAAAC,OAAA,GAAA7C,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAkB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IASvB9C,EAAA,CAAAC,cAAA,WAA8C;IAAAD,EAAA,CAAAI,MAAA,GAE5C;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IATNH,EAAA,CAAA+C,UAAA,EAAAC,MAAA,CAAAV,cAAA,mEAIC;IACDtC,EAAA,CAAAQ,UAAA,UAAAwC,MAAA,CAAAV,cAAA,+DAAuE;IAEzBtC,EAAA,CAAAO,SAAA,GAE5C;IAF4CP,EAAA,CAAAe,iBAAA,CAAAiC,MAAA,CAAAV,cAAA,+BAE5C;;;;;;IAiCRtC,EAAA,CAAAC,cAAA,cAGC;IAOOD,EAAA,CAAAE,SAAA,cAME;IAIJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAI,MAAA,GAAmC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAI1EH,EAAA,CAAAC,cAAA,eAAwD;IAEpDD,EAAA,CAAAqB,UAAA,mBAAA4B,gEAAA;MAAAjD,EAAA,CAAAuB,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAwB,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAItBpD,EAAA,CAAAC,cAAA,YAA8C;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAG5DH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAqB,UAAA,mBAAAgC,gEAAA;MAAArD,EAAA,CAAAuB,aAAA,CAAA2B,IAAA;MAAA,MAAAI,OAAA,GAAAtD,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAA2B,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAItBvD,EAAA,CAAAC,cAAA,YAA8C;IAAAD,EAAA,CAAAI,MAAA,IAE5C;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAlCJH,EAAA,CAAAO,SAAA,GAEC;IAFDP,EAAA,CAAAQ,UAAA,SAAAgD,MAAA,CAAA9C,gBAAA,kBAAA8C,MAAA,CAAA9C,gBAAA,CAAAC,KAAA,0CAAAX,EAAA,CAAAY,aAAA,CAEC,QAAA4C,MAAA,CAAA9C,gBAAA,kBAAA8C,MAAA,CAAA9C,gBAAA,CAAAG,QAAA;IAUHb,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAA0C,MAAA,CAAA9C,gBAAA,kBAAA8C,MAAA,CAAA9C,gBAAA,CAAAG,QAAA,MACF;IACiCb,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAc,kBAAA,KAAA0C,MAAA,CAAAC,gBAAA,kBAAmC;IAkBpBzD,EAAA,CAAAO,SAAA,GAE5C;IAF4CP,EAAA,CAAAe,iBAAA,CAAAyC,MAAA,CAAAE,eAAA,GAE5C;;;;;IAOV1D,EAAA,CAAAE,SAAA,oBAAkE;;;;;IAIpEF,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAApBH,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAe,iBAAA,CAAA4C,MAAA,CAAA3C,UAAA,CAAgB;;;AD7QlD,OAAM,MAAO4C,sBAAsB;EAwCjCC,YACUC,cAA8B,EAC9BC,YAA0B,EAC1BC,GAAsB;IAFtB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IA1CJ,KAAAC,UAAU,GAAgB,IAAI;IAC9B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAzD,gBAAgB,GAAgB,IAAI;IAEnC,KAAA0D,SAAS,GAAG,IAAIvE,YAAY,EAAQ;IACpC,KAAAwE,YAAY,GAAG,IAAIxE,YAAY,EAAQ;IACvC,KAAAyE,YAAY,GAAG,IAAIzE,YAAY,EAAQ;IAQjD;IACA,KAAAoB,WAAW,GAAG,KAAK;IACnB,KAAAsD,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAApD,YAAY,GAAG,CAAC;IAChB,KAAAJ,UAAU,GAAG,cAAc;IAE3B;IACA,KAAAyD,OAAO,GAAG,KAAK;IACf,KAAAnC,cAAc,GAAG,IAAI;IACrB,KAAAoC,WAAW,GAAG,KAAK;IACnB,KAAAnC,YAAY,GAAG,KAAK;IAEpB;IACA,KAAAoC,WAAW,GAAuB,IAAI;IACtC,KAAAtC,YAAY,GAAuB,IAAI;IAEvC;IACQ,KAAAuC,SAAS,GAAQ,IAAI;IACrB,KAAAC,aAAa,GAAG,IAAI/E,YAAY,EAAE;IAE1C;IACA,KAAAC,QAAQ,GAAGA,QAAQ;EAMhB;EAEH+E,QAAQA,CAAA;IACN,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACC,OAAO,EAAE;EAChB;EAEQH,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAACF,aAAa,CAACM,GAAG,CACpB,IAAI,CAACrB,cAAc,CAACsB,WAAW,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAACtB,UAAU,GAAGsB,IAAI;UACtB,IAAI,CAACC,gBAAgB,EAAE;UACvB,IAAI,CAACxB,GAAG,CAACyB,aAAa,EAAE;;MAE5B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC9D;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACb,aAAa,CAACM,GAAG,CACpB,IAAI,CAACrB,cAAc,CAAC8B,YAAY,CAACP,SAAS,CAAC;MACzCC,IAAI,EAAGO,MAAM,IAAI;QACf,IAAI,CAAClB,WAAW,GAAGkB,MAAM;QACzB,IAAI,CAACC,iBAAiB,EAAE;MAC1B;KACD,CAAC,CACH;IAED,IAAI,CAACjB,aAAa,CAACM,GAAG,CACpB,IAAI,CAACrB,cAAc,CAACiC,aAAa,CAACV,SAAS,CAAC;MAC1CC,IAAI,EAAGO,MAAM,IAAI;QACf,IAAI,CAACxD,YAAY,GAAGwD,MAAM;QAC1B,IAAI,CAACG,kBAAkB,EAAE;MAC3B;KACD,CAAC,CACH;EACH;EAEchB,sBAAsBA,CAAA;IAAA,IAAAiB,KAAA;IAAA,OAAAC,iBAAA;MAClC,IAAI,CAACD,KAAI,CAAChC,UAAU,EAAE;MAEtB,IAAI;QACF,MAAMkC,WAAW,GAAG;UAClBC,KAAK,EAAE,IAAI;UACXC,KAAK,EAAEJ,KAAI,CAAC9B,QAAQ,KAAK;SAC1B;QAED8B,KAAI,CAACtB,WAAW,SAAS2B,SAAS,CAACC,YAAY,CAACC,YAAY,CAACL,WAAW,CAAC;QACzEF,KAAI,CAACH,iBAAiB,EAAE;QAExBH,OAAO,CAACc,GAAG,CAAC,6BAA6B,CAAC;OAC3C,CAAC,OAAOf,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDO,KAAI,CAAClC,YAAY,CAAC2C,SAAS,CAAC,kCAAkC,CAAC;;IAChE;EACH;EAEQZ,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACnB,WAAW,IAAI,IAAI,CAACgC,aAAa,EAAEC,aAAa,EAAE;MACzD,IAAI,CAACD,aAAa,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI,CAAClC,WAAW;MAC7D,IAAI,CAACgC,aAAa,CAACC,aAAa,CAACE,KAAK,GAAG,IAAI,CAAC,CAAC;;EAEnD;;EAEQd,kBAAkBA,CAAA;IACxB,IAAI,IAAI,CAAC3D,YAAY,IAAI,IAAI,CAAC0E,cAAc,EAAEH,aAAa,EAAE;MAC3D,IAAI,CAACG,cAAc,CAACH,aAAa,CAACC,SAAS,GAAG,IAAI,CAACxE,YAAY;;EAEnE;EAEQmD,gBAAgBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACvB,UAAU,EAAE;IAEtB,QAAQ,IAAI,CAACA,UAAU,CAAC+C,MAAgB;MACtC,KAAK,SAAS;QACZ,IAAI,CAAChG,UAAU,GAAG,IAAI,CAACuD,UAAU,GAC7B,kBAAkB,GAClB,mBAAmB;QACvB;MACF,KAAK,UAAU;QACb,IAAI,CAACvD,UAAU,GAAG,cAAc;QAChC;MACF,KAAK,WAAW;QACd,IAAI,CAACA,UAAU,GAAG,UAAU;QAC5B,IAAI,CAACC,WAAW,GAAG,IAAI;QACvB,IAAI,CAACgG,cAAc,EAAE;QACrB;MACF,KAAK,OAAO;QACV,IAAI,CAACjG,UAAU,GAAG,eAAe;QACjC,IAAI,CAACkG,OAAO,EAAE;QACd;MACF,KAAK,UAAU;QACb,IAAI,CAAClG,UAAU,GAAG,cAAc;QAChC,IAAI,CAACkG,OAAO,EAAE;QACd;MACF;QACE,IAAI,CAAClG,UAAU,GAAG,aAAa;;EAErC;EAEQiG,cAAcA,CAAA;IACpB,IAAI,CAAC7F,YAAY,GAAG,CAAC;IACrB,IAAI,CAACwD,SAAS,GAAGuC,WAAW,CAAC,MAAK;MAChC,IAAI,CAAC/F,YAAY,EAAE;MACnB,IAAI,CAAC4C,GAAG,CAACyB,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACA2B,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACnD,UAAU,EAAE;IAEtB,IAAI,CAACQ,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,IAAI,IAAI,CAACE,WAAW,EAAE;MACpB,MAAM0C,WAAW,GAAG,IAAI,CAAC1C,WAAW,CAAC2C,cAAc,EAAE;MACrDD,WAAW,CAACE,OAAO,CAAEC,KAAK,IAAI;QAC5BA,KAAK,CAACC,OAAO,GAAG,CAAC,IAAI,CAAChD,OAAO;MAC/B,CAAC,CAAC;;IAGJ;IACA,IAAI,CAACX,cAAc,CAChB4D,WAAW,CAAC,IAAI,CAACzD,UAAU,CAAC0D,EAAE,EAAEC,SAAS,EAAE,CAAC,IAAI,CAACnD,OAAO,CAAC,CACzDY,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvB,YAAY,CAAC8D,WAAW,CAC3B,IAAI,CAACpD,OAAO,GAAG,aAAa,GAAG,cAAc,CAC9C;MACH,CAAC;MACDiB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA,IAAI,CAACjB,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;QAC5B,IAAI,IAAI,CAACE,WAAW,EAAE;UACpB,MAAM0C,WAAW,GAAG,IAAI,CAAC1C,WAAW,CAAC2C,cAAc,EAAE;UACrDD,WAAW,CAACE,OAAO,CAAEC,KAAK,IAAI;YAC5BA,KAAK,CAACC,OAAO,GAAG,CAAC,IAAI,CAAChD,OAAO;UAC/B,CAAC,CAAC;;MAEN;KACD,CAAC;EACN;EAEA3B,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACmB,UAAU,IAAI,IAAI,CAACE,QAAQ,KAAK,OAAO,EAAE;IAEnD,IAAI,CAAC7B,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C;IACA,IAAI,IAAI,CAACqC,WAAW,EAAE;MACpB,MAAMmD,WAAW,GAAG,IAAI,CAACnD,WAAW,CAACoD,cAAc,EAAE;MACrDD,WAAW,CAACP,OAAO,CAAEC,KAAK,IAAI;QAC5BA,KAAK,CAACC,OAAO,GAAG,IAAI,CAACnF,cAAc;MACrC,CAAC,CAAC;;IAGJ;IACA,IAAI,CAACwB,cAAc,CAChB4D,WAAW,CAAC,IAAI,CAACzD,UAAU,CAAC0D,EAAE,EAAE,IAAI,CAACrF,cAAc,EAAEsF,SAAS,CAAC,CAC/DvC,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvB,YAAY,CAAC8D,WAAW,CAC3B,IAAI,CAACvF,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDoD,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;QACA,IAAI,CAACpD,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,IAAI,CAACqC,WAAW,EAAE;UACpB,MAAMmD,WAAW,GAAG,IAAI,CAACnD,WAAW,CAACoD,cAAc,EAAE;UACrDD,WAAW,CAACP,OAAO,CAAEC,KAAK,IAAI;YAC5BA,KAAK,CAACC,OAAO,GAAG,IAAI,CAACnF,cAAc;UACrC,CAAC,CAAC;;MAEN;KACD,CAAC;EACN;EAEA0F,aAAaA,CAAA;IACX,IAAI,CAACtD,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,IAAI,CAACqC,cAAc,EAAEH,aAAa,EAAE;MACtC;MACA,IAAI,CAACG,cAAc,CAACH,aAAa,CAACqB,MAAM,GAAG,IAAI,CAACvD,WAAW,GAAG,CAAC,GAAG,GAAG;;IAGvE,IAAI,CAACX,YAAY,CAAC8D,WAAW,CAC3B,IAAI,CAACnD,WAAW,GAAG,qBAAqB,GAAG,wBAAwB,CACpE;EACH;EAEA9C,gBAAgBA,CAAA;IACd,IAAI,CAACW,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IAEtC,IAAI,IAAI,CAACA,YAAY,IAAI,IAAI,CAACwE,cAAc,EAAEH,aAAa,EAAE;MAC3D,IAAI,CAACG,cAAc,CAACH,aAAa,CAACsB,iBAAiB,GAAE,CAAE;KACxD,MAAM,IAAIC,QAAQ,CAACC,iBAAiB,EAAE;MACrCD,QAAQ,CAACE,cAAc,GAAE,CAAE;;EAE/B;EAEA9E,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACU,UAAU,EAAE;IAEtB,IAAI,CAACI,YAAY,CAACiE,IAAI,CAAC,IAAI,CAACrE,UAAU,CAAC;EACzC;EAEAb,UAAUA,CAAA;IACR,IAAI,CAACkB,YAAY,CAACgE,IAAI,EAAE;EAC1B;EAEApB,OAAOA,CAAA;IACL,IAAI,CAAChC,OAAO,EAAE;IACd,IAAI,CAACd,SAAS,CAACkE,IAAI,EAAE;EACvB;EAEQpD,OAAOA,CAAA;IACb;IACA,IAAI,IAAI,CAACN,SAAS,EAAE;MAClB2D,aAAa,CAAC,IAAI,CAAC3D,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB;IACA,IAAI,IAAI,CAACD,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC6D,SAAS,EAAE,CAACjB,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACiB,IAAI,EAAE,CAAC;MAC7D,IAAI,CAAC9D,WAAW,GAAG,IAAI;;IAGzB,IAAI,IAAI,CAACtC,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACmG,SAAS,EAAE,CAACjB,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACiB,IAAI,EAAE,CAAC;MAC9D,IAAI,CAACpG,YAAY,GAAG,IAAI;;IAG1B;IACA,IAAI,CAACwC,aAAa,CAAC6D,WAAW,EAAE;IAEhC;IACA,IAAI,CAACzH,WAAW,GAAG,KAAK;IACxB,IAAI,CAACG,YAAY,GAAG,CAAC;IACrB,IAAI,CAACqD,OAAO,GAAG,KAAK;IACpB,IAAI,CAACnC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACoC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACnC,YAAY,GAAG,KAAK;EAC3B;EAEA;EACApB,cAAcA,CAACwH,OAAe;IAC5B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAC/CC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACvB;EAEAvF,eAAeA,CAAA;IACb,OAAO,IAAI,CAACS,QAAQ,KAAK,OAAO,GAAG,UAAU,GAAG,MAAM;EACxD;EAEAV,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACU,QAAQ,KAAK,OAAO,GAAG,aAAa,GAAG,aAAa;EAClE;;;uBA9TWP,sBAAsB,EAAA5D,EAAA,CAAAkJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApJ,EAAA,CAAAkJ,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAtJ,EAAA,CAAAkJ,iBAAA,CAAAlJ,EAAA,CAAAuJ,iBAAA;IAAA;EAAA;;;YAAtB3F,sBAAsB;MAAA4F,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;UCpBnC3J,EAAA,CAAAC,cAAA,aAQC;UAECD,EAAA,CAAAK,UAAA,IAAAwJ,qCAAA,mBAgHM;UAGN7J,EAAA,CAAAK,UAAA,IAAAyJ,qCAAA,iBA4BM;UAGN9J,EAAA,CAAAC,cAAA,aAEC;UAIKD,EAAA,CAAAqB,UAAA,mBAAA0I,wDAAA;YAAA,OAASH,GAAA,CAAAxC,UAAA,EAAY;UAAA,EAAC;UAStBpH,EAAA,CAAAC,cAAA,WAA8C;UAAAD,EAAA,CAAAI,MAAA,GAE5C;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAIRH,EAAA,CAAAK,UAAA,IAAA2J,wCAAA,oBAcS;UAGThK,EAAA,CAAAC,cAAA,gBAWC;UAVCD,EAAA,CAAAqB,UAAA,mBAAA4I,wDAAA;YAAA,OAASL,GAAA,CAAA5B,aAAA,EAAe;UAAA,EAAC;UAWzBhI,EAAA,CAAAC,cAAA,YAA8C;UAAAD,EAAA,CAAAI,MAAA,IAE5C;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAIRH,EAAA,CAAAC,cAAA,iBAIC;UAHCD,EAAA,CAAAqB,UAAA,mBAAA6I,yDAAA;YAAA,OAASN,GAAA,CAAA1C,OAAA,EAAS;UAAA,EAAC;UAInBlH,EAAA,CAAAC,cAAA,YAA8C;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAMhEH,EAAA,CAAAK,UAAA,KAAA8J,sCAAA,kBAiDM;UAGNnK,EAAA,CAAAK,UAAA,KAAA+J,wCAAA,oBAAkE;UACpEpK,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAK,UAAA,KAAAgK,sCAAA,kBAYM;;;UAjSJrK,EAAA,CAAAmC,WAAA,gBAAAyH,GAAA,CAAA1F,SAAA,CAA+B,eAAA0F,GAAA,CAAA1F,SAAA,0BAAA0F,GAAA,CAAA1F,SAAA;UAQzBlE,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAQ,UAAA,SAAAoJ,GAAA,CAAAzF,QAAA,aAA0B;UAoH7BnE,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAQ,UAAA,SAAAoJ,GAAA,CAAAzF,QAAA,aAA0B;UAsCvBnE,EAAA,CAAAO,SAAA,GAIC;UAJDP,EAAA,CAAA+C,UAAA,CAAA6G,GAAA,CAAAnF,OAAA,mEAIC;UACDzE,EAAA,CAAAQ,UAAA,UAAAoJ,GAAA,CAAAnF,OAAA,0CAA0D;UAEZzE,EAAA,CAAAO,SAAA,GAE5C;UAF4CP,EAAA,CAAAe,iBAAA,CAAA6I,GAAA,CAAAnF,OAAA,qBAE5C;UAKDzE,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAQ,UAAA,SAAAoJ,GAAA,CAAAzF,QAAA,aAA0B;UAmB3BnE,EAAA,CAAAO,SAAA,GAIC;UAJDP,EAAA,CAAA+C,UAAA,CAAA6G,GAAA,CAAAlF,WAAA,qEAIC;UACD1E,EAAA,CAAAQ,UAAA,UAAAoJ,GAAA,CAAAlF,WAAA,2DAEC;UAE6C1E,EAAA,CAAAO,SAAA,GAE5C;UAF4CP,EAAA,CAAAe,iBAAA,CAAA6I,GAAA,CAAAlF,WAAA,+BAE5C;UAgBL1E,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAQ,UAAA,SAAAoJ,GAAA,CAAArF,UAAA,KAAAqF,GAAA,CAAA3I,WAAA,CAAgC;UAmDLjB,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAQ,UAAA,SAAAoJ,GAAA,CAAAzF,QAAA,aAA0B;UAKvDnE,EAAA,CAAAO,SAAA,GAA8C;UAA9CP,EAAA,CAAAQ,UAAA,SAAAoJ,GAAA,CAAA1F,SAAA,KAAA0F,GAAA,CAAA3I,WAAA,KAAA2I,GAAA,CAAArF,UAAA,CAA8C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}