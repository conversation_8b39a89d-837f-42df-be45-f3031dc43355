{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"src/app/services/file.service\";\nimport * as i4 from \"@app/services/rendus.service\";\nimport * as i5 from \"@angular/common\";\nfunction DetailProjectComponent_div_54_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65)(2, \"div\", 66)(3, \"div\", 67)(4, \"div\", 29);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 30);\n    i0.ɵɵelement(6, \"path\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"div\", 69)(8, \"p\", 70);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 71);\n    i0.ɵɵtext(11, \"Document\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"a\", 72)(13, \"div\", 16);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(14, \"svg\", 73);\n    i0.ɵɵelement(15, \"path\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"T\\u00E9l\\u00E9charger\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const file_r7 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getFileName(file_r7), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r6.getFileUrl(file_r7), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailProjectComponent_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, DetailProjectComponent_div_54_div_1_Template, 18, 2, \"div\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.projet.fichiers);\n  }\n}\nfunction DetailProjectComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76)(2, \"div\", 77);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 78);\n    i0.ɵɵelement(4, \"path\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 22);\n    i0.ɵɵtext(6, \"Aucun fichier joint \\u00E0 ce projet\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DetailProjectComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80)(3, \"div\", 81)(4, \"div\", 82);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailProjectComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 83)(2, \"div\", 23)(3, \"div\", 84);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 85);\n    i0.ɵɵelement(5, \"path\", 86);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"div\")(7, \"h3\", 87);\n    i0.ɵɵtext(8, \"Statistiques\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 88);\n    i0.ɵɵtext(10, \"Suivi des rendus\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 89)(12, \"div\")(13, \"div\", 90)(14, \"span\", 91);\n    i0.ɵɵtext(15, \"Progression globale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 92);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 93);\n    i0.ɵɵelement(19, \"div\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 95)(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 96)(26, \"div\", 97)(27, \"div\", 66)(28, \"div\")(29, \"p\", 98);\n    i0.ɵɵtext(30, \"Rendus\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"p\", 99);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 100);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(34, \"svg\", 101);\n    i0.ɵɵelement(35, \"path\", 102);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(36, \"div\", 103)(37, \"div\", 66)(38, \"div\")(39, \"p\", 104);\n    i0.ɵɵtext(40, \"En attente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"p\", 105);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 106);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(44, \"svg\", 107);\n    i0.ɵɵelement(45, \"path\", 21);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(46, \"div\", 108)(47, \"div\", 66)(48, \"div\")(49, \"p\", 109);\n    i0.ɵɵtext(50, \"Taux\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 110);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 111);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(54, \"svg\", 42);\n    i0.ɵɵelement(55, \"path\", 112);\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.etudiantsRendus.length, \"/\", ctx_r3.totalEtudiants, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.getProgressPercentage(), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getProgressPercentage(), \"% compl\\u00E9t\\u00E9\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getRemainingDays(), \" jours restants\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r3.etudiantsRendus.length);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r3.totalEtudiants - ctx_r3.etudiantsRendus.length);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getProgressPercentage(), \"%\");\n  }\n}\nfunction DetailProjectComponent_div_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"div\", 114);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 115)(4, \"div\", 19);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 71);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 116);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 117);\n    i0.ɵɵelement(10, \"path\", 102);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const rendu_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getStudentInitials(rendu_r8.etudiant), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getStudentName(rendu_r8.etudiant), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.formatDate(rendu_r8.dateRendu), \" \");\n  }\n}\nfunction DetailProjectComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76)(2, \"div\", 77);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 78);\n    i0.ɵɵelement(4, \"path\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 22);\n    i0.ɵɵtext(6, \"Aucun rendu pour le moment\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/admin/projects/editProjet\", a1];\n};\nconst _c1 = function () {\n  return [\"/admin/projects/rendus\"];\n};\nconst _c2 = function (a0) {\n  return {\n    projetId: a0\n  };\n};\nexport class DetailProjectComponent {\n  constructor(route, router, projectService, fileService, rendusService) {\n    this.route = route;\n    this.router = router;\n    this.projectService = projectService;\n    this.fileService = fileService;\n    this.rendusService = rendusService;\n    this.projet = null;\n    this.rendus = [];\n    this.totalEtudiants = 0;\n    this.etudiantsRendus = [];\n    this.derniersRendus = [];\n    this.isLoading = true;\n  }\n  ngOnInit() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.loadProjectData(id);\n    }\n  }\n  loadProjectData(id) {\n    this.isLoading = true;\n    // Charger les données du projet\n    this.projectService.getProjetById(id).subscribe({\n      next: data => {\n        this.projet = data;\n        this.loadProjectStatistics(id);\n      },\n      error: err => {\n        console.error('Erreur lors du chargement du projet:', err);\n        this.isLoading = false;\n      }\n    });\n  }\n  loadProjectStatistics(projetId) {\n    // Charger les rendus pour ce projet\n    this.rendusService.getRendusByProjet(projetId).subscribe({\n      next: rendus => {\n        this.rendus = rendus;\n        this.etudiantsRendus = rendus.filter(rendu => rendu.etudiant);\n        // Trier par date pour avoir les derniers rendus\n        this.derniersRendus = [...this.etudiantsRendus].sort((a, b) => new Date(b.dateRendu).getTime() - new Date(a.dateRendu).getTime()).slice(0, 5); // Prendre les 5 derniers\n        // Pour le total d'étudiants, on peut estimer ou récupérer depuis le backend\n        // Pour l'instant, on utilise le nombre d'étudiants uniques qui ont rendu + estimation\n        const etudiantsUniques = new Set(this.etudiantsRendus.map(r => r.etudiant._id || r.etudiant));\n        this.totalEtudiants = Math.max(etudiantsUniques.size, this.estimateStudentCount());\n        this.isLoading = false;\n      },\n      error: err => {\n        console.error('Erreur lors du chargement des statistiques:', err);\n        this.isLoading = false;\n      }\n    });\n  }\n  estimateStudentCount() {\n    // Estimation basée sur le groupe du projet\n    const groupe = this.projet?.groupe?.toLowerCase();\n    if (groupe?.includes('1c')) return 25; // Première année\n    if (groupe?.includes('2c')) return 20; // Deuxième année\n    if (groupe?.includes('3c')) return 15; // Troisième année\n    return 20; // Valeur par défaut\n  }\n\n  getFileUrl(filePath) {\n    return this.fileService.getDownloadUrl(filePath);\n  }\n  deleteProjet(id) {\n    if (!id) return;\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      this.projectService.deleteProjet(id).subscribe({\n        next: () => {\n          alert('Projet supprimé avec succès');\n          this.router.navigate(['/admin/projects']);\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression du projet', err);\n          alert('Erreur lors de la suppression du projet');\n        }\n      });\n    }\n  }\n  formatDate(date) {\n    const d = new Date(date);\n    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;\n  }\n  getFileName(filePath) {\n    if (!filePath) return 'Fichier';\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    return filePath;\n  }\n  getProjectStatus() {\n    if (!this.projet?.dateLimite) return 'En cours';\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n    if (deadline < now) {\n      return 'Expiré';\n    } else if (deadline.getTime() - now.getTime() < 7 * 24 * 60 * 60 * 1000) {\n      return 'Urgent';\n    } else {\n      return 'Actif';\n    }\n  }\n  getStatusClass() {\n    const status = this.getProjectStatus();\n    switch (status) {\n      case 'Actif':\n        return 'bg-success/10 dark:bg-dark-accent-secondary/20 text-success dark:text-dark-accent-secondary border border-success/20 dark:border-dark-accent-secondary/30';\n      case 'Urgent':\n        return 'bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning border border-warning/20 dark:border-warning/30';\n      case 'Expiré':\n        return 'bg-danger/10 dark:bg-danger-dark/20 text-danger dark:text-danger-dark border border-danger/20 dark:border-danger-dark/30';\n      default:\n        return 'bg-info/10 dark:bg-dark-accent-primary/20 text-info dark:text-dark-accent-primary border border-info/20 dark:border-dark-accent-primary/30';\n    }\n  }\n  getProgressPercentage() {\n    if (this.totalEtudiants === 0) return 0;\n    return Math.round(this.etudiantsRendus.length / this.totalEtudiants * 100);\n  }\n  getRemainingDays() {\n    if (!this.projet?.dateLimite) return 0;\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n    const diffTime = deadline.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  }\n  getStudentInitials(etudiant) {\n    if (!etudiant) return '??';\n    // Priorité 1: firstName + lastName\n    const firstName = etudiant.firstName || etudiant.prenom || '';\n    const lastName = etudiant.lastName || etudiant.nom || '';\n    if (firstName && lastName) {\n      return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();\n    }\n    // Priorité 2: fullName\n    const fullName = etudiant.fullName || etudiant.name || '';\n    if (fullName) {\n      const parts = fullName.trim().split(' ');\n      if (parts.length >= 2) {\n        return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();\n      } else {\n        return fullName.substring(0, 2).toUpperCase();\n      }\n    }\n    // Priorité 3: firstName seul\n    if (firstName) {\n      return firstName.substring(0, 2).toUpperCase();\n    }\n    return '??';\n  }\n  getStudentName(etudiant) {\n    if (!etudiant) return 'Utilisateur inconnu';\n    // Priorité 1: firstName + lastName\n    const firstName = etudiant.firstName || etudiant.prenom || '';\n    const lastName = etudiant.lastName || etudiant.nom || '';\n    if (firstName && lastName) {\n      return `${firstName} ${lastName}`.trim();\n    }\n    // Priorité 2: fullName\n    const fullName = etudiant.fullName || etudiant.name || '';\n    if (fullName) {\n      return fullName.trim();\n    }\n    // Priorité 3: firstName seul\n    if (firstName) {\n      return firstName.trim();\n    }\n    // Priorité 4: email comme fallback\n    if (etudiant.email) {\n      return etudiant.email;\n    }\n    return 'Utilisateur inconnu';\n  }\n  static {\n    this.ɵfac = function DetailProjectComponent_Factory(t) {\n      return new (t || DetailProjectComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.FileService), i0.ɵɵdirectiveInject(i4.RendusService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailProjectComponent,\n      selectors: [[\"app-detail-project\"]],\n      decls: 98,\n      vars: 23,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [\"routerLink\", \"/admin/projects/list-project\", 1, \"hover:text-primary\", \"dark:hover:text-dark-accent-primary\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"text-primary\", \"dark:text-dark-accent-primary\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mb-6\", \"lg:mb-0\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-3xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-warning\", \"dark:text-warning\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"px-4\", \"py-2\", \"rounded-xl\", \"text-sm\", \"font-medium\", 3, \"ngClass\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-8\"], [1, \"lg:col-span-2\", \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-4\"], [1, \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h7\"], [1, \"text-lg\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"prose\", \"prose-gray\", \"dark:prose-invert\", \"max-w-none\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"leading-relaxed\"], [1, \"bg-secondary/10\", \"dark:bg-dark-accent-secondary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-secondary\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-sm\", \"font-normal\", \"text-text\", \"dark:text-dark-text-secondary\", \"ml-2\"], [\"class\", \"grid grid-cols-1 sm:grid-cols-2 gap-4\", 4, \"ngIf\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [1, \"bg-info/10\", \"dark:bg-dark-accent-primary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-info\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-3\", \"gap-4\"], [1, \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\", \"queryParams\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [1, \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-danger\", \"to-danger-dark\", \"dark:from-danger-dark\", \"dark:to-danger\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"overflow-hidden\"], [\"class\", \"p-6\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"p-6\"], [1, \"space-y-3\"], [\"class\", \"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-4\"], [\"class\", \"group\", 4, \"ngFor\", \"ngForOf\"], [1, \"group\"], [1, \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-4\", \"border\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"hover:border-primary\", \"dark:hover:border-dark-accent-primary\", \"transition-all\", \"duration-200\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"flex-1\", \"min-w-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"flex-1\", \"min-w-0\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"truncate\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\"], [\"download\", \"\", 1, \"ml-3\", \"px-3\", \"py-2\", \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"text-primary\", \"dark:text-dark-accent-primary\", \"hover:bg-primary\", \"hover:text-white\", \"dark:hover:bg-dark-accent-primary\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"text-xs\", \"font-medium\", 3, \"href\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"text-center\", \"py-8\"], [1, \"bg-gray-100\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-6\"], [1, \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"p-3\", \"rounded-lg\", \"inline-flex\", \"items-center\", \"justify-center\", \"mb-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-gray-400\", \"dark:text-dark-text-secondary\"], [1, \"animate-pulse\", \"space-y-4\"], [1, \"h-4\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded\", \"w-3/4\"], [1, \"h-4\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded\", \"w-1/2\"], [1, \"h-8\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-6\", \"text-white\"], [1, \"bg-white/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [1, \"text-lg\", \"font-semibold\"], [1, \"text-sm\", \"text-white/80\"], [1, \"p-6\", \"space-y-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-3\"], [1, \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-sm\", \"font-bold\", \"text-primary\", \"dark:text-dark-accent-primary\"], [1, \"w-full\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded-full\", \"h-3\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"h-3\", \"rounded-full\", \"transition-all\", \"duration-500\"], [1, \"flex\", \"justify-between\", \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"mt-2\"], [1, \"grid\", \"grid-cols-1\", \"gap-4\"], [1, \"bg-gradient-to-br\", \"from-success/10\", \"to-success/5\", \"dark:from-dark-accent-secondary/20\", \"dark:to-dark-accent-secondary/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-success/20\", \"dark:border-dark-accent-secondary/30\"], [1, \"text-xs\", \"font-medium\", \"text-success\", \"dark:text-dark-accent-secondary\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-success\", \"dark:text-dark-accent-secondary\"], [1, \"bg-success/20\", \"dark:bg-dark-accent-secondary/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-success\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"bg-gradient-to-br\", \"from-warning/10\", \"to-warning/5\", \"dark:from-warning/20\", \"dark:to-warning/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-warning/20\", \"dark:border-warning/30\"], [1, \"text-xs\", \"font-medium\", \"text-warning\", \"dark:text-warning\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-warning\", \"dark:text-warning\"], [1, \"bg-warning/20\", \"dark:bg-warning/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-warning\", \"dark:text-warning\"], [1, \"bg-gradient-to-br\", \"from-info/10\", \"to-info/5\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-primary/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-info/20\", \"dark:border-dark-accent-primary/30\"], [1, \"text-xs\", \"font-medium\", \"text-info\", \"dark:text-dark-accent-primary\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-info\", \"dark:text-dark-accent-primary\"], [1, \"bg-info/20\", \"dark:bg-dark-accent-primary/30\", \"p-2\", \"rounded-lg\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"p-3\", \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\"], [1, \"h-10\", \"w-10\", \"rounded-xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-sm\", \"font-bold\", \"shadow-lg\"], [1, \"flex-1\"], [1, \"bg-success/10\", \"dark:bg-dark-accent-secondary/20\", \"p-1.5\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-success\", \"dark:text-dark-accent-secondary\"]],\n      template: function DetailProjectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"nav\", 3)(4, \"a\", 4);\n          i0.ɵɵtext(5, \"Projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 5);\n          i0.ɵɵelement(7, \"path\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(14, \"svg\", 12);\n          i0.ɵɵelement(15, \"path\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(16, \"div\")(17, \"h1\", 14);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 15)(20, \"div\", 16);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(21, \"svg\", 17);\n          i0.ɵɵelement(22, \"path\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(23, \"span\", 19);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 16);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(26, \"svg\", 20);\n          i0.ɵɵelement(27, \"path\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(28, \"span\", 22);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(30, \"div\", 23)(31, \"span\", 24);\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(33, \"div\", 25)(34, \"div\", 26)(35, \"div\", 27)(36, \"div\", 28)(37, \"div\", 29);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(38, \"svg\", 30);\n          i0.ɵɵelement(39, \"path\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(40, \"h3\", 32);\n          i0.ɵɵtext(41, \"Description du projet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 33)(43, \"p\", 34);\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 27)(46, \"div\", 28)(47, \"div\", 35);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(48, \"svg\", 36);\n          i0.ɵɵelement(49, \"path\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(50, \"h3\", 32);\n          i0.ɵɵtext(51, \" Fichiers joints \");\n          i0.ɵɵelementStart(52, \"span\", 38);\n          i0.ɵɵtext(53);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(54, DetailProjectComponent_div_54_Template, 2, 1, \"div\", 39);\n          i0.ɵɵtemplate(55, DetailProjectComponent_div_55_Template, 7, 0, \"div\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"div\", 27)(57, \"div\", 28)(58, \"div\", 41);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(59, \"svg\", 42);\n          i0.ɵɵelement(60, \"path\", 43)(61, \"path\", 44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(62, \"h3\", 32);\n          i0.ɵɵtext(63, \"Actions disponibles\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"div\", 45)(65, \"a\", 46)(66, \"div\", 47);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(67, \"svg\", 48);\n          i0.ɵɵelement(68, \"path\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(69, \"span\");\n          i0.ɵɵtext(70, \"Modifier\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(71, \"a\", 50)(72, \"div\", 47);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(73, \"svg\", 48);\n          i0.ɵɵelement(74, \"path\", 51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(75, \"span\");\n          i0.ɵɵtext(76, \"Voir rendus\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(77, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function DetailProjectComponent_Template_button_click_77_listener() {\n            return ctx.deleteProjet(ctx.projet == null ? null : ctx.projet._id);\n          });\n          i0.ɵɵelementStart(78, \"div\", 47);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(79, \"svg\", 48);\n          i0.ɵɵelement(80, \"path\", 53);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(81, \"span\");\n          i0.ɵɵtext(82, \"Supprimer\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(83, \"div\", 54)(84, \"div\", 55);\n          i0.ɵɵtemplate(85, DetailProjectComponent_div_85_Template, 5, 0, \"div\", 56);\n          i0.ɵɵtemplate(86, DetailProjectComponent_div_86_Template, 56, 9, \"div\", 57);\n          i0.ɵɵelementStart(87, \"div\", 58)(88, \"div\", 59)(89, \"div\", 28)(90, \"div\", 35);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(91, \"svg\", 36);\n          i0.ɵɵelement(92, \"path\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(93, \"h3\", 32);\n          i0.ɵɵtext(94, \"Derniers rendus\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"div\", 60);\n          i0.ɵɵtemplate(96, DetailProjectComponent_div_96_Template, 11, 3, \"div\", 61);\n          i0.ɵɵtemplate(97, DetailProjectComponent_div_97_Template, 7, 0, \"div\", 40);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.titre) || \"D\\u00E9tails du projet\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.titre) || \"Chargement...\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.groupe) || \"Tous les groupes\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.dateLimite) ? ctx.formatDate(ctx.projet == null ? null : ctx.projet.dateLimite) : \"Pas de date limite\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getProjectStatus(), \" \");\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.description) || \"Aucune description fournie pour ce projet.\", \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate2(\" (\", (ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) || 0, \" fichier\", ((ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) || 0) > 1 ? \"s\" : \"\", \") \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.projet == null ? null : ctx.projet.fichiers) || ctx.projet.fichiers.length === 0);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(18, _c0, ctx.projet == null ? null : ctx.projet._id));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(20, _c1))(\"queryParams\", i0.ɵɵpureFunction1(21, _c2, ctx.projet == null ? null : ctx.projet._id));\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.derniersRendus);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.derniersRendus || ctx.derniersRendus.length === 0);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtcHJvamVjdC5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvZGV0YWlsLXByb2plY3QvZGV0YWlsLXByb2plY3QuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r6", "getFileName", "file_r7", "ɵɵproperty", "getFileUrl", "ɵɵsanitizeUrl", "ɵɵtemplate", "DetailProjectComponent_div_54_div_1_Template", "ctx_r0", "projet", "fichiers", "ɵɵtextInterpolate2", "ctx_r3", "etudiantsRendus", "length", "totalEtudiants", "ɵɵstyleProp", "getProgressPercentage", "getRemainingDays", "ɵɵtextInterpolate", "ctx_r4", "getStudentInitials", "rendu_r8", "etudiant", "getStudentName", "formatDate", "dateRendu", "DetailProjectComponent", "constructor", "route", "router", "projectService", "fileService", "rendusService", "rendus", "derniersRendus", "isLoading", "ngOnInit", "id", "snapshot", "paramMap", "get", "loadProjectData", "getProjetById", "subscribe", "next", "data", "loadProjectStatistics", "error", "err", "console", "projetId", "getRendusByProjet", "filter", "rendu", "sort", "a", "b", "Date", "getTime", "slice", "etudiantsUniques", "Set", "map", "r", "_id", "Math", "max", "size", "estimateStudentCount", "groupe", "toLowerCase", "includes", "filePath", "getDownloadUrl", "deleteProjet", "confirm", "alert", "navigate", "date", "d", "getDate", "toString", "padStart", "getMonth", "getFullYear", "parts", "split", "getProjectStatus", "dateLimite", "now", "deadline", "getStatusClass", "status", "round", "diffTime", "diffDays", "ceil", "firstName", "prenom", "lastName", "nom", "char<PERSON>t", "toUpperCase", "fullName", "name", "trim", "substring", "email", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProjetService", "i3", "FileService", "i4", "RendusService", "selectors", "decls", "vars", "consts", "template", "DetailProjectComponent_Template", "rf", "ctx", "DetailProjectComponent_div_54_Template", "DetailProjectComponent_div_55_Template", "ɵɵlistener", "DetailProjectComponent_Template_button_click_77_listener", "DetailProjectComponent_div_85_Template", "DetailProjectComponent_div_86_Template", "DetailProjectComponent_div_96_Template", "DetailProjectComponent_div_97_Template", "titre", "description", "ɵɵpureFunction1", "_c0", "ɵɵpureFunction0", "_c1", "_c2"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\detail-project\\detail-project.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\detail-project\\detail-project.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ProjetService } from '@app/services/projets.service';\nimport { FileService } from 'src/app/services/file.service';\nimport { RendusService } from '@app/services/rendus.service';\n\n@Component({\n  selector: 'app-detail-project',\n  templateUrl: './detail-project.component.html',\n  styleUrls: ['./detail-project.component.css'],\n})\nexport class DetailProjectComponent implements OnInit {\n  projet: any = null;\n  rendus: any[] = [];\n  totalEtudiants: number = 0;\n  etudiantsRendus: any[] = [];\n  derniersRendus: any[] = [];\n  isLoading: boolean = true;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private projectService: ProjetService,\n    private fileService: FileService,\n    private rendusService: RendusService\n  ) {}\n\n  ngOnInit(): void {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.loadProjectData(id);\n    }\n  }\n\n  loadProjectData(id: string): void {\n    this.isLoading = true;\n\n    // Charger les données du projet\n    this.projectService.getProjetById(id).subscribe({\n      next: (data: any) => {\n        this.projet = data;\n        this.loadProjectStatistics(id);\n      },\n      error: (err: any) => {\n        console.error('Erreur lors du chargement du projet:', err);\n        this.isLoading = false;\n      }\n    });\n  }\n\n  loadProjectStatistics(projetId: string): void {\n    // Charger les rendus pour ce projet\n    this.rendusService.getRendusByProjet(projetId).subscribe({\n      next: (rendus: any[]) => {\n        this.rendus = rendus;\n        this.etudiantsRendus = rendus.filter(rendu => rendu.etudiant);\n\n        // Trier par date pour avoir les derniers rendus\n        this.derniersRendus = [...this.etudiantsRendus]\n          .sort((a, b) => new Date(b.dateRendu).getTime() - new Date(a.dateRendu).getTime())\n          .slice(0, 5); // Prendre les 5 derniers\n\n        // Pour le total d'étudiants, on peut estimer ou récupérer depuis le backend\n        // Pour l'instant, on utilise le nombre d'étudiants uniques qui ont rendu + estimation\n        const etudiantsUniques = new Set(this.etudiantsRendus.map(r => r.etudiant._id || r.etudiant));\n        this.totalEtudiants = Math.max(etudiantsUniques.size, this.estimateStudentCount());\n\n        this.isLoading = false;\n      },\n      error: (err: any) => {\n        console.error('Erreur lors du chargement des statistiques:', err);\n        this.isLoading = false;\n      }\n    });\n  }\n\n  estimateStudentCount(): number {\n    // Estimation basée sur le groupe du projet\n    const groupe = this.projet?.groupe?.toLowerCase();\n    if (groupe?.includes('1c')) return 25; // Première année\n    if (groupe?.includes('2c')) return 20; // Deuxième année\n    if (groupe?.includes('3c')) return 15; // Troisième année\n    return 20; // Valeur par défaut\n  }\n\n  getFileUrl(filePath: string): string {\n    return this.fileService.getDownloadUrl(filePath);\n  }\n\n  deleteProjet(id: string | undefined): void {\n    if (!id) return;\n\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      this.projectService.deleteProjet(id).subscribe({\n        next: () => {\n          alert('Projet supprimé avec succès');\n          this.router.navigate(['/admin/projects']);\n        },\n        error: (err) => {\n          console.error('Erreur lors de la suppression du projet', err);\n          alert('Erreur lors de la suppression du projet');\n        },\n      });\n    }\n  }\n\n  formatDate(date: string | Date): string {\n    const d = new Date(date);\n    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1)\n      .toString()\n      .padStart(2, '0')}/${d.getFullYear()}`;\n  }\n\n  getFileName(filePath: string): string {\n    if (!filePath) return 'Fichier';\n\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n\n    return filePath;\n  }\n\n  getProjectStatus(): string {\n    if (!this.projet?.dateLimite) return 'En cours';\n\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n\n    if (deadline < now) {\n      return 'Expiré';\n    } else if (deadline.getTime() - now.getTime() < 7 * 24 * 60 * 60 * 1000) {\n      return 'Urgent';\n    } else {\n      return 'Actif';\n    }\n  }\n\n  getStatusClass(): string {\n    const status = this.getProjectStatus();\n\n    switch (status) {\n      case 'Actif':\n        return 'bg-success/10 dark:bg-dark-accent-secondary/20 text-success dark:text-dark-accent-secondary border border-success/20 dark:border-dark-accent-secondary/30';\n      case 'Urgent':\n        return 'bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning border border-warning/20 dark:border-warning/30';\n      case 'Expiré':\n        return 'bg-danger/10 dark:bg-danger-dark/20 text-danger dark:text-danger-dark border border-danger/20 dark:border-danger-dark/30';\n      default:\n        return 'bg-info/10 dark:bg-dark-accent-primary/20 text-info dark:text-dark-accent-primary border border-info/20 dark:border-dark-accent-primary/30';\n    }\n  }\n\n  getProgressPercentage(): number {\n    if (this.totalEtudiants === 0) return 0;\n    return Math.round((this.etudiantsRendus.length / this.totalEtudiants) * 100);\n  }\n\n  getRemainingDays(): number {\n    if (!this.projet?.dateLimite) return 0;\n\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n    const diffTime = deadline.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    return Math.max(0, diffDays);\n  }\n\n  getStudentInitials(etudiant: any): string {\n    if (!etudiant) return '??';\n\n    // Priorité 1: firstName + lastName\n    const firstName = etudiant.firstName || etudiant.prenom || '';\n    const lastName = etudiant.lastName || etudiant.nom || '';\n\n    if (firstName && lastName) {\n      return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();\n    }\n\n    // Priorité 2: fullName\n    const fullName = etudiant.fullName || etudiant.name || '';\n    if (fullName) {\n      const parts = fullName.trim().split(' ');\n      if (parts.length >= 2) {\n        return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();\n      } else {\n        return fullName.substring(0, 2).toUpperCase();\n      }\n    }\n\n    // Priorité 3: firstName seul\n    if (firstName) {\n      return firstName.substring(0, 2).toUpperCase();\n    }\n\n    return '??';\n  }\n\n  getStudentName(etudiant: any): string {\n    if (!etudiant) return 'Utilisateur inconnu';\n\n    // Priorité 1: firstName + lastName\n    const firstName = etudiant.firstName || etudiant.prenom || '';\n    const lastName = etudiant.lastName || etudiant.nom || '';\n\n    if (firstName && lastName) {\n      return `${firstName} ${lastName}`.trim();\n    }\n\n    // Priorité 2: fullName\n    const fullName = etudiant.fullName || etudiant.name || '';\n    if (fullName) {\n      return fullName.trim();\n    }\n\n    // Priorité 3: firstName seul\n    if (firstName) {\n      return firstName.trim();\n    }\n\n    // Priorité 4: email comme fallback\n    if (etudiant.email) {\n      return etudiant.email;\n    }\n\n    return 'Utilisateur inconnu';\n  }\n}\n", "<!-- Begin Page Content -->\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary\">\n  <div class=\"container mx-auto px-4 py-8\">\n\n    <!-- Header moderne avec breadcrumb -->\n    <div class=\"mb-8\">\n      <nav class=\"flex items-center space-x-2 text-sm text-text dark:text-dark-text-secondary mb-4\">\n        <a routerLink=\"/admin/projects/list-project\" class=\"hover:text-primary dark:hover:text-dark-accent-primary transition-colors\">Projets</a>\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n        </svg>\n        <span class=\"text-primary dark:text-dark-accent-primary font-medium\">{{ projet?.titre || 'Détails du projet' }}</span>\n      </nav>\n\n      <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\n        <div class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n          <div class=\"flex items-center space-x-4 mb-6 lg:mb-0\">\n            <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center shadow-lg\">\n              <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\n              </svg>\n            </div>\n            <div>\n              <h1 class=\"text-3xl font-bold text-text-dark dark:text-dark-text-primary\">\n                {{ projet?.titre || 'Chargement...' }}\n              </h1>\n              <div class=\"flex items-center space-x-4 mt-2\">\n                <div class=\"flex items-center space-x-1\">\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                  </svg>\n                  <span class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary\">{{ projet?.groupe || 'Tous les groupes' }}</span>\n                </div>\n                <div class=\"flex items-center space-x-1\">\n                  <svg class=\"w-4 h-4 text-warning dark:text-warning\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                  </svg>\n                  <span class=\"text-sm text-text dark:text-dark-text-secondary\">{{ projet?.dateLimite ? formatDate(projet?.dateLimite) : 'Pas de date limite' }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Badge de statut -->\n          <div class=\"flex items-center space-x-3\">\n            <span [ngClass]=\"getStatusClass()\" class=\"px-4 py-2 rounded-xl text-sm font-medium\">\n              {{ getProjectStatus() }}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Contenu principal -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n      <!-- Carte principale du projet -->\n      <div class=\"lg:col-span-2 space-y-6\">\n\n        <!-- Description du projet -->\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\n          <div class=\"flex items-center space-x-3 mb-4\">\n            <div class=\"bg-primary/10 dark:bg-dark-accent-primary/20 p-2 rounded-lg\">\n              <svg class=\"w-5 h-5 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h7\"></path>\n              </svg>\n            </div>\n            <h3 class=\"text-lg font-semibold text-text-dark dark:text-dark-text-primary\">Description du projet</h3>\n          </div>\n          <div class=\"prose prose-gray dark:prose-invert max-w-none\">\n            <p class=\"text-text dark:text-dark-text-secondary leading-relaxed\">\n              {{ projet?.description || 'Aucune description fournie pour ce projet.' }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Fichiers du projet -->\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\n          <div class=\"flex items-center space-x-3 mb-4\">\n            <div class=\"bg-secondary/10 dark:bg-dark-accent-secondary/20 p-2 rounded-lg\">\n              <svg class=\"w-5 h-5 text-secondary dark:text-dark-accent-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n              </svg>\n            </div>\n            <h3 class=\"text-lg font-semibold text-text-dark dark:text-dark-text-primary\">\n              Fichiers joints\n              <span class=\"text-sm font-normal text-text dark:text-dark-text-secondary ml-2\">\n                ({{ projet?.fichiers?.length || 0 }} fichier{{ (projet?.fichiers?.length || 0) > 1 ? 's' : '' }})\n              </span>\n            </h3>\n          </div>\n\n          <div *ngIf=\"projet?.fichiers?.length > 0\" class=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n            <div *ngFor=\"let file of projet.fichiers\" class=\"group\">\n              <div class=\"bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl p-4 border border-gray-200 dark:border-dark-bg-tertiary hover:border-primary dark:hover:border-dark-accent-primary transition-all duration-200\">\n                <div class=\"flex items-center justify-between\">\n                  <div class=\"flex items-center space-x-3 flex-1 min-w-0\">\n                    <div class=\"bg-primary/10 dark:bg-dark-accent-primary/20 p-2 rounded-lg\">\n                      <svg class=\"w-5 h-5 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"></path>\n                      </svg>\n                    </div>\n                    <div class=\"flex-1 min-w-0\">\n                      <p class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary truncate\">\n                        {{ getFileName(file) }}\n                      </p>\n                      <p class=\"text-xs text-text dark:text-dark-text-secondary\">Document</p>\n                    </div>\n                  </div>\n                  <a [href]=\"getFileUrl(file)\" download\n                     class=\"ml-3 px-3 py-2 bg-primary/10 dark:bg-dark-accent-primary/20 text-primary dark:text-dark-accent-primary hover:bg-primary hover:text-white dark:hover:bg-dark-accent-primary rounded-lg transition-all duration-200 text-xs font-medium\">\n                    <div class=\"flex items-center space-x-1\">\n                      <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"></path>\n                      </svg>\n                      <span>Télécharger</span>\n                    </div>\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div *ngIf=\"!projet?.fichiers || projet.fichiers.length === 0\" class=\"text-center py-8\">\n            <div class=\"bg-gray-100 dark:bg-dark-bg-tertiary/50 rounded-xl p-6\">\n              <div class=\"bg-gray-200 dark:bg-dark-bg-tertiary p-3 rounded-lg inline-flex items-center justify-center mb-3\">\n                <svg class=\"w-6 h-6 text-gray-400 dark:text-dark-text-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                </svg>\n              </div>\n              <p class=\"text-sm text-text dark:text-dark-text-secondary\">Aucun fichier joint à ce projet</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- Actions du projet -->\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\n          <div class=\"flex items-center space-x-3 mb-4\">\n            <div class=\"bg-info/10 dark:bg-dark-accent-primary/20 p-2 rounded-lg\">\n              <svg class=\"w-5 h-5 text-info dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n              </svg>\n            </div>\n            <h3 class=\"text-lg font-semibold text-text-dark dark:text-dark-text-primary\">Actions disponibles</h3>\n          </div>\n\n          <div class=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n            <a [routerLink]=\"['/admin/projects/editProjet', projet?._id]\"\n               class=\"group px-4 py-3 bg-gradient-to-r from-secondary to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\n              <div class=\"flex items-center justify-center space-x-2\">\n                <svg class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\n                </svg>\n                <span>Modifier</span>\n              </div>\n            </a>\n\n            <a [routerLink]=\"['/admin/projects/rendus']\" [queryParams]=\"{projetId: projet?._id}\"\n               class=\"group px-4 py-3 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\n              <div class=\"flex items-center justify-center space-x-2\">\n                <svg class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"></path>\n                </svg>\n                <span>Voir rendus</span>\n              </div>\n            </a>\n\n            <button (click)=\"deleteProjet(projet?._id)\"\n                    class=\"group px-4 py-3 bg-gradient-to-r from-danger to-danger-dark dark:from-danger-dark dark:to-danger text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\n              <div class=\"flex items-center justify-center space-x-2\">\n                <svg class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\n                </svg>\n                <span>Supprimer</span>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Sidebar avec statistiques -->\n      <div class=\"space-y-6\">\n\n        <!-- Statistiques de rendu -->\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 overflow-hidden\">\n\n          <!-- Indicateur de chargement -->\n          <div *ngIf=\"isLoading\" class=\"p-6\">\n            <div class=\"animate-pulse space-y-4\">\n              <div class=\"h-4 bg-gray-200 dark:bg-dark-bg-tertiary rounded w-3/4\"></div>\n              <div class=\"h-4 bg-gray-200 dark:bg-dark-bg-tertiary rounded w-1/2\"></div>\n              <div class=\"h-8 bg-gray-200 dark:bg-dark-bg-tertiary rounded\"></div>\n            </div>\n          </div>\n\n          <!-- Contenu des statistiques -->\n          <div *ngIf=\"!isLoading\">\n          <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-6 text-white\">\n            <div class=\"flex items-center space-x-3\">\n              <div class=\"bg-white/20 p-2 rounded-lg\">\n                <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n                </svg>\n              </div>\n              <div>\n                <h3 class=\"text-lg font-semibold\">Statistiques</h3>\n                <p class=\"text-sm text-white/80\">Suivi des rendus</p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"p-6 space-y-6\">\n            <!-- Progression générale -->\n            <div>\n              <div class=\"flex justify-between items-center mb-3\">\n                <span class=\"text-sm font-semibold text-text-dark dark:text-dark-text-primary\">Progression globale</span>\n                <span class=\"text-sm font-bold text-primary dark:text-dark-accent-primary\">\n                  {{ etudiantsRendus.length }}/{{ totalEtudiants }}\n                </span>\n              </div>\n              <div class=\"w-full bg-gray-200 dark:bg-dark-bg-tertiary rounded-full h-3\">\n                <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary h-3 rounded-full transition-all duration-500\"\n                     [style.width.%]=\"getProgressPercentage()\"></div>\n              </div>\n              <div class=\"flex justify-between text-xs text-text dark:text-dark-text-secondary mt-2\">\n                <span>{{ getProgressPercentage() }}% complété</span>\n                <span>{{ getRemainingDays() }} jours restants</span>\n              </div>\n            </div>\n\n            <!-- Cartes statistiques -->\n            <div class=\"grid grid-cols-1 gap-4\">\n              <!-- Rendus soumis -->\n              <div class=\"bg-gradient-to-br from-success/10 to-success/5 dark:from-dark-accent-secondary/20 dark:to-dark-accent-secondary/10 rounded-xl p-4 border border-success/20 dark:border-dark-accent-secondary/30\">\n                <div class=\"flex items-center justify-between\">\n                  <div>\n                    <p class=\"text-xs font-medium text-success dark:text-dark-accent-secondary uppercase tracking-wider\">Rendus</p>\n                    <p class=\"text-2xl font-bold text-success dark:text-dark-accent-secondary\">{{ etudiantsRendus.length }}</p>\n                  </div>\n                  <div class=\"bg-success/20 dark:bg-dark-accent-secondary/30 p-2 rounded-lg\">\n                    <svg class=\"w-5 h-5 text-success dark:text-dark-accent-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                    </svg>\n                  </div>\n                </div>\n              </div>\n\n              <!-- En attente -->\n              <div class=\"bg-gradient-to-br from-warning/10 to-warning/5 dark:from-warning/20 dark:to-warning/10 rounded-xl p-4 border border-warning/20 dark:border-warning/30\">\n                <div class=\"flex items-center justify-between\">\n                  <div>\n                    <p class=\"text-xs font-medium text-warning dark:text-warning uppercase tracking-wider\">En attente</p>\n                    <p class=\"text-2xl font-bold text-warning dark:text-warning\">{{ totalEtudiants - etudiantsRendus.length }}</p>\n                  </div>\n                  <div class=\"bg-warning/20 dark:bg-warning/30 p-2 rounded-lg\">\n                    <svg class=\"w-5 h-5 text-warning dark:text-warning\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                    </svg>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Taux de réussite -->\n              <div class=\"bg-gradient-to-br from-info/10 to-info/5 dark:from-dark-accent-primary/20 dark:to-dark-accent-primary/10 rounded-xl p-4 border border-info/20 dark:border-dark-accent-primary/30\">\n                <div class=\"flex items-center justify-between\">\n                  <div>\n                    <p class=\"text-xs font-medium text-info dark:text-dark-accent-primary uppercase tracking-wider\">Taux</p>\n                    <p class=\"text-2xl font-bold text-info dark:text-dark-accent-primary\">{{ getProgressPercentage() }}%</p>\n                  </div>\n                  <div class=\"bg-info/20 dark:bg-dark-accent-primary/30 p-2 rounded-lg\">\n                    <svg class=\"w-5 h-5 text-info dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"></path>\n                    </svg>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Derniers rendus -->\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\n          <div class=\"p-6\">\n            <div class=\"flex items-center space-x-3 mb-4\">\n              <div class=\"bg-secondary/10 dark:bg-dark-accent-secondary/20 p-2 rounded-lg\">\n                <svg class=\"w-5 h-5 text-secondary dark:text-dark-accent-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                </svg>\n              </div>\n              <h3 class=\"text-lg font-semibold text-text-dark dark:text-dark-text-primary\">Derniers rendus</h3>\n            </div>\n\n            <div class=\"space-y-3\">\n              <div *ngFor=\"let rendu of derniersRendus\" class=\"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl\">\n                <div class=\"h-10 w-10 rounded-xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center text-white text-sm font-bold shadow-lg\">\n                  {{ getStudentInitials(rendu.etudiant) }}\n                </div>\n                <div class=\"flex-1\">\n                  <div class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary\">\n                    {{ getStudentName(rendu.etudiant) }}\n                  </div>\n                  <div class=\"text-xs text-text dark:text-dark-text-secondary\">\n                    {{ formatDate(rendu.dateRendu) }}\n                  </div>\n                </div>\n                <div class=\"bg-success/10 dark:bg-dark-accent-secondary/20 p-1.5 rounded-lg\">\n                  <svg class=\"w-4 h-4 text-success dark:text-dark-accent-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                  </svg>\n                </div>\n              </div>\n\n              <div *ngIf=\"!derniersRendus || derniersRendus.length === 0\" class=\"text-center py-8\">\n                <div class=\"bg-gray-100 dark:bg-dark-bg-tertiary/50 rounded-xl p-6\">\n                  <div class=\"bg-gray-200 dark:bg-dark-bg-tertiary p-3 rounded-lg inline-flex items-center justify-center mb-3\">\n                    <svg class=\"w-6 h-6 text-gray-400 dark:text-dark-text-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"></path>\n                    </svg>\n                  </div>\n                  <p class=\"text-sm text-text dark:text-dark-text-secondary\">Aucun rendu pour le moment</p>\n                </div>\n              </div>\n            </div>\n          </div>\n          </div> <!-- Fin du contenu des statistiques -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n\n\n\n\n\n\n\n\n\n"], "mappings": ";;;;;;;;IC4FYA,EAAA,CAAAC,cAAA,cAAwD;IAK9CD,EAAA,CAAAE,cAAA,EAAsH;IAAtHF,EAAA,CAAAC,cAAA,cAAsH;IACpHD,EAAA,CAAAG,SAAA,eAA4L;IAC9LH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAA4B;IAA5BL,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAC,cAAA,aAA2D;IAAAD,EAAA,CAAAM,MAAA,gBAAQ;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAG3EJ,EAAA,CAAAC,cAAA,aACiP;IAE7OD,EAAA,CAAAE,cAAA,EAA2E;IAA3EF,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAG,SAAA,gBAAgJ;IAClJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,6BAAW;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IAXtBJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAC,MAAA,CAAAC,WAAA,CAAAC,OAAA,OACF;IAIDX,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAY,UAAA,SAAAH,MAAA,CAAAI,UAAA,CAAAF,OAAA,GAAAX,EAAA,CAAAc,aAAA,CAAyB;;;;;IAjBpCd,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAAe,UAAA,IAAAC,4CAAA,mBA2BM;IACRhB,EAAA,CAAAI,YAAA,EAAM;;;;IA5BkBJ,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAY,UAAA,YAAAK,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAkB;;;;;IA8B1CnB,EAAA,CAAAC,cAAA,cAAwF;IAGlFD,EAAA,CAAAE,cAAA,EAAuH;IAAvHF,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAG,SAAA,eAAsM;IACxMH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAA2D;IAA3DL,EAAA,CAAAC,cAAA,YAA2D;IAAAD,EAAA,CAAAM,MAAA,2CAA+B;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;IA0DlGJ,EAAA,CAAAC,cAAA,cAAmC;IAE/BD,EAAA,CAAAG,SAAA,cAA0E;IAG5EH,EAAA,CAAAI,YAAA,EAAM;;;;;IAIRJ,EAAA,CAAAC,cAAA,UAAwB;IAIlBD,EAAA,CAAAE,cAAA,EAA2E;IAA3EF,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAG,SAAA,eAAsR;IACxRH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAAK;IAALL,EAAA,CAAAC,cAAA,UAAK;IAC+BD,EAAA,CAAAM,MAAA,mBAAY;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACnDJ,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAM,MAAA,wBAAgB;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAK3DJ,EAAA,CAAAC,cAAA,eAA2B;IAI0DD,EAAA,CAAAM,MAAA,2BAAmB;IAAAN,EAAA,CAAAI,YAAA,EAAO;IACzGJ,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAETJ,EAAA,CAAAC,cAAA,eAA0E;IACxED,EAAA,CAAAG,SAAA,eACqD;IACvDH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAuF;IAC/ED,EAAA,CAAAM,MAAA,IAAuC;IAAAN,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,IAAuC;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAKxDJ,EAAA,CAAAC,cAAA,eAAoC;IAKyED,EAAA,CAAAM,MAAA,cAAM;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAC/GJ,EAAA,CAAAC,cAAA,aAA2E;IAAAD,EAAA,CAAAM,MAAA,IAA4B;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAE7GJ,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAE,cAAA,EAAwH;IAAxHF,EAAA,CAAAC,cAAA,gBAAwH;IACtHD,EAAA,CAAAG,SAAA,iBAA+H;IACjIH,EAAA,CAAAI,YAAA,EAAM;IAMZJ,EAAA,CAAAK,eAAA,EAAmK;IAAnKL,EAAA,CAAAC,cAAA,gBAAmK;IAGtED,EAAA,CAAAM,MAAA,kBAAU;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACrGJ,EAAA,CAAAC,cAAA,cAA6D;IAAAD,EAAA,CAAAM,MAAA,IAA6C;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAEhHJ,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAE,cAAA,EAA0G;IAA1GF,EAAA,CAAAC,cAAA,gBAA0G;IACxGD,EAAA,CAAAG,SAAA,gBAA6H;IAC/HH,EAAA,CAAAI,YAAA,EAAM;IAMZJ,EAAA,CAAAK,eAAA,EAA8L;IAA9LL,EAAA,CAAAC,cAAA,gBAA8L;IAGxFD,EAAA,CAAAM,MAAA,YAAI;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACxGJ,EAAA,CAAAC,cAAA,cAAsE;IAAAD,EAAA,CAAAM,MAAA,IAA8B;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAE1GJ,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAE,cAAA,EAAmH;IAAnHF,EAAA,CAAAC,cAAA,eAAmH;IACjHD,EAAA,CAAAG,SAAA,iBAAgH;IAClHH,EAAA,CAAAI,YAAA,EAAM;;;;IAvDRJ,EAAA,CAAAO,SAAA,IACF;IADEP,EAAA,CAAAoB,kBAAA,MAAAC,MAAA,CAAAC,eAAA,CAAAC,MAAA,OAAAF,MAAA,CAAAG,cAAA,MACF;IAIKxB,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAyB,WAAA,UAAAJ,MAAA,CAAAK,qBAAA,QAAyC;IAGxC1B,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAQ,kBAAA,KAAAa,MAAA,CAAAK,qBAAA,2BAAuC;IACvC1B,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAQ,kBAAA,KAAAa,MAAA,CAAAM,gBAAA,sBAAuC;IAWkC3B,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAA4B,iBAAA,CAAAP,MAAA,CAAAC,eAAA,CAAAC,MAAA,CAA4B;IAe1CvB,EAAA,CAAAO,SAAA,IAA6C;IAA7CP,EAAA,CAAA4B,iBAAA,CAAAP,MAAA,CAAAG,cAAA,GAAAH,MAAA,CAAAC,eAAA,CAAAC,MAAA,CAA6C;IAepCvB,EAAA,CAAAO,SAAA,IAA8B;IAA9BP,EAAA,CAAAQ,kBAAA,KAAAa,MAAA,CAAAK,qBAAA,QAA8B;;;;;IA0B1G1B,EAAA,CAAAC,cAAA,eAAoI;IAEhID,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAoB;IAEhBD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAAE,cAAA,EAAwH;IAAxHF,EAAA,CAAAC,cAAA,eAAwH;IACtHD,EAAA,CAAAG,SAAA,iBAA+H;IACjIH,EAAA,CAAAI,YAAA,EAAM;;;;;IAbNJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAqB,MAAA,CAAAC,kBAAA,CAAAC,QAAA,CAAAC,QAAA,OACF;IAGIhC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAqB,MAAA,CAAAI,cAAA,CAAAF,QAAA,CAAAC,QAAA,OACF;IAEEhC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAqB,MAAA,CAAAK,UAAA,CAAAH,QAAA,CAAAI,SAAA,OACF;;;;;IASJnC,EAAA,CAAAC,cAAA,cAAqF;IAG/ED,EAAA,CAAAE,cAAA,EAAuH;IAAvHF,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAG,SAAA,eAAiN;IACnNH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAA2D;IAA3DL,EAAA,CAAAC,cAAA,YAA2D;IAAAD,EAAA,CAAAM,MAAA,iCAA0B;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;;;;;;;;;;ADpT3G,OAAM,MAAOgC,sBAAsB;EAQjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA6B,EAC7BC,WAAwB,EACxBC,aAA4B;IAJ5B,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IAZvB,KAAAxB,MAAM,GAAQ,IAAI;IAClB,KAAAyB,MAAM,GAAU,EAAE;IAClB,KAAAnB,cAAc,GAAW,CAAC;IAC1B,KAAAF,eAAe,GAAU,EAAE;IAC3B,KAAAsB,cAAc,GAAU,EAAE;IAC1B,KAAAC,SAAS,GAAY,IAAI;EAQtB;EAEHC,QAAQA,CAAA;IACN,MAAMC,EAAE,GAAG,IAAI,CAACT,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAIH,EAAE,EAAE;MACN,IAAI,CAACI,eAAe,CAACJ,EAAE,CAAC;;EAE5B;EAEAI,eAAeA,CAACJ,EAAU;IACxB,IAAI,CAACF,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACL,cAAc,CAACY,aAAa,CAACL,EAAE,CAAC,CAACM,SAAS,CAAC;MAC9CC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAACrC,MAAM,GAAGqC,IAAI;QAClB,IAAI,CAACC,qBAAqB,CAACT,EAAE,CAAC;MAChC,CAAC;MACDU,KAAK,EAAGC,GAAQ,IAAI;QAClBC,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEC,GAAG,CAAC;QAC1D,IAAI,CAACb,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAW,qBAAqBA,CAACI,QAAgB;IACpC;IACA,IAAI,CAAClB,aAAa,CAACmB,iBAAiB,CAACD,QAAQ,CAAC,CAACP,SAAS,CAAC;MACvDC,IAAI,EAAGX,MAAa,IAAI;QACtB,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACrB,eAAe,GAAGqB,MAAM,CAACmB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC/B,QAAQ,CAAC;QAE7D;QACA,IAAI,CAACY,cAAc,GAAG,CAAC,GAAG,IAAI,CAACtB,eAAe,CAAC,CAC5C0C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAAC/B,SAAS,CAAC,CAACiC,OAAO,EAAE,GAAG,IAAID,IAAI,CAACF,CAAC,CAAC9B,SAAS,CAAC,CAACiC,OAAO,EAAE,CAAC,CACjFC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEhB;QACA;QACA,MAAMC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,IAAI,CAACjD,eAAe,CAACkD,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzC,QAAQ,CAAC0C,GAAG,IAAID,CAAC,CAACzC,QAAQ,CAAC,CAAC;QAC7F,IAAI,CAACR,cAAc,GAAGmD,IAAI,CAACC,GAAG,CAACN,gBAAgB,CAACO,IAAI,EAAE,IAAI,CAACC,oBAAoB,EAAE,CAAC;QAElF,IAAI,CAACjC,SAAS,GAAG,KAAK;MACxB,CAAC;MACDY,KAAK,EAAGC,GAAQ,IAAI;QAClBC,OAAO,CAACF,KAAK,CAAC,6CAA6C,EAAEC,GAAG,CAAC;QACjE,IAAI,CAACb,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAiC,oBAAoBA,CAAA;IAClB;IACA,MAAMC,MAAM,GAAG,IAAI,CAAC7D,MAAM,EAAE6D,MAAM,EAAEC,WAAW,EAAE;IACjD,IAAID,MAAM,EAAEE,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACvC,IAAIF,MAAM,EAAEE,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACvC,IAAIF,MAAM,EAAEE,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACvC,OAAO,EAAE,CAAC,CAAC;EACb;;EAEApE,UAAUA,CAACqE,QAAgB;IACzB,OAAO,IAAI,CAACzC,WAAW,CAAC0C,cAAc,CAACD,QAAQ,CAAC;EAClD;EAEAE,YAAYA,CAACrC,EAAsB;IACjC,IAAI,CAACA,EAAE,EAAE;IAET,IAAIsC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAC7D,IAAI,CAAC7C,cAAc,CAAC4C,YAAY,CAACrC,EAAE,CAAC,CAACM,SAAS,CAAC;QAC7CC,IAAI,EAAEA,CAAA,KAAK;UACTgC,KAAK,CAAC,6BAA6B,CAAC;UACpC,IAAI,CAAC/C,MAAM,CAACgD,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;QAC3C,CAAC;QACD9B,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;UAC7D4B,KAAK,CAAC,yCAAyC,CAAC;QAClD;OACD,CAAC;;EAEN;EAEApD,UAAUA,CAACsD,IAAmB;IAC5B,MAAMC,CAAC,GAAG,IAAItB,IAAI,CAACqB,IAAI,CAAC;IACxB,OAAO,GAAGC,CAAC,CAACC,OAAO,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAACH,CAAC,CAACI,QAAQ,EAAE,GAAG,CAAC,EACnEF,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,CAAC,CAACK,WAAW,EAAE,EAAE;EAC1C;EAEApF,WAAWA,CAACwE,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACD,QAAQ,CAAC,GAAG,CAAC,IAAIC,QAAQ,CAACD,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMc,KAAK,GAAGb,QAAQ,CAACc,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACxE,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAO2D,QAAQ;EACjB;EAEAe,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC/E,MAAM,EAAEgF,UAAU,EAAE,OAAO,UAAU;IAE/C,MAAMC,GAAG,GAAG,IAAIhC,IAAI,EAAE;IACtB,MAAMiC,QAAQ,GAAG,IAAIjC,IAAI,CAAC,IAAI,CAACjD,MAAM,CAACgF,UAAU,CAAC;IAEjD,IAAIE,QAAQ,GAAGD,GAAG,EAAE;MAClB,OAAO,QAAQ;KAChB,MAAM,IAAIC,QAAQ,CAAChC,OAAO,EAAE,GAAG+B,GAAG,CAAC/B,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;MACvE,OAAO,QAAQ;KAChB,MAAM;MACL,OAAO,OAAO;;EAElB;EAEAiC,cAAcA,CAAA;IACZ,MAAMC,MAAM,GAAG,IAAI,CAACL,gBAAgB,EAAE;IAEtC,QAAQK,MAAM;MACZ,KAAK,OAAO;QACV,OAAO,2JAA2J;MACpK,KAAK,QAAQ;QACX,OAAO,iHAAiH;MAC1H,KAAK,QAAQ;QACX,OAAO,0HAA0H;MACnI;QACE,OAAO,4IAA4I;;EAEzJ;EAEA5E,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACF,cAAc,KAAK,CAAC,EAAE,OAAO,CAAC;IACvC,OAAOmD,IAAI,CAAC4B,KAAK,CAAE,IAAI,CAACjF,eAAe,CAACC,MAAM,GAAG,IAAI,CAACC,cAAc,GAAI,GAAG,CAAC;EAC9E;EAEAG,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACT,MAAM,EAAEgF,UAAU,EAAE,OAAO,CAAC;IAEtC,MAAMC,GAAG,GAAG,IAAIhC,IAAI,EAAE;IACtB,MAAMiC,QAAQ,GAAG,IAAIjC,IAAI,CAAC,IAAI,CAACjD,MAAM,CAACgF,UAAU,CAAC;IACjD,MAAMM,QAAQ,GAAGJ,QAAQ,CAAChC,OAAO,EAAE,GAAG+B,GAAG,CAAC/B,OAAO,EAAE;IACnD,MAAMqC,QAAQ,GAAG9B,IAAI,CAAC+B,IAAI,CAACF,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,OAAO7B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE6B,QAAQ,CAAC;EAC9B;EAEA3E,kBAAkBA,CAACE,QAAa;IAC9B,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAI;IAE1B;IACA,MAAM2E,SAAS,GAAG3E,QAAQ,CAAC2E,SAAS,IAAI3E,QAAQ,CAAC4E,MAAM,IAAI,EAAE;IAC7D,MAAMC,QAAQ,GAAG7E,QAAQ,CAAC6E,QAAQ,IAAI7E,QAAQ,CAAC8E,GAAG,IAAI,EAAE;IAExD,IAAIH,SAAS,IAAIE,QAAQ,EAAE;MACzB,OAAO,CAACF,SAAS,CAACI,MAAM,CAAC,CAAC,CAAC,GAAGF,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,EAAEC,WAAW,EAAE;;IAGjE;IACA,MAAMC,QAAQ,GAAGjF,QAAQ,CAACiF,QAAQ,IAAIjF,QAAQ,CAACkF,IAAI,IAAI,EAAE;IACzD,IAAID,QAAQ,EAAE;MACZ,MAAMlB,KAAK,GAAGkB,QAAQ,CAACE,IAAI,EAAE,CAACnB,KAAK,CAAC,GAAG,CAAC;MACxC,IAAID,KAAK,CAACxE,MAAM,IAAI,CAAC,EAAE;QACrB,OAAO,CAACwE,KAAK,CAAC,CAAC,CAAC,CAACgB,MAAM,CAAC,CAAC,CAAC,GAAGhB,KAAK,CAAC,CAAC,CAAC,CAACgB,MAAM,CAAC,CAAC,CAAC,EAAEC,WAAW,EAAE;OAC/D,MAAM;QACL,OAAOC,QAAQ,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACJ,WAAW,EAAE;;;IAIjD;IACA,IAAIL,SAAS,EAAE;MACb,OAAOA,SAAS,CAACS,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACJ,WAAW,EAAE;;IAGhD,OAAO,IAAI;EACb;EAEA/E,cAAcA,CAACD,QAAa;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,qBAAqB;IAE3C;IACA,MAAM2E,SAAS,GAAG3E,QAAQ,CAAC2E,SAAS,IAAI3E,QAAQ,CAAC4E,MAAM,IAAI,EAAE;IAC7D,MAAMC,QAAQ,GAAG7E,QAAQ,CAAC6E,QAAQ,IAAI7E,QAAQ,CAAC8E,GAAG,IAAI,EAAE;IAExD,IAAIH,SAAS,IAAIE,QAAQ,EAAE;MACzB,OAAO,GAAGF,SAAS,IAAIE,QAAQ,EAAE,CAACM,IAAI,EAAE;;IAG1C;IACA,MAAMF,QAAQ,GAAGjF,QAAQ,CAACiF,QAAQ,IAAIjF,QAAQ,CAACkF,IAAI,IAAI,EAAE;IACzD,IAAID,QAAQ,EAAE;MACZ,OAAOA,QAAQ,CAACE,IAAI,EAAE;;IAGxB;IACA,IAAIR,SAAS,EAAE;MACb,OAAOA,SAAS,CAACQ,IAAI,EAAE;;IAGzB;IACA,IAAInF,QAAQ,CAACqF,KAAK,EAAE;MAClB,OAAOrF,QAAQ,CAACqF,KAAK;;IAGvB,OAAO,qBAAqB;EAC9B;;;uBA1NWjF,sBAAsB,EAAApC,EAAA,CAAAsH,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxH,EAAA,CAAAsH,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAzH,EAAA,CAAAsH,iBAAA,CAAAI,EAAA,CAAAC,aAAA,GAAA3H,EAAA,CAAAsH,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA7H,EAAA,CAAAsH,iBAAA,CAAAQ,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAtB3F,sBAAsB;MAAA4F,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVnCtI,EAAA,CAAAC,cAAA,aAAiK;UAM3BD,EAAA,CAAAM,MAAA,cAAO;UAAAN,EAAA,CAAAI,YAAA,EAAI;UACzIJ,EAAA,CAAAE,cAAA,EAA2E;UAA3EF,EAAA,CAAAC,cAAA,aAA2E;UACzED,EAAA,CAAAG,SAAA,cAA8F;UAChGH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAqE;UAArEL,EAAA,CAAAC,cAAA,cAAqE;UAAAD,EAAA,CAAAM,MAAA,GAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAGxHJ,EAAA,CAAAC,cAAA,cAA2J;UAInJD,EAAA,CAAAE,cAAA,EAAsF;UAAtFF,EAAA,CAAAC,cAAA,eAAsF;UACpFD,EAAA,CAAAG,SAAA,gBAA4J;UAC9JH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAAK;UAALL,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,eAA8C;UAE1CD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAwV;UAC1VH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,gBAA6E;UAAAD,EAAA,CAAAM,MAAA,IAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAEhIJ,EAAA,CAAAC,cAAA,eAAyC;UACvCD,EAAA,CAAAE,cAAA,EAA0G;UAA1GF,EAAA,CAAAC,cAAA,eAA0G;UACxGD,EAAA,CAAAG,SAAA,gBAA6H;UAC/HH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA8D;UAA9DL,EAAA,CAAAC,cAAA,gBAA8D;UAAAD,EAAA,CAAAM,MAAA,IAAgF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAO7JJ,EAAA,CAAAC,cAAA,eAAyC;UAErCD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAOfJ,EAAA,CAAAC,cAAA,eAAmD;UAQzCD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAwG;UAC1GH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,cAA6E;UAAAD,EAAA,CAAAM,MAAA,6BAAqB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAEzGJ,EAAA,CAAAC,cAAA,eAA2D;UAEvDD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAKRJ,EAAA,CAAAC,cAAA,eAA2J;UAGrJD,EAAA,CAAAE,cAAA,EAA0H;UAA1HF,EAAA,CAAAC,cAAA,eAA0H;UACxHD,EAAA,CAAAG,SAAA,gBAAsM;UACxMH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,cAA6E;UAC3ED,EAAA,CAAAM,MAAA,yBACA;UAAAN,EAAA,CAAAC,cAAA,gBAA+E;UAC7ED,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAIXJ,EAAA,CAAAe,UAAA,KAAAyH,sCAAA,kBA6BM;UAENxI,EAAA,CAAAe,UAAA,KAAA0H,sCAAA,kBASM;UACRzI,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAA2J;UAGrJD,EAAA,CAAAE,cAAA,EAAmH;UAAnHF,EAAA,CAAAC,cAAA,eAAmH;UACjHD,EAAA,CAAAG,SAAA,gBAAqjB;UAEvjBH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,cAA6E;UAAAD,EAAA,CAAAM,MAAA,2BAAmB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAGvGJ,EAAA,CAAAC,cAAA,eAAmD;UAI7CD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAwM;UAC1MH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAM;UAANL,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAIzBJ,EAAA,CAAAC,cAAA,aACmO;UAE/ND,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAiN;UACnNH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAM;UAANL,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAM,MAAA,mBAAW;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAI5BJ,EAAA,CAAAC,cAAA,kBAC+M;UADvMD,EAAA,CAAA0I,UAAA,mBAAAC,yDAAA;YAAA,OAASJ,GAAA,CAAAnD,YAAA,CAAAmD,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAAwD,GAAA,CAAyB;UAAA,EAAC;UAEzC1E,EAAA,CAAAC,cAAA,eAAwD;UACtDD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAA8M;UAChNH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAM;UAANL,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAM,MAAA,iBAAS;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAQhCJ,EAAA,CAAAC,cAAA,eAAuB;UAMnBD,EAAA,CAAAe,UAAA,KAAA6H,sCAAA,kBAMM;UAGN5I,EAAA,CAAAe,UAAA,KAAA8H,sCAAA,mBAkFI;UAGN7I,EAAA,CAAAC,cAAA,eAAuJ;UAI/ID,EAAA,CAAAE,cAAA,EAA0H;UAA1HF,EAAA,CAAAC,cAAA,eAA0H;UACxHD,EAAA,CAAAG,SAAA,gBAA6H;UAC/HH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,cAA6E;UAAAD,EAAA,CAAAM,MAAA,uBAAe;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAGnGJ,EAAA,CAAAC,cAAA,eAAuB;UACrBD,EAAA,CAAAe,UAAA,KAAA+H,sCAAA,mBAiBM;UAEN9I,EAAA,CAAAe,UAAA,KAAAgI,sCAAA,kBASM;UACR/I,EAAA,CAAAI,YAAA,EAAM;;;UAvT2DJ,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAA4B,iBAAA,EAAA2G,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAA8H,KAAA,8BAA0C;UAavGhJ,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAQ,kBAAA,OAAA+H,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAA8H,KAAA,0BACF;UAMiFhJ,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAA4B,iBAAA,EAAA2G,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAA6D,MAAA,wBAA0C;UAMzD/E,EAAA,CAAAO,SAAA,GAAgF;UAAhFP,EAAA,CAAA4B,iBAAA,EAAA2G,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAAgF,UAAA,IAAAqC,GAAA,CAAArG,UAAA,CAAAqG,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAAgF,UAAA,yBAAgF;UAQ9IlG,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAY,UAAA,YAAA2H,GAAA,CAAAlC,cAAA,GAA4B;UAChCrG,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAQ,kBAAA,MAAA+H,GAAA,CAAAtC,gBAAA,QACF;UAuBEjG,EAAA,CAAAO,SAAA,IACF;UADEP,EAAA,CAAAQ,kBAAA,OAAA+H,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAA+H,WAAA,uDACF;UAeIjJ,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAoB,kBAAA,QAAAmH,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAAC,QAAA,kBAAAoH,GAAA,CAAArH,MAAA,CAAAC,QAAA,CAAAI,MAAA,sBAAAgH,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAAC,QAAA,kBAAAoH,GAAA,CAAArH,MAAA,CAAAC,QAAA,CAAAI,MAAA,6BACF;UAIEvB,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAY,UAAA,UAAA2H,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAAC,QAAA,kBAAAoH,GAAA,CAAArH,MAAA,CAAAC,QAAA,CAAAI,MAAA,MAAkC;UA+BlCvB,EAAA,CAAAO,SAAA,GAAuD;UAAvDP,EAAA,CAAAY,UAAA,WAAA2H,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAAC,QAAA,KAAAoH,GAAA,CAAArH,MAAA,CAAAC,QAAA,CAAAI,MAAA,OAAuD;UAyBxDvB,EAAA,CAAAO,SAAA,IAA0D;UAA1DP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAAkJ,eAAA,KAAAC,GAAA,EAAAZ,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAAwD,GAAA,EAA0D;UAU1D1E,EAAA,CAAAO,SAAA,GAAyC;UAAzCP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAAoJ,eAAA,KAAAC,GAAA,EAAyC,gBAAArJ,EAAA,CAAAkJ,eAAA,KAAAI,GAAA,EAAAf,GAAA,CAAArH,MAAA,kBAAAqH,GAAA,CAAArH,MAAA,CAAAwD,GAAA;UA8BxC1E,EAAA,CAAAO,SAAA,IAAe;UAAfP,EAAA,CAAAY,UAAA,SAAA2H,GAAA,CAAA1F,SAAA,CAAe;UASf7C,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAY,UAAA,UAAA2H,GAAA,CAAA1F,SAAA,CAAgB;UAiGK7C,EAAA,CAAAO,SAAA,IAAiB;UAAjBP,EAAA,CAAAY,UAAA,YAAA2H,GAAA,CAAA3F,cAAA,CAAiB;UAmBlC5C,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAAY,UAAA,UAAA2H,GAAA,CAAA3F,cAAA,IAAA2F,GAAA,CAAA3F,cAAA,CAAArB,MAAA,OAAoD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}