<!-- Indicateur de statut GraphQL -->
<div
  *ngIf="showStatus || !isConnected"
  class="fixed top-16 left-1/2 transform -translate-x-1/2 z-40 transition-all duration-300"
  [ngClass]="{
    'animate-slideInDown': showStatus,
    'animate-slideOutUp': !showStatus && isConnected
  }"
>
  <div
    [ngClass]="getStatusColor()"
    class="px-4 py-2 rounded-full shadow-lg text-white text-sm font-medium flex items-center space-x-2 backdrop-blur-sm"
  >
    <i [class]="getStatusIcon()" [ngClass]="{ 'animate-pulse': !isConnected }"></i>
    <span>{{ getStatusText() }}</span>
    
    <!-- Bouton retry (si déconnecté) -->
    <button
      *ngIf="!isConnected"
      (click)="retry()"
      class="ml-2 p-1 hover:bg-white/20 rounded-full transition-colors"
      title="Réessayer"
    >
      <i class="fas fa-redo text-xs"></i>
    </button>
    
    <!-- Bouton fermer (si connecté) -->
    <button
      *ngIf="isConnected"
      (click)="hideStatus()"
      class="ml-2 p-1 hover:bg-white/20 rounded-full transition-colors"
      title="Fermer"
    >
      <i class="fas fa-times text-xs"></i>
    </button>
  </div>
</div>

<!-- Indicateur discret en bas à droite si problème GraphQL -->
<div
  *ngIf="!isConnected"
  class="fixed bottom-4 left-4 z-30"
>
  <div class="bg-orange-500 text-white p-3 rounded-lg shadow-lg max-w-sm">
    <div class="flex items-start space-x-2">
      <i class="fas fa-exclamation-triangle text-yellow-300 mt-0.5"></i>
      <div class="flex-1">
        <p class="text-sm font-medium">Problème de connexion</p>
        <p class="text-xs opacity-90 mt-1">{{ lastError || 'Connexion GraphQL interrompue' }}</p>
        <button
          (click)="retry()"
          class="mt-2 text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded transition-colors"
        >
          Réessayer
        </button>
      </div>
      <button
        (click)="hideStatus()"
        class="p-1 hover:bg-white/20 rounded transition-colors"
        title="Fermer"
      >
        <i class="fas fa-times text-xs"></i>
      </button>
    </div>
  </div>
</div>

<!-- Styles CSS intégrés -->
<style>
  @keyframes slideInDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @keyframes slideOutUp {
    from {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
    to {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
  }

  .animate-slideInDown {
    animation: slideInDown 0.3s ease-out;
  }

  .animate-slideOutUp {
    animation: slideOutUp 0.3s ease-in;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .animate-pulse {
    animation: pulse 2s infinite;
  }
</style>
