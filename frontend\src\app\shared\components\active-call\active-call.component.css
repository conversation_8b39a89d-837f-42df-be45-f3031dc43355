/* Styles pour le composant d'appel actif */

.active-call-container {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.call-controls button {
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.call-controls button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.call-controls button:active {
  transform: translateY(0);
}

.video-container {
  position: relative;
  overflow: hidden;
}

.local-video {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 5rem;
  height: 4rem;
  border-radius: 0.5rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  z-index: 10;
}

.remote-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.call-duration {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.minimize-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.call-avatar {
  position: relative;
}

.call-avatar::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, #10b981, #059669);
  animation: rotate 3s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 640px) {
  .active-call-container:not(.minimized) {
    width: calc(100vw - 2rem);
    right: 1rem;
    left: 1rem;
    max-width: 20rem;
  }
  
  .active-call-container.minimized {
    width: calc(100vw - 2rem);
    right: 1rem;
    left: 1rem;
    max-width: 16rem;
  }
}

/* Animation d'entrée */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.active-call-container {
  animation: slideInFromRight 0.3s ease-out;
}

/* Effets de glow pour les boutons actifs */
.btn-muted {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
}

.btn-video-off {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
}

.btn-end-call {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.7);
}

.btn-end-call:hover {
  box-shadow: 0 0 30px rgba(239, 68, 68, 0.9);
}
