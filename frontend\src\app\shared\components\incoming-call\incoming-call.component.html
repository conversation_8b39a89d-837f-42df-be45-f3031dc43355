<!-- Modal d'appel entrant -->
<div
  *ngIf="isVisible && incomingCall"
  class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
>
  <!-- Contenu de la modal -->
  <div
    class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 max-w-sm w-full mx-4 transform transition-all duration-300 scale-100"
    style="animation: slideInUp 0.3s ease-out"
  >
    <!-- Avatar et info de l'appelant -->
    <div class="text-center mb-6">
      <div class="relative inline-block mb-4">
        <img
          [src]="incomingCall.caller.image || 'assets/images/default-avatar.png'"
          [alt]="incomingCall.caller.username"
          class="w-24 h-24 rounded-full object-cover border-4 border-blue-500 shadow-lg"
        />
        <!-- Indicateur d'appel animé -->
        <div
          class="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white shadow-lg"
          style="animation: pulse 2s infinite"
        >
          <i [class]="getCallTypeIcon()" class="text-sm"></i>
        </div>
      </div>
      
      <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
        {{ incomingCall.caller.username }}
      </h3>
      
      <p class="text-gray-600 dark:text-gray-300 text-sm">
        {{ getCallTypeText() }} entrant...
      </p>
    </div>

    <!-- Boutons d'action -->
    <div class="flex justify-center space-x-6">
      <!-- Bouton Rejeter -->
      <button
        (click)="rejectCall()"
        class="w-16 h-16 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-4 focus:ring-red-300"
        title="Rejeter l'appel"
      >
        <i class="fas fa-phone-slash text-xl"></i>
      </button>

      <!-- Bouton Accepter -->
      <button
        (click)="acceptCall()"
        class="w-16 h-16 bg-green-500 hover:bg-green-600 text-white rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-4 focus:ring-green-300"
        title="Accepter l'appel"
        style="animation: bounce 2s infinite"
      >
        <i class="fas fa-phone text-xl"></i>
      </button>
    </div>

    <!-- Indicateur de sonnerie -->
    <div class="mt-6 text-center">
      <div class="flex justify-center items-center space-x-1">
        <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
        <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
        <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
      </div>
      <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Sonnerie...</p>
    </div>
  </div>
</div>

<!-- Styles CSS intégrés -->
<style>
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes pulse {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }
  }

  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translateY(0);
    }
    40%, 43% {
      transform: translateY(-8px);
    }
    70% {
      transform: translateY(-4px);
    }
  }
</style>
