{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CallType, CallStatus } from '../../models/message.model';\nexport let ActiveCallComponent = class ActiveCallComponent {\n  constructor(messageService, callService, logger) {\n    this.messageService = messageService;\n    this.callService = callService;\n    this.logger = logger;\n    this.activeCall = null;\n    this.callDuration = '00:00';\n    this.isAudioMuted = false;\n    this.isVideoMuted = false;\n    this.isSpeakerOn = true;\n    // Propriétés pour l'interface\n    this.hasLocalVideo = false;\n    this.hasRemoteVideo = false;\n    this.audioLevels = [];\n    this.callStartTime = null;\n    this.subscriptions = [];\n    // Exposer les énums au template\n    this.CallType = CallType;\n    this.CallStatus = CallStatus;\n  }\n  ngOnInit() {\n    // S'abonner à l'appel actif\n    const activeCallSub = this.callService.activeCall$.subscribe(call => {\n      const previousCall = this.activeCall;\n      this.activeCall = call;\n      console.log('📞 [ActiveCall] Active call updated:', call);\n      if (call && call.status === CallStatus.CONNECTED) {\n        if (!previousCall || previousCall.id !== call.id) {\n          // Nouvel appel connecté\n          console.log('✅ [ActiveCall] Call connected, starting timer and setting up media');\n          this.startCallTimer();\n          this.setupMediaStreams();\n        }\n      } else if (!call || call.status !== CallStatus.CONNECTED) {\n        // Appel terminé ou non connecté\n        this.stopCallTimer();\n        this.stopAudioVisualizer();\n      }\n    });\n    this.subscriptions.push(activeCallSub);\n  }\n  /**\n   * Configure les flux média pour l'appel\n   */\n  setupMediaStreams() {\n    console.log('🎥 [ActiveCall] Setting up media streams...');\n    // Configurer le flux local\n    const localStream = this.callService.getLocalStream();\n    if (localStream && this.localVideo?.nativeElement) {\n      console.log('📹 [ActiveCall] Setting local video stream');\n      this.localVideo.nativeElement.srcObject = localStream;\n      this.hasLocalVideo = localStream.getVideoTracks().length > 0;\n    }\n    // Initialiser le visualiseur audio pour les appels audio\n    if (!this.isVideoCall()) {\n      this.startAudioVisualizer();\n    }\n    // Mettre à jour les états des contrôles\n    this.isAudioMuted = !this.callService.getAudioEnabled();\n    this.isVideoMuted = !this.callService.getVideoEnabled();\n  }\n  /**\n   * Démarre le visualiseur audio\n   */\n  startAudioVisualizer() {\n    // Générer des niveaux audio animés\n    this.audioLevels = Array.from({\n      length: 15\n    }, () => Math.random() * 30 + 10);\n    this.audioVisualizerInterval = setInterval(() => {\n      this.audioLevels = this.audioLevels.map(() => Math.random() * 40 + 5);\n    }, 150);\n  }\n  /**\n   * Arrête le visualiseur audio\n   */\n  stopAudioVisualizer() {\n    if (this.audioVisualizerInterval) {\n      clearInterval(this.audioVisualizerInterval);\n      this.audioVisualizerInterval = null;\n    }\n  }\n  ngAfterViewInit() {\n    // Configurer les éléments vidéo après le rendu du composant\n    if (this.activeCall && this.activeCall.status === CallStatus.CONNECTED) {\n      this.setupMediaStreams();\n    }\n  }\n  // Démarrer le minuteur d'appel\n  startCallTimer() {\n    this.callStartTime = new Date();\n    this.stopCallTimer(); // Arrêter tout minuteur existant\n    this.durationInterval = setInterval(() => {\n      if (!this.callStartTime) return;\n      const now = new Date();\n      const diff = Math.floor((now.getTime() - this.callStartTime.getTime()) / 1000);\n      const minutes = Math.floor(diff / 60).toString().padStart(2, '0');\n      const seconds = (diff % 60).toString().padStart(2, '0');\n      this.callDuration = `${minutes}:${seconds}`;\n    }, 1000);\n  }\n  // Arrêter le minuteur d'appel\n  stopCallTimer() {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n  }\n  // Terminer l'appel\n  endCall() {\n    if (!this.activeCall) {\n      return;\n    }\n    this.logger.debug('Ending call', {\n      callId: this.activeCall.id\n    });\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: result => {\n        this.logger.debug('Call ended successfully', {\n          callId: this.activeCall?.id,\n          success: result.success\n        });\n      },\n      error: error => {\n        this.logger.error('Error ending call', error);\n      }\n    });\n  }\n  // Basculer le micro\n  toggleMicrophone() {\n    if (!this.activeCall) {\n      return;\n    }\n    console.log('🎤 [ActiveCall] Toggling microphone...');\n    // Basculer localement d'abord\n    const newAudioState = this.callService.toggleAudio();\n    this.isAudioMuted = !newAudioState;\n    console.log('🎤 [ActiveCall] Audio toggled:', {\n      enabled: newAudioState,\n      muted: this.isAudioMuted\n    });\n    // Optionnel : Notifier le serveur\n    this.callService.toggleMedia(this.activeCall.id, undefined, newAudioState).subscribe({\n      next: result => {\n        console.log('✅ [ActiveCall] Microphone toggle sent to server');\n      },\n      error: error => {\n        console.error('❌ [ActiveCall] Error toggling microphone on server:', error);\n        // Continuer même si le serveur échoue\n      }\n    });\n  }\n  // Basculer la caméra\n  toggleCamera() {\n    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) {\n      return;\n    }\n    console.log('📹 [ActiveCall] Toggling camera...');\n    // Basculer localement d'abord\n    const newVideoState = this.callService.toggleVideo();\n    this.isVideoMuted = !newVideoState;\n    console.log('📹 [ActiveCall] Video toggled:', {\n      enabled: newVideoState,\n      muted: this.isVideoMuted\n    });\n    // Optionnel : Notifier le serveur\n    this.callService.toggleMedia(this.activeCall.id, newVideoState, undefined).subscribe({\n      next: result => {\n        console.log('✅ [ActiveCall] Camera toggle sent to server');\n      },\n      error: error => {\n        console.error('❌ [ActiveCall] Error toggling camera on server:', error);\n        // Continuer même si le serveur échoue\n      }\n    });\n  }\n  // Basculer le haut-parleur\n  toggleSpeaker() {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    if (this.remoteVideo?.nativeElement) {\n      this.remoteVideo.nativeElement.volume = this.isSpeakerOn ? 1 : 0;\n    }\n    this.logger.debug('Speaker toggled', {\n      on: this.isSpeakerOn\n    });\n  }\n  // Obtenir le nom de l'autre participant\n  getOtherParticipantName() {\n    if (!this.activeCall) {\n      return '';\n    }\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n    return isCurrentUserCaller ? this.activeCall.recipient.username : this.activeCall.caller.username;\n  }\n  // Obtenir l'avatar de l'autre participant\n  getOtherParticipantAvatar() {\n    if (!this.activeCall) {\n      return 'assets/images/default-avatar.png';\n    }\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n    const avatar = isCurrentUserCaller ? this.activeCall.recipient.image : this.activeCall.caller.image;\n    return avatar || 'assets/images/default-avatar.png';\n  }\n  // Obtenir le statut de l'appel sous forme de texte\n  getCallStatusText() {\n    if (!this.activeCall) {\n      return '';\n    }\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Appel en cours...';\n      case CallStatus.CONNECTED:\n        return this.callDuration;\n      case CallStatus.ENDED:\n        return 'Appel terminé';\n      case CallStatus.MISSED:\n        return 'Appel manqué';\n      case CallStatus.REJECTED:\n        return 'Appel rejeté';\n      case CallStatus.FAILED:\n        return \"Échec de l'appel\";\n      default:\n        return '';\n    }\n  }\n  // Vérifier si l'appel est connecté\n  isCallConnected() {\n    return this.activeCall?.status === CallStatus.CONNECTED;\n  }\n  // Vérifier si l'appel est en cours de sonnerie\n  isCallRinging() {\n    return this.activeCall?.status === CallStatus.RINGING;\n  }\n  // Vérifier si l'appel est un appel vidéo\n  isVideoCall() {\n    return this.activeCall?.type === CallType.VIDEO || this.activeCall?.type === CallType.VIDEO_ONLY;\n  }\n  ngOnDestroy() {\n    // Nettoyer les abonnements et les minuteurs\n    this.stopCallTimer();\n    this.stopAudioVisualizer();\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n};\n__decorate([ViewChild('localVideo')], ActiveCallComponent.prototype, \"localVideo\", void 0);\n__decorate([ViewChild('remoteVideo')], ActiveCallComponent.prototype, \"remoteVideo\", void 0);\nActiveCallComponent = __decorate([Component({\n  selector: 'app-active-call',\n  templateUrl: './active-call.component.html',\n  styleUrls: ['./active-call.component.css']\n})], ActiveCallComponent);", "map": {"version": 3, "names": ["Component", "ViewChild", "CallType", "CallStatus", "ActiveCallComponent", "constructor", "messageService", "callService", "logger", "activeCall", "callDuration", "isAudioMuted", "isVideoMuted", "isSpeakerOn", "hasLocalVideo", "hasRemoteVideo", "audioLevels", "callStartTime", "subscriptions", "ngOnInit", "activeCallSub", "activeCall$", "subscribe", "call", "previousCall", "console", "log", "status", "CONNECTED", "id", "startCallTimer", "setupMediaStreams", "stopCallTimer", "stopAudioVisualizer", "push", "localStream", "getLocalStream", "localVideo", "nativeElement", "srcObject", "getVideoTracks", "length", "isVideoCall", "startAudioVisualizer", "getAudioEnabled", "getVideoEnabled", "Array", "from", "Math", "random", "audioVisualizerInterval", "setInterval", "map", "clearInterval", "ngAfterViewInit", "Date", "durationInterval", "now", "diff", "floor", "getTime", "minutes", "toString", "padStart", "seconds", "endCall", "debug", "callId", "next", "result", "success", "error", "toggleMicrophone", "newAudioState", "toggleAudio", "enabled", "muted", "toggleMedia", "undefined", "toggleCamera", "type", "AUDIO", "newVideoState", "toggleVideo", "toggleSpeaker", "remoteVideo", "volume", "on", "getOtherParticipantName", "currentUserId", "localStorage", "getItem", "isCurrentUserCaller", "caller", "recipient", "username", "getOtherParticipantAvatar", "avatar", "image", "getCallStatusText", "RINGING", "ENDED", "MISSED", "REJECTED", "FAILED", "isCallConnected", "isCallRinging", "VIDEO", "VIDEO_ONLY", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.ts"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ViewChild,\n  ElementRef,\n  AfterViewInit,\n} from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { Call, CallType, CallStatus } from '../../models/message.model';\nimport { MessageService } from '../../services/message.service';\nimport { CallService } from '../../services/call.service';\nimport { LoggerService } from '../../services/logger.service';\n\n@Component({\n  selector: 'app-active-call',\n  templateUrl: './active-call.component.html',\n  styleUrls: ['./active-call.component.css'],\n})\nexport class ActiveCallComponent implements OnInit, OnDestroy, AfterViewInit {\n  @ViewChild('localVideo') localVideo!: ElementRef<HTMLVideoElement>;\n  @ViewChild('remoteVideo') remoteVideo!: ElementRef<HTMLVideoElement>;\n\n  activeCall: Call | null = null;\n  callDuration: string = '00:00';\n  isAudioMuted: boolean = false;\n  isVideoMuted: boolean = false;\n  isSpeakerOn: boolean = true;\n\n  // Propriétés pour l'interface\n  hasLocalVideo: boolean = false;\n  hasRemoteVideo: boolean = false;\n  audioLevels: number[] = [];\n\n  private durationInterval: any;\n  private callStartTime: Date | null = null;\n  private subscriptions: Subscription[] = [];\n  private audioVisualizerInterval: any;\n\n  // Exposer les énums au template\n  CallType = CallType;\n  CallStatus = CallStatus;\n\n  constructor(\n    private messageService: MessageService,\n    private callService: CallService,\n    private logger: LoggerService\n  ) {}\n\n  ngOnInit(): void {\n    // S'abonner à l'appel actif\n    const activeCallSub = this.callService.activeCall$.subscribe((call) => {\n      const previousCall = this.activeCall;\n      this.activeCall = call;\n\n      console.log('📞 [ActiveCall] Active call updated:', call);\n\n      if (call && call.status === CallStatus.CONNECTED) {\n        if (!previousCall || previousCall.id !== call.id) {\n          // Nouvel appel connecté\n          console.log(\n            '✅ [ActiveCall] Call connected, starting timer and setting up media'\n          );\n          this.startCallTimer();\n          this.setupMediaStreams();\n        }\n      } else if (!call || call.status !== CallStatus.CONNECTED) {\n        // Appel terminé ou non connecté\n        this.stopCallTimer();\n        this.stopAudioVisualizer();\n      }\n    });\n\n    this.subscriptions.push(activeCallSub);\n  }\n\n  /**\n   * Configure les flux média pour l'appel\n   */\n  private setupMediaStreams(): void {\n    console.log('🎥 [ActiveCall] Setting up media streams...');\n\n    // Configurer le flux local\n    const localStream = this.callService.getLocalStream();\n    if (localStream && this.localVideo?.nativeElement) {\n      console.log('📹 [ActiveCall] Setting local video stream');\n      this.localVideo.nativeElement.srcObject = localStream;\n      this.hasLocalVideo = localStream.getVideoTracks().length > 0;\n    }\n\n    // Initialiser le visualiseur audio pour les appels audio\n    if (!this.isVideoCall()) {\n      this.startAudioVisualizer();\n    }\n\n    // Mettre à jour les états des contrôles\n    this.isAudioMuted = !this.callService.getAudioEnabled();\n    this.isVideoMuted = !this.callService.getVideoEnabled();\n  }\n\n  /**\n   * Démarre le visualiseur audio\n   */\n  private startAudioVisualizer(): void {\n    // Générer des niveaux audio animés\n    this.audioLevels = Array.from(\n      { length: 15 },\n      () => Math.random() * 30 + 10\n    );\n\n    this.audioVisualizerInterval = setInterval(() => {\n      this.audioLevels = this.audioLevels.map(() => Math.random() * 40 + 5);\n    }, 150);\n  }\n\n  /**\n   * Arrête le visualiseur audio\n   */\n  private stopAudioVisualizer(): void {\n    if (this.audioVisualizerInterval) {\n      clearInterval(this.audioVisualizerInterval);\n      this.audioVisualizerInterval = null;\n    }\n  }\n\n  ngAfterViewInit(): void {\n    // Configurer les éléments vidéo après le rendu du composant\n    if (this.activeCall && this.activeCall.status === CallStatus.CONNECTED) {\n      this.setupMediaStreams();\n    }\n  }\n\n  // Démarrer le minuteur d'appel\n  private startCallTimer(): void {\n    this.callStartTime = new Date();\n    this.stopCallTimer(); // Arrêter tout minuteur existant\n\n    this.durationInterval = setInterval(() => {\n      if (!this.callStartTime) return;\n\n      const now = new Date();\n      const diff = Math.floor(\n        (now.getTime() - this.callStartTime.getTime()) / 1000\n      );\n\n      const minutes = Math.floor(diff / 60)\n        .toString()\n        .padStart(2, '0');\n      const seconds = (diff % 60).toString().padStart(2, '0');\n\n      this.callDuration = `${minutes}:${seconds}`;\n    }, 1000);\n  }\n\n  // Arrêter le minuteur d'appel\n  private stopCallTimer(): void {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n  }\n\n  // Terminer l'appel\n  endCall(): void {\n    if (!this.activeCall) {\n      return;\n    }\n\n    this.logger.debug('Ending call', { callId: this.activeCall.id });\n\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: (result) => {\n        this.logger.debug('Call ended successfully', {\n          callId: this.activeCall?.id,\n          success: result.success,\n        });\n      },\n      error: (error) => {\n        this.logger.error('Error ending call', error);\n      },\n    });\n  }\n\n  // Basculer le micro\n  toggleMicrophone(): void {\n    if (!this.activeCall) {\n      return;\n    }\n\n    console.log('🎤 [ActiveCall] Toggling microphone...');\n\n    // Basculer localement d'abord\n    const newAudioState = this.callService.toggleAudio();\n    this.isAudioMuted = !newAudioState;\n\n    console.log('🎤 [ActiveCall] Audio toggled:', {\n      enabled: newAudioState,\n      muted: this.isAudioMuted,\n    });\n\n    // Optionnel : Notifier le serveur\n    this.callService\n      .toggleMedia(this.activeCall.id, undefined, newAudioState)\n      .subscribe({\n        next: (result) => {\n          console.log('✅ [ActiveCall] Microphone toggle sent to server');\n        },\n        error: (error) => {\n          console.error(\n            '❌ [ActiveCall] Error toggling microphone on server:',\n            error\n          );\n          // Continuer même si le serveur échoue\n        },\n      });\n  }\n\n  // Basculer la caméra\n  toggleCamera(): void {\n    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) {\n      return;\n    }\n\n    console.log('📹 [ActiveCall] Toggling camera...');\n\n    // Basculer localement d'abord\n    const newVideoState = this.callService.toggleVideo();\n    this.isVideoMuted = !newVideoState;\n\n    console.log('📹 [ActiveCall] Video toggled:', {\n      enabled: newVideoState,\n      muted: this.isVideoMuted,\n    });\n\n    // Optionnel : Notifier le serveur\n    this.callService\n      .toggleMedia(this.activeCall.id, newVideoState, undefined)\n      .subscribe({\n        next: (result) => {\n          console.log('✅ [ActiveCall] Camera toggle sent to server');\n        },\n        error: (error) => {\n          console.error(\n            '❌ [ActiveCall] Error toggling camera on server:',\n            error\n          );\n          // Continuer même si le serveur échoue\n        },\n      });\n  }\n\n  // Basculer le haut-parleur\n  toggleSpeaker(): void {\n    this.isSpeakerOn = !this.isSpeakerOn;\n\n    if (this.remoteVideo?.nativeElement) {\n      this.remoteVideo.nativeElement.volume = this.isSpeakerOn ? 1 : 0;\n    }\n\n    this.logger.debug('Speaker toggled', { on: this.isSpeakerOn });\n  }\n\n  // Obtenir le nom de l'autre participant\n  getOtherParticipantName(): string {\n    if (!this.activeCall) {\n      return '';\n    }\n\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n\n    return isCurrentUserCaller\n      ? this.activeCall.recipient.username\n      : this.activeCall.caller.username;\n  }\n\n  // Obtenir l'avatar de l'autre participant\n  getOtherParticipantAvatar(): string {\n    if (!this.activeCall) {\n      return 'assets/images/default-avatar.png';\n    }\n\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n\n    const avatar = isCurrentUserCaller\n      ? this.activeCall.recipient.image\n      : this.activeCall.caller.image;\n\n    return avatar || 'assets/images/default-avatar.png';\n  }\n\n  // Obtenir le statut de l'appel sous forme de texte\n  getCallStatusText(): string {\n    if (!this.activeCall) {\n      return '';\n    }\n\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Appel en cours...';\n      case CallStatus.CONNECTED:\n        return this.callDuration;\n      case CallStatus.ENDED:\n        return 'Appel terminé';\n      case CallStatus.MISSED:\n        return 'Appel manqué';\n      case CallStatus.REJECTED:\n        return 'Appel rejeté';\n      case CallStatus.FAILED:\n        return \"Échec de l'appel\";\n      default:\n        return '';\n    }\n  }\n\n  // Vérifier si l'appel est connecté\n  isCallConnected(): boolean {\n    return this.activeCall?.status === CallStatus.CONNECTED;\n  }\n\n  // Vérifier si l'appel est en cours de sonnerie\n  isCallRinging(): boolean {\n    return this.activeCall?.status === CallStatus.RINGING;\n  }\n\n  // Vérifier si l'appel est un appel vidéo\n  isVideoCall(): boolean {\n    return (\n      this.activeCall?.type === CallType.VIDEO ||\n      this.activeCall?.type === CallType.VIDEO_ONLY\n    );\n  }\n\n  ngOnDestroy(): void {\n    // Nettoyer les abonnements et les minuteurs\n    this.stopCallTimer();\n    this.stopAudioVisualizer();\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n}\n"], "mappings": ";AAAA,SACEA,SAAS,EAGTC,SAAS,QAGJ,eAAe;AAEtB,SAAeC,QAAQ,EAAEC,UAAU,QAAQ,4BAA4B;AAUhE,WAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAwB9BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,MAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAvBhB,KAAAC,UAAU,GAAgB,IAAI;IAC9B,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,WAAW,GAAY,IAAI;IAE3B;IACA,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,WAAW,GAAa,EAAE;IAGlB,KAAAC,aAAa,GAAgB,IAAI;IACjC,KAAAC,aAAa,GAAmB,EAAE;IAG1C;IACA,KAAAhB,QAAQ,GAAGA,QAAQ;IACnB,KAAAC,UAAU,GAAGA,UAAU;EAMpB;EAEHgB,QAAQA,CAAA;IACN;IACA,MAAMC,aAAa,GAAG,IAAI,CAACb,WAAW,CAACc,WAAW,CAACC,SAAS,CAAEC,IAAI,IAAI;MACpE,MAAMC,YAAY,GAAG,IAAI,CAACf,UAAU;MACpC,IAAI,CAACA,UAAU,GAAGc,IAAI;MAEtBE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEH,IAAI,CAAC;MAEzD,IAAIA,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKxB,UAAU,CAACyB,SAAS,EAAE;QAChD,IAAI,CAACJ,YAAY,IAAIA,YAAY,CAACK,EAAE,KAAKN,IAAI,CAACM,EAAE,EAAE;UAChD;UACAJ,OAAO,CAACC,GAAG,CACT,oEAAoE,CACrE;UACD,IAAI,CAACI,cAAc,EAAE;UACrB,IAAI,CAACC,iBAAiB,EAAE;;OAE3B,MAAM,IAAI,CAACR,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKxB,UAAU,CAACyB,SAAS,EAAE;QACxD;QACA,IAAI,CAACI,aAAa,EAAE;QACpB,IAAI,CAACC,mBAAmB,EAAE;;IAE9B,CAAC,CAAC;IAEF,IAAI,CAACf,aAAa,CAACgB,IAAI,CAACd,aAAa,CAAC;EACxC;EAEA;;;EAGQW,iBAAiBA,CAAA;IACvBN,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAE1D;IACA,MAAMS,WAAW,GAAG,IAAI,CAAC5B,WAAW,CAAC6B,cAAc,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,CAACE,UAAU,EAAEC,aAAa,EAAE;MACjDb,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD,IAAI,CAACW,UAAU,CAACC,aAAa,CAACC,SAAS,GAAGJ,WAAW;MACrD,IAAI,CAACrB,aAAa,GAAGqB,WAAW,CAACK,cAAc,EAAE,CAACC,MAAM,GAAG,CAAC;;IAG9D;IACA,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,EAAE;MACvB,IAAI,CAACC,oBAAoB,EAAE;;IAG7B;IACA,IAAI,CAAChC,YAAY,GAAG,CAAC,IAAI,CAACJ,WAAW,CAACqC,eAAe,EAAE;IACvD,IAAI,CAAChC,YAAY,GAAG,CAAC,IAAI,CAACL,WAAW,CAACsC,eAAe,EAAE;EACzD;EAEA;;;EAGQF,oBAAoBA,CAAA;IAC1B;IACA,IAAI,CAAC3B,WAAW,GAAG8B,KAAK,CAACC,IAAI,CAC3B;MAAEN,MAAM,EAAE;IAAE,CAAE,EACd,MAAMO,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAC9B;IAED,IAAI,CAACC,uBAAuB,GAAGC,WAAW,CAAC,MAAK;MAC9C,IAAI,CAACnC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACoC,GAAG,CAAC,MAAMJ,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACvE,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGQhB,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACiB,uBAAuB,EAAE;MAChCG,aAAa,CAAC,IAAI,CAACH,uBAAuB,CAAC;MAC3C,IAAI,CAACA,uBAAuB,GAAG,IAAI;;EAEvC;EAEAI,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAAC7C,UAAU,IAAI,IAAI,CAACA,UAAU,CAACkB,MAAM,KAAKxB,UAAU,CAACyB,SAAS,EAAE;MACtE,IAAI,CAACG,iBAAiB,EAAE;;EAE5B;EAEA;EACQD,cAAcA,CAAA;IACpB,IAAI,CAACb,aAAa,GAAG,IAAIsC,IAAI,EAAE;IAC/B,IAAI,CAACvB,aAAa,EAAE,CAAC,CAAC;IAEtB,IAAI,CAACwB,gBAAgB,GAAGL,WAAW,CAAC,MAAK;MACvC,IAAI,CAAC,IAAI,CAAClC,aAAa,EAAE;MAEzB,MAAMwC,GAAG,GAAG,IAAIF,IAAI,EAAE;MACtB,MAAMG,IAAI,GAAGV,IAAI,CAACW,KAAK,CACrB,CAACF,GAAG,CAACG,OAAO,EAAE,GAAG,IAAI,CAAC3C,aAAa,CAAC2C,OAAO,EAAE,IAAI,IAAI,CACtD;MAED,MAAMC,OAAO,GAAGb,IAAI,CAACW,KAAK,CAACD,IAAI,GAAG,EAAE,CAAC,CAClCI,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnB,MAAMC,OAAO,GAAG,CAACN,IAAI,GAAG,EAAE,EAAEI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAEvD,IAAI,CAACrD,YAAY,GAAG,GAAGmD,OAAO,IAAIG,OAAO,EAAE;IAC7C,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQhC,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACwB,gBAAgB,EAAE;MACzBH,aAAa,CAAC,IAAI,CAACG,gBAAgB,CAAC;MACpC,IAAI,CAACA,gBAAgB,GAAG,IAAI;;EAEhC;EAEA;EACAS,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACxD,UAAU,EAAE;MACpB;;IAGF,IAAI,CAACD,MAAM,CAAC0D,KAAK,CAAC,aAAa,EAAE;MAAEC,MAAM,EAAE,IAAI,CAAC1D,UAAU,CAACoB;IAAE,CAAE,CAAC;IAEhE,IAAI,CAACtB,WAAW,CAAC0D,OAAO,CAAC,IAAI,CAACxD,UAAU,CAACoB,EAAE,CAAC,CAACP,SAAS,CAAC;MACrD8C,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAAC7D,MAAM,CAAC0D,KAAK,CAAC,yBAAyB,EAAE;UAC3CC,MAAM,EAAE,IAAI,CAAC1D,UAAU,EAAEoB,EAAE;UAC3ByC,OAAO,EAAED,MAAM,CAACC;SACjB,CAAC;MACJ,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/D,MAAM,CAAC+D,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEA;EACAC,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC/D,UAAU,EAAE;MACpB;;IAGFgB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IAErD;IACA,MAAM+C,aAAa,GAAG,IAAI,CAAClE,WAAW,CAACmE,WAAW,EAAE;IACpD,IAAI,CAAC/D,YAAY,GAAG,CAAC8D,aAAa;IAElChD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CiD,OAAO,EAAEF,aAAa;MACtBG,KAAK,EAAE,IAAI,CAACjE;KACb,CAAC;IAEF;IACA,IAAI,CAACJ,WAAW,CACbsE,WAAW,CAAC,IAAI,CAACpE,UAAU,CAACoB,EAAE,EAAEiD,SAAS,EAAEL,aAAa,CAAC,CACzDnD,SAAS,CAAC;MACT8C,IAAI,EAAGC,MAAM,IAAI;QACf5C,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAChE,CAAC;MACD6C,KAAK,EAAGA,KAAK,IAAI;QACf9C,OAAO,CAAC8C,KAAK,CACX,qDAAqD,EACrDA,KAAK,CACN;QACD;MACF;KACD,CAAC;EACN;EAEA;EACAQ,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACtE,UAAU,IAAI,IAAI,CAACA,UAAU,CAACuE,IAAI,KAAK9E,QAAQ,CAAC+E,KAAK,EAAE;MAC/D;;IAGFxD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACA,MAAMwD,aAAa,GAAG,IAAI,CAAC3E,WAAW,CAAC4E,WAAW,EAAE;IACpD,IAAI,CAACvE,YAAY,GAAG,CAACsE,aAAa;IAElCzD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CiD,OAAO,EAAEO,aAAa;MACtBN,KAAK,EAAE,IAAI,CAAChE;KACb,CAAC;IAEF;IACA,IAAI,CAACL,WAAW,CACbsE,WAAW,CAAC,IAAI,CAACpE,UAAU,CAACoB,EAAE,EAAEqD,aAAa,EAAEJ,SAAS,CAAC,CACzDxD,SAAS,CAAC;MACT8C,IAAI,EAAGC,MAAM,IAAI;QACf5C,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC5D,CAAC;MACD6C,KAAK,EAAGA,KAAK,IAAI;QACf9C,OAAO,CAAC8C,KAAK,CACX,iDAAiD,EACjDA,KAAK,CACN;QACD;MACF;KACD,CAAC;EACN;EAEA;EACAa,aAAaA,CAAA;IACX,IAAI,CAACvE,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,IAAI,CAACwE,WAAW,EAAE/C,aAAa,EAAE;MACnC,IAAI,CAAC+C,WAAW,CAAC/C,aAAa,CAACgD,MAAM,GAAG,IAAI,CAACzE,WAAW,GAAG,CAAC,GAAG,CAAC;;IAGlE,IAAI,CAACL,MAAM,CAAC0D,KAAK,CAAC,iBAAiB,EAAE;MAAEqB,EAAE,EAAE,IAAI,CAAC1E;IAAW,CAAE,CAAC;EAChE;EAEA;EACA2E,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC/E,UAAU,EAAE;MACpB,OAAO,EAAE;;IAGX;IACA,MAAMgF,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACpD,MAAMC,mBAAmB,GAAG,IAAI,CAACnF,UAAU,CAACoF,MAAM,CAAChE,EAAE,KAAK4D,aAAa;IAEvE,OAAOG,mBAAmB,GACtB,IAAI,CAACnF,UAAU,CAACqF,SAAS,CAACC,QAAQ,GAClC,IAAI,CAACtF,UAAU,CAACoF,MAAM,CAACE,QAAQ;EACrC;EAEA;EACAC,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACvF,UAAU,EAAE;MACpB,OAAO,kCAAkC;;IAG3C;IACA,MAAMgF,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACpD,MAAMC,mBAAmB,GAAG,IAAI,CAACnF,UAAU,CAACoF,MAAM,CAAChE,EAAE,KAAK4D,aAAa;IAEvE,MAAMQ,MAAM,GAAGL,mBAAmB,GAC9B,IAAI,CAACnF,UAAU,CAACqF,SAAS,CAACI,KAAK,GAC/B,IAAI,CAACzF,UAAU,CAACoF,MAAM,CAACK,KAAK;IAEhC,OAAOD,MAAM,IAAI,kCAAkC;EACrD;EAEA;EACAE,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC1F,UAAU,EAAE;MACpB,OAAO,EAAE;;IAGX,QAAQ,IAAI,CAACA,UAAU,CAACkB,MAAM;MAC5B,KAAKxB,UAAU,CAACiG,OAAO;QACrB,OAAO,mBAAmB;MAC5B,KAAKjG,UAAU,CAACyB,SAAS;QACvB,OAAO,IAAI,CAAClB,YAAY;MAC1B,KAAKP,UAAU,CAACkG,KAAK;QACnB,OAAO,eAAe;MACxB,KAAKlG,UAAU,CAACmG,MAAM;QACpB,OAAO,cAAc;MACvB,KAAKnG,UAAU,CAACoG,QAAQ;QACtB,OAAO,cAAc;MACvB,KAAKpG,UAAU,CAACqG,MAAM;QACpB,OAAO,kBAAkB;MAC3B;QACE,OAAO,EAAE;;EAEf;EAEA;EACAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAAChG,UAAU,EAAEkB,MAAM,KAAKxB,UAAU,CAACyB,SAAS;EACzD;EAEA;EACA8E,aAAaA,CAAA;IACX,OAAO,IAAI,CAACjG,UAAU,EAAEkB,MAAM,KAAKxB,UAAU,CAACiG,OAAO;EACvD;EAEA;EACA1D,WAAWA,CAAA;IACT,OACE,IAAI,CAACjC,UAAU,EAAEuE,IAAI,KAAK9E,QAAQ,CAACyG,KAAK,IACxC,IAAI,CAAClG,UAAU,EAAEuE,IAAI,KAAK9E,QAAQ,CAAC0G,UAAU;EAEjD;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAAC7E,aAAa,EAAE;IACpB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACf,aAAa,CAAC4F,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;CACD;AAlU0BC,UAAA,EAAxBhH,SAAS,CAAC,YAAY,CAAC,C,sDAA2C;AACzCgH,UAAA,EAAzBhH,SAAS,CAAC,aAAa,CAAC,C,uDAA4C;AAF1DG,mBAAmB,GAAA6G,UAAA,EAL/BjH,SAAS,CAAC;EACTkH,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,CAAC,6BAA6B;CAC1C,CAAC,C,EACWhH,mBAAmB,CAmU/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}