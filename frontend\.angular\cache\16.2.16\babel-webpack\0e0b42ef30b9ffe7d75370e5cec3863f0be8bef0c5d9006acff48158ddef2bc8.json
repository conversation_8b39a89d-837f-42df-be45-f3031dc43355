{"ast": null, "code": "import { CallType, CallStatus } from '../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/message.service\";\nimport * as i2 from \"../../services/call.service\";\nimport * as i3 from \"../../services/logger.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"localVideo\"];\nconst _c1 = [\"remoteVideo\"];\nfunction ActiveCallComponent_div_0_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleCamera());\n    });\n    i0.ɵɵelement(1, \"i\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.isVideoMuted ? \"bg-red-500\" : \"bg-blue-500\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r1.isVideoMuted ? \"fa-video-slash\" : \"fa-video\");\n  }\n}\nfunction ActiveCallComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"span\", 4);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 5);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8);\n    i0.ɵɵelement(10, \"img\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"h2\", 10);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 11);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13)(17, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleMicrophone());\n    });\n    i0.ɵɵelement(18, \"i\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ActiveCallComponent_div_0_button_19_Template, 2, 4, \"button\", 16);\n    i0.ɵɵelementStart(20, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.endCall());\n    });\n    i0.ɵɵelement(21, \"i\", 18);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.getCallStatusText());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.callDuration);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r0.getOtherParticipantAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.getOtherParticipantName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getOtherParticipantName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isVideoCall() ? \"Appel vid\\u00E9o\" : \"Appel audio\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r0.isAudioMuted ? \"bg-red-500\" : \"bg-blue-500\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.isAudioMuted ? \"fa-microphone-slash\" : \"fa-microphone\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoCall());\n  }\n}\nexport class ActiveCallComponent {\n  constructor(messageService, callService, logger) {\n    this.messageService = messageService;\n    this.callService = callService;\n    this.logger = logger;\n    this.activeCall = null;\n    this.callDuration = '00:00';\n    this.isAudioMuted = false;\n    this.isVideoMuted = false;\n    this.isSpeakerOn = true;\n    this.callStartTime = null;\n    this.subscriptions = [];\n    // Exposer les énums au template\n    this.CallType = CallType;\n    this.CallStatus = CallStatus;\n  }\n  ngOnInit() {\n    // S'abonner à l'appel actif\n    const activeCallSub = this.callService.activeCall$.subscribe(call => {\n      const previousCall = this.activeCall;\n      this.activeCall = call;\n      console.log('📞 [ActiveCall] Active call updated:', call);\n      if (call && call.status === CallStatus.CONNECTED) {\n        if (!previousCall || previousCall.id !== call.id) {\n          // Nouvel appel connecté\n          console.log('✅ [ActiveCall] Call connected, starting timer');\n          this.startCallTimer();\n        }\n      } else if (!call || call.status !== CallStatus.CONNECTED) {\n        // Appel terminé ou non connecté\n        this.stopCallTimer();\n        this.stopAudioVisualizer();\n      }\n    });\n    this.subscriptions.push(activeCallSub);\n  }\n  /**\n   * Configure les flux média pour l'appel\n   */\n  setupMediaStreams() {\n    console.log('🎥 [ActiveCall] Setting up media streams...');\n    // Configurer le flux local\n    const localStream = this.callService.getLocalStream();\n    if (localStream && this.localVideo?.nativeElement) {\n      console.log('📹 [ActiveCall] Setting local video stream');\n      this.localVideo.nativeElement.srcObject = localStream;\n      this.hasLocalVideo = localStream.getVideoTracks().length > 0;\n    }\n    // Initialiser le visualiseur audio pour les appels audio\n    if (!this.isVideoCall()) {\n      this.startAudioVisualizer();\n    }\n    // Mettre à jour les états des contrôles\n    this.isAudioMuted = !this.callService.getAudioEnabled();\n    this.isVideoMuted = !this.callService.getVideoEnabled();\n  }\n  /**\n   * Démarre le visualiseur audio\n   */\n  startAudioVisualizer() {\n    // Générer des niveaux audio animés\n    this.audioLevels = Array.from({\n      length: 15\n    }, () => Math.random() * 30 + 10);\n    this.audioVisualizerInterval = setInterval(() => {\n      this.audioLevels = this.audioLevels.map(() => Math.random() * 40 + 5);\n    }, 150);\n  }\n  /**\n   * Arrête le visualiseur audio\n   */\n  stopAudioVisualizer() {\n    if (this.audioVisualizerInterval) {\n      clearInterval(this.audioVisualizerInterval);\n      this.audioVisualizerInterval = null;\n    }\n  }\n  ngAfterViewInit() {\n    // Configurer les éléments vidéo après le rendu du composant\n    if (this.activeCall && this.activeCall.status === CallStatus.CONNECTED) {\n      this.setupMediaStreams();\n    }\n  }\n  // Démarrer le minuteur d'appel\n  startCallTimer() {\n    this.callStartTime = new Date();\n    this.stopCallTimer(); // Arrêter tout minuteur existant\n    this.durationInterval = setInterval(() => {\n      if (!this.callStartTime) return;\n      const now = new Date();\n      const diff = Math.floor((now.getTime() - this.callStartTime.getTime()) / 1000);\n      const minutes = Math.floor(diff / 60).toString().padStart(2, '0');\n      const seconds = (diff % 60).toString().padStart(2, '0');\n      this.callDuration = `${minutes}:${seconds}`;\n    }, 1000);\n  }\n  // Arrêter le minuteur d'appel\n  stopCallTimer() {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n  }\n  // Terminer l'appel\n  endCall() {\n    if (!this.activeCall) {\n      return;\n    }\n    this.logger.debug('Ending call', {\n      callId: this.activeCall.id\n    });\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: result => {\n        this.logger.debug('Call ended successfully', {\n          callId: this.activeCall?.id,\n          success: result.success\n        });\n      },\n      error: error => {\n        this.logger.error('Error ending call', error);\n      }\n    });\n  }\n  // Basculer le micro\n  toggleMicrophone() {\n    if (!this.activeCall) {\n      return;\n    }\n    console.log('🎤 [ActiveCall] Toggling microphone...');\n    // Basculer localement d'abord\n    const newAudioState = this.callService.toggleAudio();\n    this.isAudioMuted = !newAudioState;\n    console.log('🎤 [ActiveCall] Audio toggled:', {\n      enabled: newAudioState,\n      muted: this.isAudioMuted\n    });\n    // Optionnel : Notifier le serveur\n    this.callService.toggleMedia(this.activeCall.id, undefined, newAudioState).subscribe({\n      next: result => {\n        console.log('✅ [ActiveCall] Microphone toggle sent to server');\n      },\n      error: error => {\n        console.error('❌ [ActiveCall] Error toggling microphone on server:', error);\n        // Continuer même si le serveur échoue\n      }\n    });\n  }\n  // Basculer la caméra\n  toggleCamera() {\n    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) {\n      return;\n    }\n    console.log('📹 [ActiveCall] Toggling camera...');\n    // Basculer localement d'abord\n    const newVideoState = this.callService.toggleVideo();\n    this.isVideoMuted = !newVideoState;\n    console.log('📹 [ActiveCall] Video toggled:', {\n      enabled: newVideoState,\n      muted: this.isVideoMuted\n    });\n    // Optionnel : Notifier le serveur\n    this.callService.toggleMedia(this.activeCall.id, newVideoState, undefined).subscribe({\n      next: result => {\n        console.log('✅ [ActiveCall] Camera toggle sent to server');\n      },\n      error: error => {\n        console.error('❌ [ActiveCall] Error toggling camera on server:', error);\n        // Continuer même si le serveur échoue\n      }\n    });\n  }\n  // Basculer le haut-parleur\n  toggleSpeaker() {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    if (this.remoteVideo?.nativeElement) {\n      this.remoteVideo.nativeElement.volume = this.isSpeakerOn ? 1 : 0;\n    }\n    this.logger.debug('Speaker toggled', {\n      on: this.isSpeakerOn\n    });\n  }\n  // Obtenir le nom de l'autre participant\n  getOtherParticipantName() {\n    if (!this.activeCall) {\n      return '';\n    }\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n    return isCurrentUserCaller ? this.activeCall.recipient.username : this.activeCall.caller.username;\n  }\n  // Obtenir l'avatar de l'autre participant\n  getOtherParticipantAvatar() {\n    if (!this.activeCall) {\n      return 'assets/images/default-avatar.png';\n    }\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n    const avatar = isCurrentUserCaller ? this.activeCall.recipient.image : this.activeCall.caller.image;\n    return avatar || 'assets/images/default-avatar.png';\n  }\n  // Obtenir le statut de l'appel sous forme de texte\n  getCallStatusText() {\n    if (!this.activeCall) {\n      return '';\n    }\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Appel en cours...';\n      case CallStatus.CONNECTED:\n        return this.callDuration;\n      case CallStatus.ENDED:\n        return 'Appel terminé';\n      case CallStatus.MISSED:\n        return 'Appel manqué';\n      case CallStatus.REJECTED:\n        return 'Appel rejeté';\n      case CallStatus.FAILED:\n        return \"Échec de l'appel\";\n      default:\n        return '';\n    }\n  }\n  // Vérifier si l'appel est connecté\n  isCallConnected() {\n    return this.activeCall?.status === CallStatus.CONNECTED;\n  }\n  // Vérifier si l'appel est en cours de sonnerie\n  isCallRinging() {\n    return this.activeCall?.status === CallStatus.RINGING;\n  }\n  // Vérifier si l'appel est un appel vidéo\n  isVideoCall() {\n    return this.activeCall?.type === CallType.VIDEO || this.activeCall?.type === CallType.VIDEO_ONLY;\n  }\n  ngOnDestroy() {\n    // Nettoyer les abonnements et les minuteurs\n    this.stopCallTimer();\n    this.stopAudioVisualizer();\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  static {\n    this.ɵfac = function ActiveCallComponent_Factory(t) {\n      return new (t || ActiveCallComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.CallService), i0.ɵɵdirectiveInject(i3.LoggerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActiveCallComponent,\n      selectors: [[\"app-active-call\"]],\n      viewQuery: function ActiveCallComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localVideo = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.remoteVideo = _t.first);\n        }\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"fixed inset-0 z-50 bg-black/90 flex flex-col\", 4, \"ngIf\"], [1, \"fixed\", \"inset-0\", \"z-50\", \"bg-black/90\", \"flex\", \"flex-col\"], [1, \"p-4\", \"text-center\"], [1, \"text-white\"], [1, \"text-sm\"], [1, \"ml-4\", \"text-sm\"], [1, \"flex-1\", \"flex\", \"items-center\", \"justify-center\"], [1, \"text-center\", \"text-white\"], [1, \"w-32\", \"h-32\", \"rounded-full\", \"overflow-hidden\", \"mx-auto\", \"mb-4\", \"border-2\", \"border-blue-500\"], [1, \"w-full\", \"h-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"text-2xl\", \"font-bold\", \"mb-2\"], [1, \"text-gray-400\", \"mb-8\"], [1, \"p-6\"], [1, \"flex\", \"justify-center\", \"space-x-8\"], [1, \"w-14\", \"h-14\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\"], [1, \"fas\", \"text-white\"], [\"class\", \"w-14 h-14 rounded-full flex items-center justify-center\", 3, \"class\", \"click\", 4, \"ngIf\"], [1, \"w-14\", \"h-14\", \"rounded-full\", \"bg-red-600\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\"], [1, \"fas\", \"fa-phone-slash\", \"text-white\"]],\n      template: function ActiveCallComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ActiveCallComponent_div_0_Template, 22, 11, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.activeCall);\n        }\n      },\n      dependencies: [i4.NgIf],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.active-call-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  right: 0;\\n  z-index: 1000;\\n  background-color: var(--dark-bg);\\n  border-radius: var(--border-radius-lg) 0 0 0;\\n  box-shadow: 0 -4px 30px rgba(0, 0, 0, 0.3);\\n  overflow: hidden;\\n  transition: all var(--transition-medium);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n}\\n\\n.active-call-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(\\n    90deg,\\n    transparent,\\n    var(--accent-color),\\n    transparent\\n  );\\n  opacity: 0.5;\\n  z-index: 1;\\n}\\n\\n.active-call-container.video-call[_ngcontent-%COMP%] {\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 0;\\n  background-color: var(--dark-bg);\\n}\\n\\n\\n\\n.video-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.video-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    circle at center,\\n    transparent 50%,\\n    var(--dark-bg) 150%\\n  );\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n\\n.remote-video-wrapper[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.remote-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background: linear-gradient(135deg, var(--medium-bg), var(--dark-bg));\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  width: 300px;\\n  height: 300px;\\n  background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);\\n  opacity: 0.1;\\n  filter: blur(40px);\\n  animation: _ngcontent-%COMP%_pulse 4s infinite;\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  border-radius: 50%;\\n  border: 3px solid var(--accent-color);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n  z-index: 2;\\n  position: relative;\\n}\\n\\n.call-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 30px;\\n  left: 30px;\\n  color: var(--text-light);\\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 15px 20px;\\n  border-radius: var(--border-radius-md);\\n  border-left: 3px solid var(--accent-color);\\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.participant-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  color: transparent;\\n  text-shadow: none;\\n}\\n\\n.call-status[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  font-size: 16px;\\n  opacity: 0.9;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.call-status[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: inline-block;\\n  width: 8px;\\n  height: 8px;\\n  background-color: var(--accent-color);\\n  border-radius: 50%;\\n  margin-right: 8px;\\n  box-shadow: 0 0 8px var(--accent-color);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.local-video-wrapper[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  right: 30px;\\n  width: 200px;\\n  height: 130px;\\n  border-radius: var(--border-radius-md);\\n  overflow: hidden;\\n  border: 2px solid var(--accent-color);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n  z-index: 10;\\n  transition: all var(--transition-medium);\\n}\\n\\n.local-video-wrapper[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 0 30px rgba(0, 247, 255, 0.5);\\n}\\n\\n.local-video-wrapper[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border: 1px solid rgba(0, 247, 255, 0.5);\\n  border-radius: var(--border-radius-md);\\n  pointer-events: none;\\n}\\n\\n.local-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transform: scaleX(-1); \\n\\n}\\n\\n\\n\\n.audio-call-info[_ngcontent-%COMP%] {\\n  padding: 30px;\\n  text-align: center;\\n  width: 350px;\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.05),\\n    rgba(157, 78, 221, 0.05)\\n  );\\n}\\n\\n.participant-avatar[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n\\n.participant-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 100px;\\n  height: 100px;\\n  background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);\\n  opacity: 0.3;\\n  filter: blur(20px);\\n  animation: _ngcontent-%COMP%_pulse 3s infinite;\\n  z-index: -1;\\n}\\n\\n.participant-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  border: 3px solid var(--accent-color);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n}\\n\\n\\n\\n.call-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 20px;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-top: 1px solid rgba(0, 247, 255, 0.1);\\n}\\n\\n.video-call[_ngcontent-%COMP%]   .call-controls[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.control-btn[_ngcontent-%COMP%], .end-call-btn[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  border: none;\\n  margin: 0 12px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.control-btn[_ngcontent-%COMP%]::before, .end-call-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    circle at center,\\n    rgba(255, 255, 255, 0.1) 0%,\\n    transparent 70%\\n  );\\n  opacity: 0;\\n  transition: opacity var(--transition-fast);\\n}\\n\\n.control-btn[_ngcontent-%COMP%]:hover::before, .end-call-btn[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.control-btn[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--text-light);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.control-btn.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  border: none;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n.control-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);\\n}\\n\\n.control-btn.active[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.7);\\n}\\n\\n.end-call-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3547, #ff5252);\\n  color: white;\\n  box-shadow: 0 0 15px rgba(255, 53, 71, 0.3);\\n}\\n\\n.end-call-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 0 20px rgba(255, 53, 71, 0.5);\\n}\\n\\n.control-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .end-call-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 0.3;\\n    transform: scale(0.95);\\n  }\\n  50% {\\n    opacity: 0.6;\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    opacity: 0.3;\\n    transform: scale(0.95);\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CallType", "CallStatus", "i0", "ɵɵelementStart", "ɵɵlistener", "ActiveCallComponent_div_0_button_19_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "toggleCamera", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassMap", "ctx_r1", "isVideoMuted", "ɵɵadvance", "ɵɵtext", "ActiveCallComponent_div_0_Template_button_click_17_listener", "_r5", "ctx_r4", "toggleMicrophone", "ɵɵtemplate", "ActiveCallComponent_div_0_button_19_Template", "ActiveCallComponent_div_0_Template_button_click_20_listener", "ctx_r6", "endCall", "ɵɵtextInterpolate", "ctx_r0", "getCallStatusText", "callDuration", "ɵɵproperty", "getOtherParticipantAvatar", "ɵɵsanitizeUrl", "getOtherParticipantName", "ɵɵtextInterpolate1", "isVideoCall", "isAudioMuted", "ActiveCallComponent", "constructor", "messageService", "callService", "logger", "activeCall", "isSpeakerOn", "callStartTime", "subscriptions", "ngOnInit", "activeCallSub", "activeCall$", "subscribe", "call", "previousCall", "console", "log", "status", "CONNECTED", "id", "startCallTimer", "stopCallTimer", "stopAudioVisualizer", "push", "setupMediaStreams", "localStream", "getLocalStream", "localVideo", "nativeElement", "srcObject", "hasLocalVideo", "getVideoTracks", "length", "startAudioVisualizer", "getAudioEnabled", "getVideoEnabled", "audioLevels", "Array", "from", "Math", "random", "audioVisualizerInterval", "setInterval", "map", "clearInterval", "ngAfterViewInit", "Date", "durationInterval", "now", "diff", "floor", "getTime", "minutes", "toString", "padStart", "seconds", "debug", "callId", "next", "result", "success", "error", "newAudioState", "toggleAudio", "enabled", "muted", "toggleMedia", "undefined", "type", "AUDIO", "newVideoState", "toggleVideo", "toggleSpeaker", "remoteVideo", "volume", "on", "currentUserId", "localStorage", "getItem", "isCurrentUserCaller", "caller", "recipient", "username", "avatar", "image", "RINGING", "ENDED", "MISSED", "REJECTED", "FAILED", "isCallConnected", "isCallRinging", "VIDEO", "VIDEO_ONLY", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "CallService", "i3", "LoggerService", "selectors", "viewQuery", "ActiveCallComponent_Query", "rf", "ctx", "ActiveCallComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.html"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ViewChild,\n  ElementRef,\n  AfterViewInit,\n} from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { Call, CallType, CallStatus } from '../../models/message.model';\nimport { MessageService } from '../../services/message.service';\nimport { CallService } from '../../services/call.service';\nimport { LoggerService } from '../../services/logger.service';\n\n@Component({\n  selector: 'app-active-call',\n  templateUrl: './active-call.component.html',\n  styleUrls: ['./active-call.component.css'],\n})\nexport class ActiveCallComponent implements OnInit, OnDestroy, AfterViewInit {\n  @ViewChild('localVideo') localVideo!: ElementRef<HTMLVideoElement>;\n  @ViewChild('remoteVideo') remoteVideo!: ElementRef<HTMLVideoElement>;\n\n  activeCall: Call | null = null;\n  callDuration: string = '00:00';\n  isAudioMuted: boolean = false;\n  isVideoMuted: boolean = false;\n  isSpeakerOn: boolean = true;\n\n  private durationInterval: any;\n  private callStartTime: Date | null = null;\n  private subscriptions: Subscription[] = [];\n\n  // Exposer les énums au template\n  CallType = CallType;\n  CallStatus = CallStatus;\n\n  constructor(\n    private messageService: MessageService,\n    private callService: CallService,\n    private logger: LoggerService\n  ) {}\n\n  ngOnInit(): void {\n    // S'abonner à l'appel actif\n    const activeCallSub = this.callService.activeCall$.subscribe((call) => {\n      const previousCall = this.activeCall;\n      this.activeCall = call;\n\n      console.log('📞 [ActiveCall] Active call updated:', call);\n\n      if (call && call.status === CallStatus.CONNECTED) {\n        if (!previousCall || previousCall.id !== call.id) {\n          // Nouvel appel connecté\n          console.log('✅ [ActiveCall] Call connected, starting timer');\n          this.startCallTimer();\n        }\n      } else if (!call || call.status !== CallStatus.CONNECTED) {\n        // Appel terminé ou non connecté\n        this.stopCallTimer();\n        this.stopAudioVisualizer();\n      }\n    });\n\n    this.subscriptions.push(activeCallSub);\n  }\n\n  /**\n   * Configure les flux média pour l'appel\n   */\n  private setupMediaStreams(): void {\n    console.log('🎥 [ActiveCall] Setting up media streams...');\n\n    // Configurer le flux local\n    const localStream = this.callService.getLocalStream();\n    if (localStream && this.localVideo?.nativeElement) {\n      console.log('📹 [ActiveCall] Setting local video stream');\n      this.localVideo.nativeElement.srcObject = localStream;\n      this.hasLocalVideo = localStream.getVideoTracks().length > 0;\n    }\n\n    // Initialiser le visualiseur audio pour les appels audio\n    if (!this.isVideoCall()) {\n      this.startAudioVisualizer();\n    }\n\n    // Mettre à jour les états des contrôles\n    this.isAudioMuted = !this.callService.getAudioEnabled();\n    this.isVideoMuted = !this.callService.getVideoEnabled();\n  }\n\n  /**\n   * Démarre le visualiseur audio\n   */\n  private startAudioVisualizer(): void {\n    // Générer des niveaux audio animés\n    this.audioLevels = Array.from(\n      { length: 15 },\n      () => Math.random() * 30 + 10\n    );\n\n    this.audioVisualizerInterval = setInterval(() => {\n      this.audioLevels = this.audioLevels.map(() => Math.random() * 40 + 5);\n    }, 150);\n  }\n\n  /**\n   * Arrête le visualiseur audio\n   */\n  private stopAudioVisualizer(): void {\n    if (this.audioVisualizerInterval) {\n      clearInterval(this.audioVisualizerInterval);\n      this.audioVisualizerInterval = null;\n    }\n  }\n\n  ngAfterViewInit(): void {\n    // Configurer les éléments vidéo après le rendu du composant\n    if (this.activeCall && this.activeCall.status === CallStatus.CONNECTED) {\n      this.setupMediaStreams();\n    }\n  }\n\n  // Démarrer le minuteur d'appel\n  private startCallTimer(): void {\n    this.callStartTime = new Date();\n    this.stopCallTimer(); // Arrêter tout minuteur existant\n\n    this.durationInterval = setInterval(() => {\n      if (!this.callStartTime) return;\n\n      const now = new Date();\n      const diff = Math.floor(\n        (now.getTime() - this.callStartTime.getTime()) / 1000\n      );\n\n      const minutes = Math.floor(diff / 60)\n        .toString()\n        .padStart(2, '0');\n      const seconds = (diff % 60).toString().padStart(2, '0');\n\n      this.callDuration = `${minutes}:${seconds}`;\n    }, 1000);\n  }\n\n  // Arrêter le minuteur d'appel\n  private stopCallTimer(): void {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n  }\n\n  // Terminer l'appel\n  endCall(): void {\n    if (!this.activeCall) {\n      return;\n    }\n\n    this.logger.debug('Ending call', { callId: this.activeCall.id });\n\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: (result) => {\n        this.logger.debug('Call ended successfully', {\n          callId: this.activeCall?.id,\n          success: result.success,\n        });\n      },\n      error: (error) => {\n        this.logger.error('Error ending call', error);\n      },\n    });\n  }\n\n  // Basculer le micro\n  toggleMicrophone(): void {\n    if (!this.activeCall) {\n      return;\n    }\n\n    console.log('🎤 [ActiveCall] Toggling microphone...');\n\n    // Basculer localement d'abord\n    const newAudioState = this.callService.toggleAudio();\n    this.isAudioMuted = !newAudioState;\n\n    console.log('🎤 [ActiveCall] Audio toggled:', {\n      enabled: newAudioState,\n      muted: this.isAudioMuted,\n    });\n\n    // Optionnel : Notifier le serveur\n    this.callService\n      .toggleMedia(this.activeCall.id, undefined, newAudioState)\n      .subscribe({\n        next: (result) => {\n          console.log('✅ [ActiveCall] Microphone toggle sent to server');\n        },\n        error: (error) => {\n          console.error(\n            '❌ [ActiveCall] Error toggling microphone on server:',\n            error\n          );\n          // Continuer même si le serveur échoue\n        },\n      });\n  }\n\n  // Basculer la caméra\n  toggleCamera(): void {\n    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) {\n      return;\n    }\n\n    console.log('📹 [ActiveCall] Toggling camera...');\n\n    // Basculer localement d'abord\n    const newVideoState = this.callService.toggleVideo();\n    this.isVideoMuted = !newVideoState;\n\n    console.log('📹 [ActiveCall] Video toggled:', {\n      enabled: newVideoState,\n      muted: this.isVideoMuted,\n    });\n\n    // Optionnel : Notifier le serveur\n    this.callService\n      .toggleMedia(this.activeCall.id, newVideoState, undefined)\n      .subscribe({\n        next: (result) => {\n          console.log('✅ [ActiveCall] Camera toggle sent to server');\n        },\n        error: (error) => {\n          console.error(\n            '❌ [ActiveCall] Error toggling camera on server:',\n            error\n          );\n          // Continuer même si le serveur échoue\n        },\n      });\n  }\n\n  // Basculer le haut-parleur\n  toggleSpeaker(): void {\n    this.isSpeakerOn = !this.isSpeakerOn;\n\n    if (this.remoteVideo?.nativeElement) {\n      this.remoteVideo.nativeElement.volume = this.isSpeakerOn ? 1 : 0;\n    }\n\n    this.logger.debug('Speaker toggled', { on: this.isSpeakerOn });\n  }\n\n  // Obtenir le nom de l'autre participant\n  getOtherParticipantName(): string {\n    if (!this.activeCall) {\n      return '';\n    }\n\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n\n    return isCurrentUserCaller\n      ? this.activeCall.recipient.username\n      : this.activeCall.caller.username;\n  }\n\n  // Obtenir l'avatar de l'autre participant\n  getOtherParticipantAvatar(): string {\n    if (!this.activeCall) {\n      return 'assets/images/default-avatar.png';\n    }\n\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n\n    const avatar = isCurrentUserCaller\n      ? this.activeCall.recipient.image\n      : this.activeCall.caller.image;\n\n    return avatar || 'assets/images/default-avatar.png';\n  }\n\n  // Obtenir le statut de l'appel sous forme de texte\n  getCallStatusText(): string {\n    if (!this.activeCall) {\n      return '';\n    }\n\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Appel en cours...';\n      case CallStatus.CONNECTED:\n        return this.callDuration;\n      case CallStatus.ENDED:\n        return 'Appel terminé';\n      case CallStatus.MISSED:\n        return 'Appel manqué';\n      case CallStatus.REJECTED:\n        return 'Appel rejeté';\n      case CallStatus.FAILED:\n        return \"Échec de l'appel\";\n      default:\n        return '';\n    }\n  }\n\n  // Vérifier si l'appel est connecté\n  isCallConnected(): boolean {\n    return this.activeCall?.status === CallStatus.CONNECTED;\n  }\n\n  // Vérifier si l'appel est en cours de sonnerie\n  isCallRinging(): boolean {\n    return this.activeCall?.status === CallStatus.RINGING;\n  }\n\n  // Vérifier si l'appel est un appel vidéo\n  isVideoCall(): boolean {\n    return (\n      this.activeCall?.type === CallType.VIDEO ||\n      this.activeCall?.type === CallType.VIDEO_ONLY\n    );\n  }\n\n  ngOnDestroy(): void {\n    // Nettoyer les abonnements et les minuteurs\n    this.stopCallTimer();\n    this.stopAudioVisualizer();\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n}\n", "<!-- Interface d'appel ultra-simple -->\n<div *ngIf=\"activeCall\" class=\"fixed inset-0 z-50 bg-black/90 flex flex-col\">\n  \n  <!-- Header simple -->\n  <div class=\"p-4 text-center\">\n    <div class=\"text-white\">\n      <span class=\"text-sm\">{{ getCallStatusText() }}</span>\n      <span class=\"ml-4 text-sm\">{{ callDuration }}</span>\n    </div>\n  </div>\n\n  <!-- Zone principale -->\n  <div class=\"flex-1 flex items-center justify-center\">\n    <div class=\"text-center text-white\">\n      \n      <!-- Avatar -->\n      <div class=\"w-32 h-32 rounded-full overflow-hidden mx-auto mb-4 border-2 border-blue-500\">\n        <img \n          [src]=\"getOtherParticipantAvatar()\" \n          [alt]=\"getOtherParticipantName()\"\n          class=\"w-full h-full object-cover\"\n        />\n      </div>\n      \n      <!-- Nom -->\n      <h2 class=\"text-2xl font-bold mb-2\">{{ getOtherParticipantName() }}</h2>\n      \n      <!-- Type d'appel -->\n      <p class=\"text-gray-400 mb-8\">\n        {{ isVideoCall() ? 'Appel vidéo' : 'Appel audio' }}\n      </p>\n      \n    </div>\n  </div>\n\n  <!-- Contrôles simples -->\n  <div class=\"p-6\">\n    <div class=\"flex justify-center space-x-8\">\n      \n      <!-- Micro -->\n      <button \n        (click)=\"toggleMicrophone()\"\n        class=\"w-14 h-14 rounded-full flex items-center justify-center\"\n        [class]=\"isAudioMuted ? 'bg-red-500' : 'bg-blue-500'\"\n      >\n        <i class=\"fas text-white\" [class]=\"isAudioMuted ? 'fa-microphone-slash' : 'fa-microphone'\"></i>\n      </button>\n\n      <!-- Caméra (si vidéo) -->\n      <button \n        *ngIf=\"isVideoCall()\"\n        (click)=\"toggleCamera()\"\n        class=\"w-14 h-14 rounded-full flex items-center justify-center\"\n        [class]=\"isVideoMuted ? 'bg-red-500' : 'bg-blue-500'\"\n      >\n        <i class=\"fas text-white\" [class]=\"isVideoMuted ? 'fa-video-slash' : 'fa-video'\"></i>\n      </button>\n\n      <!-- Raccrocher -->\n      <button \n        (click)=\"endCall()\"\n        class=\"w-14 h-14 rounded-full bg-red-600 flex items-center justify-center\"\n      >\n        <i class=\"fas fa-phone-slash text-white\"></i>\n      </button>\n      \n    </div>\n  </div>\n  \n</div>\n"], "mappings": "AASA,SAAeA,QAAQ,EAAEC,UAAU,QAAQ,4BAA4B;;;;;;;;;;;ICwCjEC,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAE,UAAA,mBAAAC,qEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAIxBT,EAAA,CAAAU,SAAA,YAAqF;IACvFV,EAAA,CAAAW,YAAA,EAAS;;;;IAHPX,EAAA,CAAAY,UAAA,CAAAC,MAAA,CAAAC,YAAA,gCAAqD;IAE3Bd,EAAA,CAAAe,SAAA,GAAsD;IAAtDf,EAAA,CAAAY,UAAA,CAAAC,MAAA,CAAAC,YAAA,iCAAsD;;;;;;IAtDxFd,EAAA,CAAAC,cAAA,aAA6E;IAKjDD,EAAA,CAAAgB,MAAA,GAAyB;IAAAhB,EAAA,CAAAW,YAAA,EAAO;IACtDX,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAgB,MAAA,GAAkB;IAAAhB,EAAA,CAAAW,YAAA,EAAO;IAKxDX,EAAA,CAAAC,cAAA,aAAqD;IAK/CD,EAAA,CAAAU,SAAA,cAIE;IACJV,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAgB,MAAA,IAA+B;IAAAhB,EAAA,CAAAW,YAAA,EAAK;IAGxEX,EAAA,CAAAC,cAAA,aAA8B;IAC5BD,EAAA,CAAAgB,MAAA,IACF;IAAAhB,EAAA,CAAAW,YAAA,EAAI;IAMRX,EAAA,CAAAC,cAAA,eAAiB;IAKXD,EAAA,CAAAE,UAAA,mBAAAe,4DAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAc,GAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAW,MAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAI5BpB,EAAA,CAAAU,SAAA,aAA+F;IACjGV,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAqB,UAAA,KAAAC,4CAAA,qBAOS;IAGTtB,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAE,UAAA,mBAAAqB,4DAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAc,GAAA;MAAA,MAAAM,MAAA,GAAAxB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAgB,MAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAGnBzB,EAAA,CAAAU,SAAA,aAA6C;IAC/CV,EAAA,CAAAW,YAAA,EAAS;;;;IA1DaX,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAA0B,iBAAA,CAAAC,MAAA,CAAAC,iBAAA,GAAyB;IACpB5B,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAA0B,iBAAA,CAAAC,MAAA,CAAAE,YAAA,CAAkB;IAWzC7B,EAAA,CAAAe,SAAA,GAAmC;IAAnCf,EAAA,CAAA8B,UAAA,QAAAH,MAAA,CAAAI,yBAAA,IAAA/B,EAAA,CAAAgC,aAAA,CAAmC,QAAAL,MAAA,CAAAM,uBAAA;IAOHjC,EAAA,CAAAe,SAAA,GAA+B;IAA/Bf,EAAA,CAAA0B,iBAAA,CAAAC,MAAA,CAAAM,uBAAA,GAA+B;IAIjEjC,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAkC,kBAAA,MAAAP,MAAA,CAAAQ,WAAA,6CACF;IAaEnC,EAAA,CAAAe,SAAA,GAAqD;IAArDf,EAAA,CAAAY,UAAA,CAAAe,MAAA,CAAAS,YAAA,gCAAqD;IAE3BpC,EAAA,CAAAe,SAAA,GAAgE;IAAhEf,EAAA,CAAAY,UAAA,CAAAe,MAAA,CAAAS,YAAA,2CAAgE;IAKzFpC,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAA8B,UAAA,SAAAH,MAAA,CAAAQ,WAAA,GAAmB;;;AD/B5B,OAAM,MAAOE,mBAAmB;EAkB9BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,MAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAjBhB,KAAAC,UAAU,GAAgB,IAAI;IAC9B,KAAAb,YAAY,GAAW,OAAO;IAC9B,KAAAO,YAAY,GAAY,KAAK;IAC7B,KAAAtB,YAAY,GAAY,KAAK;IAC7B,KAAA6B,WAAW,GAAY,IAAI;IAGnB,KAAAC,aAAa,GAAgB,IAAI;IACjC,KAAAC,aAAa,GAAmB,EAAE;IAE1C;IACA,KAAA/C,QAAQ,GAAGA,QAAQ;IACnB,KAAAC,UAAU,GAAGA,UAAU;EAMpB;EAEH+C,QAAQA,CAAA;IACN;IACA,MAAMC,aAAa,GAAG,IAAI,CAACP,WAAW,CAACQ,WAAW,CAACC,SAAS,CAAEC,IAAI,IAAI;MACpE,MAAMC,YAAY,GAAG,IAAI,CAACT,UAAU;MACpC,IAAI,CAACA,UAAU,GAAGQ,IAAI;MAEtBE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEH,IAAI,CAAC;MAEzD,IAAIA,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKvD,UAAU,CAACwD,SAAS,EAAE;QAChD,IAAI,CAACJ,YAAY,IAAIA,YAAY,CAACK,EAAE,KAAKN,IAAI,CAACM,EAAE,EAAE;UAChD;UACAJ,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5D,IAAI,CAACI,cAAc,EAAE;;OAExB,MAAM,IAAI,CAACP,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKvD,UAAU,CAACwD,SAAS,EAAE;QACxD;QACA,IAAI,CAACG,aAAa,EAAE;QACpB,IAAI,CAACC,mBAAmB,EAAE;;IAE9B,CAAC,CAAC;IAEF,IAAI,CAACd,aAAa,CAACe,IAAI,CAACb,aAAa,CAAC;EACxC;EAEA;;;EAGQc,iBAAiBA,CAAA;IACvBT,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAE1D;IACA,MAAMS,WAAW,GAAG,IAAI,CAACtB,WAAW,CAACuB,cAAc,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,CAACE,UAAU,EAAEC,aAAa,EAAE;MACjDb,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD,IAAI,CAACW,UAAU,CAACC,aAAa,CAACC,SAAS,GAAGJ,WAAW;MACrD,IAAI,CAACK,aAAa,GAAGL,WAAW,CAACM,cAAc,EAAE,CAACC,MAAM,GAAG,CAAC;;IAG9D;IACA,IAAI,CAAC,IAAI,CAAClC,WAAW,EAAE,EAAE;MACvB,IAAI,CAACmC,oBAAoB,EAAE;;IAG7B;IACA,IAAI,CAAClC,YAAY,GAAG,CAAC,IAAI,CAACI,WAAW,CAAC+B,eAAe,EAAE;IACvD,IAAI,CAACzD,YAAY,GAAG,CAAC,IAAI,CAAC0B,WAAW,CAACgC,eAAe,EAAE;EACzD;EAEA;;;EAGQF,oBAAoBA,CAAA;IAC1B;IACA,IAAI,CAACG,WAAW,GAAGC,KAAK,CAACC,IAAI,CAC3B;MAAEN,MAAM,EAAE;IAAE,CAAE,EACd,MAAMO,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAC9B;IAED,IAAI,CAACC,uBAAuB,GAAGC,WAAW,CAAC,MAAK;MAC9C,IAAI,CAACN,WAAW,GAAG,IAAI,CAACA,WAAW,CAACO,GAAG,CAAC,MAAMJ,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACvE,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGQlB,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACmB,uBAAuB,EAAE;MAChCG,aAAa,CAAC,IAAI,CAACH,uBAAuB,CAAC;MAC3C,IAAI,CAACA,uBAAuB,GAAG,IAAI;;EAEvC;EAEAI,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAACxC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACY,MAAM,KAAKvD,UAAU,CAACwD,SAAS,EAAE;MACtE,IAAI,CAACM,iBAAiB,EAAE;;EAE5B;EAEA;EACQJ,cAAcA,CAAA;IACpB,IAAI,CAACb,aAAa,GAAG,IAAIuC,IAAI,EAAE;IAC/B,IAAI,CAACzB,aAAa,EAAE,CAAC,CAAC;IAEtB,IAAI,CAAC0B,gBAAgB,GAAGL,WAAW,CAAC,MAAK;MACvC,IAAI,CAAC,IAAI,CAACnC,aAAa,EAAE;MAEzB,MAAMyC,GAAG,GAAG,IAAIF,IAAI,EAAE;MACtB,MAAMG,IAAI,GAAGV,IAAI,CAACW,KAAK,CACrB,CAACF,GAAG,CAACG,OAAO,EAAE,GAAG,IAAI,CAAC5C,aAAa,CAAC4C,OAAO,EAAE,IAAI,IAAI,CACtD;MAED,MAAMC,OAAO,GAAGb,IAAI,CAACW,KAAK,CAACD,IAAI,GAAG,EAAE,CAAC,CAClCI,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnB,MAAMC,OAAO,GAAG,CAACN,IAAI,GAAG,EAAE,EAAEI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAEvD,IAAI,CAAC9D,YAAY,GAAG,GAAG4D,OAAO,IAAIG,OAAO,EAAE;IAC7C,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQlC,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC0B,gBAAgB,EAAE;MACzBH,aAAa,CAAC,IAAI,CAACG,gBAAgB,CAAC;MACpC,IAAI,CAACA,gBAAgB,GAAG,IAAI;;EAEhC;EAEA;EACA3D,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACiB,UAAU,EAAE;MACpB;;IAGF,IAAI,CAACD,MAAM,CAACoD,KAAK,CAAC,aAAa,EAAE;MAAEC,MAAM,EAAE,IAAI,CAACpD,UAAU,CAACc;IAAE,CAAE,CAAC;IAEhE,IAAI,CAAChB,WAAW,CAACf,OAAO,CAAC,IAAI,CAACiB,UAAU,CAACc,EAAE,CAAC,CAACP,SAAS,CAAC;MACrD8C,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAACvD,MAAM,CAACoD,KAAK,CAAC,yBAAyB,EAAE;UAC3CC,MAAM,EAAE,IAAI,CAACpD,UAAU,EAAEc,EAAE;UAC3ByC,OAAO,EAAED,MAAM,CAACC;SACjB,CAAC;MACJ,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzD,MAAM,CAACyD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEA;EACA9E,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACsB,UAAU,EAAE;MACpB;;IAGFU,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IAErD;IACA,MAAM8C,aAAa,GAAG,IAAI,CAAC3D,WAAW,CAAC4D,WAAW,EAAE;IACpD,IAAI,CAAChE,YAAY,GAAG,CAAC+D,aAAa;IAElC/C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CgD,OAAO,EAAEF,aAAa;MACtBG,KAAK,EAAE,IAAI,CAAClE;KACb,CAAC;IAEF;IACA,IAAI,CAACI,WAAW,CACb+D,WAAW,CAAC,IAAI,CAAC7D,UAAU,CAACc,EAAE,EAAEgD,SAAS,EAAEL,aAAa,CAAC,CACzDlD,SAAS,CAAC;MACT8C,IAAI,EAAGC,MAAM,IAAI;QACf5C,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAChE,CAAC;MACD6C,KAAK,EAAGA,KAAK,IAAI;QACf9C,OAAO,CAAC8C,KAAK,CACX,qDAAqD,EACrDA,KAAK,CACN;QACD;MACF;KACD,CAAC;EACN;EAEA;EACAzF,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACiC,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC+D,IAAI,KAAK3G,QAAQ,CAAC4G,KAAK,EAAE;MAC/D;;IAGFtD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACA,MAAMsD,aAAa,GAAG,IAAI,CAACnE,WAAW,CAACoE,WAAW,EAAE;IACpD,IAAI,CAAC9F,YAAY,GAAG,CAAC6F,aAAa;IAElCvD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CgD,OAAO,EAAEM,aAAa;MACtBL,KAAK,EAAE,IAAI,CAACxF;KACb,CAAC;IAEF;IACA,IAAI,CAAC0B,WAAW,CACb+D,WAAW,CAAC,IAAI,CAAC7D,UAAU,CAACc,EAAE,EAAEmD,aAAa,EAAEH,SAAS,CAAC,CACzDvD,SAAS,CAAC;MACT8C,IAAI,EAAGC,MAAM,IAAI;QACf5C,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC5D,CAAC;MACD6C,KAAK,EAAGA,KAAK,IAAI;QACf9C,OAAO,CAAC8C,KAAK,CACX,iDAAiD,EACjDA,KAAK,CACN;QACD;MACF;KACD,CAAC;EACN;EAEA;EACAW,aAAaA,CAAA;IACX,IAAI,CAAClE,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,IAAI,CAACmE,WAAW,EAAE7C,aAAa,EAAE;MACnC,IAAI,CAAC6C,WAAW,CAAC7C,aAAa,CAAC8C,MAAM,GAAG,IAAI,CAACpE,WAAW,GAAG,CAAC,GAAG,CAAC;;IAGlE,IAAI,CAACF,MAAM,CAACoD,KAAK,CAAC,iBAAiB,EAAE;MAAEmB,EAAE,EAAE,IAAI,CAACrE;IAAW,CAAE,CAAC;EAChE;EAEA;EACAV,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACS,UAAU,EAAE;MACpB,OAAO,EAAE;;IAGX;IACA,MAAMuE,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACpD,MAAMC,mBAAmB,GAAG,IAAI,CAAC1E,UAAU,CAAC2E,MAAM,CAAC7D,EAAE,KAAKyD,aAAa;IAEvE,OAAOG,mBAAmB,GACtB,IAAI,CAAC1E,UAAU,CAAC4E,SAAS,CAACC,QAAQ,GAClC,IAAI,CAAC7E,UAAU,CAAC2E,MAAM,CAACE,QAAQ;EACrC;EAEA;EACAxF,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACW,UAAU,EAAE;MACpB,OAAO,kCAAkC;;IAG3C;IACA,MAAMuE,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACpD,MAAMC,mBAAmB,GAAG,IAAI,CAAC1E,UAAU,CAAC2E,MAAM,CAAC7D,EAAE,KAAKyD,aAAa;IAEvE,MAAMO,MAAM,GAAGJ,mBAAmB,GAC9B,IAAI,CAAC1E,UAAU,CAAC4E,SAAS,CAACG,KAAK,GAC/B,IAAI,CAAC/E,UAAU,CAAC2E,MAAM,CAACI,KAAK;IAEhC,OAAOD,MAAM,IAAI,kCAAkC;EACrD;EAEA;EACA5F,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACc,UAAU,EAAE;MACpB,OAAO,EAAE;;IAGX,QAAQ,IAAI,CAACA,UAAU,CAACY,MAAM;MAC5B,KAAKvD,UAAU,CAAC2H,OAAO;QACrB,OAAO,mBAAmB;MAC5B,KAAK3H,UAAU,CAACwD,SAAS;QACvB,OAAO,IAAI,CAAC1B,YAAY;MAC1B,KAAK9B,UAAU,CAAC4H,KAAK;QACnB,OAAO,eAAe;MACxB,KAAK5H,UAAU,CAAC6H,MAAM;QACpB,OAAO,cAAc;MACvB,KAAK7H,UAAU,CAAC8H,QAAQ;QACtB,OAAO,cAAc;MACvB,KAAK9H,UAAU,CAAC+H,MAAM;QACpB,OAAO,kBAAkB;MAC3B;QACE,OAAO,EAAE;;EAEf;EAEA;EACAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACrF,UAAU,EAAEY,MAAM,KAAKvD,UAAU,CAACwD,SAAS;EACzD;EAEA;EACAyE,aAAaA,CAAA;IACX,OAAO,IAAI,CAACtF,UAAU,EAAEY,MAAM,KAAKvD,UAAU,CAAC2H,OAAO;EACvD;EAEA;EACAvF,WAAWA,CAAA;IACT,OACE,IAAI,CAACO,UAAU,EAAE+D,IAAI,KAAK3G,QAAQ,CAACmI,KAAK,IACxC,IAAI,CAACvF,UAAU,EAAE+D,IAAI,KAAK3G,QAAQ,CAACoI,UAAU;EAEjD;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAACzE,aAAa,EAAE;IACpB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACd,aAAa,CAACuF,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;;;uBAzTWjG,mBAAmB,EAAArC,EAAA,CAAAuI,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzI,EAAA,CAAAuI,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA3I,EAAA,CAAAuI,iBAAA,CAAAK,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAnBxG,mBAAmB;MAAAyG,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UClBhCjJ,EAAA,CAAAqB,UAAA,IAAA8H,kCAAA,mBAoEM;;;UApEAnJ,EAAA,CAAA8B,UAAA,SAAAoH,GAAA,CAAAxG,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}