/* Styles pour le composant IncomingCall */

/* Animation de pulsation lente pour la carte */
@keyframes pulse-slow {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.9;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

/* Animation pour les cercles de pulsation */
@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

/* Effet de glow pour les boutons */
.glow-green {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
}

.glow-green:hover {
  box-shadow: 0 0 30px rgba(34, 197, 94, 0.7);
}

.glow-red {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
}

.glow-red:hover {
  box-shadow: 0 0 30px rgba(239, 68, 68, 0.7);
}

/* Animation de vibration pour le bouton d'acceptation */
@keyframes vibrate {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.animate-vibrate {
  animation: vibrate 0.5s ease-in-out infinite;
}

/* Effet de backdrop blur personnalisé */
.backdrop-blur-custom {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Transition fluide pour tous les boutons */
button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

button:active {
  transform: scale(0.95);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Effet de survol pour les boutons */
button:hover:not(:disabled) {
  filter: brightness(1.1);
}

/* Animation de rotation pour les icônes de chargement */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Effet de pulsation pour le bouton d'acceptation */
@keyframes pulse-accept {
  0%, 100% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(34, 197, 94, 0.8);
  }
}

.pulse-accept {
  animation: pulse-accept 2s ease-in-out infinite;
}

/* Style pour les icônes avec ombre */
.fas {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Animation de slide-in pour la carte */
@keyframes slide-in {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in {
  animation: slide-in 0.5s ease-out;
}

/* Effet de gradient animé pour les bordures */
@keyframes gradient-border {
  0% {
    border-color: rgba(59, 130, 246, 0.3);
  }
  50% {
    border-color: rgba(147, 51, 234, 0.5);
  }
  100% {
    border-color: rgba(59, 130, 246, 0.3);
  }
}

.animated-border {
  animation: gradient-border 3s ease-in-out infinite;
}

/* Responsive design */
@media (max-width: 640px) {
  .w-32 {
    width: 6rem;
  }
  
  .h-32 {
    height: 6rem;
  }
  
  .text-2xl {
    font-size: 1.5rem;
  }
  
  .w-16 {
    width: 3.5rem;
  }
  
  .h-16 {
    height: 3.5rem;
  }
  
  .space-x-8 > * + * {
    margin-left: 1.5rem;
  }
}

/* Effet de flou pour l'arrière-plan */
.backdrop-overlay {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Animation de battement de cœur pour l'avatar */
@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}
