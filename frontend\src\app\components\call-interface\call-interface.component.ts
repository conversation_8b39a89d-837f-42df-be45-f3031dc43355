import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Call, CallType } from '../../models/message.model';

@Component({
  selector: 'app-call-interface',
  templateUrl: './call-interface.component.html',
  styleUrls: ['./call-interface.component.css']
})
export class CallInterfaceComponent {
  @Input() isVisible = false;
  @Input() activeCall: Call | null = null;
  @Input() callType: 'VIDEO' | 'AUDIO' | null = null;
  @Input() otherParticipant: any = null;

  @Output() callEnded = new EventEmitter<void>();
  @Output() callAccepted = new EventEmitter<Call>();
  @Output() callRejected = new EventEmitter<void>();

  endCall(): void {
    this.callEnded.emit();
  }

  acceptCall(): void {
    if (this.activeCall) {
      this.callAccepted.emit(this.activeCall);
    }
  }

  rejectCall(): void {
    this.callRejected.emit();
  }
}
