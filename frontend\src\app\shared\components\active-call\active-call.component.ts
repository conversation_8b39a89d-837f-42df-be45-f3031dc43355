import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { MessageService } from '../../../services/message.service';
import { ToastService } from '../../../services/toast.service';
import { Call, CallType, CallStatus } from '../../../models/message.model';

@Component({
  selector: 'app-active-call',
  templateUrl: './active-call.component.html',
  styleUrls: ['./active-call.component.css']
})
export class ActiveCallComponent implements OnInit, OnDestroy {
  activeCall: Call | null = null;
  isVisible = false;
  callDuration = 0;
  isMuted = false;
  isVideoEnabled = true;
  isMinimized = false;
  
  private subscriptions = new Subscription();
  private callTimer: any = null;

  constructor(
    private messageService: MessageService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    // S'abonner aux appels actifs
    this.subscriptions.add(
      this.messageService.activeCall$.subscribe({
        next: (call) => {
          if (call && call.status === CallStatus.CONNECTED) {
            this.activeCall = call;
            this.isVisible = true;
            this.startCallTimer();
          } else {
            this.isVisible = false;
            this.stopCallTimer();
          }
        },
        error: (error) => {
          console.error('❌ Error in active call subscription:', error);
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.stopCallTimer();
  }

  endCall(): void {
    if (this.activeCall) {
      console.log('🔚 Ending active call:', this.activeCall.id);
      this.messageService.endCall(this.activeCall.id).subscribe({
        next: () => {
          this.toastService.showInfo('Appel terminé');
          this.closeCall();
        },
        error: (error) => {
          console.error('❌ Error ending call:', error);
          this.toastService.showError('Erreur lors de la fin de l\'appel');
        }
      });
    }
  }

  toggleMute(): void {
    this.isMuted = !this.isMuted;
    // TODO: Implémenter la logique de mute/unmute
    this.toastService.showInfo(this.isMuted ? 'Micro coupé' : 'Micro activé');
  }

  toggleVideo(): void {
    if (this.activeCall?.type === CallType.VIDEO) {
      this.isVideoEnabled = !this.isVideoEnabled;
      // TODO: Implémenter la logique d'activation/désactivation vidéo
      this.toastService.showInfo(this.isVideoEnabled ? 'Vidéo activée' : 'Vidéo désactivée');
    }
  }

  toggleMinimize(): void {
    this.isMinimized = !this.isMinimized;
  }

  getCallTypeIcon(): string {
    if (!this.activeCall) return 'fas fa-phone';
    return this.activeCall.type === CallType.VIDEO ? 'fas fa-video' : 'fas fa-phone';
  }

  getCallTypeText(): string {
    if (!this.activeCall) return 'Appel';
    return this.activeCall.type === CallType.VIDEO ? 'Appel vidéo' : 'Appel vocal';
  }

  getOtherParticipant(): any {
    if (!this.activeCall) return null;
    // Retourner l'autre participant (pas l'utilisateur actuel)
    // TODO: Implémenter la logique pour déterminer l'autre participant
    return this.activeCall.recipient;
  }

  formatCallDuration(): string {
    const minutes = Math.floor(this.callDuration / 60);
    const seconds = this.callDuration % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  private startCallTimer(): void {
    this.callDuration = 0;
    this.callTimer = setInterval(() => {
      this.callDuration++;
    }, 1000);
  }

  private stopCallTimer(): void {
    if (this.callTimer) {
      clearInterval(this.callTimer);
      this.callTimer = null;
    }
  }

  private closeCall(): void {
    this.isVisible = false;
    this.activeCall = null;
    this.stopCallTimer();
    this.callDuration = 0;
    this.isMuted = false;
    this.isVideoEnabled = true;
    this.isMinimized = false;
  }
}
