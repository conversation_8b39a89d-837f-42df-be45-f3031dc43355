<!-- Interface d'appel actif moderne -->
<div *ngIf="activeCall" class="fixed inset-0 z-50 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex flex-col">
  
  <!-- Header avec informations d'appel -->
  <div class="flex-shrink-0 p-6 text-center">
    <div class="inline-flex items-center space-x-3 px-6 py-3 rounded-full bg-black/30 backdrop-blur-sm border border-blue-500/20">
      <div class="w-3 h-3 rounded-full bg-green-400 animate-pulse"></div>
      <span class="text-white text-lg font-medium">{{ getCallStatusText() }}</span>
      <span class="text-blue-300 text-lg font-mono">{{ callDuration }}</span>
    </div>
  </div>

  <!-- Zone principale d'appel -->
  <div class="flex-1 flex items-center justify-center px-6">
    
    <!-- Appel vidéo -->
    <div *ngIf="isVideoCall()" class="w-full max-w-4xl mx-auto relative">
      <!-- Zone vidéo principale -->
      <div class="relative w-full aspect-video rounded-3xl overflow-hidden bg-gray-900 border-2 border-blue-500/30 shadow-2xl">
        <!-- Placeholder pour vidéo locale -->
        <div class="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
          <div class="text-center">
            <div class="w-32 h-32 rounded-full bg-blue-500/20 flex items-center justify-center mx-auto mb-6 border-4 border-blue-400/50">
              <i class="fas fa-video text-5xl text-blue-400"></i>
            </div>
            <p class="text-white text-xl font-medium">Vidéo en cours...</p>
            <p class="text-blue-300 text-sm mt-2">{{ getOtherParticipantName() }}</p>
          </div>
        </div>

        <!-- Overlay pour vidéo coupée -->
        <div *ngIf="isVideoMuted" class="absolute inset-0 bg-black/70 flex items-center justify-center">
          <div class="text-center">
            <i class="fas fa-video-slash text-6xl text-red-400 mb-4"></i>
            <p class="text-white text-lg">Caméra désactivée</p>
          </div>
        </div>
      </div>

      <!-- Vidéo locale (petite, en overlay) -->
      <div class="absolute top-4 right-4 w-48 h-36 rounded-2xl overflow-hidden bg-gray-800 border-2 border-blue-400/50 shadow-lg">
        <div class="w-full h-full bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center">
          <div class="text-center">
            <img
              [src]="getOtherParticipantAvatar()"
              [alt]="getOtherParticipantName()"
              class="w-20 h-20 rounded-full object-cover mx-auto mb-2 border-2 border-blue-400"
            />
            <p class="text-white text-sm">Vous</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Appel audio -->
    <div *ngIf="!isVideoCall()" class="text-center">
      <!-- Avatar principal avec effets -->
      <div class="relative mb-8">
        <!-- Cercles d'animation -->
        <div class="absolute inset-0 rounded-full border-4 border-blue-400/30 animate-ping"></div>
        <div class="absolute -inset-4 rounded-full border-2 border-blue-400/20 animate-ping" style="animation-delay: 0.5s;"></div>
        <div class="absolute -inset-8 rounded-full border border-blue-400/10 animate-ping" style="animation-delay: 1s;"></div>
        
        <!-- Avatar -->
        <div class="relative w-48 h-48 rounded-full overflow-hidden border-4 border-blue-500/50 mx-auto shadow-2xl">
          <img
            [src]="getOtherParticipantAvatar()"
            [alt]="getOtherParticipantName()"
            class="w-full h-full object-cover"
          />
          <!-- Overlay pour micro coupé -->
          <div *ngIf="isAudioMuted" class="absolute inset-0 bg-red-500/20 flex items-center justify-center">
            <i class="fas fa-microphone-slash text-4xl text-red-400"></i>
          </div>
        </div>
      </div>

      <!-- Informations utilisateur -->
      <h2 class="text-4xl font-bold text-white mb-4 drop-shadow-lg">{{ getOtherParticipantName() }}</h2>
      <p class="text-blue-300 text-xl mb-6">Appel audio en cours</p>
      
      <!-- Visualiseur audio -->
      <div class="flex items-center justify-center space-x-1 h-16 mb-8">
        <div *ngFor="let i of [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]" 
             class="w-1 bg-gradient-to-t from-blue-600 to-blue-400 rounded-full transition-all duration-300"
             [style.height.px]="15 + (i % 5) * 8"
             [style.animation-delay.ms]="i * 100"
             [class.animate-pulse]="!isAudioMuted">
        </div>
      </div>
    </div>
  </div>

  <!-- Contrôles d'appel -->
  <div class="flex-shrink-0 p-8">
    <div class="flex justify-center items-center space-x-8">
      
      <!-- Bouton Microphone -->
      <button 
        (click)="toggleMicrophone()"
        class="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg"
        [class]="isAudioMuted ? 'bg-red-500 hover:bg-red-600 shadow-red-500/50' : 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50'"
        [title]="isAudioMuted ? 'Activer le micro' : 'Couper le micro'"
      >
        <i class="fas text-white text-xl" [class]="isAudioMuted ? 'fa-microphone-slash' : 'fa-microphone'"></i>
      </button>

      <!-- Bouton Caméra (seulement pour appels vidéo) -->
      <button 
        *ngIf="isVideoCall()"
        (click)="toggleCamera()"
        class="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg"
        [class]="isVideoMuted ? 'bg-red-500 hover:bg-red-600 shadow-red-500/50' : 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50'"
        [title]="isVideoMuted ? 'Activer la caméra' : 'Désactiver la caméra'"
      >
        <i class="fas text-white text-xl" [class]="isVideoMuted ? 'fa-video-slash' : 'fa-video'"></i>
      </button>

      <!-- Bouton Haut-parleur -->
      <button 
        (click)="toggleSpeaker()"
        class="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg"
        [class]="isSpeakerOn ? 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50' : 'bg-gray-600 hover:bg-gray-700 shadow-gray-600/50'"
        [title]="isSpeakerOn ? 'Désactiver le haut-parleur' : 'Activer le haut-parleur'"
      >
        <i class="fas text-white text-xl" [class]="isSpeakerOn ? 'fa-volume-up' : 'fa-volume-mute'"></i>
      </button>

      <!-- Bouton Raccrocher -->
      <button 
        (click)="endCall()"
        class="w-16 h-16 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg shadow-red-500/50"
        title="Raccrocher"
      >
        <i class="fas fa-phone-slash text-white text-xl"></i>
      </button>
      
    </div>
  </div>
  
</div>
