<!-- Interface d'appel simple -->
<div *ngIf="isVisible" class="call-interface-overlay">
  <div class="call-interface-content">
    <h3>Interface d'appel</h3>
    <p *ngIf="otherParticipant">Appel avec {{ otherParticipant.username }}</p>
    <p>Type: {{ callType }}</p>
    
    <div class="call-controls">
      <button (click)="acceptCall()" class="btn-accept">Accepter</button>
      <button (click)="rejectCall()" class="btn-reject">Rejeter</button>
      <button (click)="endCall()" class="btn-end">Terminer</button>
    </div>
  </div>
</div>
