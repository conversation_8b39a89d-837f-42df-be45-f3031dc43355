{"ast": null, "code": "import { CallType, CallStatus } from '../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/message.service\";\nimport * as i2 from \"../../services/call.service\";\nimport * as i3 from \"../../services/logger.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"localVideo\"];\nconst _c1 = [\"remoteVideo\"];\nfunction ActiveCallComponent_div_0_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30);\n    i0.ɵɵelement(3, \"img\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 32)(5, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r5.getOtherParticipantAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r5.getOtherParticipantName());\n  }\n}\nfunction ActiveCallComponent_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵelement(2, \"video\", 14, 15)(4, \"div\", 16);\n    i0.ɵɵtemplate(5, ActiveCallComponent_div_0_div_1_div_5_Template, 6, 2, \"div\", 17);\n    i0.ɵɵelementStart(6, \"div\", 18)(7, \"div\", 19);\n    i0.ɵɵelement(8, \"div\", 20);\n    i0.ɵɵelementStart(9, \"span\", 21);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 22)(12, \"h3\", 23);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 24);\n    i0.ɵɵelement(15, \"video\", 25, 26)(17, \"div\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r4 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !_r4.srcObject);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.getCallStatusText());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getOtherParticipantName(), \" \");\n  }\n}\nfunction ActiveCallComponent_div_0_div_2_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 42);\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"height\", 10 + i_r8 % 5 * 6, \"px\")(\"animation-delay\", i_r8 * 100, \"ms\");\n  }\n}\nconst _c2 = function () {\n  return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15];\n};\nfunction ActiveCallComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 30);\n    i0.ɵɵelement(3, \"img\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 32)(5, \"div\", 36)(6, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"h2\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 39);\n    i0.ɵɵelement(10, \"div\", 20);\n    i0.ɵɵelementStart(11, \"span\", 21);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 40);\n    i0.ɵɵtemplate(14, ActiveCallComponent_div_0_div_2_div_14_Template, 1, 4, \"div\", 41);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r2.getOtherParticipantAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.getOtherParticipantName());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getOtherParticipantName(), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.getCallStatusText());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(5, _c2));\n  }\n}\nconst _c3 = function (a0, a1) {\n  return {\n    \"bg-[#4f5fad] dark:bg-[#6d78c9] text-white\": a0,\n    \"bg-[#1e1e1e] text-[#6d6870] border border-[#2a2a2a]\": a1\n  };\n};\nconst _c4 = function (a0, a1) {\n  return {\n    \"opacity-100 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50\": a0,\n    \"opacity-0 group-hover:opacity-30 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30\": a1\n  };\n};\nfunction ActiveCallComponent_div_0_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.toggleCamera());\n    });\n    i0.ɵɵelement(1, \"div\", 6)(2, \"i\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c3, !ctx_r3.isVideoMuted, ctx_r3.isVideoMuted));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c4, !ctx_r3.isVideoMuted, ctx_r3.isVideoMuted));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.isVideoMuted ? \"fa-video-slash\" : \"fa-video\");\n  }\n}\nfunction ActiveCallComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, ActiveCallComponent_div_0_div_1_Template, 18, 3, \"div\", 2);\n    i0.ɵɵtemplate(2, ActiveCallComponent_div_0_div_2_Template, 15, 6, \"div\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.toggleMicrophone());\n    });\n    i0.ɵɵelement(5, \"div\", 6)(6, \"i\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ActiveCallComponent_div_0_button_7_Template, 3, 9, \"button\", 8);\n    i0.ɵɵelementStart(8, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.toggleSpeaker());\n    });\n    i0.ɵɵelement(9, \"div\", 6)(10, \"i\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.endCall());\n    });\n    i0.ɵɵelement(12, \"div\", 10)(13, \"i\", 11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoCall());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isVideoCall());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c3, !ctx_r0.isAudioMuted, ctx_r0.isAudioMuted));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(12, _c4, !ctx_r0.isAudioMuted, ctx_r0.isAudioMuted));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isAudioMuted ? \"fa-microphone-slash\" : \"fa-microphone\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoCall());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(15, _c3, ctx_r0.isSpeakerOn, !ctx_r0.isSpeakerOn));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c4, ctx_r0.isSpeakerOn, !ctx_r0.isSpeakerOn));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isSpeakerOn ? \"fa-volume-up\" : \"fa-volume-mute\");\n  }\n}\nexport class ActiveCallComponent {\n  constructor(messageService, callService, logger) {\n    this.messageService = messageService;\n    this.callService = callService;\n    this.logger = logger;\n    this.activeCall = null;\n    this.callDuration = '00:00';\n    this.isAudioMuted = false;\n    this.isVideoMuted = false;\n    this.isSpeakerOn = true;\n    this.callStartTime = null;\n    this.subscriptions = [];\n    // Exposer les énums au template\n    this.CallType = CallType;\n    this.CallStatus = CallStatus;\n  }\n  ngOnInit() {\n    // S'abonner à l'appel actif\n    const activeCallSub = this.callService.activeCall$.subscribe(call => {\n      const previousCall = this.activeCall;\n      this.activeCall = call;\n      console.log('📞 [ActiveCall] Active call updated:', call);\n      if (call && call.status === CallStatus.CONNECTED) {\n        if (!previousCall || previousCall.id !== call.id) {\n          // Nouvel appel connecté\n          console.log('✅ [ActiveCall] Call connected, starting timer and setting up media');\n          this.startCallTimer();\n          this.setupMediaStreams();\n        }\n      } else if (!call || call.status !== CallStatus.CONNECTED) {\n        // Appel terminé ou non connecté\n        this.stopCallTimer();\n      }\n    });\n    this.subscriptions.push(activeCallSub);\n  }\n  /**\n   * Configure les flux média pour l'appel\n   */\n  setupMediaStreams() {\n    console.log('🎥 [ActiveCall] Setting up media streams...');\n    // Configurer le flux local\n    const localStream = this.callService.getLocalStream();\n    if (localStream && this.localVideo?.nativeElement) {\n      console.log('📹 [ActiveCall] Setting local video stream');\n      this.localVideo.nativeElement.srcObject = localStream;\n    }\n    // Pour l'instant, pas de flux distant réel (WebRTC complet nécessaire)\n    // Mais on peut simuler ou utiliser le même flux pour les tests\n    if (this.remoteVideo?.nativeElement && localStream) {\n      console.log('📹 [ActiveCall] Setting remote video stream (simulated)');\n      // Pour les tests, on peut utiliser le même flux ou laisser vide\n      // this.remoteVideo.nativeElement.srcObject = localStream;\n    }\n  }\n\n  ngAfterViewInit() {\n    // Configurer les éléments vidéo après le rendu du composant\n    const localStream = this.messageService.localStream$.getValue();\n    if (localStream && this.localVideo?.nativeElement) {\n      this.localVideo.nativeElement.srcObject = localStream;\n    }\n    const remoteStream = this.messageService.remoteStream$.getValue();\n    if (remoteStream && this.remoteVideo?.nativeElement) {\n      this.remoteVideo.nativeElement.srcObject = remoteStream;\n    }\n  }\n  // Démarrer le minuteur d'appel\n  startCallTimer() {\n    this.callStartTime = new Date();\n    this.stopCallTimer(); // Arrêter tout minuteur existant\n    this.durationInterval = setInterval(() => {\n      if (!this.callStartTime) return;\n      const now = new Date();\n      const diff = Math.floor((now.getTime() - this.callStartTime.getTime()) / 1000);\n      const minutes = Math.floor(diff / 60).toString().padStart(2, '0');\n      const seconds = (diff % 60).toString().padStart(2, '0');\n      this.callDuration = `${minutes}:${seconds}`;\n    }, 1000);\n  }\n  // Arrêter le minuteur d'appel\n  stopCallTimer() {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n  }\n  // Terminer l'appel\n  endCall() {\n    if (!this.activeCall) {\n      return;\n    }\n    this.logger.debug('Ending call', {\n      callId: this.activeCall.id\n    });\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: result => {\n        this.logger.debug('Call ended successfully', {\n          callId: this.activeCall?.id,\n          success: result.success\n        });\n      },\n      error: error => {\n        this.logger.error('Error ending call', error);\n      }\n    });\n  }\n  // Basculer le micro\n  toggleMicrophone() {\n    if (!this.activeCall) {\n      return;\n    }\n    this.isAudioMuted = !this.isAudioMuted;\n    this.callService.toggleMedia(this.activeCall.id, undefined, !this.isAudioMuted).subscribe({\n      next: result => {\n        this.logger.debug('Microphone toggled', {\n          muted: this.isAudioMuted\n        });\n      },\n      error: error => {\n        this.logger.error('Error toggling microphone', error);\n        // Revenir à l'état précédent en cas d'erreur\n        this.isAudioMuted = !this.isAudioMuted;\n      }\n    });\n  }\n  // Basculer la caméra\n  toggleCamera() {\n    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) {\n      return;\n    }\n    this.isVideoMuted = !this.isVideoMuted;\n    this.callService.toggleMedia(this.activeCall.id, !this.isVideoMuted, undefined).subscribe({\n      next: result => {\n        this.logger.debug('Camera toggled', {\n          muted: this.isVideoMuted\n        });\n      },\n      error: error => {\n        this.logger.error('Error toggling camera', error);\n        // Revenir à l'état précédent en cas d'erreur\n        this.isVideoMuted = !this.isVideoMuted;\n      }\n    });\n  }\n  // Basculer le haut-parleur\n  toggleSpeaker() {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    if (this.remoteVideo?.nativeElement) {\n      this.remoteVideo.nativeElement.volume = this.isSpeakerOn ? 1 : 0;\n    }\n    this.logger.debug('Speaker toggled', {\n      on: this.isSpeakerOn\n    });\n  }\n  // Obtenir le nom de l'autre participant\n  getOtherParticipantName() {\n    if (!this.activeCall) {\n      return '';\n    }\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n    return isCurrentUserCaller ? this.activeCall.recipient.username : this.activeCall.caller.username;\n  }\n  // Obtenir l'avatar de l'autre participant\n  getOtherParticipantAvatar() {\n    if (!this.activeCall) {\n      return 'assets/images/default-avatar.png';\n    }\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n    const avatar = isCurrentUserCaller ? this.activeCall.recipient.image : this.activeCall.caller.image;\n    return avatar || 'assets/images/default-avatar.png';\n  }\n  // Obtenir le statut de l'appel sous forme de texte\n  getCallStatusText() {\n    if (!this.activeCall) {\n      return '';\n    }\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Appel en cours...';\n      case CallStatus.CONNECTED:\n        return this.callDuration;\n      case CallStatus.ENDED:\n        return 'Appel terminé';\n      case CallStatus.MISSED:\n        return 'Appel manqué';\n      case CallStatus.REJECTED:\n        return 'Appel rejeté';\n      case CallStatus.FAILED:\n        return \"Échec de l'appel\";\n      default:\n        return '';\n    }\n  }\n  // Vérifier si l'appel est connecté\n  isCallConnected() {\n    return this.activeCall?.status === CallStatus.CONNECTED;\n  }\n  // Vérifier si l'appel est en cours de sonnerie\n  isCallRinging() {\n    return this.activeCall?.status === CallStatus.RINGING;\n  }\n  // Vérifier si l'appel est un appel vidéo\n  isVideoCall() {\n    return this.activeCall?.type === CallType.VIDEO || this.activeCall?.type === CallType.VIDEO_ONLY;\n  }\n  ngOnDestroy() {\n    // Nettoyer les abonnements et le minuteur\n    this.stopCallTimer();\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  static {\n    this.ɵfac = function ActiveCallComponent_Factory(t) {\n      return new (t || ActiveCallComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.CallService), i0.ɵɵdirectiveInject(i3.LoggerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActiveCallComponent,\n      selectors: [[\"app-active-call\"]],\n      viewQuery: function ActiveCallComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localVideo = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.remoteVideo = _t.first);\n        }\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"fixed inset-0 z-50 bg-[#121212]/90 backdrop-blur-sm flex flex-col\", 4, \"ngIf\"], [1, \"fixed\", \"inset-0\", \"z-50\", \"bg-[#121212]/90\", \"backdrop-blur-sm\", \"flex\", \"flex-col\"], [\"class\", \"flex-1 relative\", 4, \"ngIf\"], [\"class\", \"flex-1 flex flex-col items-center justify-center\", 4, \"ngIf\"], [1, \"p-6\", \"flex\", \"justify-center\", \"space-x-4\"], [1, \"w-14\", \"h-14\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"relative\", \"group\", 3, \"ngClass\", \"click\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"blur-md\", \"transition-opacity\", 3, \"ngClass\"], [1, \"fas\", \"text-lg\", \"relative\", \"z-10\", 3, \"ngClass\"], [\"class\", \"w-14 h-14 rounded-full flex items-center justify-center relative group\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [1, \"w-14\", \"h-14\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"bg-gradient-to-r\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"text-white\", \"relative\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/50\", \"rounded-full\", \"blur-md\", \"opacity-70\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"fas\", \"fa-phone-slash\", \"text-lg\", \"relative\", \"z-10\"], [1, \"flex-1\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\"], [\"autoplay\", \"\", \"playsinline\", \"\", 1, \"w-full\", \"h-full\", \"object-cover\"], [\"remoteVideo\", \"\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-t\", \"from-[#121212]/80\", \"to-transparent\", \"pointer-events-none\"], [\"class\", \"absolute inset-0 flex items-center justify-center bg-[#121212]/70\", 4, \"ngIf\"], [1, \"absolute\", \"top-6\", \"left-0\", \"right-0\", \"flex\", \"justify-center\", \"pointer-events-none\"], [1, \"px-4\", \"py-2\", \"rounded-full\", \"bg-[#121212]/50\", \"backdrop-blur-sm\", \"border\", \"border-[#2a2a2a]\", \"flex\", \"items-center\", \"space-x-2\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"animate-pulse\"], [1, \"text-white\", \"text-sm\", \"font-medium\"], [1, \"absolute\", \"bottom-24\", \"left-0\", \"right-0\", \"text-center\", \"pointer-events-none\"], [1, \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"absolute\", \"top-4\", \"right-4\", \"w-32\", \"h-48\", \"rounded-lg\", \"overflow-hidden\", \"shadow-lg\", \"border\", \"border-[#2a2a2a]\", \"z-10\"], [\"autoplay\", \"\", \"playsinline\", \"\", \"muted\", \"\", 1, \"w-full\", \"h-full\", \"object-cover\"], [\"localVideo\", \"\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-t\", \"from-[#121212]/30\", \"to-transparent\", \"pointer-events-none\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\", \"bg-[#121212]/70\"], [1, \"relative\"], [1, \"w-32\", \"h-32\", \"rounded-full\", \"overflow-hidden\", \"border-2\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"h-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/30\", \"dark:bg-[#6d78c9]/30\", \"rounded-full\", \"blur-md\"], [1, \"absolute\", \"inset-0\", \"border-2\", \"border-[#4f5fad]/40\", \"dark:border-[#6d78c9]/40\", \"rounded-full\", \"animate-ping\", \"opacity-75\"], [1, \"flex-1\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\"], [1, \"relative\", \"mb-8\"], [1, \"absolute\", \"-inset-4\", \"border-2\", \"border-[#4f5fad]/10\", \"dark:border-[#6d78c9]/10\", \"rounded-full\", \"animate-ping\", \"opacity-75\"], [1, \"absolute\", \"-inset-8\", \"border\", \"border-[#4f5fad]/5\", \"dark:border-[#6d78c9]/5\", \"rounded-full\", \"animate-ping\", \"opacity-50\", \"delay-300\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-2\"], [1, \"px-4\", \"py-2\", \"rounded-full\", \"bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"border\", \"border-[#2a2a2a]\", \"flex\", \"items-center\", \"space-x-2\", \"mb-8\"], [1, \"flex\", \"items-center\", \"h-16\", \"space-x-1\", \"mb-8\"], [\"class\", \"w-1 bg-gradient-to-t from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\", 3, \"height\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-1\", \"bg-gradient-to-t\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"]],\n      template: function ActiveCallComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ActiveCallComponent_div_0_Template, 14, 21, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.activeCall);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.active-call-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  right: 0;\\n  z-index: 1000;\\n  background-color: var(--dark-bg);\\n  border-radius: var(--border-radius-lg) 0 0 0;\\n  box-shadow: 0 -4px 30px rgba(0, 0, 0, 0.3);\\n  overflow: hidden;\\n  transition: all var(--transition-medium);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n}\\n\\n.active-call-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(\\n    90deg,\\n    transparent,\\n    var(--accent-color),\\n    transparent\\n  );\\n  opacity: 0.5;\\n  z-index: 1;\\n}\\n\\n.active-call-container.video-call[_ngcontent-%COMP%] {\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 0;\\n  background-color: var(--dark-bg);\\n}\\n\\n\\n\\n.video-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.video-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    circle at center,\\n    transparent 50%,\\n    var(--dark-bg) 150%\\n  );\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n\\n.remote-video-wrapper[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.remote-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background: linear-gradient(135deg, var(--medium-bg), var(--dark-bg));\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  width: 300px;\\n  height: 300px;\\n  background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);\\n  opacity: 0.1;\\n  filter: blur(40px);\\n  animation: _ngcontent-%COMP%_pulse 4s infinite;\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  border-radius: 50%;\\n  border: 3px solid var(--accent-color);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n  z-index: 2;\\n  position: relative;\\n}\\n\\n.call-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 30px;\\n  left: 30px;\\n  color: var(--text-light);\\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 15px 20px;\\n  border-radius: var(--border-radius-md);\\n  border-left: 3px solid var(--accent-color);\\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.participant-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  color: transparent;\\n  text-shadow: none;\\n}\\n\\n.call-status[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  font-size: 16px;\\n  opacity: 0.9;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.call-status[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: inline-block;\\n  width: 8px;\\n  height: 8px;\\n  background-color: var(--accent-color);\\n  border-radius: 50%;\\n  margin-right: 8px;\\n  box-shadow: 0 0 8px var(--accent-color);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.local-video-wrapper[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  right: 30px;\\n  width: 200px;\\n  height: 130px;\\n  border-radius: var(--border-radius-md);\\n  overflow: hidden;\\n  border: 2px solid var(--accent-color);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n  z-index: 10;\\n  transition: all var(--transition-medium);\\n}\\n\\n.local-video-wrapper[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 0 30px rgba(0, 247, 255, 0.5);\\n}\\n\\n.local-video-wrapper[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border: 1px solid rgba(0, 247, 255, 0.5);\\n  border-radius: var(--border-radius-md);\\n  pointer-events: none;\\n}\\n\\n.local-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transform: scaleX(-1); \\n\\n}\\n\\n\\n\\n.audio-call-info[_ngcontent-%COMP%] {\\n  padding: 30px;\\n  text-align: center;\\n  width: 350px;\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.05),\\n    rgba(157, 78, 221, 0.05)\\n  );\\n}\\n\\n.participant-avatar[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n\\n.participant-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 100px;\\n  height: 100px;\\n  background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);\\n  opacity: 0.3;\\n  filter: blur(20px);\\n  animation: _ngcontent-%COMP%_pulse 3s infinite;\\n  z-index: -1;\\n}\\n\\n.participant-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  border: 3px solid var(--accent-color);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n}\\n\\n\\n\\n.call-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 20px;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-top: 1px solid rgba(0, 247, 255, 0.1);\\n}\\n\\n.video-call[_ngcontent-%COMP%]   .call-controls[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.control-btn[_ngcontent-%COMP%], .end-call-btn[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  border: none;\\n  margin: 0 12px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.control-btn[_ngcontent-%COMP%]::before, .end-call-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    circle at center,\\n    rgba(255, 255, 255, 0.1) 0%,\\n    transparent 70%\\n  );\\n  opacity: 0;\\n  transition: opacity var(--transition-fast);\\n}\\n\\n.control-btn[_ngcontent-%COMP%]:hover::before, .end-call-btn[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.control-btn[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--text-light);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.control-btn.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  border: none;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n.control-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);\\n}\\n\\n.control-btn.active[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.7);\\n}\\n\\n.end-call-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3547, #ff5252);\\n  color: white;\\n  box-shadow: 0 0 15px rgba(255, 53, 71, 0.3);\\n}\\n\\n.end-call-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 0 20px rgba(255, 53, 71, 0.5);\\n}\\n\\n.control-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .end-call-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 0.3;\\n    transform: scale(0.95);\\n  }\\n  50% {\\n    opacity: 0.6;\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    opacity: 0.3;\\n    transform: scale(0.95);\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"@keyframes _ngcontent-%COMP%_audio-visualizer {\\n    0%,\\n    100% {\\n      height: var(--min-height, 10px);\\n    }\\n    50% {\\n      height: var(--max-height, 40px);\\n    }\\n  }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CallType", "CallStatus", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r5", "getOtherParticipantAvatar", "ɵɵsanitizeUrl", "getOtherParticipantName", "ɵɵtemplate", "ActiveCallComponent_div_0_div_1_div_5_Template", "ɵɵtext", "_r4", "srcObject", "ɵɵtextInterpolate", "ctx_r1", "getCallStatusText", "ɵɵtextInterpolate1", "ɵɵstyleProp", "i_r8", "ActiveCallComponent_div_0_div_2_div_14_Template", "ctx_r2", "ɵɵpureFunction0", "_c2", "ɵɵlistener", "ActiveCallComponent_div_0_button_7_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "toggleCamera", "ɵɵpureFunction2", "_c3", "ctx_r3", "isVideoMuted", "_c4", "ActiveCallComponent_div_0_div_1_Template", "ActiveCallComponent_div_0_div_2_Template", "ActiveCallComponent_div_0_Template_button_click_4_listener", "_r12", "ctx_r11", "toggleMicrophone", "ActiveCallComponent_div_0_button_7_Template", "ActiveCallComponent_div_0_Template_button_click_8_listener", "ctx_r13", "toggleSpeaker", "ActiveCallComponent_div_0_Template_button_click_11_listener", "ctx_r14", "endCall", "ctx_r0", "isVideoCall", "isAudioMuted", "isSpeakerOn", "ActiveCallComponent", "constructor", "messageService", "callService", "logger", "activeCall", "callDuration", "callStartTime", "subscriptions", "ngOnInit", "activeCallSub", "activeCall$", "subscribe", "call", "previousCall", "console", "log", "status", "CONNECTED", "id", "startCallTimer", "setupMediaStreams", "stopCallTimer", "push", "localStream", "getLocalStream", "localVideo", "nativeElement", "remoteVideo", "ngAfterViewInit", "localStream$", "getValue", "remoteStream", "remoteStream$", "Date", "durationInterval", "setInterval", "now", "diff", "Math", "floor", "getTime", "minutes", "toString", "padStart", "seconds", "clearInterval", "debug", "callId", "next", "result", "success", "error", "toggleMedia", "undefined", "muted", "type", "AUDIO", "volume", "on", "currentUserId", "localStorage", "getItem", "isCurrentUserCaller", "caller", "recipient", "username", "avatar", "image", "RINGING", "ENDED", "MISSED", "REJECTED", "FAILED", "isCallConnected", "isCallRinging", "VIDEO", "VIDEO_ONLY", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "CallService", "i3", "LoggerService", "selectors", "viewQuery", "ActiveCallComponent_Query", "rf", "ctx", "ActiveCallComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.html"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ViewChild,\n  ElementRef,\n  AfterViewInit,\n} from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { Call, CallType, CallStatus } from '../../models/message.model';\nimport { MessageService } from '../../services/message.service';\nimport { CallService } from '../../services/call.service';\nimport { LoggerService } from '../../services/logger.service';\n\n@Component({\n  selector: 'app-active-call',\n  templateUrl: './active-call.component.html',\n  styleUrls: ['./active-call.component.css'],\n})\nexport class ActiveCallComponent implements OnInit, OnDestroy, AfterViewInit {\n  @ViewChild('localVideo') localVideo!: ElementRef<HTMLVideoElement>;\n  @ViewChild('remoteVideo') remoteVideo!: ElementRef<HTMLVideoElement>;\n\n  activeCall: Call | null = null;\n  callDuration: string = '00:00';\n  isAudioMuted: boolean = false;\n  isVideoMuted: boolean = false;\n  isSpeakerOn: boolean = true;\n\n  private durationInterval: any;\n  private callStartTime: Date | null = null;\n  private subscriptions: Subscription[] = [];\n\n  // Exposer les énums au template\n  CallType = CallType;\n  CallStatus = CallStatus;\n\n  constructor(\n    private messageService: MessageService,\n    private callService: CallService,\n    private logger: LoggerService\n  ) {}\n\n  ngOnInit(): void {\n    // S'abonner à l'appel actif\n    const activeCallSub = this.callService.activeCall$.subscribe((call) => {\n      const previousCall = this.activeCall;\n      this.activeCall = call;\n\n      console.log('📞 [ActiveCall] Active call updated:', call);\n\n      if (call && call.status === CallStatus.CONNECTED) {\n        if (!previousCall || previousCall.id !== call.id) {\n          // Nouvel appel connecté\n          console.log(\n            '✅ [ActiveCall] Call connected, starting timer and setting up media'\n          );\n          this.startCallTimer();\n          this.setupMediaStreams();\n        }\n      } else if (!call || call.status !== CallStatus.CONNECTED) {\n        // Appel terminé ou non connecté\n        this.stopCallTimer();\n      }\n    });\n\n    this.subscriptions.push(activeCallSub);\n  }\n\n  /**\n   * Configure les flux média pour l'appel\n   */\n  private setupMediaStreams(): void {\n    console.log('🎥 [ActiveCall] Setting up media streams...');\n\n    // Configurer le flux local\n    const localStream = this.callService.getLocalStream();\n    if (localStream && this.localVideo?.nativeElement) {\n      console.log('📹 [ActiveCall] Setting local video stream');\n      this.localVideo.nativeElement.srcObject = localStream;\n    }\n\n    // Pour l'instant, pas de flux distant réel (WebRTC complet nécessaire)\n    // Mais on peut simuler ou utiliser le même flux pour les tests\n    if (this.remoteVideo?.nativeElement && localStream) {\n      console.log('📹 [ActiveCall] Setting remote video stream (simulated)');\n      // Pour les tests, on peut utiliser le même flux ou laisser vide\n      // this.remoteVideo.nativeElement.srcObject = localStream;\n    }\n  }\n\n  ngAfterViewInit(): void {\n    // Configurer les éléments vidéo après le rendu du composant\n    const localStream = this.messageService.localStream$.getValue();\n    if (localStream && this.localVideo?.nativeElement) {\n      this.localVideo.nativeElement.srcObject = localStream;\n    }\n\n    const remoteStream = this.messageService.remoteStream$.getValue();\n    if (remoteStream && this.remoteVideo?.nativeElement) {\n      this.remoteVideo.nativeElement.srcObject = remoteStream;\n    }\n  }\n\n  // Démarrer le minuteur d'appel\n  private startCallTimer(): void {\n    this.callStartTime = new Date();\n    this.stopCallTimer(); // Arrêter tout minuteur existant\n\n    this.durationInterval = setInterval(() => {\n      if (!this.callStartTime) return;\n\n      const now = new Date();\n      const diff = Math.floor(\n        (now.getTime() - this.callStartTime.getTime()) / 1000\n      );\n\n      const minutes = Math.floor(diff / 60)\n        .toString()\n        .padStart(2, '0');\n      const seconds = (diff % 60).toString().padStart(2, '0');\n\n      this.callDuration = `${minutes}:${seconds}`;\n    }, 1000);\n  }\n\n  // Arrêter le minuteur d'appel\n  private stopCallTimer(): void {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n  }\n\n  // Terminer l'appel\n  endCall(): void {\n    if (!this.activeCall) {\n      return;\n    }\n\n    this.logger.debug('Ending call', { callId: this.activeCall.id });\n\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: (result) => {\n        this.logger.debug('Call ended successfully', {\n          callId: this.activeCall?.id,\n          success: result.success,\n        });\n      },\n      error: (error) => {\n        this.logger.error('Error ending call', error);\n      },\n    });\n  }\n\n  // Basculer le micro\n  toggleMicrophone(): void {\n    if (!this.activeCall) {\n      return;\n    }\n\n    this.isAudioMuted = !this.isAudioMuted;\n\n    this.callService\n      .toggleMedia(this.activeCall.id, undefined, !this.isAudioMuted)\n      .subscribe({\n        next: (result) => {\n          this.logger.debug('Microphone toggled', { muted: this.isAudioMuted });\n        },\n        error: (error) => {\n          this.logger.error('Error toggling microphone', error);\n          // Revenir à l'état précédent en cas d'erreur\n          this.isAudioMuted = !this.isAudioMuted;\n        },\n      });\n  }\n\n  // Basculer la caméra\n  toggleCamera(): void {\n    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) {\n      return;\n    }\n\n    this.isVideoMuted = !this.isVideoMuted;\n\n    this.callService\n      .toggleMedia(this.activeCall.id, !this.isVideoMuted, undefined)\n      .subscribe({\n        next: (result) => {\n          this.logger.debug('Camera toggled', { muted: this.isVideoMuted });\n        },\n        error: (error) => {\n          this.logger.error('Error toggling camera', error);\n          // Revenir à l'état précédent en cas d'erreur\n          this.isVideoMuted = !this.isVideoMuted;\n        },\n      });\n  }\n\n  // Basculer le haut-parleur\n  toggleSpeaker(): void {\n    this.isSpeakerOn = !this.isSpeakerOn;\n\n    if (this.remoteVideo?.nativeElement) {\n      this.remoteVideo.nativeElement.volume = this.isSpeakerOn ? 1 : 0;\n    }\n\n    this.logger.debug('Speaker toggled', { on: this.isSpeakerOn });\n  }\n\n  // Obtenir le nom de l'autre participant\n  getOtherParticipantName(): string {\n    if (!this.activeCall) {\n      return '';\n    }\n\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n\n    return isCurrentUserCaller\n      ? this.activeCall.recipient.username\n      : this.activeCall.caller.username;\n  }\n\n  // Obtenir l'avatar de l'autre participant\n  getOtherParticipantAvatar(): string {\n    if (!this.activeCall) {\n      return 'assets/images/default-avatar.png';\n    }\n\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n\n    const avatar = isCurrentUserCaller\n      ? this.activeCall.recipient.image\n      : this.activeCall.caller.image;\n\n    return avatar || 'assets/images/default-avatar.png';\n  }\n\n  // Obtenir le statut de l'appel sous forme de texte\n  getCallStatusText(): string {\n    if (!this.activeCall) {\n      return '';\n    }\n\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Appel en cours...';\n      case CallStatus.CONNECTED:\n        return this.callDuration;\n      case CallStatus.ENDED:\n        return 'Appel terminé';\n      case CallStatus.MISSED:\n        return 'Appel manqué';\n      case CallStatus.REJECTED:\n        return 'Appel rejeté';\n      case CallStatus.FAILED:\n        return \"Échec de l'appel\";\n      default:\n        return '';\n    }\n  }\n\n  // Vérifier si l'appel est connecté\n  isCallConnected(): boolean {\n    return this.activeCall?.status === CallStatus.CONNECTED;\n  }\n\n  // Vérifier si l'appel est en cours de sonnerie\n  isCallRinging(): boolean {\n    return this.activeCall?.status === CallStatus.RINGING;\n  }\n\n  // Vérifier si l'appel est un appel vidéo\n  isVideoCall(): boolean {\n    return (\n      this.activeCall?.type === CallType.VIDEO ||\n      this.activeCall?.type === CallType.VIDEO_ONLY\n    );\n  }\n\n  ngOnDestroy(): void {\n    // Nettoyer les abonnements et le minuteur\n    this.stopCallTimer();\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n}\n", "<div\n  *ngIf=\"activeCall\"\n  class=\"fixed inset-0 z-50 bg-[#121212]/90 backdrop-blur-sm flex flex-col\"\n>\n  <!-- Appel vidéo -->\n  <div *ngIf=\"isVideoCall()\" class=\"flex-1 relative\">\n    <!-- Vidéo distante -->\n    <div class=\"absolute inset-0 overflow-hidden\">\n      <video\n        #remoteVideo\n        autoplay\n        playsinline\n        class=\"w-full h-full object-cover\"\n      ></video>\n\n      <!-- Overlay gradient -->\n      <div\n        class=\"absolute inset-0 bg-gradient-to-t from-[#121212]/80 to-transparent pointer-events-none\"\n      ></div>\n\n      <!-- Afficher l'avatar si la vidéo est désactivée -->\n      <div\n        *ngIf=\"!remoteVideo.srcObject\"\n        class=\"absolute inset-0 flex items-center justify-center bg-[#121212]/70\"\n      >\n        <div class=\"relative\">\n          <div\n            class=\"w-32 h-32 rounded-full overflow-hidden border-2 border-[#4f5fad] dark:border-[#6d78c9]\"\n          >\n            <img\n              [src]=\"getOtherParticipantAvatar()\"\n              [alt]=\"getOtherParticipantName()\"\n              class=\"w-full h-full object-cover\"\n            />\n          </div>\n\n          <!-- Glow effect -->\n          <div\n            class=\"absolute inset-0 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30 rounded-full blur-md\"\n          ></div>\n\n          <!-- Animated rings -->\n          <div\n            class=\"absolute inset-0 border-2 border-[#4f5fad]/40 dark:border-[#6d78c9]/40 rounded-full animate-ping opacity-75\"\n          ></div>\n        </div>\n      </div>\n\n      <!-- Informations sur l'appel -->\n      <div\n        class=\"absolute top-6 left-0 right-0 flex justify-center pointer-events-none\"\n      >\n        <div\n          class=\"px-4 py-2 rounded-full bg-[#121212]/50 backdrop-blur-sm border border-[#2a2a2a] flex items-center space-x-2\"\n        >\n          <div\n            class=\"w-2 h-2 rounded-full bg-[#4f5fad] dark:bg-[#6d78c9] animate-pulse\"\n          ></div>\n          <span class=\"text-white text-sm font-medium\">{{\n            getCallStatusText()\n          }}</span>\n        </div>\n      </div>\n\n      <div\n        class=\"absolute bottom-24 left-0 right-0 text-center pointer-events-none\"\n      >\n        <h3\n          class=\"text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\n        >\n          {{ getOtherParticipantName() }}\n        </h3>\n      </div>\n    </div>\n\n    <!-- Vidéo locale -->\n    <div\n      class=\"absolute top-4 right-4 w-32 h-48 rounded-lg overflow-hidden shadow-lg border border-[#2a2a2a] z-10\"\n    >\n      <video\n        #localVideo\n        autoplay\n        playsinline\n        muted\n        class=\"w-full h-full object-cover\"\n      ></video>\n\n      <!-- Overlay gradient -->\n      <div\n        class=\"absolute inset-0 bg-gradient-to-t from-[#121212]/30 to-transparent pointer-events-none\"\n      ></div>\n    </div>\n  </div>\n\n  <!-- Appel audio -->\n  <div\n    *ngIf=\"!isVideoCall()\"\n    class=\"flex-1 flex flex-col items-center justify-center\"\n  >\n    <div class=\"relative mb-8\">\n      <div\n        class=\"w-32 h-32 rounded-full overflow-hidden border-2 border-[#4f5fad] dark:border-[#6d78c9]\"\n      >\n        <img\n          [src]=\"getOtherParticipantAvatar()\"\n          [alt]=\"getOtherParticipantName()\"\n          class=\"w-full h-full object-cover\"\n        />\n      </div>\n\n      <!-- Glow effect -->\n      <div\n        class=\"absolute inset-0 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30 rounded-full blur-md\"\n      ></div>\n\n      <!-- Animated rings -->\n      <div\n        class=\"absolute -inset-4 border-2 border-[#4f5fad]/10 dark:border-[#6d78c9]/10 rounded-full animate-ping opacity-75\"\n      ></div>\n      <div\n        class=\"absolute -inset-8 border border-[#4f5fad]/5 dark:border-[#6d78c9]/5 rounded-full animate-ping opacity-50 delay-300\"\n      ></div>\n    </div>\n\n    <h2\n      class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-2\"\n    >\n      {{ getOtherParticipantName() }}\n    </h2>\n\n    <div\n      class=\"px-4 py-2 rounded-full bg-[#1e1e1e]/80 backdrop-blur-sm border border-[#2a2a2a] flex items-center space-x-2 mb-8\"\n    >\n      <div\n        class=\"w-2 h-2 rounded-full bg-[#4f5fad] dark:bg-[#6d78c9] animate-pulse\"\n      ></div>\n      <span class=\"text-white text-sm font-medium\">{{\n        getCallStatusText()\n      }}</span>\n    </div>\n\n    <!-- Visualisation audio -->\n    <div class=\"flex items-center h-16 space-x-1 mb-8\">\n      <div\n        *ngFor=\"let i of [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]\"\n        class=\"w-1 bg-gradient-to-t from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\n        [style.height.px]=\"10 + (i % 5) * 6\"\n        [style.animation-delay.ms]=\"i * 100\"\n      ></div>\n    </div>\n  </div>\n\n  <!-- Contrôles d'appel -->\n  <div class=\"p-6 flex justify-center space-x-4\">\n    <button\n      (click)=\"toggleMicrophone()\"\n      class=\"w-14 h-14 rounded-full flex items-center justify-center relative group\"\n      [ngClass]=\"{\n        'bg-[#4f5fad] dark:bg-[#6d78c9] text-white': !isAudioMuted,\n        'bg-[#1e1e1e] text-[#6d6870] border border-[#2a2a2a]': isAudioMuted\n      }\"\n    >\n      <!-- Glow effect -->\n      <div\n        class=\"absolute inset-0 rounded-full blur-md transition-opacity\"\n        [ngClass]=\"{\n          'opacity-100 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50': !isAudioMuted,\n          'opacity-0 group-hover:opacity-30 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30':\n            isAudioMuted\n        }\"\n      ></div>\n      <i\n        class=\"fas text-lg relative z-10\"\n        [ngClass]=\"isAudioMuted ? 'fa-microphone-slash' : 'fa-microphone'\"\n      ></i>\n    </button>\n\n    <button\n      *ngIf=\"isVideoCall()\"\n      (click)=\"toggleCamera()\"\n      class=\"w-14 h-14 rounded-full flex items-center justify-center relative group\"\n      [ngClass]=\"{\n        'bg-[#4f5fad] dark:bg-[#6d78c9] text-white': !isVideoMuted,\n        'bg-[#1e1e1e] text-[#6d6870] border border-[#2a2a2a]': isVideoMuted\n      }\"\n    >\n      <!-- Glow effect -->\n      <div\n        class=\"absolute inset-0 rounded-full blur-md transition-opacity\"\n        [ngClass]=\"{\n          'opacity-100 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50': !isVideoMuted,\n          'opacity-0 group-hover:opacity-30 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30':\n            isVideoMuted\n        }\"\n      ></div>\n      <i\n        class=\"fas text-lg relative z-10\"\n        [ngClass]=\"isVideoMuted ? 'fa-video-slash' : 'fa-video'\"\n      ></i>\n    </button>\n\n    <button\n      (click)=\"toggleSpeaker()\"\n      class=\"w-14 h-14 rounded-full flex items-center justify-center relative group\"\n      [ngClass]=\"{\n        'bg-[#4f5fad] dark:bg-[#6d78c9] text-white': isSpeakerOn,\n        'bg-[#1e1e1e] text-[#6d6870] border border-[#2a2a2a]': !isSpeakerOn\n      }\"\n    >\n      <!-- Glow effect -->\n      <div\n        class=\"absolute inset-0 rounded-full blur-md transition-opacity\"\n        [ngClass]=\"{\n          'opacity-100 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50': isSpeakerOn,\n          'opacity-0 group-hover:opacity-30 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30':\n            !isSpeakerOn\n        }\"\n      ></div>\n      <i\n        class=\"fas text-lg relative z-10\"\n        [ngClass]=\"isSpeakerOn ? 'fa-volume-up' : 'fa-volume-mute'\"\n      ></i>\n    </button>\n\n    <button\n      (click)=\"endCall()\"\n      class=\"w-14 h-14 rounded-full flex items-center justify-center bg-gradient-to-r from-[#ff6b69] to-[#ff8785] text-white relative group\"\n    >\n      <!-- Glow effect -->\n      <div\n        class=\"absolute inset-0 bg-[#ff6b69]/50 rounded-full blur-md opacity-70 group-hover:opacity-100 transition-opacity\"\n      ></div>\n      <i class=\"fas fa-phone-slash text-lg relative z-10\"></i>\n    </button>\n  </div>\n</div>\n\n<style>\n  @keyframes audio-visualizer {\n    0%,\n    100% {\n      height: var(--min-height, 10px);\n    }\n    50% {\n      height: var(--max-height, 40px);\n    }\n  }\n</style>\n"], "mappings": "AASA,SAAeA,QAAQ,EAAEC,UAAU,QAAQ,4BAA4B;;;;;;;;;;ICYjEC,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,cAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAE,SAAA,cAEO;IAMTF,EAAA,CAAAG,YAAA,EAAM;;;;IAfAH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,UAAA,QAAAC,MAAA,CAAAC,yBAAA,IAAAP,EAAA,CAAAQ,aAAA,CAAmC,QAAAF,MAAA,CAAAG,uBAAA;;;;;IAzB/CT,EAAA,CAAAC,cAAA,cAAmD;IAG/CD,EAAA,CAAAE,SAAA,oBAKS;IAQTF,EAAA,CAAAU,UAAA,IAAAC,8CAAA,kBAyBM;IAGNX,EAAA,CAAAC,cAAA,cAEC;IAIGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,eAA6C;IAAAD,EAAA,CAAAY,MAAA,IAE3C;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IAIbH,EAAA,CAAAC,cAAA,eAEC;IAIGD,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAKTH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,qBAMS;IAMXF,EAAA,CAAAG,YAAA,EAAM;;;;;IArEDH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,UAAA,UAAAQ,GAAA,CAAAC,SAAA,CAA4B;IAoCkBd,EAAA,CAAAI,SAAA,GAE3C;IAF2CJ,EAAA,CAAAe,iBAAA,CAAAC,MAAA,CAAAC,iBAAA,GAE3C;IAUFjB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAF,MAAA,CAAAP,uBAAA,QACF;;;;;IAwEFT,EAAA,CAAAE,SAAA,cAKO;;;;IAFLF,EAAA,CAAAmB,WAAA,gBAAAC,IAAA,eAAoC,oBAAAA,IAAA;;;;;;;;IAnD1CpB,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,cAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAE,SAAA,cAEO;IASTF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,gBAA6C;IAAAD,EAAA,CAAAY,MAAA,IAE3C;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IAIXH,EAAA,CAAAC,cAAA,eAAmD;IACjDD,EAAA,CAAAU,UAAA,KAAAW,+CAAA,kBAKO;IACTrB,EAAA,CAAAG,YAAA,EAAM;;;;IA7CAH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,UAAA,QAAAiB,MAAA,CAAAf,yBAAA,IAAAP,EAAA,CAAAQ,aAAA,CAAmC,QAAAc,MAAA,CAAAb,uBAAA;IAuBvCT,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAI,MAAA,CAAAb,uBAAA,QACF;IAQ+CT,EAAA,CAAAI,SAAA,GAE3C;IAF2CJ,EAAA,CAAAe,iBAAA,CAAAO,MAAA,CAAAL,iBAAA,GAE3C;IAMcjB,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAuB,eAAA,IAAAC,GAAA,EAAsD;;;;;;;;;;;;;;;;;;IAiCxExB,EAAA,CAAAC,cAAA,gBAQC;IANCD,EAAA,CAAAyB,UAAA,mBAAAC,oEAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAQxBhC,EAAA,CAAAE,SAAA,aAOO;IAKTF,EAAA,CAAAG,YAAA,EAAS;;;;IAlBPH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAiC,eAAA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,YAAA,EAAAD,MAAA,CAAAC,YAAA,EAGE;IAKApC,EAAA,CAAAI,SAAA,GAIE;IAJFJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAiC,eAAA,IAAAI,GAAA,GAAAF,MAAA,CAAAC,YAAA,EAAAD,MAAA,CAAAC,YAAA,EAIE;IAIFpC,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,UAAA,YAAA8B,MAAA,CAAAC,YAAA,iCAAwD;;;;;;IArMhEpC,EAAA,CAAAC,cAAA,aAGC;IAECD,EAAA,CAAAU,UAAA,IAAA4B,wCAAA,kBAuFM;IAGNtC,EAAA,CAAAU,UAAA,IAAA6B,wCAAA,kBAuDM;IAGNvC,EAAA,CAAAC,cAAA,aAA+C;IAE3CD,EAAA,CAAAyB,UAAA,mBAAAe,2DAAA;MAAAxC,EAAA,CAAA2B,aAAA,CAAAc,IAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAW,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAQ5B3C,EAAA,CAAAE,SAAA,aAOO;IAKTF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAU,UAAA,IAAAkC,2CAAA,oBAsBS;IAET5C,EAAA,CAAAC,cAAA,gBAOC;IANCD,EAAA,CAAAyB,UAAA,mBAAAoB,2DAAA;MAAA7C,EAAA,CAAA2B,aAAA,CAAAc,IAAA;MAAA,MAAAK,OAAA,GAAA9C,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAe,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAQzB/C,EAAA,CAAAE,SAAA,aAOO;IAKTF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAyB,UAAA,mBAAAuB,4DAAA;MAAAhD,EAAA,CAAA2B,aAAA,CAAAc,IAAA;MAAA,MAAAQ,OAAA,GAAAjD,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAkB,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAInBlD,EAAA,CAAAE,SAAA,eAEO;IAETF,EAAA,CAAAG,YAAA,EAAS;;;;IApOLH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,UAAA,SAAA8C,MAAA,CAAAC,WAAA,GAAmB;IA2FtBpD,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,UAAA,UAAA8C,MAAA,CAAAC,WAAA,GAAoB;IA6DnBpD,EAAA,CAAAI,SAAA,GAGE;IAHFJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAiC,eAAA,IAAAC,GAAA,GAAAiB,MAAA,CAAAE,YAAA,EAAAF,MAAA,CAAAE,YAAA,EAGE;IAKArD,EAAA,CAAAI,SAAA,GAIE;IAJFJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAiC,eAAA,KAAAI,GAAA,GAAAc,MAAA,CAAAE,YAAA,EAAAF,MAAA,CAAAE,YAAA,EAIE;IAIFrD,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAAK,UAAA,YAAA8C,MAAA,CAAAE,YAAA,2CAAkE;IAKnErD,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,UAAA,SAAA8C,MAAA,CAAAC,WAAA,GAAmB;IA0BpBpD,EAAA,CAAAI,SAAA,GAGE;IAHFJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAiC,eAAA,KAAAC,GAAA,EAAAiB,MAAA,CAAAG,WAAA,GAAAH,MAAA,CAAAG,WAAA,EAGE;IAKAtD,EAAA,CAAAI,SAAA,GAIE;IAJFJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAiC,eAAA,KAAAI,GAAA,EAAAc,MAAA,CAAAG,WAAA,GAAAH,MAAA,CAAAG,WAAA,EAIE;IAIFtD,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAK,UAAA,YAAA8C,MAAA,CAAAG,WAAA,qCAA2D;;;ADzMnE,OAAM,MAAOC,mBAAmB;EAkB9BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,MAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAjBhB,KAAAC,UAAU,GAAgB,IAAI;IAC9B,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAR,YAAY,GAAY,KAAK;IAC7B,KAAAjB,YAAY,GAAY,KAAK;IAC7B,KAAAkB,WAAW,GAAY,IAAI;IAGnB,KAAAQ,aAAa,GAAgB,IAAI;IACjC,KAAAC,aAAa,GAAmB,EAAE;IAE1C;IACA,KAAAjE,QAAQ,GAAGA,QAAQ;IACnB,KAAAC,UAAU,GAAGA,UAAU;EAMpB;EAEHiE,QAAQA,CAAA;IACN;IACA,MAAMC,aAAa,GAAG,IAAI,CAACP,WAAW,CAACQ,WAAW,CAACC,SAAS,CAAEC,IAAI,IAAI;MACpE,MAAMC,YAAY,GAAG,IAAI,CAACT,UAAU;MACpC,IAAI,CAACA,UAAU,GAAGQ,IAAI;MAEtBE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEH,IAAI,CAAC;MAEzD,IAAIA,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKzE,UAAU,CAAC0E,SAAS,EAAE;QAChD,IAAI,CAACJ,YAAY,IAAIA,YAAY,CAACK,EAAE,KAAKN,IAAI,CAACM,EAAE,EAAE;UAChD;UACAJ,OAAO,CAACC,GAAG,CACT,oEAAoE,CACrE;UACD,IAAI,CAACI,cAAc,EAAE;UACrB,IAAI,CAACC,iBAAiB,EAAE;;OAE3B,MAAM,IAAI,CAACR,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKzE,UAAU,CAAC0E,SAAS,EAAE;QACxD;QACA,IAAI,CAACI,aAAa,EAAE;;IAExB,CAAC,CAAC;IAEF,IAAI,CAACd,aAAa,CAACe,IAAI,CAACb,aAAa,CAAC;EACxC;EAEA;;;EAGQW,iBAAiBA,CAAA;IACvBN,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAE1D;IACA,MAAMQ,WAAW,GAAG,IAAI,CAACrB,WAAW,CAACsB,cAAc,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,CAACE,UAAU,EAAEC,aAAa,EAAE;MACjDZ,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD,IAAI,CAACU,UAAU,CAACC,aAAa,CAACpE,SAAS,GAAGiE,WAAW;;IAGvD;IACA;IACA,IAAI,IAAI,CAACI,WAAW,EAAED,aAAa,IAAIH,WAAW,EAAE;MAClDT,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtE;MACA;;EAEJ;;EAEAa,eAAeA,CAAA;IACb;IACA,MAAML,WAAW,GAAG,IAAI,CAACtB,cAAc,CAAC4B,YAAY,CAACC,QAAQ,EAAE;IAC/D,IAAIP,WAAW,IAAI,IAAI,CAACE,UAAU,EAAEC,aAAa,EAAE;MACjD,IAAI,CAACD,UAAU,CAACC,aAAa,CAACpE,SAAS,GAAGiE,WAAW;;IAGvD,MAAMQ,YAAY,GAAG,IAAI,CAAC9B,cAAc,CAAC+B,aAAa,CAACF,QAAQ,EAAE;IACjE,IAAIC,YAAY,IAAI,IAAI,CAACJ,WAAW,EAAED,aAAa,EAAE;MACnD,IAAI,CAACC,WAAW,CAACD,aAAa,CAACpE,SAAS,GAAGyE,YAAY;;EAE3D;EAEA;EACQZ,cAAcA,CAAA;IACpB,IAAI,CAACb,aAAa,GAAG,IAAI2B,IAAI,EAAE;IAC/B,IAAI,CAACZ,aAAa,EAAE,CAAC,CAAC;IAEtB,IAAI,CAACa,gBAAgB,GAAGC,WAAW,CAAC,MAAK;MACvC,IAAI,CAAC,IAAI,CAAC7B,aAAa,EAAE;MAEzB,MAAM8B,GAAG,GAAG,IAAIH,IAAI,EAAE;MACtB,MAAMI,IAAI,GAAGC,IAAI,CAACC,KAAK,CACrB,CAACH,GAAG,CAACI,OAAO,EAAE,GAAG,IAAI,CAAClC,aAAa,CAACkC,OAAO,EAAE,IAAI,IAAI,CACtD;MAED,MAAMC,OAAO,GAAGH,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,EAAE,CAAC,CAClCK,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnB,MAAMC,OAAO,GAAG,CAACP,IAAI,GAAG,EAAE,EAAEK,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAEvD,IAAI,CAACtC,YAAY,GAAG,GAAGoC,OAAO,IAAIG,OAAO,EAAE;IAC7C,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQvB,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACa,gBAAgB,EAAE;MACzBW,aAAa,CAAC,IAAI,CAACX,gBAAgB,CAAC;MACpC,IAAI,CAACA,gBAAgB,GAAG,IAAI;;EAEhC;EAEA;EACAxC,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACU,UAAU,EAAE;MACpB;;IAGF,IAAI,CAACD,MAAM,CAAC2C,KAAK,CAAC,aAAa,EAAE;MAAEC,MAAM,EAAE,IAAI,CAAC3C,UAAU,CAACc;IAAE,CAAE,CAAC;IAEhE,IAAI,CAAChB,WAAW,CAACR,OAAO,CAAC,IAAI,CAACU,UAAU,CAACc,EAAE,CAAC,CAACP,SAAS,CAAC;MACrDqC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAAC9C,MAAM,CAAC2C,KAAK,CAAC,yBAAyB,EAAE;UAC3CC,MAAM,EAAE,IAAI,CAAC3C,UAAU,EAAEc,EAAE;UAC3BgC,OAAO,EAAED,MAAM,CAACC;SACjB,CAAC;MACJ,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChD,MAAM,CAACgD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEA;EACAhE,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACiB,UAAU,EAAE;MACpB;;IAGF,IAAI,CAACP,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IAEtC,IAAI,CAACK,WAAW,CACbkD,WAAW,CAAC,IAAI,CAAChD,UAAU,CAACc,EAAE,EAAEmC,SAAS,EAAE,CAAC,IAAI,CAACxD,YAAY,CAAC,CAC9Dc,SAAS,CAAC;MACTqC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAAC9C,MAAM,CAAC2C,KAAK,CAAC,oBAAoB,EAAE;UAAEQ,KAAK,EAAE,IAAI,CAACzD;QAAY,CAAE,CAAC;MACvE,CAAC;MACDsD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChD,MAAM,CAACgD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACrD;QACA,IAAI,CAACtD,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;MACxC;KACD,CAAC;EACN;EAEA;EACArB,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC4B,UAAU,IAAI,IAAI,CAACA,UAAU,CAACmD,IAAI,KAAKjH,QAAQ,CAACkH,KAAK,EAAE;MAC/D;;IAGF,IAAI,CAAC5E,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IAEtC,IAAI,CAACsB,WAAW,CACbkD,WAAW,CAAC,IAAI,CAAChD,UAAU,CAACc,EAAE,EAAE,CAAC,IAAI,CAACtC,YAAY,EAAEyE,SAAS,CAAC,CAC9D1C,SAAS,CAAC;MACTqC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAAC9C,MAAM,CAAC2C,KAAK,CAAC,gBAAgB,EAAE;UAAEQ,KAAK,EAAE,IAAI,CAAC1E;QAAY,CAAE,CAAC;MACnE,CAAC;MACDuE,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChD,MAAM,CAACgD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QACjD;QACA,IAAI,CAACvE,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;MACxC;KACD,CAAC;EACN;EAEA;EACAW,aAAaA,CAAA;IACX,IAAI,CAACO,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,IAAI,CAAC6B,WAAW,EAAED,aAAa,EAAE;MACnC,IAAI,CAACC,WAAW,CAACD,aAAa,CAAC+B,MAAM,GAAG,IAAI,CAAC3D,WAAW,GAAG,CAAC,GAAG,CAAC;;IAGlE,IAAI,CAACK,MAAM,CAAC2C,KAAK,CAAC,iBAAiB,EAAE;MAAEY,EAAE,EAAE,IAAI,CAAC5D;IAAW,CAAE,CAAC;EAChE;EAEA;EACA7C,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACmD,UAAU,EAAE;MACpB,OAAO,EAAE;;IAGX;IACA,MAAMuD,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACpD,MAAMC,mBAAmB,GAAG,IAAI,CAAC1D,UAAU,CAAC2D,MAAM,CAAC7C,EAAE,KAAKyC,aAAa;IAEvE,OAAOG,mBAAmB,GACtB,IAAI,CAAC1D,UAAU,CAAC4D,SAAS,CAACC,QAAQ,GAClC,IAAI,CAAC7D,UAAU,CAAC2D,MAAM,CAACE,QAAQ;EACrC;EAEA;EACAlH,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACqD,UAAU,EAAE;MACpB,OAAO,kCAAkC;;IAG3C;IACA,MAAMuD,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACpD,MAAMC,mBAAmB,GAAG,IAAI,CAAC1D,UAAU,CAAC2D,MAAM,CAAC7C,EAAE,KAAKyC,aAAa;IAEvE,MAAMO,MAAM,GAAGJ,mBAAmB,GAC9B,IAAI,CAAC1D,UAAU,CAAC4D,SAAS,CAACG,KAAK,GAC/B,IAAI,CAAC/D,UAAU,CAAC2D,MAAM,CAACI,KAAK;IAEhC,OAAOD,MAAM,IAAI,kCAAkC;EACrD;EAEA;EACAzG,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC2C,UAAU,EAAE;MACpB,OAAO,EAAE;;IAGX,QAAQ,IAAI,CAACA,UAAU,CAACY,MAAM;MAC5B,KAAKzE,UAAU,CAAC6H,OAAO;QACrB,OAAO,mBAAmB;MAC5B,KAAK7H,UAAU,CAAC0E,SAAS;QACvB,OAAO,IAAI,CAACZ,YAAY;MAC1B,KAAK9D,UAAU,CAAC8H,KAAK;QACnB,OAAO,eAAe;MACxB,KAAK9H,UAAU,CAAC+H,MAAM;QACpB,OAAO,cAAc;MACvB,KAAK/H,UAAU,CAACgI,QAAQ;QACtB,OAAO,cAAc;MACvB,KAAKhI,UAAU,CAACiI,MAAM;QACpB,OAAO,kBAAkB;MAC3B;QACE,OAAO,EAAE;;EAEf;EAEA;EACAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACrE,UAAU,EAAEY,MAAM,KAAKzE,UAAU,CAAC0E,SAAS;EACzD;EAEA;EACAyD,aAAaA,CAAA;IACX,OAAO,IAAI,CAACtE,UAAU,EAAEY,MAAM,KAAKzE,UAAU,CAAC6H,OAAO;EACvD;EAEA;EACAxE,WAAWA,CAAA;IACT,OACE,IAAI,CAACQ,UAAU,EAAEmD,IAAI,KAAKjH,QAAQ,CAACqI,KAAK,IACxC,IAAI,CAACvE,UAAU,EAAEmD,IAAI,KAAKjH,QAAQ,CAACsI,UAAU;EAEjD;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAACxD,aAAa,EAAE;IACpB,IAAI,CAACd,aAAa,CAACuE,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;;;uBA7QWjF,mBAAmB,EAAAvD,EAAA,CAAAyI,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA3I,EAAA,CAAAyI,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA7I,EAAA,CAAAyI,iBAAA,CAAAK,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAnBxF,mBAAmB;MAAAyF,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCnBhCnJ,EAAA,CAAAU,UAAA,IAAA2I,kCAAA,mBA2OM;;;UA1OHrJ,EAAA,CAAAK,UAAA,SAAA+I,GAAA,CAAAxF,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}