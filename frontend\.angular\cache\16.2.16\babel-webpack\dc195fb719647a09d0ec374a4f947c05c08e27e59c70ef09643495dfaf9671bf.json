{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"src/app/services/rendus.service\";\nimport * as i4 from \"src/app/services/authuser.service\";\nimport * as i5 from \"@angular/common\";\nfunction ProjectDetailComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31);\n    i0.ɵɵelement(2, \"div\", 32)(3, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectDetailComponent_div_38_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79)(2, \"div\", 80)(3, \"div\", 81)(4, \"div\", 82);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 39);\n    i0.ɵɵelement(6, \"path\", 83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"div\", 84)(8, \"p\", 85);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 86);\n    i0.ɵɵtext(11, \"Document de projet\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"a\", 87)(13, \"div\", 19);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(14, \"svg\", 88);\n    i0.ɵɵelement(15, \"path\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"T\\u00E9l\\u00E9charger\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const file_r7 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getFileName(file_r7), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r6.getFileUrl(file_r7), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProjectDetailComponent_div_38_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtemplate(1, ProjectDetailComponent_div_38_div_21_div_1_Template, 18, 2, \"div\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.projet.fichiers);\n  }\n}\nfunction ProjectDetailComponent_div_38_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"div\", 91)(2, \"div\", 92);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 93);\n    i0.ɵɵelement(4, \"path\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 25);\n    i0.ɵɵtext(6, \"Aucun fichier joint \\u00E0 ce projet\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectDetailComponent_div_38_ng_container_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 94)(2, \"div\", 26)(3, \"div\", 66);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 67);\n    i0.ɵɵelement(5, \"path\", 95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"div\")(7, \"p\", 96);\n    i0.ɵɵtext(8, \"Projet soumis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 97);\n    i0.ɵɵtext(10, \"Votre rendu a \\u00E9t\\u00E9 enregistr\\u00E9 avec succ\\u00E8s\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/projects/submit\", a1];\n};\nfunction ProjectDetailComponent_div_38_ng_container_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 98)(2, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 74);\n    i0.ɵɵelement(4, \"path\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"Soumettre mon projet\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, ctx_r5.projetId));\n  }\n}\nfunction ProjectDetailComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 36)(3, \"div\", 37)(4, \"div\", 38);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 39);\n    i0.ɵɵelement(6, \"path\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"h3\", 41);\n    i0.ɵɵtext(8, \"Description du projet\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 42)(10, \"p\", 43);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 36)(13, \"div\", 37)(14, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(15, \"svg\", 45);\n    i0.ɵɵelement(16, \"path\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(17, \"h3\", 41);\n    i0.ɵɵtext(18, \" Ressources du projet \");\n    i0.ɵɵelementStart(19, \"span\", 47);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, ProjectDetailComponent_div_38_div_21_Template, 2, 1, \"div\", 48);\n    i0.ɵɵtemplate(22, ProjectDetailComponent_div_38_div_22_Template, 7, 0, \"div\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 50)(24, \"div\", 51)(25, \"div\", 52)(26, \"div\", 26)(27, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 54);\n    i0.ɵɵelement(29, \"path\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(30, \"div\")(31, \"h3\", 56);\n    i0.ɵɵtext(32, \"Informations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 57);\n    i0.ɵɵtext(34, \"D\\u00E9tails du projet\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(35, \"div\", 58)(36, \"div\", 59)(37, \"div\", 26)(38, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(39, \"svg\", 60);\n    i0.ɵɵelement(40, \"path\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"div\")(42, \"p\", 61);\n    i0.ɵɵtext(43, \"Date limite\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 62);\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(47, \"div\", 59)(48, \"div\", 26)(49, \"div\", 63);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(50, \"svg\", 64);\n    i0.ɵɵelement(51, \"path\", 65);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(52, \"div\")(53, \"p\", 61);\n    i0.ɵɵtext(54, \"Temps restant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"p\", 62);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(57, \"div\", 59)(58, \"div\", 26)(59, \"div\", 38);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(60, \"svg\", 20);\n    i0.ɵɵelement(61, \"path\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(62, \"div\")(63, \"p\", 61);\n    i0.ɵɵtext(64, \"Groupe cible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"p\", 62);\n    i0.ɵɵtext(66);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(67, \"div\", 36)(68, \"div\", 37)(69, \"div\", 66);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(70, \"svg\", 67);\n    i0.ɵɵelement(71, \"path\", 68)(72, \"path\", 69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(73, \"h3\", 41);\n    i0.ɵɵtext(74, \"Actions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 70);\n    i0.ɵɵtemplate(76, ProjectDetailComponent_div_38_ng_container_76_Template, 11, 0, \"ng-container\", 71);\n    i0.ɵɵtemplate(77, ProjectDetailComponent_div_38_ng_container_77_Template, 7, 3, \"ng-container\", 71);\n    i0.ɵɵelementStart(78, \"a\", 72)(79, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(80, \"svg\", 74);\n    i0.ɵɵelement(81, \"path\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(82, \"span\");\n    i0.ɵɵtext(83, \"Retour aux projets\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.projet == null ? null : ctx_r1.projet.description) || \"Aucune description fournie pour ce projet.\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate2(\" (\", (ctx_r1.projet == null ? null : ctx_r1.projet.fichiers == null ? null : ctx_r1.projet.fichiers.length) || 0, \" fichier\", ((ctx_r1.projet == null ? null : ctx_r1.projet.fichiers == null ? null : ctx_r1.projet.fichiers.length) || 0) > 1 ? \"s\" : \"\", \") \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.projet == null ? null : ctx_r1.projet.fichiers == null ? null : ctx_r1.projet.fichiers.length) > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.projet == null ? null : ctx_r1.projet.fichiers) || ctx_r1.projet.fichiers.length === 0);\n    i0.ɵɵadvance(23);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(46, 10, ctx_r1.projet == null ? null : ctx_r1.projet.dateLimite, \"dd/MM/yyyy\" || \"Non d\\u00E9finie\"));\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getRemainingDays(), \" jours\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate((ctx_r1.projet == null ? null : ctx_r1.projet.groupe) || \"Tous les groupes\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasSubmitted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasSubmitted);\n  }\n}\n// Composant pour afficher les détails d'un projet\nexport class ProjectDetailComponent {\n  constructor(route, router, projetService, rendusService, authService) {\n    this.route = route;\n    this.router = router;\n    this.projetService = projetService;\n    this.rendusService = rendusService;\n    this.authService = authService;\n    this.projetId = '';\n    this.isLoading = true;\n    this.hasSubmitted = false;\n  }\n  ngOnInit() {\n    this.projetId = this.route.snapshot.paramMap.get('id') || '';\n    this.loadProjetDetails();\n    this.checkRenduStatus();\n  }\n  loadProjetDetails() {\n    this.isLoading = true;\n    this.projetService.getProjetById(this.projetId).subscribe({\n      next: projet => {\n        this.projet = projet;\n        this.isLoading = false;\n      },\n      error: err => {\n        console.error('Erreur lors du chargement du projet', err);\n        this.isLoading = false;\n        this.router.navigate(['/projects']);\n      }\n    });\n  }\n  checkRenduStatus() {\n    const etudiantId = this.authService.getCurrentUserId();\n    if (etudiantId) {\n      this.rendusService.checkRenduExists(this.projetId, etudiantId).subscribe({\n        next: exists => {\n          console.log(exists);\n          this.hasSubmitted = exists;\n        },\n        error: err => {\n          console.error('Erreur lors de la vérification du rendu', err);\n        }\n      });\n    }\n  }\n  getFileUrl(filePath) {\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    // Utiliser l'endpoint API spécifique pour le téléchargement\n    return `http://localhost:3000/api/projets/download/${fileName}`;\n  }\n  getFileName(filePath) {\n    if (!filePath) return 'Fichier';\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    return filePath;\n  }\n  getProjectStatus() {\n    if (this.hasSubmitted) return 'completed';\n    if (!this.projet?.dateLimite) return 'active';\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n    if (deadline < now) return 'expired';\n    const oneWeekFromNow = new Date();\n    oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);\n    if (deadline <= oneWeekFromNow) return 'urgent';\n    return 'active';\n  }\n  getStatusClass() {\n    const status = this.getProjectStatus();\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 border border-green-200 dark:border-green-800/30';\n      case 'urgent':\n        return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-400 border border-orange-200 dark:border-orange-800/30';\n      case 'expired':\n        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 border border-red-200 dark:border-red-800/30';\n      default:\n        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 border border-blue-200 dark:border-blue-800/30';\n    }\n  }\n  getStatusText() {\n    const status = this.getProjectStatus();\n    switch (status) {\n      case 'completed':\n        return 'Projet soumis';\n      case 'urgent':\n        return 'Urgent';\n      case 'expired':\n        return 'Expiré';\n      default:\n        return 'Actif';\n    }\n  }\n  getRemainingDays() {\n    if (!this.projet?.dateLimite) return 0;\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n    const diffTime = deadline.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  }\n  static {\n    this.ɵfac = function ProjectDetailComponent_Factory(t) {\n      return new (t || ProjectDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.RendusService), i0.ɵɵdirectiveInject(i4.AuthuserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectDetailComponent,\n      selectors: [[\"app-project-detail\"]],\n      decls: 39,\n      vars: 11,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\", \"relative\", \"z-10\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-4\"], [\"routerLink\", \"/projects\", 1, \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mb-6\", \"lg:mb-0\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-3xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-orange-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"px-4\", \"py-2\", \"rounded-xl\", \"text-sm\", \"font-medium\", 3, \"ngClass\"], [\"class\", \"flex justify-center my-12\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 lg:grid-cols-3 gap-8\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-12\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-8\"], [1, \"lg:col-span-2\", \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-4\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h7\"], [1, \"text-lg\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [1, \"prose\", \"prose-gray\", \"dark:prose-invert\", \"max-w-none\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"leading-relaxed\"], [1, \"bg-orange-100\", \"dark:bg-orange-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-orange-600\", \"dark:text-orange-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-sm\", \"font-normal\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"ml-2\"], [\"class\", \"grid grid-cols-1 sm:grid-cols-2 gap-4\", 4, \"ngIf\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [1, \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"p-6\", \"text-white\"], [1, \"bg-white/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-lg\", \"font-semibold\"], [1, \"text-sm\", \"text-white/80\"], [1, \"p-6\", \"space-y-4\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-3\", \"bg-[#edf1f4]/50\", \"dark:bg-[#2a2a2a]/50\", \"rounded-xl\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-orange-600\", \"dark:text-orange-400\"], [1, \"text-xs\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"uppercase\", \"tracking-wider\"], [1, \"text-sm\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [1, \"bg-blue-100\", \"dark:bg-blue-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-blue-600\", \"dark:text-blue-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"], [1, \"bg-green-100\", \"dark:bg-green-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-green-600\", \"dark:text-green-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"space-y-3\"], [4, \"ngIf\"], [\"routerLink\", \"/projects\", 1, \"block\", \"w-full\", \"px-6\", \"py-3\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"rounded-xl\", \"transition-all\", \"duration-200\", \"font-medium\", \"text-center\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-4\"], [\"class\", \"group\", 4, \"ngFor\", \"ngForOf\"], [1, \"group\"], [1, \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"rounded-xl\", \"p-4\", \"border\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"hover:border-[#4f5fad]\", \"dark:hover:border-[#6d78c9]\", \"transition-all\", \"duration-200\", \"hover:shadow-md\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"flex-1\", \"min-w-0\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"p-2\", \"rounded-lg\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"flex-1\", \"min-w-0\"], [1, \"text-sm\", \"font-medium\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"truncate\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"download\", \"\", 1, \"ml-3\", \"px-3\", \"py-2\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:bg-[#4f5fad]\", \"hover:text-white\", \"dark:hover:bg-[#6d78c9]\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"text-xs\", \"font-medium\", \"group-hover:scale-105\", 3, \"href\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"text-center\", \"py-8\"], [1, \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"rounded-xl\", \"p-6\"], [1, \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"p-3\", \"rounded-lg\", \"inline-flex\", \"items-center\", \"justify-center\", \"mb-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"p-4\", \"bg-green-50\", \"dark:bg-green-900/20\", \"border\", \"border-green-200\", \"dark:border-green-800/30\", \"rounded-xl\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"font-semibold\", \"text-green-800\", \"dark:text-green-400\"], [1, \"text-xs\", \"text-green-600\", \"dark:text-green-500\"], [1, \"block\", \"w-full\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", \"text-center\", 3, \"routerLink\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"]],\n      template: function ProjectDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"nav\", 6)(7, \"a\", 7);\n          i0.ɵɵtext(8, \"Mes Projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(9, \"svg\", 8);\n          i0.ɵɵelement(10, \"path\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(11, \"span\", 10);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"div\", 14);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(17, \"svg\", 15);\n          i0.ɵɵelement(18, \"path\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(19, \"div\")(20, \"h1\", 17);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 18)(23, \"div\", 19);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(24, \"svg\", 20);\n          i0.ɵɵelement(25, \"path\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(26, \"span\", 22);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 19);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(29, \"svg\", 23);\n          i0.ɵɵelement(30, \"path\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(31, \"span\", 25);\n          i0.ɵɵtext(32);\n          i0.ɵɵpipe(33, \"date\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(34, \"div\", 26)(35, \"span\", 27);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(37, ProjectDetailComponent_div_37_Template, 4, 0, \"div\", 28);\n          i0.ɵɵtemplate(38, ProjectDetailComponent_div_38_Template, 84, 13, \"div\", 29);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.titre) || \"D\\u00E9tails du projet\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.titre) || \"Chargement...\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.groupe) || \"Tous les groupes\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(33, 8, ctx.projet == null ? null : ctx.projet.dateLimite, \"dd/MM/yyyy\" || \"Pas de date limite\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getStatusText(), \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.RouterLink, i5.DatePipe],\n      styles: [\"\\n\\n.project-container[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  background-color: #fff;\\n  border-radius: 0.5rem;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.project-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  border-bottom: 1px solid #e5e7eb;\\n  padding-bottom: 1rem;\\n}\\n\\n.project-description[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.project-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.project-meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2plY3QtZGV0YWlsLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsaURBQWlEO0FBQ2pEO0VBQ0UsZUFBZTtFQUNmLHNCQUFzQjtFQUN0QixxQkFBcUI7RUFDckIsd0NBQXdDO0FBQzFDOztBQUVBO0VBQ0UscUJBQXFCO0VBQ3JCLGdDQUFnQztFQUNoQyxvQkFBb0I7QUFDdEI7O0FBRUE7RUFDRSxxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsZUFBZTtFQUNmLFNBQVM7RUFDVCxxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFdBQVc7QUFDYiIsImZpbGUiOiJwcm9qZWN0LWRldGFpbC5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLyogU3R5bGVzIHBvdXIgbGUgY29tcG9zYW50IGRlIGTDqXRhaWwgZGUgcHJvamV0ICovXG4ucHJvamVjdC1jb250YWluZXIge1xuICBwYWRkaW5nOiAxLjVyZW07XG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmY7XG4gIGJvcmRlci1yYWRpdXM6IDAuNXJlbTtcbiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbn1cblxuLnByb2plY3QtaGVhZGVyIHtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTdlYjtcbiAgcGFkZGluZy1ib3R0b206IDFyZW07XG59XG5cbi5wcm9qZWN0LWRlc2NyaXB0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xufVxuXG4ucHJvamVjdC1tZXRhIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC13cmFwOiB3cmFwO1xuICBnYXA6IDFyZW07XG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbn1cblxuLnByb2plY3QtbWV0YS1pdGVtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAwLjVyZW07XG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcHJvamVjdHMvcHJvamVjdC1kZXRhaWwvcHJvamVjdC1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxpREFBaUQ7QUFDakQ7RUFDRSxlQUFlO0VBQ2Ysc0JBQXNCO0VBQ3RCLHFCQUFxQjtFQUNyQix3Q0FBd0M7QUFDMUM7O0FBRUE7RUFDRSxxQkFBcUI7RUFDckIsZ0NBQWdDO0VBQ2hDLG9CQUFvQjtBQUN0Qjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLGFBQWE7RUFDYixlQUFlO0VBQ2YsU0FBUztFQUNULHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsV0FBVztBQUNiO0FBQ0EsNHdDQUE0d0MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBTdHlsZXMgcG91ciBsZSBjb21wb3NhbnQgZGUgZMODwql0YWlsIGRlIHByb2pldCAqL1xuLnByb2plY3QtY29udGFpbmVyIHtcbiAgcGFkZGluZzogMS41cmVtO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XG4gIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG59XG5cbi5wcm9qZWN0LWhlYWRlciB7XG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU3ZWI7XG4gIHBhZGRpbmctYm90dG9tOiAxcmVtO1xufVxuXG4ucHJvamVjdC1kZXNjcmlwdGlvbiB7XG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbn1cblxuLnByb2plY3QtbWV0YSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtd3JhcDogd3JhcDtcbiAgZ2FwOiAxcmVtO1xuICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG59XG5cbi5wcm9qZWN0LW1ldGEtaXRlbSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC41cmVtO1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r6", "getFileName", "file_r7", "ɵɵproperty", "getFileUrl", "ɵɵsanitizeUrl", "ɵɵtemplate", "ProjectDetailComponent_div_38_div_21_div_1_Template", "ctx_r2", "projet", "fichiers", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵpureFunction1", "_c0", "ctx_r5", "projetId", "ProjectDetailComponent_div_38_div_21_Template", "ProjectDetailComponent_div_38_div_22_Template", "ProjectDetailComponent_div_38_ng_container_76_Template", "ProjectDetailComponent_div_38_ng_container_77_Template", "ctx_r1", "description", "ɵɵtextInterpolate2", "length", "ɵɵtextInterpolate", "ɵɵpipeBind2", "dateLimite", "getRemainingDays", "groupe", "hasSubmitted", "ProjectDetailComponent", "constructor", "route", "router", "projetService", "rendusService", "authService", "isLoading", "ngOnInit", "snapshot", "paramMap", "get", "loadProjetDetails", "checkRenduStatus", "getProjetById", "subscribe", "next", "error", "err", "console", "navigate", "etudiantId", "getCurrentUserId", "checkRenduExists", "exists", "log", "filePath", "fileName", "includes", "parts", "split", "getProjectStatus", "now", "Date", "deadline", "oneWeekFromNow", "setDate", "getDate", "getStatusClass", "status", "getStatusText", "diffTime", "getTime", "diffDays", "Math", "ceil", "max", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProjetService", "i3", "RendusService", "i4", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "ProjectDetailComponent_Template", "rf", "ctx", "ProjectDetailComponent_div_37_Template", "ProjectDetailComponent_div_38_Template", "titre"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\projects\\project-detail\\project-detail.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\projects\\project-detail\\project-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ProjetService } from '@app/services/projets.service';\nimport { RendusService } from 'src/app/services/rendus.service';\nimport { AuthuserService } from 'src/app/services/authuser.service';\n\n// Composant pour afficher les détails d'un projet\n@Component({\n  selector: 'app-project-detail',\n  templateUrl: './project-detail.component.html',\n  styleUrls: ['./project-detail.component.css']\n})\nexport class ProjectDetailComponent implements OnInit {\n  projetId: string = '';\n  projet: any;\n  rendu: any;\n  isLoading = true;\n  hasSubmitted = false;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private projetService: ProjetService,\n    private rendusService: RendusService,\n    private authService: AuthuserService\n  ) {}\n\n  ngOnInit(): void {\n    this.projetId = this.route.snapshot.paramMap.get('id') || '';\n    this.loadProjetDetails();\n    this.checkRenduStatus();\n  }\n\n  loadProjetDetails(): void {\n    this.isLoading = true;\n    this.projetService.getProjetById(this.projetId).subscribe({\n      next: (projet: any) => {\n        this.projet = projet;\n        this.isLoading = false;\n      },\n      error: (err) => {\n        console.error('Erreur lors du chargement du projet', err);\n        this.isLoading = false;\n        this.router.navigate(['/projects']);\n      },\n    });\n  }\n\n  checkRenduStatus(): void {\n    const etudiantId = this.authService.getCurrentUserId();\n    if (etudiantId) {\n      this.rendusService.checkRenduExists(this.projetId, etudiantId).subscribe({\n        next: (exists: boolean) => {\n          console.log(exists)\n          this.hasSubmitted = exists;\n        },\n        error: (err: any) => {\n          console.error('Erreur lors de la vérification du rendu', err);\n        },\n      });\n    }\n  }\n\n  getFileUrl(filePath: string): string {\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n\n    // Utiliser l'endpoint API spécifique pour le téléchargement\n    return `http://localhost:3000/api/projets/download/${fileName}`;\n  }\n\n  getFileName(filePath: string): string {\n    if (!filePath) return 'Fichier';\n\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n\n    return filePath;\n  }\n\n  getProjectStatus(): 'completed' | 'urgent' | 'expired' | 'active' {\n    if (this.hasSubmitted) return 'completed';\n\n    if (!this.projet?.dateLimite) return 'active';\n\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n\n    if (deadline < now) return 'expired';\n\n    const oneWeekFromNow = new Date();\n    oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);\n\n    if (deadline <= oneWeekFromNow) return 'urgent';\n\n    return 'active';\n  }\n\n  getStatusClass(): string {\n    const status = this.getProjectStatus();\n\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 border border-green-200 dark:border-green-800/30';\n      case 'urgent':\n        return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-400 border border-orange-200 dark:border-orange-800/30';\n      case 'expired':\n        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 border border-red-200 dark:border-red-800/30';\n      default:\n        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 border border-blue-200 dark:border-blue-800/30';\n    }\n  }\n\n  getStatusText(): string {\n    const status = this.getProjectStatus();\n\n    switch (status) {\n      case 'completed':\n        return 'Projet soumis';\n      case 'urgent':\n        return 'Urgent';\n      case 'expired':\n        return 'Expiré';\n      default:\n        return 'Actif';\n    }\n  }\n\n  getRemainingDays(): number {\n    if (!this.projet?.dateLimite) return 0;\n\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n    const diffTime = deadline.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    return Math.max(0, diffDays);\n  }\n}\n", "<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary relative\">\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"></div>\n    <div class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"></div>\n  </div>\n\n  <div class=\"container mx-auto px-4 py-8 relative z-10\">\n\n    <!-- Header moderne avec breadcrumb -->\n    <div class=\"mb-8\">\n      <nav class=\"flex items-center space-x-2 text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-4\">\n        <a routerLink=\"/projects\" class=\"hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\">Mes Projets</a>\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n        </svg>\n        <span class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ projet?.titre || 'Détails du projet' }}</span>\n      </nav>\n\n      <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\n        <div class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n          <div class=\"flex items-center space-x-4 mb-6 lg:mb-0\">\n            <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] flex items-center justify-center shadow-lg\">\n              <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\n              </svg>\n            </div>\n            <div>\n              <h1 class=\"text-3xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\">\n                {{ projet?.titre || 'Chargement...' }}\n              </h1>\n              <div class=\"flex items-center space-x-4 mt-2\">\n                <div class=\"flex items-center space-x-1\">\n                  <svg class=\"w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                  </svg>\n                  <span class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">{{ projet?.groupe || 'Tous les groupes' }}</span>\n                </div>\n                <div class=\"flex items-center space-x-1\">\n                  <svg class=\"w-4 h-4 text-orange-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                  </svg>\n                  <span class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">{{ projet?.dateLimite | date : \"dd/MM/yyyy\" || 'Pas de date limite' }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Badge de statut -->\n          <div class=\"flex items-center space-x-3\">\n            <span [ngClass]=\"getStatusClass()\" class=\"px-4 py-2 rounded-xl text-sm font-medium\">\n              {{ getStatusText() }}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading Indicator -->\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-12\">\n      <div class=\"relative\">\n        <div class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"></div>\n        <!-- Glow effect -->\n        <div class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"></div>\n      </div>\n    </div>\n\n    <!-- Contenu principal -->\n    <div *ngIf=\"!isLoading\" class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n\n      <!-- Carte principale du projet -->\n      <div class=\"lg:col-span-2 space-y-6\">\n\n        <!-- Description du projet -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\n          <div class=\"flex items-center space-x-3 mb-4\">\n            <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-2 rounded-lg\">\n              <svg class=\"w-5 h-5 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h7\"></path>\n              </svg>\n            </div>\n            <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Description du projet</h3>\n          </div>\n          <div class=\"prose prose-gray dark:prose-invert max-w-none\">\n            <p class=\"text-[#6d6870] dark:text-[#a0a0a0] leading-relaxed\">\n              {{ projet?.description || 'Aucune description fournie pour ce projet.' }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Fichiers du projet -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\n          <div class=\"flex items-center space-x-3 mb-4\">\n            <div class=\"bg-orange-100 dark:bg-orange-900/30 p-2 rounded-lg\">\n              <svg class=\"w-5 h-5 text-orange-600 dark:text-orange-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n              </svg>\n            </div>\n            <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">\n              Ressources du projet\n              <span class=\"text-sm font-normal text-[#6d6870] dark:text-[#a0a0a0] ml-2\">\n                ({{ projet?.fichiers?.length || 0 }} fichier{{ (projet?.fichiers?.length || 0) > 1 ? 's' : '' }})\n              </span>\n            </h3>\n          </div>\n\n          <div *ngIf=\"projet?.fichiers?.length > 0\" class=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n            <div *ngFor=\"let file of projet.fichiers\" class=\"group\">\n              <div class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-xl p-4 border border-[#edf1f4] dark:border-[#2a2a2a] hover:border-[#4f5fad] dark:hover:border-[#6d78c9] transition-all duration-200 hover:shadow-md\">\n                <div class=\"flex items-center justify-between\">\n                  <div class=\"flex items-center space-x-3 flex-1 min-w-0\">\n                    <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-2 rounded-lg group-hover:scale-110 transition-transform\">\n                      <svg class=\"w-5 h-5 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"></path>\n                      </svg>\n                    </div>\n                    <div class=\"flex-1 min-w-0\">\n                      <p class=\"text-sm font-medium text-[#3d4a85] dark:text-[#6d78c9] truncate\">\n                        {{ getFileName(file) }}\n                      </p>\n                      <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">Document de projet</p>\n                    </div>\n                  </div>\n                  <a [href]=\"getFileUrl(file)\" download\n                     class=\"ml-3 px-3 py-2 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] hover:bg-[#4f5fad] hover:text-white dark:hover:bg-[#6d78c9] rounded-lg transition-all duration-200 text-xs font-medium group-hover:scale-105\">\n                    <div class=\"flex items-center space-x-1\">\n                      <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"></path>\n                      </svg>\n                      <span>Télécharger</span>\n                    </div>\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div *ngIf=\"!projet?.fichiers || projet.fichiers.length === 0\" class=\"text-center py-8\">\n            <div class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-xl p-6\">\n              <div class=\"bg-[#edf1f4] dark:bg-[#2a2a2a] p-3 rounded-lg inline-flex items-center justify-center mb-3\">\n                <svg class=\"w-6 h-6 text-[#6d6870] dark:text-[#a0a0a0]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                </svg>\n              </div>\n              <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">Aucun fichier joint à ce projet</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Sidebar avec informations et actions -->\n      <div class=\"space-y-6\">\n\n        <!-- Informations du projet -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\">\n          <div class=\"bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] p-6 text-white\">\n            <div class=\"flex items-center space-x-3\">\n              <div class=\"bg-white/20 p-2 rounded-lg\">\n                <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                </svg>\n              </div>\n              <div>\n                <h3 class=\"text-lg font-semibold\">Informations</h3>\n                <p class=\"text-sm text-white/80\">Détails du projet</p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"p-6 space-y-4\">\n            <!-- Date limite -->\n            <div class=\"flex items-center justify-between p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"bg-orange-100 dark:bg-orange-900/30 p-2 rounded-lg\">\n                  <svg class=\"w-4 h-4 text-orange-600 dark:text-orange-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                  </svg>\n                </div>\n                <div>\n                  <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Date limite</p>\n                  <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">{{ projet?.dateLimite | date : \"dd/MM/yyyy\" || 'Non définie' }}</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- Temps restant -->\n            <div class=\"flex items-center justify-between p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg\">\n                  <svg class=\"w-4 h-4 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"></path>\n                  </svg>\n                </div>\n                <div>\n                  <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Temps restant</p>\n                  <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">{{ getRemainingDays() }} jours</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- Groupe -->\n            <div class=\"flex items-center justify-between p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-2 rounded-lg\">\n                  <svg class=\"w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                  </svg>\n                </div>\n                <div>\n                  <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Groupe cible</p>\n                  <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">{{ projet?.groupe || 'Tous les groupes' }}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Actions -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\n          <div class=\"flex items-center space-x-3 mb-4\">\n            <div class=\"bg-green-100 dark:bg-green-900/30 p-2 rounded-lg\">\n              <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n              </svg>\n            </div>\n            <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Actions</h3>\n          </div>\n\n          <div class=\"space-y-3\">\n            <ng-container *ngIf=\"hasSubmitted\">\n              <div class=\"p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30 rounded-xl\">\n                <div class=\"flex items-center space-x-3\">\n                  <div class=\"bg-green-100 dark:bg-green-900/30 p-2 rounded-lg\">\n                    <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <p class=\"text-sm font-semibold text-green-800 dark:text-green-400\">Projet soumis</p>\n                    <p class=\"text-xs text-green-600 dark:text-green-500\">Votre rendu a été enregistré avec succès</p>\n                  </div>\n                </div>\n              </div>\n            </ng-container>\n\n            <ng-container *ngIf=\"!hasSubmitted\">\n              <a [routerLink]=\"['/projects/submit', projetId]\"\n                 class=\"block w-full px-6 py-3 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium text-center\">\n                <div class=\"flex items-center justify-center space-x-2\">\n                  <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                  </svg>\n                  <span>Soumettre mon projet</span>\n                </div>\n              </a>\n            </ng-container>\n\n            <!-- Bouton retour -->\n            <a routerLink=\"/projects\"\n               class=\"block w-full px-6 py-3 bg-[#edf1f4] dark:bg-[#2a2a2a] text-[#3d4a85] dark:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded-xl transition-all duration-200 font-medium text-center\">\n              <div class=\"flex items-center justify-center space-x-2\">\n                <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"></path>\n                </svg>\n                <span>Retour aux projets</span>\n              </div>\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;IC2DIA,EAAA,CAAAC,cAAA,cAAyD;IAErDD,EAAA,CAAAE,SAAA,cAAwJ;IAG1JF,EAAA,CAAAG,YAAA,EAAM;;;;;IA2CAH,EAAA,CAAAC,cAAA,cAAwD;IAK9CD,EAAA,CAAAI,cAAA,EAA8G;IAA9GJ,EAAA,CAAAC,cAAA,cAA8G;IAC5GD,EAAA,CAAAE,SAAA,eAA4L;IAC9LF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAA4B;IAA5BL,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAM,MAAA,0BAAkB;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAGhFH,EAAA,CAAAC,cAAA,aAC+O;IAE3OD,EAAA,CAAAI,cAAA,EAA2E;IAA3EJ,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAE,SAAA,gBAAgJ;IAClJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,6BAAW;IAAAN,EAAA,CAAAG,YAAA,EAAO;;;;;IAXtBH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAC,MAAA,CAAAC,WAAA,CAAAC,OAAA,OACF;IAIDX,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAY,UAAA,SAAAH,MAAA,CAAAI,UAAA,CAAAF,OAAA,GAAAX,EAAA,CAAAc,aAAA,CAAyB;;;;;IAjBpCd,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAAe,UAAA,IAAAC,mDAAA,mBA2BM;IACRhB,EAAA,CAAAG,YAAA,EAAM;;;;IA5BkBH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAY,UAAA,YAAAK,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAkB;;;;;IA8B1CnB,EAAA,CAAAC,cAAA,cAAwF;IAGlFD,EAAA,CAAAI,cAAA,EAA8G;IAA9GJ,EAAA,CAAAC,cAAA,cAA8G;IAC5GD,EAAA,CAAAE,SAAA,eAAsM;IACxMF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAsD;IAAtDL,EAAA,CAAAC,cAAA,YAAsD;IAAAD,EAAA,CAAAM,MAAA,2CAA+B;IAAAN,EAAA,CAAAG,YAAA,EAAI;;;;;IAsF3FH,EAAA,CAAAoB,uBAAA,GAAmC;IACjCpB,EAAA,CAAAC,cAAA,cAA8G;IAGxGD,EAAA,CAAAI,cAAA,EAA8G;IAA9GJ,EAAA,CAAAC,cAAA,cAA8G;IAC5GD,EAAA,CAAAE,SAAA,eAA+H;IACjIF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAK;IAALL,EAAA,CAAAC,cAAA,UAAK;IACiED,EAAA,CAAAM,MAAA,oBAAa;IAAAN,EAAA,CAAAG,YAAA,EAAI;IACrFH,EAAA,CAAAC,cAAA,YAAsD;IAAAD,EAAA,CAAAM,MAAA,oEAAwC;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAI1GH,EAAA,CAAAqB,qBAAA,EAAe;;;;;;;;IAEfrB,EAAA,CAAAoB,uBAAA,GAAoC;IAClCpB,EAAA,CAAAC,cAAA,YAC+N;IAE3ND,EAAA,CAAAI,cAAA,EAA2E;IAA3EJ,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAE,SAAA,eAAuK;IACzKF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,2BAAoB;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAGvCH,EAAA,CAAAqB,qBAAA,EAAe;;;;IATVrB,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAAsB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,QAAA,EAA6C;;;;;IAnL1DzB,EAAA,CAAAC,cAAA,cAAsE;IAS5DD,EAAA,CAAAI,cAAA,EAA8G;IAA9GJ,EAAA,CAAAC,cAAA,cAA8G;IAC5GD,EAAA,CAAAE,SAAA,eAAwG;IAC1GF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAqE;IAArEL,EAAA,CAAAC,cAAA,aAAqE;IAAAD,EAAA,CAAAM,MAAA,4BAAqB;IAAAN,EAAA,CAAAG,YAAA,EAAK;IAEjGH,EAAA,CAAAC,cAAA,cAA2D;IAEvDD,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAKRH,EAAA,CAAAC,cAAA,eAA0I;IAGpID,EAAA,CAAAI,cAAA,EAAgH;IAAhHJ,EAAA,CAAAC,cAAA,eAAgH;IAC9GD,EAAA,CAAAE,SAAA,gBAAsM;IACxMF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAqE;IAArEL,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAM,MAAA,8BACA;IAAAN,EAAA,CAAAC,cAAA,gBAA0E;IACxED,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAIXH,EAAA,CAAAe,UAAA,KAAAW,6CAAA,kBA6BM;IAEN1B,EAAA,CAAAe,UAAA,KAAAY,6CAAA,kBASM;IACR3B,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAAuB;IAObD,EAAA,CAAAI,cAAA,EAA2E;IAA3EJ,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAE,SAAA,gBAA2I;IAC7IF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAK;IAALL,EAAA,CAAAC,cAAA,WAAK;IAC+BD,EAAA,CAAAM,MAAA,oBAAY;IAAAN,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAM,MAAA,8BAAiB;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAK5DH,EAAA,CAAAC,cAAA,eAA2B;IAKnBD,EAAA,CAAAI,cAAA,EAAgH;IAAhHJ,EAAA,CAAAC,cAAA,eAAgH;IAC9GD,EAAA,CAAAE,SAAA,gBAA6H;IAC/HF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAK;IAALL,EAAA,CAAAC,cAAA,WAAK;IACwFD,EAAA,CAAAM,MAAA,mBAAW;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAC1GH,EAAA,CAAAC,cAAA,aAAoE;IAAAD,EAAA,CAAAM,MAAA,IAA+D;;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAM7IH,EAAA,CAAAC,cAAA,eAAmG;IAG7FD,EAAA,CAAAI,cAAA,EAA4G;IAA5GJ,EAAA,CAAAC,cAAA,eAA4G;IAC1GD,EAAA,CAAAE,SAAA,gBAAgH;IAClHF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAK;IAALL,EAAA,CAAAC,cAAA,WAAK;IACwFD,EAAA,CAAAM,MAAA,qBAAa;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAC5GH,EAAA,CAAAC,cAAA,aAAoE;IAAAD,EAAA,CAAAM,MAAA,IAA8B;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAM5GH,EAAA,CAAAC,cAAA,eAAmG;IAG7FD,EAAA,CAAAI,cAAA,EAA8G;IAA9GJ,EAAA,CAAAC,cAAA,eAA8G;IAC5GD,EAAA,CAAAE,SAAA,gBAAwV;IAC1VF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAK;IAALL,EAAA,CAAAC,cAAA,WAAK;IACwFD,EAAA,CAAAM,MAAA,oBAAY;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAC3GH,EAAA,CAAAC,cAAA,aAAoE;IAAAD,EAAA,CAAAM,MAAA,IAA0C;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAQ5HH,EAAA,CAAAC,cAAA,eAA0I;IAGpID,EAAA,CAAAI,cAAA,EAA8G;IAA9GJ,EAAA,CAAAC,cAAA,eAA8G;IAC5GD,EAAA,CAAAE,SAAA,gBAAqjB;IAEvjBF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAqE;IAArEL,EAAA,CAAAC,cAAA,cAAqE;IAAAD,EAAA,CAAAM,MAAA,eAAO;IAAAN,EAAA,CAAAG,YAAA,EAAK;IAGnFH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAe,UAAA,KAAAa,sDAAA,4BAce;IAEf5B,EAAA,CAAAe,UAAA,KAAAc,sDAAA,2BAUe;IAGf7B,EAAA,CAAAC,cAAA,aACoN;IAEhND,EAAA,CAAAI,cAAA,EAA2E;IAA3EJ,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAE,SAAA,gBAA6G;IAC/GF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,0BAAkB;IAAAN,EAAA,CAAAG,YAAA,EAAO;;;;IApLjCH,EAAA,CAAAO,SAAA,IACF;IADEP,EAAA,CAAAQ,kBAAA,OAAAsB,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAa,WAAA,uDACF;IAeI/B,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAgC,kBAAA,QAAAF,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAC,QAAA,kBAAAW,MAAA,CAAAZ,MAAA,CAAAC,QAAA,CAAAc,MAAA,sBAAAH,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAC,QAAA,kBAAAW,MAAA,CAAAZ,MAAA,CAAAC,QAAA,CAAAc,MAAA,6BACF;IAIEjC,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAY,UAAA,UAAAkB,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAC,QAAA,kBAAAW,MAAA,CAAAZ,MAAA,CAAAC,QAAA,CAAAc,MAAA,MAAkC;IA+BlCjC,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAY,UAAA,WAAAkB,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAC,QAAA,KAAAW,MAAA,CAAAZ,MAAA,CAAAC,QAAA,CAAAc,MAAA,OAAuD;IA2CejC,EAAA,CAAAO,SAAA,IAA+D;IAA/DP,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,SAAAL,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAkB,UAAA,sCAA+D;IAe/DpC,EAAA,CAAAO,SAAA,IAA8B;IAA9BP,EAAA,CAAAQ,kBAAA,KAAAsB,MAAA,CAAAO,gBAAA,aAA8B;IAe9BrC,EAAA,CAAAO,SAAA,IAA0C;IAA1CP,EAAA,CAAAkC,iBAAA,EAAAJ,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAoB,MAAA,wBAA0C;IAoBrGtC,EAAA,CAAAO,SAAA,IAAkB;IAAlBP,EAAA,CAAAY,UAAA,SAAAkB,MAAA,CAAAS,YAAA,CAAkB;IAgBlBvC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAY,UAAA,UAAAkB,MAAA,CAAAS,YAAA,CAAmB;;;ADhP9C;AAMA,OAAM,MAAOC,sBAAsB;EAOjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAA4B;IAJ5B,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAXrB,KAAArB,QAAQ,GAAW,EAAE;IAGrB,KAAAsB,SAAS,GAAG,IAAI;IAChB,KAAAR,YAAY,GAAG,KAAK;EAQjB;EAEHS,QAAQA,CAAA;IACN,IAAI,CAACvB,QAAQ,GAAG,IAAI,CAACiB,KAAK,CAACO,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC5D,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAD,iBAAiBA,CAAA;IACf,IAAI,CAACL,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,aAAa,CAACU,aAAa,CAAC,IAAI,CAAC7B,QAAQ,CAAC,CAAC8B,SAAS,CAAC;MACxDC,IAAI,EAAGtC,MAAW,IAAI;QACpB,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAAC6B,SAAS,GAAG,KAAK;MACxB,CAAC;MACDU,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,qCAAqC,EAAEC,GAAG,CAAC;QACzD,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAI,CAACJ,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC;KACD,CAAC;EACJ;EAEAP,gBAAgBA,CAAA;IACd,MAAMQ,UAAU,GAAG,IAAI,CAACf,WAAW,CAACgB,gBAAgB,EAAE;IACtD,IAAID,UAAU,EAAE;MACd,IAAI,CAAChB,aAAa,CAACkB,gBAAgB,CAAC,IAAI,CAACtC,QAAQ,EAAEoC,UAAU,CAAC,CAACN,SAAS,CAAC;QACvEC,IAAI,EAAGQ,MAAe,IAAI;UACxBL,OAAO,CAACM,GAAG,CAACD,MAAM,CAAC;UACnB,IAAI,CAACzB,YAAY,GAAGyB,MAAM;QAC5B,CAAC;QACDP,KAAK,EAAGC,GAAQ,IAAI;UAClBC,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;QAC/D;OACD,CAAC;;EAEN;EAEA7C,UAAUA,CAACqD,QAAgB;IACzB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAACpC,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,8CAA8CkC,QAAQ,EAAE;EACjE;EAEAzD,WAAWA,CAACwD,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACpC,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOiC,QAAQ;EACjB;EAEAK,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAChC,YAAY,EAAE,OAAO,WAAW;IAEzC,IAAI,CAAC,IAAI,CAACrB,MAAM,EAAEkB,UAAU,EAAE,OAAO,QAAQ;IAE7C,MAAMoC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAAC,IAAI,CAACvD,MAAM,CAACkB,UAAU,CAAC;IAEjD,IAAIsC,QAAQ,GAAGF,GAAG,EAAE,OAAO,SAAS;IAEpC,MAAMG,cAAc,GAAG,IAAIF,IAAI,EAAE;IACjCE,cAAc,CAACC,OAAO,CAACD,cAAc,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAEpD,IAAIH,QAAQ,IAAIC,cAAc,EAAE,OAAO,QAAQ;IAE/C,OAAO,QAAQ;EACjB;EAEAG,cAAcA,CAAA;IACZ,MAAMC,MAAM,GAAG,IAAI,CAACR,gBAAgB,EAAE;IAEtC,QAAQQ,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,uHAAuH;MAChI,KAAK,QAAQ;QACX,OAAO,6HAA6H;MACtI,KAAK,SAAS;QACZ,OAAO,2GAA2G;MACpH;QACE,OAAO,iHAAiH;;EAE9H;EAEAC,aAAaA,CAAA;IACX,MAAMD,MAAM,GAAG,IAAI,CAACR,gBAAgB,EAAE;IAEtC,QAAQQ,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,eAAe;MACxB,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB,KAAK,SAAS;QACZ,OAAO,QAAQ;MACjB;QACE,OAAO,OAAO;;EAEpB;EAEA1C,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACnB,MAAM,EAAEkB,UAAU,EAAE,OAAO,CAAC;IAEtC,MAAMoC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAAC,IAAI,CAACvD,MAAM,CAACkB,UAAU,CAAC;IACjD,MAAM6C,QAAQ,GAAGP,QAAQ,CAACQ,OAAO,EAAE,GAAGV,GAAG,CAACU,OAAO,EAAE;IACnD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,OAAOG,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC;EAC9B;;;uBAtIW3C,sBAAsB,EAAAxC,EAAA,CAAAuF,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzF,EAAA,CAAAuF,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA1F,EAAA,CAAAuF,iBAAA,CAAAI,EAAA,CAAAC,aAAA,GAAA5F,EAAA,CAAAuF,iBAAA,CAAAM,EAAA,CAAAC,aAAA,GAAA9F,EAAA,CAAAuF,iBAAA,CAAAQ,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAtBxD,sBAAsB;MAAAyD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZnCvG,EAAA,CAAAC,cAAA,aAA0K;UAGtKD,EAAA,CAAAE,SAAA,aAA6K;UAE/KF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,aAAuD;UAKkDD,EAAA,CAAAM,MAAA,kBAAW;UAAAN,EAAA,CAAAG,YAAA,EAAI;UAClHH,EAAA,CAAAI,cAAA,EAA2E;UAA3EJ,EAAA,CAAAC,cAAA,aAA2E;UACzED,EAAA,CAAAE,SAAA,eAA8F;UAChGF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAK,eAAA,EAA6D;UAA7DL,EAAA,CAAAC,cAAA,gBAA6D;UAAAD,EAAA,CAAAM,MAAA,IAA0C;UAAAN,EAAA,CAAAG,YAAA,EAAO;UAGhHH,EAAA,CAAAC,cAAA,eAA0I;UAIlID,EAAA,CAAAI,cAAA,EAAsF;UAAtFJ,EAAA,CAAAC,cAAA,eAAsF;UACpFD,EAAA,CAAAE,SAAA,gBAA4J;UAC9JF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAK,eAAA,EAAK;UAALL,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAA8C;UAE1CD,EAAA,CAAAI,cAAA,EAA8G;UAA9GJ,EAAA,CAAAC,cAAA,eAA8G;UAC5GD,EAAA,CAAAE,SAAA,gBAAwV;UAC1VF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAK,eAAA,EAAqE;UAArEL,EAAA,CAAAC,cAAA,gBAAqE;UAAAD,EAAA,CAAAM,MAAA,IAA0C;UAAAN,EAAA,CAAAG,YAAA,EAAO;UAExHH,EAAA,CAAAC,cAAA,eAAyC;UACvCD,EAAA,CAAAI,cAAA,EAA2F;UAA3FJ,EAAA,CAAAC,cAAA,eAA2F;UACzFD,EAAA,CAAAE,SAAA,gBAA6H;UAC/HF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAK,eAAA,EAAyD;UAAzDL,EAAA,CAAAC,cAAA,gBAAyD;UAAAD,EAAA,CAAAM,MAAA,IAAsE;;UAAAN,EAAA,CAAAG,YAAA,EAAO;UAO9IH,EAAA,CAAAC,cAAA,eAAyC;UAErCD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAG,YAAA,EAAO;UAOfH,EAAA,CAAAe,UAAA,KAAA0F,sCAAA,kBAMM;UAGNzG,EAAA,CAAAe,UAAA,KAAA2F,sCAAA,oBA2MM;UACR1G,EAAA,CAAAG,YAAA,EAAM;;;UAhQ6DH,EAAA,CAAAO,SAAA,IAA0C;UAA1CP,EAAA,CAAAkC,iBAAA,EAAAsE,GAAA,CAAAtF,MAAA,kBAAAsF,GAAA,CAAAtF,MAAA,CAAAyF,KAAA,8BAA0C;UAa/F3G,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAQ,kBAAA,OAAAgG,GAAA,CAAAtF,MAAA,kBAAAsF,GAAA,CAAAtF,MAAA,CAAAyF,KAAA,0BACF;UAMyE3G,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAkC,iBAAA,EAAAsE,GAAA,CAAAtF,MAAA,kBAAAsF,GAAA,CAAAtF,MAAA,CAAAoB,MAAA,wBAA0C;UAMtDtC,EAAA,CAAAO,SAAA,GAAsE;UAAtEP,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,QAAAqE,GAAA,CAAAtF,MAAA,kBAAAsF,GAAA,CAAAtF,MAAA,CAAAkB,UAAA,wCAAsE;UAQ/HpC,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAY,UAAA,YAAA4F,GAAA,CAAA1B,cAAA,GAA4B;UAChC9E,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAQ,kBAAA,MAAAgG,GAAA,CAAAxB,aAAA,QACF;UAOFhF,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAY,UAAA,SAAA4F,GAAA,CAAAzD,SAAA,CAAe;UASf/C,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAY,UAAA,UAAA4F,GAAA,CAAAzD,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}