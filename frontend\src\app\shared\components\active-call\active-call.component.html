<!-- Interface d'appel actif -->
<div
  *ngIf="isVisible && activeCall"
  [ngClass]="{
    'fixed top-4 right-4 w-80 h-96': !isMinimized,
    'fixed top-4 right-4 w-64 h-20': isMinimized
  }"
  class="bg-gray-900 text-white rounded-2xl shadow-2xl z-50 overflow-hidden transition-all duration-300"
>
  <!-- En-tête de l'appel -->
  <div class="bg-gray-800 p-4 flex items-center justify-between">
    <div class="flex items-center space-x-3">
      <div class="relative">
        <img
          [src]="getOtherParticipant()?.image || 'assets/images/default-avatar.png'"
          [alt]="getOtherParticipant()?.username"
          class="w-10 h-10 rounded-full object-cover"
        />
        <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-800"></div>
      </div>
      
      <div *ngIf="!isMinimized">
        <h3 class="font-semibold text-sm">{{ getOtherParticipant()?.username }}</h3>
        <p class="text-xs text-gray-400">{{ getCallTypeText() }} • {{ formatCallDuration() }}</p>
      </div>
      
      <div *ngIf="isMinimized" class="flex items-center space-x-2">
        <span class="text-sm font-medium">{{ getOtherParticipant()?.username }}</span>
        <span class="text-xs text-gray-400">{{ formatCallDuration() }}</span>
      </div>
    </div>

    <div class="flex items-center space-x-2">
      <!-- Bouton minimiser/maximiser -->
      <button
        (click)="toggleMinimize()"
        class="p-2 hover:bg-gray-700 rounded-lg transition-colors"
        [title]="isMinimized ? 'Agrandir' : 'Réduire'"
      >
        <i [class]="isMinimized ? 'fas fa-expand' : 'fas fa-compress'" class="text-sm"></i>
      </button>
    </div>
  </div>

  <!-- Zone vidéo (si appel vidéo et non minimisé) -->
  <div
    *ngIf="activeCall.type === 'VIDEO' && !isMinimized"
    class="relative h-48 bg-gray-800"
  >
    <!-- Vidéo distante -->
    <div class="w-full h-full bg-gray-700 flex items-center justify-center">
      <div class="text-center">
        <i class="fas fa-user text-4xl text-gray-500 mb-2"></i>
        <p class="text-sm text-gray-400">Vidéo en attente...</p>
      </div>
    </div>

    <!-- Vidéo locale (picture-in-picture) -->
    <div class="absolute top-2 right-2 w-20 h-16 bg-gray-600 rounded-lg overflow-hidden">
      <div class="w-full h-full bg-gray-600 flex items-center justify-center">
        <i class="fas fa-user text-gray-400"></i>
      </div>
    </div>

    <!-- Indicateur de statut vidéo -->
    <div
      *ngIf="!isVideoEnabled"
      class="absolute inset-0 bg-black/50 flex items-center justify-center"
    >
      <div class="text-center">
        <i class="fas fa-video-slash text-2xl mb-2"></i>
        <p class="text-sm">Vidéo désactivée</p>
      </div>
    </div>
  </div>

  <!-- Zone audio (si appel vocal et non minimisé) -->
  <div
    *ngIf="activeCall.type === 'AUDIO' && !isMinimized"
    class="h-48 bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center"
  >
    <div class="text-center">
      <div class="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto">
        <img
          [src]="getOtherParticipant()?.image || 'assets/images/default-avatar.png'"
          [alt]="getOtherParticipant()?.username"
          class="w-20 h-20 rounded-full object-cover"
        />
      </div>
      <h3 class="text-lg font-semibold">{{ getOtherParticipant()?.username }}</h3>
      <p class="text-sm opacity-80">{{ formatCallDuration() }}</p>
    </div>
  </div>

  <!-- Contrôles d'appel -->
  <div *ngIf="!isMinimized" class="p-4 bg-gray-800">
    <div class="flex justify-center space-x-4">
      <!-- Bouton Mute -->
      <button
        (click)="toggleMute()"
        [class]="isMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'"
        class="w-12 h-12 rounded-full flex items-center justify-center transition-colors"
        [title]="isMuted ? 'Activer le micro' : 'Couper le micro'"
      >
        <i [class]="isMuted ? 'fas fa-microphone-slash' : 'fas fa-microphone'"></i>
      </button>

      <!-- Bouton Vidéo (si appel vidéo) -->
      <button
        *ngIf="activeCall.type === 'VIDEO'"
        (click)="toggleVideo()"
        [class]="!isVideoEnabled ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'"
        class="w-12 h-12 rounded-full flex items-center justify-center transition-colors"
        [title]="isVideoEnabled ? 'Désactiver la vidéo' : 'Activer la vidéo'"
      >
        <i [class]="isVideoEnabled ? 'fas fa-video' : 'fas fa-video-slash'"></i>
      </button>

      <!-- Bouton Raccrocher -->
      <button
        (click)="endCall()"
        class="w-12 h-12 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center transition-colors"
        title="Raccrocher"
      >
        <i class="fas fa-phone-slash"></i>
      </button>
    </div>
  </div>

  <!-- Contrôles minimisés -->
  <div *ngIf="isMinimized" class="absolute right-2 top-1/2 transform -translate-y-1/2">
    <button
      (click)="endCall()"
      class="w-8 h-8 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center transition-colors"
      title="Raccrocher"
    >
      <i class="fas fa-phone-slash text-xs"></i>
    </button>
  </div>
</div>
