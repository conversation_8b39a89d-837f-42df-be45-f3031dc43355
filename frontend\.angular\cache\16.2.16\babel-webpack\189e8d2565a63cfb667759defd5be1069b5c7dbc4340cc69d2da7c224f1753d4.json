{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { CallType } from '../models/message.model';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // État des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // WebRTC pour l'audio/vidéo réel\n    this.localStream = null;\n    this.remoteStream = null;\n    this.peerConnection = null;\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n    this.preloadSounds();\n    this.initializeSubscriptions();\n  }\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  initializeSubscriptions() {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n    }, 2000);\n  }\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n  }\n  /**\n   * Charge un fichier audio\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      console.error(`Error loading sound ${name}:`, error);\n    }\n  }\n  /**\n   * Joue un son\n   */\n  play(name, loop = false) {\n    if (this.muted) return;\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n      sound.loop = loop;\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(console.error);\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      console.error(`Error playing sound ${name}:`, error);\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      console.error(`Error stopping sound ${name}:`, error);\n    }\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    console.log('🔔 [CallService] Setting up incoming call subscription...');\n    this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION\n    }).subscribe({\n      next: ({\n        data\n      }) => {\n        if (data?.incomingCall) {\n          console.log('📞 [CallService] Incoming call received:', {\n            callId: data.incomingCall.id,\n            callType: data.incomingCall.type,\n            caller: data.incomingCall.caller?.username,\n            conversationId: data.incomingCall.conversationId\n          });\n          this.handleIncomingCall(data.incomingCall);\n        }\n      },\n      error: error => {\n        console.error('❌ [CallService] Error in incoming call subscription:', error);\n        // Réessayer après 5 secondes en cas d'erreur\n        setTimeout(() => {\n          console.log('🔄 [CallService] Retrying incoming call subscription...');\n          this.subscribeToIncomingCalls();\n        }, 5000);\n      }\n    });\n  }\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  reinitializeSubscription() {\n    console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId\n    });\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n    console.log('🔊 [CallService] Ringtone started, call notification sent to UI');\n  }\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    // Pour l'instant, utiliser une offre WebRTC factice\n    const offer = JSON.stringify({\n      type: 'offer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    const variables = {\n      recipientId,\n      callType: callType,\n      callId,\n      offer,\n      conversationId\n    };\n    console.log('📤 [CallService] Sending initiate call mutation:', variables);\n    return this.apollo.mutate({\n      mutation: INITIATE_CALL_MUTATION,\n      variables\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call initiated successfully:', result);\n      if (!result.data?.initiateCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.initiateCall;\n      console.log('📞 [CallService] Call details:', {\n        id: call.id,\n        type: call.type,\n        status: call.status,\n        caller: call.caller?.username,\n        recipient: call.recipient?.username\n      });\n      // Mettre à jour l'état local\n      this.activeCall.next(call);\n      return call;\n    }), catchError(error => {\n      console.error('❌ [CallService] initiateCall error:', error);\n      this.logger.error('Error initiating call:', error);\n      let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n      if (error.networkError) {\n        errorMessage = 'Erreur de connexion réseau';\n      } else if (error.graphQLErrors?.length > 0) {\n        errorMessage = error.graphQLErrors[0].message || errorMessage;\n      }\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall) {\n    var _this = this;\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    return this.apollo.mutate({\n      mutation: ACCEPT_CALL_MUTATION,\n      variables: {\n        callId: incomingCall.id,\n        answer\n      }\n    }).pipe(map( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (result) {\n        console.log('✅ [CallService] Call accepted successfully:', result);\n        if (!result.data?.acceptCall) {\n          throw new Error('No call data received from server');\n        }\n        const call = result.data.acceptCall;\n        // Arrêter la sonnerie\n        _this.stop('ringtone');\n        _this.play('call-connected');\n        // Démarrer les médias pour l'appel\n        try {\n          console.log('🎥 [CallService] Starting media for call...');\n          const isVideoCall = incomingCall.type === CallType.VIDEO;\n          yield _this.getUserMedia(isVideoCall, true);\n          console.log('✅ [CallService] Media started successfully');\n        } catch (error) {\n          console.error('❌ [CallService] Error starting media:', error);\n          // Continuer même si les médias échouent\n        }\n        // Mettre à jour l'état local\n        _this.activeCall.next(call);\n        _this.incomingCall.next(null); // Supprimer l'appel entrant\n        return call;\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }()), catchError(error => {\n      console.error('❌ [CallService] acceptCall error:', error);\n      this.logger.error('Error accepting call:', error);\n      return throwError(() => new Error(\"Erreur lors de l'acceptation de l'appel\"));\n    }));\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId, reason) {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call rejected successfully:', result);\n      if (!result.data?.rejectCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      return result.data.rejectCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] rejectCall error:', error);\n      this.logger.error('Error rejecting call:', error);\n      return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId) {\n    console.log('🔄 [CallService] Ending call:', callId);\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback: null // Pas de feedback pour l'instant\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call ended successfully:', result);\n      if (!result.data?.endCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.activeCall.next(null);\n      this.incomingCall.next(null);\n      // Arrêter tous les sons\n      this.stopAllSounds();\n      this.play('call-end');\n      return result.data.endCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] endCall error:', error);\n      this.logger.error('Error ending call:', error);\n      return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n    }));\n  }\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio\n    });\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Media toggled successfully:', result);\n      if (!result.data?.toggleCallMedia) {\n        throw new Error('No response received from server');\n      }\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('❌ [CallService] toggleMedia error:', error);\n      this.logger.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  /**\n   * Obtient les médias utilisateur (caméra/microphone)\n   */\n  getUserMedia() {\n    var _this2 = this;\n    return _asyncToGenerator(function* (video = true, audio = true) {\n      console.log('🎥 [CallService] Getting user media:', {\n        video,\n        audio\n      });\n      try {\n        const constraints = {\n          video: video ? {\n            width: 640,\n            height: 480\n          } : false,\n          audio: audio\n        };\n        _this2.localStream = yield navigator.mediaDevices.getUserMedia(constraints);\n        _this2.isVideoEnabled = video;\n        _this2.isAudioEnabled = audio;\n        console.log('✅ [CallService] User media obtained successfully');\n        return _this2.localStream;\n      } catch (error) {\n        console.error('❌ [CallService] Error getting user media:', error);\n        throw new Error(\"Impossible d'accéder à la caméra/microphone\");\n      }\n    }).apply(this, arguments);\n  }\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo() {\n    if (this.localStream) {\n      const videoTracks = this.localStream.getVideoTracks();\n      if (videoTracks.length > 0) {\n        this.isVideoEnabled = !this.isVideoEnabled;\n        videoTracks[0].enabled = this.isVideoEnabled;\n        console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n      }\n    }\n    return this.isVideoEnabled;\n  }\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio() {\n    if (this.localStream) {\n      const audioTracks = this.localStream.getAudioTracks();\n      if (audioTracks.length > 0) {\n        this.isAudioEnabled = !this.isAudioEnabled;\n        audioTracks[0].enabled = this.isAudioEnabled;\n        console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n      }\n    }\n    return this.isAudioEnabled;\n  }\n  /**\n   * Obtient le stream local\n   */\n  getLocalStream() {\n    return this.localStream;\n  }\n  /**\n   * Obtient le stream distant\n   */\n  getRemoteStream() {\n    return this.remoteStream;\n  }\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled() {\n    return this.isVideoEnabled;\n  }\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled() {\n    return this.isAudioEnabled;\n  }\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  cleanupWebRTC() {\n    console.log('🧹 [CallService] Cleaning up WebRTC resources');\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => {\n        track.stop();\n      });\n      this.localStream = null;\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  ngOnDestroy() {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "map", "catchError", "CallType", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "activeCall$", "asObservable", "incomingCall$", "sounds", "isPlaying", "muted", "localStream", "remoteStream", "peerConnection", "isVideoEnabled", "isAudioEnabled", "preloadSounds", "initializeSubscriptions", "setTimeout", "subscribeToIncomingCalls", "loadSound", "name", "path", "audio", "Audio", "load", "addEventListener", "error", "console", "play", "loop", "sound", "currentTime", "catch", "stop", "pause", "log", "subscribe", "query", "next", "data", "callId", "id", "callType", "type", "caller", "username", "conversationId", "handleIncomingCall", "reinitializeSubscription", "call", "initiateCall", "recipientId", "Error", "Date", "now", "Math", "random", "toString", "substr", "offer", "JSON", "stringify", "sdp", "variables", "mutate", "mutation", "pipe", "result", "status", "recipient", "errorMessage", "networkError", "graphQLErrors", "length", "message", "acceptCall", "_this", "answer", "_ref", "_asyncToGenerator", "isVideoCall", "VIDEO", "getUserMedia", "_x", "apply", "arguments", "rejectCall", "reason", "endCall", "feedback", "stopAllSounds", "toggleMedia", "enableVideo", "enableAudio", "video", "toggleCallMedia", "_this2", "constraints", "width", "height", "navigator", "mediaDevices", "toggleVideo", "videoTracks", "getVideoTracks", "enabled", "toggleAudio", "audioTracks", "getAudioTracks", "getLocalStream", "getRemoteStream", "getVideoEnabled", "getAudioEnabled", "cleanupWebRTC", "getTracks", "for<PERSON>ach", "track", "close", "Object", "keys", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnDestroy {\n  // État des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  // WebRTC pour l'audio/vidéo réel\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private peerConnection: RTCPeerConnection | null = null;\n  private isVideoEnabled = true;\n  private isAudioEnabled = true;\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.preloadSounds();\n    this.initializeSubscriptions();\n  }\n\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  private initializeSubscriptions(): void {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n    }, 2000);\n  }\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n  }\n\n  /**\n   * Charge un fichier audio\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      console.error(`Error loading sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * Joue un son\n   */\n  private play(name: string, loop: boolean = false): void {\n    if (this.muted) return;\n\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n\n      sound.loop = loop;\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(console.error);\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      console.error(`Error playing sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  private stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      console.error(`Error stopping sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    console.log('🔔 [CallService] Setting up incoming call subscription...');\n\n    this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n      })\n      .subscribe({\n        next: ({ data }) => {\n          if (data?.incomingCall) {\n            console.log('📞 [CallService] Incoming call received:', {\n              callId: data.incomingCall.id,\n              callType: data.incomingCall.type,\n              caller: data.incomingCall.caller?.username,\n              conversationId: data.incomingCall.conversationId,\n            });\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: (error) => {\n          console.error(\n            '❌ [CallService] Error in incoming call subscription:',\n            error\n          );\n\n          // Réessayer après 5 secondes en cas d'erreur\n          setTimeout(() => {\n            console.log(\n              '🔄 [CallService] Retrying incoming call subscription...'\n            );\n            this.subscribeToIncomingCalls();\n          }, 5000);\n        },\n      });\n  }\n\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  public reinitializeSubscription(): void {\n    console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId,\n    });\n\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n\n    console.log(\n      '🔊 [CallService] Ringtone started, call notification sent to UI'\n    );\n  }\n\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n\n    // Pour l'instant, utiliser une offre WebRTC factice\n    const offer = JSON.stringify({\n      type: 'offer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    const variables = {\n      recipientId,\n      callType: callType,\n      callId,\n      offer,\n      conversationId,\n    };\n\n    console.log('📤 [CallService] Sending initiate call mutation:', variables);\n\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables,\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call initiated successfully:', result);\n\n          if (!result.data?.initiateCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.initiateCall;\n          console.log('📞 [CallService] Call details:', {\n            id: call.id,\n            type: call.type,\n            status: call.status,\n            caller: call.caller?.username,\n            recipient: call.recipient?.username,\n          });\n\n          // Mettre à jour l'état local\n          this.activeCall.next(call);\n\n          return call;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] initiateCall error:', error);\n          this.logger.error('Error initiating call:', error);\n\n          let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n          if (error.networkError) {\n            errorMessage = 'Erreur de connexion réseau';\n          } else if (error.graphQLErrors?.length > 0) {\n            errorMessage = error.graphQLErrors[0].message || errorMessage;\n          }\n\n          return throwError(() => new Error(errorMessage));\n        })\n      );\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer,\n        },\n      })\n      .pipe(\n        map(async (result) => {\n          console.log('✅ [CallService] Call accepted successfully:', result);\n\n          if (!result.data?.acceptCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.acceptCall;\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n          this.play('call-connected');\n\n          // Démarrer les médias pour l'appel\n          try {\n            console.log('🎥 [CallService] Starting media for call...');\n            const isVideoCall = incomingCall.type === CallType.VIDEO;\n            await this.getUserMedia(isVideoCall, true);\n            console.log('✅ [CallService] Media started successfully');\n          } catch (error) {\n            console.error('❌ [CallService] Error starting media:', error);\n            // Continuer même si les médias échouent\n          }\n\n          // Mettre à jour l'état local\n          this.activeCall.next(call);\n          this.incomingCall.next(null); // Supprimer l'appel entrant\n\n          return call;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] acceptCall error:', error);\n          this.logger.error('Error accepting call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de l'acceptation de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason: reason || 'User rejected',\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call rejected successfully:', result);\n\n          if (!result.data?.rejectCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n\n          return result.data.rejectCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] rejectCall error:', error);\n          this.logger.error('Error rejecting call:', error);\n          return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Ending call:', callId);\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback: null, // Pas de feedback pour l'instant\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call ended successfully:', result);\n\n          if (!result.data?.endCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n\n          // Arrêter tous les sons\n          this.stopAllSounds();\n          this.play('call-end');\n\n          return result.data.endCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] endCall error:', error);\n          this.logger.error('Error ending call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de la fin de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio,\n    });\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video: enableVideo,\n          audio: enableAudio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Media toggled successfully:', result);\n\n          if (!result.data?.toggleCallMedia) {\n            throw new Error('No response received from server');\n          }\n\n          return result.data.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] toggleMedia error:', error);\n          this.logger.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  /**\n   * Obtient les médias utilisateur (caméra/microphone)\n   */\n  async getUserMedia(\n    video: boolean = true,\n    audio: boolean = true\n  ): Promise<MediaStream> {\n    console.log('🎥 [CallService] Getting user media:', { video, audio });\n\n    try {\n      const constraints: MediaStreamConstraints = {\n        video: video ? { width: 640, height: 480 } : false,\n        audio: audio,\n      };\n\n      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);\n      this.isVideoEnabled = video;\n      this.isAudioEnabled = audio;\n\n      console.log('✅ [CallService] User media obtained successfully');\n      return this.localStream;\n    } catch (error) {\n      console.error('❌ [CallService] Error getting user media:', error);\n      throw new Error(\"Impossible d'accéder à la caméra/microphone\");\n    }\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    if (this.localStream) {\n      const videoTracks = this.localStream.getVideoTracks();\n      if (videoTracks.length > 0) {\n        this.isVideoEnabled = !this.isVideoEnabled;\n        videoTracks[0].enabled = this.isVideoEnabled;\n        console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n      }\n    }\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    if (this.localStream) {\n      const audioTracks = this.localStream.getAudioTracks();\n      if (audioTracks.length > 0) {\n        this.isAudioEnabled = !this.isAudioEnabled;\n        audioTracks[0].enabled = this.isAudioEnabled;\n        console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n      }\n    }\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient le stream local\n   */\n  getLocalStream(): MediaStream | null {\n    return this.localStream;\n  }\n\n  /**\n   * Obtient le stream distant\n   */\n  getRemoteStream(): MediaStream | null {\n    return this.remoteStream;\n  }\n\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  private cleanupWebRTC(): void {\n    console.log('🧹 [CallService] Cleaning up WebRTC resources');\n\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => {\n        track.stop();\n      });\n      this.localStream = null;\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAcC,UAAU,QAAY,MAAM;AAClE,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;AAChD,SAEEC,QAAQ,QAIH,yBAAyB;AAChC,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,QACrB,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EAqBtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IApBlD;IACQ,KAAAC,UAAU,GAAG,IAAIf,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAgB,YAAY,GAAG,IAAIhB,eAAe,CAAsB,IAAI,CAAC;IAErE;IACO,KAAAiB,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAEvD;IACQ,KAAAE,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAErB;IACQ,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,cAAc,GAA6B,IAAI;IAC/C,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAG3B,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEA;;;EAGQA,uBAAuBA,CAAA;IAC7B;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,wBAAwB,EAAE;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQH,aAAaA,CAAA;IACnB,IAAI,CAACI,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;EACtE;EAEA;;;EAGQA,SAASA,CAACC,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC;MAC7BC,KAAK,CAACE,IAAI,EAAE;MACZ,IAAI,CAACjB,MAAM,CAACa,IAAI,CAAC,GAAGE,KAAK;MACzB,IAAI,CAACd,SAAS,CAACY,IAAI,CAAC,GAAG,KAAK;MAE5BE,KAAK,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAACjB,SAAS,CAACY,IAAI,CAAC,GAAG,KAAK;MAC9B,CAAC,CAAC;KACH,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuBN,IAAI,GAAG,EAAEM,KAAK,CAAC;;EAExD;EAEA;;;EAGQE,IAAIA,CAACR,IAAY,EAAES,IAAA,GAAgB,KAAK;IAC9C,IAAI,IAAI,CAACpB,KAAK,EAAE;IAEhB,IAAI;MACF,MAAMqB,KAAK,GAAG,IAAI,CAACvB,MAAM,CAACa,IAAI,CAAC;MAC/B,IAAI,CAACU,KAAK,EAAE;MAEZA,KAAK,CAACD,IAAI,GAAGA,IAAI;MACjB,IAAI,CAAC,IAAI,CAACrB,SAAS,CAACY,IAAI,CAAC,EAAE;QACzBU,KAAK,CAACC,WAAW,GAAG,CAAC;QACrBD,KAAK,CAACF,IAAI,EAAE,CAACI,KAAK,CAACL,OAAO,CAACD,KAAK,CAAC;QACjC,IAAI,CAAClB,SAAS,CAACY,IAAI,CAAC,GAAG,IAAI;;KAE9B,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuBN,IAAI,GAAG,EAAEM,KAAK,CAAC;;EAExD;EAEA;;;EAGQO,IAAIA,CAACb,IAAY;IACvB,IAAI;MACF,MAAMU,KAAK,GAAG,IAAI,CAACvB,MAAM,CAACa,IAAI,CAAC;MAC/B,IAAI,CAACU,KAAK,EAAE;MAEZ,IAAI,IAAI,CAACtB,SAAS,CAACY,IAAI,CAAC,EAAE;QACxBU,KAAK,CAACI,KAAK,EAAE;QACbJ,KAAK,CAACC,WAAW,GAAG,CAAC;QACrB,IAAI,CAACvB,SAAS,CAACY,IAAI,CAAC,GAAG,KAAK;;KAE/B,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwBN,IAAI,GAAG,EAAEM,KAAK,CAAC;;EAEzD;EAEA;;;EAGQR,wBAAwBA,CAAA;IAC9BS,OAAO,CAACQ,GAAG,CAAC,2DAA2D,CAAC;IAExE,IAAI,CAACnC,MAAM,CACRoC,SAAS,CAAiC;MACzCC,KAAK,EAAExC;KACR,CAAC,CACDuC,SAAS,CAAC;MACTE,IAAI,EAAEA,CAAC;QAAEC;MAAI,CAAE,KAAI;QACjB,IAAIA,IAAI,EAAEpC,YAAY,EAAE;UACtBwB,OAAO,CAACQ,GAAG,CAAC,0CAA0C,EAAE;YACtDK,MAAM,EAAED,IAAI,CAACpC,YAAY,CAACsC,EAAE;YAC5BC,QAAQ,EAAEH,IAAI,CAACpC,YAAY,CAACwC,IAAI;YAChCC,MAAM,EAAEL,IAAI,CAACpC,YAAY,CAACyC,MAAM,EAAEC,QAAQ;YAC1CC,cAAc,EAAEP,IAAI,CAACpC,YAAY,CAAC2C;WACnC,CAAC;UACF,IAAI,CAACC,kBAAkB,CAACR,IAAI,CAACpC,YAAY,CAAC;;MAE9C,CAAC;MACDuB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;QAED;QACAT,UAAU,CAAC,MAAK;UACdU,OAAO,CAACQ,GAAG,CACT,yDAAyD,CAC1D;UACD,IAAI,CAACjB,wBAAwB,EAAE;QACjC,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACN;EAEA;;;EAGO8B,wBAAwBA,CAAA;IAC7BrB,OAAO,CAACQ,GAAG,CAAC,0DAA0D,CAAC;IACvE,IAAI,CAACjB,wBAAwB,EAAE;EACjC;EAEA;;;EAGQ6B,kBAAkBA,CAACE,IAAkB;IAC3CtB,OAAO,CAACQ,GAAG,CAAC,0CAA0C,EAAE;MACtDK,MAAM,EAAES,IAAI,CAACR,EAAE;MACfC,QAAQ,EAAEO,IAAI,CAACN,IAAI;MACnBC,MAAM,EAAEK,IAAI,CAACL,MAAM,EAAEC,QAAQ;MAC7BC,cAAc,EAAEG,IAAI,CAACH;KACtB,CAAC;IAEF,IAAI,CAAC3C,YAAY,CAACmC,IAAI,CAACW,IAAI,CAAC;IAC5B,IAAI,CAACrB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3BD,OAAO,CAACQ,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGAe,YAAYA,CACVC,WAAmB,EACnBT,QAAkB,EAClBI,cAAuB;IAEvBnB,OAAO,CAACQ,GAAG,CAAC,mCAAmC,EAAE;MAC/CgB,WAAW;MACXT,QAAQ;MACRI;KACD,CAAC;IAEF,IAAI,CAACK,WAAW,EAAE;MAChB,MAAMzB,KAAK,GAAG,IAAI0B,KAAK,CAAC,0BAA0B,CAAC;MACnDzB,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAOtC,UAAU,CAAC,MAAMsC,KAAK,CAAC;;IAGhC;IACA,MAAMc,MAAM,GAAG,QAAQa,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAC/CC,QAAQ,CAAC,EAAE,CAAC,CACZC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAEjB;IACA,MAAMC,KAAK,GAAGC,IAAI,CAACC,SAAS,CAAC;MAC3BlB,IAAI,EAAE,OAAO;MACbmB,GAAG,EAAE;KACN,CAAC;IAEF,MAAMC,SAAS,GAAG;MAChBZ,WAAW;MACXT,QAAQ,EAAEA,QAAQ;MAClBF,MAAM;MACNmB,KAAK;MACLb;KACD;IAEDnB,OAAO,CAACQ,GAAG,CAAC,kDAAkD,EAAE4B,SAAS,CAAC;IAE1E,OAAO,IAAI,CAAC/D,MAAM,CACfgE,MAAM,CAAyB;MAC9BC,QAAQ,EAAEzE,sBAAsB;MAChCuE;KACD,CAAC,CACDG,IAAI,CACH7E,GAAG,CAAE8E,MAAM,IAAI;MACbxC,OAAO,CAACQ,GAAG,CAAC,8CAA8C,EAAEgC,MAAM,CAAC;MAEnE,IAAI,CAACA,MAAM,CAAC5B,IAAI,EAAEW,YAAY,EAAE;QAC9B,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMH,IAAI,GAAGkB,MAAM,CAAC5B,IAAI,CAACW,YAAY;MACrCvB,OAAO,CAACQ,GAAG,CAAC,gCAAgC,EAAE;QAC5CM,EAAE,EAAEQ,IAAI,CAACR,EAAE;QACXE,IAAI,EAAEM,IAAI,CAACN,IAAI;QACfyB,MAAM,EAAEnB,IAAI,CAACmB,MAAM;QACnBxB,MAAM,EAAEK,IAAI,CAACL,MAAM,EAAEC,QAAQ;QAC7BwB,SAAS,EAAEpB,IAAI,CAACoB,SAAS,EAAExB;OAC5B,CAAC;MAEF;MACA,IAAI,CAAC3C,UAAU,CAACoC,IAAI,CAACW,IAAI,CAAC;MAE1B,OAAOA,IAAI;IACb,CAAC,CAAC,EACF3D,UAAU,CAAEoC,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAACzB,MAAM,CAACyB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAElD,IAAI4C,YAAY,GAAG,wCAAwC;MAC3D,IAAI5C,KAAK,CAAC6C,YAAY,EAAE;QACtBD,YAAY,GAAG,4BAA4B;OAC5C,MAAM,IAAI5C,KAAK,CAAC8C,aAAa,EAAEC,MAAM,GAAG,CAAC,EAAE;QAC1CH,YAAY,GAAG5C,KAAK,CAAC8C,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO,IAAIJ,YAAY;;MAG/D,OAAOlF,UAAU,CAAC,MAAM,IAAIgE,KAAK,CAACkB,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAK,UAAUA,CAACxE,YAA0B;IAAA,IAAAyE,KAAA;IACnCjD,OAAO,CAACQ,GAAG,CAAC,kCAAkC,EAAEhC,YAAY,CAACsC,EAAE,CAAC;IAEhE;IACA,MAAMoC,MAAM,GAAGjB,IAAI,CAACC,SAAS,CAAC;MAC5BlB,IAAI,EAAE,QAAQ;MACdmB,GAAG,EAAE;KACN,CAAC;IAEF,OAAO,IAAI,CAAC9D,MAAM,CACfgE,MAAM,CAAuB;MAC5BC,QAAQ,EAAExE,oBAAoB;MAC9BsE,SAAS,EAAE;QACTvB,MAAM,EAAErC,YAAY,CAACsC,EAAE;QACvBoC;;KAEH,CAAC,CACDX,IAAI,CACH7E,GAAG;MAAA,IAAAyF,IAAA,GAAAC,iBAAA,CAAC,WAAOZ,MAAM,EAAI;QACnBxC,OAAO,CAACQ,GAAG,CAAC,6CAA6C,EAAEgC,MAAM,CAAC;QAElE,IAAI,CAACA,MAAM,CAAC5B,IAAI,EAAEoC,UAAU,EAAE;UAC5B,MAAM,IAAIvB,KAAK,CAAC,mCAAmC,CAAC;;QAGtD,MAAMH,IAAI,GAAGkB,MAAM,CAAC5B,IAAI,CAACoC,UAAU;QAEnC;QACAC,KAAI,CAAC3C,IAAI,CAAC,UAAU,CAAC;QACrB2C,KAAI,CAAChD,IAAI,CAAC,gBAAgB,CAAC;QAE3B;QACA,IAAI;UACFD,OAAO,CAACQ,GAAG,CAAC,6CAA6C,CAAC;UAC1D,MAAM6C,WAAW,GAAG7E,YAAY,CAACwC,IAAI,KAAKpD,QAAQ,CAAC0F,KAAK;UACxD,MAAML,KAAI,CAACM,YAAY,CAACF,WAAW,EAAE,IAAI,CAAC;UAC1CrD,OAAO,CAACQ,GAAG,CAAC,4CAA4C,CAAC;SAC1D,CAAC,OAAOT,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D;;QAGF;QACAkD,KAAI,CAAC1E,UAAU,CAACoC,IAAI,CAACW,IAAI,CAAC;QAC1B2B,KAAI,CAACzE,YAAY,CAACmC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE9B,OAAOW,IAAI;MACb,CAAC;MAAA,iBAAAkC,EAAA;QAAA,OAAAL,IAAA,CAAAM,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC,EACF/F,UAAU,CAAEoC,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACzB,MAAM,CAACyB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOtC,UAAU,CACf,MAAM,IAAIgE,KAAK,CAAC,yCAAyC,CAAC,CAC3D;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAkC,UAAUA,CAAC9C,MAAc,EAAE+C,MAAe;IACxC5D,OAAO,CAACQ,GAAG,CAAC,kCAAkC,EAAEK,MAAM,EAAE+C,MAAM,CAAC;IAE/D,OAAO,IAAI,CAACvF,MAAM,CACfgE,MAAM,CAA8B;MACnCC,QAAQ,EAAEvE,oBAAoB;MAC9BqE,SAAS,EAAE;QACTvB,MAAM;QACN+C,MAAM,EAAEA,MAAM,IAAI;;KAErB,CAAC,CACDrB,IAAI,CACH7E,GAAG,CAAE8E,MAAM,IAAI;MACbxC,OAAO,CAACQ,GAAG,CAAC,6CAA6C,EAAEgC,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC5B,IAAI,EAAE+C,UAAU,EAAE;QAC5B,MAAM,IAAIlC,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAACjD,YAAY,CAACmC,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACpC,UAAU,CAACoC,IAAI,CAAC,IAAI,CAAC;MAE1B;MACA,IAAI,CAACL,IAAI,CAAC,UAAU,CAAC;MAErB,OAAOkC,MAAM,CAAC5B,IAAI,CAAC+C,UAAU;IAC/B,CAAC,CAAC,EACFhG,UAAU,CAAEoC,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACzB,MAAM,CAACyB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOtC,UAAU,CAAC,MAAM,IAAIgE,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAoC,OAAOA,CAAChD,MAAc;IACpBb,OAAO,CAACQ,GAAG,CAAC,+BAA+B,EAAEK,MAAM,CAAC;IAEpD,OAAO,IAAI,CAACxC,MAAM,CACfgE,MAAM,CAA2B;MAChCC,QAAQ,EAAEtE,iBAAiB;MAC3BoE,SAAS,EAAE;QACTvB,MAAM;QACNiD,QAAQ,EAAE,IAAI,CAAE;;KAEnB,CAAC,CACDvB,IAAI,CACH7E,GAAG,CAAE8E,MAAM,IAAI;MACbxC,OAAO,CAACQ,GAAG,CAAC,0CAA0C,EAAEgC,MAAM,CAAC;MAE/D,IAAI,CAACA,MAAM,CAAC5B,IAAI,EAAEiD,OAAO,EAAE;QACzB,MAAM,IAAIpC,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAAClD,UAAU,CAACoC,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAACnC,YAAY,CAACmC,IAAI,CAAC,IAAI,CAAC;MAE5B;MACA,IAAI,CAACoD,aAAa,EAAE;MACpB,IAAI,CAAC9D,IAAI,CAAC,UAAU,CAAC;MAErB,OAAOuC,MAAM,CAAC5B,IAAI,CAACiD,OAAO;IAC5B,CAAC,CAAC,EACFlG,UAAU,CAAEoC,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAACzB,MAAM,CAACyB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC9C,OAAOtC,UAAU,CACf,MAAM,IAAIgE,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAuC,WAAWA,CACTnD,MAAc,EACdoD,WAAqB,EACrBC,WAAqB;IAErBlE,OAAO,CAACQ,GAAG,CAAC,kCAAkC,EAAE;MAC9CK,MAAM;MACNoD,WAAW;MACXC;KACD,CAAC;IAEF,OAAO,IAAI,CAAC7F,MAAM,CACfgE,MAAM,CAAmC;MACxCC,QAAQ,EAAErE,0BAA0B;MACpCmE,SAAS,EAAE;QACTvB,MAAM;QACNsD,KAAK,EAAEF,WAAW;QAClBtE,KAAK,EAAEuE;;KAEV,CAAC,CACD3B,IAAI,CACH7E,GAAG,CAAE8E,MAAM,IAAI;MACbxC,OAAO,CAACQ,GAAG,CAAC,6CAA6C,EAAEgC,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC5B,IAAI,EAAEwD,eAAe,EAAE;QACjC,MAAM,IAAI3C,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,OAAOe,MAAM,CAAC5B,IAAI,CAACwD,eAAe;IACpC,CAAC,CAAC,EACFzG,UAAU,CAAEoC,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,IAAI,CAACzB,MAAM,CAACyB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOtC,UAAU,CACf,MAAM,IAAIgE,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGM8B,YAAYA,CAAA,EAEK;IAAA,IAAAc,MAAA;IAAA,OAAAjB,iBAAA,YADrBe,KAAA,GAAiB,IAAI,EACrBxE,KAAA,GAAiB,IAAI;MAErBK,OAAO,CAACQ,GAAG,CAAC,sCAAsC,EAAE;QAAE2D,KAAK;QAAExE;MAAK,CAAE,CAAC;MAErE,IAAI;QACF,MAAM2E,WAAW,GAA2B;UAC1CH,KAAK,EAAEA,KAAK,GAAG;YAAEI,KAAK,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAG,CAAE,GAAG,KAAK;UAClD7E,KAAK,EAAEA;SACR;QAED0E,MAAI,CAACtF,WAAW,SAAS0F,SAAS,CAACC,YAAY,CAACnB,YAAY,CAACe,WAAW,CAAC;QACzED,MAAI,CAACnF,cAAc,GAAGiF,KAAK;QAC3BE,MAAI,CAAClF,cAAc,GAAGQ,KAAK;QAE3BK,OAAO,CAACQ,GAAG,CAAC,kDAAkD,CAAC;QAC/D,OAAO6D,MAAI,CAACtF,WAAW;OACxB,CAAC,OAAOgB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE,MAAM,IAAI0B,KAAK,CAAC,6CAA6C,CAAC;;IAC/D,GAAAgC,KAAA,OAAAC,SAAA;EACH;EAEA;;;EAGAiB,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC5F,WAAW,EAAE;MACpB,MAAM6F,WAAW,GAAG,IAAI,CAAC7F,WAAW,CAAC8F,cAAc,EAAE;MACrD,IAAID,WAAW,CAAC9B,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,CAAC5D,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C0F,WAAW,CAAC,CAAC,CAAC,CAACE,OAAO,GAAG,IAAI,CAAC5F,cAAc;QAC5Cc,OAAO,CAACQ,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACtB,cAAc,CAAC;;;IAGvE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGA6F,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChG,WAAW,EAAE;MACpB,MAAMiG,WAAW,GAAG,IAAI,CAACjG,WAAW,CAACkG,cAAc,EAAE;MACrD,IAAID,WAAW,CAAClC,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,CAAC3D,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C6F,WAAW,CAAC,CAAC,CAAC,CAACF,OAAO,GAAG,IAAI,CAAC3F,cAAc;QAC5Ca,OAAO,CAACQ,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACrB,cAAc,CAAC;;;IAGvE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGA+F,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACnG,WAAW;EACzB;EAEA;;;EAGAoG,eAAeA,CAAA;IACb,OAAO,IAAI,CAACnG,YAAY;EAC1B;EAEA;;;EAGAoG,eAAeA,CAAA;IACb,OAAO,IAAI,CAAClG,cAAc;EAC5B;EAEA;;;EAGAmG,eAAeA,CAAA;IACb,OAAO,IAAI,CAAClG,cAAc;EAC5B;EAEA;;;EAGQmG,aAAaA,CAAA;IACnBtF,OAAO,CAACQ,GAAG,CAAC,+CAA+C,CAAC;IAE5D,IAAI,IAAI,CAACzB,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACwG,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAI;QAC7CA,KAAK,CAACnF,IAAI,EAAE;MACd,CAAC,CAAC;MACF,IAAI,CAACvB,WAAW,GAAG,IAAI;;IAGzB,IAAI,IAAI,CAACE,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACyG,KAAK,EAAE;MAC3B,IAAI,CAACzG,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EAEA;;;EAGQ4E,aAAaA,CAAA;IACnB4B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChH,MAAM,CAAC,CAAC4G,OAAO,CAAE/F,IAAI,IAAI;MACxC,IAAI,CAACa,IAAI,CAACb,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEAoG,WAAWA,CAAA;IACT,IAAI,CAAC9B,aAAa,EAAE;IACpB,IAAI,CAACuB,aAAa,EAAE;EACtB;;;uBA7iBWnH,WAAW,EAAA2H,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAXhI,WAAW;MAAAiI,OAAA,EAAXjI,WAAW,CAAAkI,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}