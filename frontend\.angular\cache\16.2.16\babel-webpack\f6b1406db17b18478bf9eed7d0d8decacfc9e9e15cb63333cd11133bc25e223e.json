{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AiChatbotComponent } from '../components/ai-chatbot/ai-chatbot.component';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(t) {\n      return new (t || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, HttpClientModule, CommonModule, ReactiveFormsModule, FormsModule, RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    declarations: [AiChatbotComponent],\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, HttpClientModule],\n    exports: [AiChatbotComponent, CommonModule, ReactiveFormsModule, FormsModule, RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "RouterModule", "HttpClientModule", "AiChatbotComponent", "SharedModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HttpClientModule } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\n\nimport { AiChatbotComponent } from '../components/ai-chatbot/ai-chatbot.component';\n\n@NgModule({\n  declarations: [AiChatbotComponent],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    RouterModule,\n    HttpClientModule,\n  ],\n  exports: [\n    AiChatbotComponent,\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    RouterModule,\n  ],\n})\nexport class SharedModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AAGvD,SAASC,kBAAkB,QAAQ,+CAA+C;;AAmBlF,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAdrBN,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZC,gBAAgB,EAIhBJ,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY;IAAA;EAAA;;;2EAGHG,YAAY;IAAAC,YAAA,GAhBRF,kBAAkB;IAAAG,OAAA,GAE/BR,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZC,gBAAgB;IAAAK,OAAA,GAGhBJ,kBAAkB,EAClBL,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}