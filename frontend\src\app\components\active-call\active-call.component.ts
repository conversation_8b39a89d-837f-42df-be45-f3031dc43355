import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  ElementRef,
  AfterViewInit,
} from '@angular/core';
import { Subscription } from 'rxjs';
import { Call, CallType, CallStatus } from '../../models/message.model';
import { MessageService } from '../../services/message.service';
import { CallService } from '../../services/call.service';
import { LoggerService } from '../../services/logger.service';

@Component({
  selector: 'app-active-call',
  templateUrl: './active-call.component.html',
  styleUrls: ['./active-call.component.css'],
})
export class ActiveCallComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('localVideo') localVideo!: ElementRef<HTMLVideoElement>;
  @ViewChild('remoteVideo') remoteVideo!: ElementRef<HTMLVideoElement>;

  activeCall: Call | null = null;
  callDuration: string = '00:00';
  isAudioMuted: boolean = false;
  isVideoMuted: boolean = false;
  isSpeakerOn: boolean = true;

  private durationInterval: any;
  private callStartTime: Date | null = null;
  private subscriptions: Subscription[] = [];

  // Exposer les énums au template
  CallType = CallType;
  CallStatus = CallStatus;

  constructor(
    private messageService: MessageService,
    private callService: CallService,
    private logger: LoggerService
  ) {}

  ngOnInit(): void {
    // S'abonner à l'appel actif
    const activeCallSub = this.callService.activeCall$.subscribe((call) => {
      const previousCall = this.activeCall;
      this.activeCall = call;

      console.log('📞 [ActiveCall] Active call updated:', call);

      if (call && call.status === CallStatus.CONNECTED) {
        if (!previousCall || previousCall.id !== call.id) {
          // Nouvel appel connecté
          console.log(
            '✅ [ActiveCall] Call connected, starting timer and setting up media'
          );
          this.startCallTimer();
          this.setupMediaStreams();
        }
      } else if (!call || call.status !== CallStatus.CONNECTED) {
        // Appel terminé ou non connecté
        this.stopCallTimer();
      }
    });

    this.subscriptions.push(activeCallSub);
  }

  /**
   * Configure les flux média pour l'appel
   */
  private setupMediaStreams(): void {
    console.log('🎥 [ActiveCall] Setting up media streams...');

    // Configurer le flux local
    const localStream = this.callService.getLocalStream();
    if (localStream && this.localVideo?.nativeElement) {
      console.log('📹 [ActiveCall] Setting local video stream');
      this.localVideo.nativeElement.srcObject = localStream;
    }

    // Pour l'instant, pas de flux distant réel (WebRTC complet nécessaire)
    // Mais on peut simuler ou utiliser le même flux pour les tests
    if (this.remoteVideo?.nativeElement && localStream) {
      console.log('📹 [ActiveCall] Setting remote video stream (simulated)');
      // Pour les tests, on peut utiliser le même flux ou laisser vide
      // this.remoteVideo.nativeElement.srcObject = localStream;
    }
  }

  ngAfterViewInit(): void {
    // Configurer les éléments vidéo après le rendu du composant
    if (this.activeCall && this.activeCall.status === CallStatus.CONNECTED) {
      this.setupMediaStreams();
    }
  }

  // Démarrer le minuteur d'appel
  private startCallTimer(): void {
    this.callStartTime = new Date();
    this.stopCallTimer(); // Arrêter tout minuteur existant

    this.durationInterval = setInterval(() => {
      if (!this.callStartTime) return;

      const now = new Date();
      const diff = Math.floor(
        (now.getTime() - this.callStartTime.getTime()) / 1000
      );

      const minutes = Math.floor(diff / 60)
        .toString()
        .padStart(2, '0');
      const seconds = (diff % 60).toString().padStart(2, '0');

      this.callDuration = `${minutes}:${seconds}`;
    }, 1000);
  }

  // Arrêter le minuteur d'appel
  private stopCallTimer(): void {
    if (this.durationInterval) {
      clearInterval(this.durationInterval);
      this.durationInterval = null;
    }
  }

  // Terminer l'appel
  endCall(): void {
    if (!this.activeCall) {
      return;
    }

    this.logger.debug('Ending call', { callId: this.activeCall.id });

    this.callService.endCall(this.activeCall.id).subscribe({
      next: (result) => {
        this.logger.debug('Call ended successfully', {
          callId: this.activeCall?.id,
          success: result.success,
        });
      },
      error: (error) => {
        this.logger.error('Error ending call', error);
      },
    });
  }

  // Basculer le micro
  toggleMicrophone(): void {
    if (!this.activeCall) {
      return;
    }

    console.log('🎤 [ActiveCall] Toggling microphone...');

    // Basculer localement d'abord
    const newAudioState = this.callService.toggleAudio();
    this.isAudioMuted = !newAudioState;

    console.log('🎤 [ActiveCall] Audio toggled:', {
      enabled: newAudioState,
      muted: this.isAudioMuted,
    });

    // Optionnel : Notifier le serveur
    this.callService
      .toggleMedia(this.activeCall.id, undefined, newAudioState)
      .subscribe({
        next: (result) => {
          console.log('✅ [ActiveCall] Microphone toggle sent to server');
        },
        error: (error) => {
          console.error(
            '❌ [ActiveCall] Error toggling microphone on server:',
            error
          );
          // Continuer même si le serveur échoue
        },
      });
  }

  // Basculer la caméra
  toggleCamera(): void {
    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) {
      return;
    }

    console.log('📹 [ActiveCall] Toggling camera...');

    // Basculer localement d'abord
    const newVideoState = this.callService.toggleVideo();
    this.isVideoMuted = !newVideoState;

    console.log('📹 [ActiveCall] Video toggled:', {
      enabled: newVideoState,
      muted: this.isVideoMuted,
    });

    // Optionnel : Notifier le serveur
    this.callService
      .toggleMedia(this.activeCall.id, newVideoState, undefined)
      .subscribe({
        next: (result) => {
          console.log('✅ [ActiveCall] Camera toggle sent to server');
        },
        error: (error) => {
          console.error(
            '❌ [ActiveCall] Error toggling camera on server:',
            error
          );
          // Continuer même si le serveur échoue
        },
      });
  }

  // Basculer le haut-parleur
  toggleSpeaker(): void {
    this.isSpeakerOn = !this.isSpeakerOn;

    if (this.remoteVideo?.nativeElement) {
      this.remoteVideo.nativeElement.volume = this.isSpeakerOn ? 1 : 0;
    }

    this.logger.debug('Speaker toggled', { on: this.isSpeakerOn });
  }

  // Obtenir le nom de l'autre participant
  getOtherParticipantName(): string {
    if (!this.activeCall) {
      return '';
    }

    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire
    const currentUserId = localStorage.getItem('userId');
    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;

    return isCurrentUserCaller
      ? this.activeCall.recipient.username
      : this.activeCall.caller.username;
  }

  // Obtenir l'avatar de l'autre participant
  getOtherParticipantAvatar(): string {
    if (!this.activeCall) {
      return 'assets/images/default-avatar.png';
    }

    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire
    const currentUserId = localStorage.getItem('userId');
    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;

    const avatar = isCurrentUserCaller
      ? this.activeCall.recipient.image
      : this.activeCall.caller.image;

    return avatar || 'assets/images/default-avatar.png';
  }

  // Obtenir le statut de l'appel sous forme de texte
  getCallStatusText(): string {
    if (!this.activeCall) {
      return '';
    }

    switch (this.activeCall.status) {
      case CallStatus.RINGING:
        return 'Appel en cours...';
      case CallStatus.CONNECTED:
        return this.callDuration;
      case CallStatus.ENDED:
        return 'Appel terminé';
      case CallStatus.MISSED:
        return 'Appel manqué';
      case CallStatus.REJECTED:
        return 'Appel rejeté';
      case CallStatus.FAILED:
        return "Échec de l'appel";
      default:
        return '';
    }
  }

  // Vérifier si l'appel est connecté
  isCallConnected(): boolean {
    return this.activeCall?.status === CallStatus.CONNECTED;
  }

  // Vérifier si l'appel est en cours de sonnerie
  isCallRinging(): boolean {
    return this.activeCall?.status === CallStatus.RINGING;
  }

  // Vérifier si l'appel est un appel vidéo
  isVideoCall(): boolean {
    return (
      this.activeCall?.type === CallType.VIDEO ||
      this.activeCall?.type === CallType.VIDEO_ONLY
    );
  }

  ngOnDestroy(): void {
    // Nettoyer les abonnements et le minuteur
    this.stopCallTimer();
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
