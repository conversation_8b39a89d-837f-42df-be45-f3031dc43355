{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class GeminiChatService {\n  constructor(http) {\n    this.http = http;\n    this.GEMINI_API_KEY = 'AIzaSyBL2R5ESS0q2DtZMbW6f-aMnk_y3bd4re8';\n    this.GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';\n    this.messagesSubject = new BehaviorSubject([]);\n    this.messages$ = this.messagesSubject.asObservable();\n    this.isOpenSubject = new BehaviorSubject(false);\n    this.isOpen$ = this.isOpenSubject.asObservable();\n    this.initializeChat();\n  }\n  initializeChat() {\n    const welcomeMessage = {\n      id: this.generateId(),\n      content: \"👋 Bonjour ! Je suis votre assistant IA. Je peux vous aider avec vos projets, répondre à vos questions sur la plateforme, ou vous donner des conseils académiques. Comment puis-je vous assister aujourd'hui ?\",\n      isUser: false,\n      timestamp: new Date()\n    };\n    this.messagesSubject.next([welcomeMessage]);\n  }\n  toggleChat() {\n    this.isOpenSubject.next(!this.isOpenSubject.value);\n  }\n  closeChat() {\n    this.isOpenSubject.next(false);\n  }\n  openChat() {\n    this.isOpenSubject.next(true);\n  }\n  sendMessage(content) {\n    const userMessage = {\n      id: this.generateId(),\n      content: content.trim(),\n      isUser: true,\n      timestamp: new Date()\n    };\n    // Ajouter le message utilisateur\n    const currentMessages = this.messagesSubject.value;\n    this.messagesSubject.next([...currentMessages, userMessage]);\n    // Ajouter un indicateur de frappe\n    const typingMessage = {\n      id: 'typing',\n      content: '',\n      isUser: false,\n      timestamp: new Date(),\n      isTyping: true\n    };\n    this.messagesSubject.next([...this.messagesSubject.value, typingMessage]);\n    // Préparer le contexte pour Gemini\n    const contextualPrompt = this.buildContextualPrompt(content);\n    return this.callGeminiAPI(contextualPrompt).pipe(map(response => {\n      // Supprimer l'indicateur de frappe\n      const messagesWithoutTyping = this.messagesSubject.value.filter(msg => msg.id !== 'typing');\n      const aiMessage = {\n        id: this.generateId(),\n        content: response,\n        isUser: false,\n        timestamp: new Date()\n      };\n      // Ajouter la réponse IA\n      this.messagesSubject.next([...messagesWithoutTyping, aiMessage]);\n      return aiMessage;\n    }), catchError(error => {\n      // Supprimer l'indicateur de frappe en cas d'erreur\n      const messagesWithoutTyping = this.messagesSubject.value.filter(msg => msg.id !== 'typing');\n      const errorMessage = {\n        id: this.generateId(),\n        content: \"Désolé, je rencontre des difficultés techniques. Veuillez réessayer dans quelques instants.\",\n        isUser: false,\n        timestamp: new Date()\n      };\n      this.messagesSubject.next([...messagesWithoutTyping, errorMessage]);\n      return of(errorMessage);\n    }));\n  }\n  buildContextualPrompt(userMessage) {\n    return `Tu es un assistant IA pour une plateforme de gestion de projets étudiants. Tu aides les professeurs, étudiants et administrateurs.\n\nContexte de la plateforme :\n- Les professeurs peuvent créer des projets, voir les rendus des étudiants, et gérer les évaluations\n- Les étudiants peuvent voir leurs projets assignés, soumettre leurs travaux, et suivre leurs notes\n- Les administrateurs gèrent les utilisateurs et supervisent la plateforme\n\nTon rôle :\n- Répondre aux questions sur l'utilisation de la plateforme\n- Donner des conseils académiques et techniques\n- Aider à résoudre les problèmes courants\n- Être bienveillant, professionnel et pédagogique\n\nRéponds en français, de manière claire et concise. Utilise des emojis appropriés pour rendre tes réponses plus engageantes.\n\nQuestion de l'utilisateur : ${userMessage}`;\n  }\n  callGeminiAPI(prompt) {\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n    const body = {\n      contents: [{\n        parts: [{\n          text: prompt\n        }]\n      }],\n      generationConfig: {\n        temperature: 0.7,\n        topK: 40,\n        topP: 0.95,\n        maxOutputTokens: 1024\n      }\n    };\n    return this.http.post(`${this.GEMINI_API_URL}?key=${this.GEMINI_API_KEY}`, body, {\n      headers\n    }).pipe(map(response => {\n      if (response.candidates && response.candidates.length > 0) {\n        return response.candidates[0].content.parts[0].text;\n      }\n      throw new Error('Réponse invalide de l\\'API');\n    }));\n  }\n  clearChat() {\n    this.initializeChat();\n  }\n  generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n  getCurrentMessages() {\n    return this.messagesSubject.value;\n  }\n  static {\n    this.ɵfac = function GeminiChatService_Factory(t) {\n      return new (t || GeminiChatService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GeminiChatService,\n      factory: GeminiChatService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "map", "catchError", "of", "GeminiChatService", "constructor", "http", "GEMINI_API_KEY", "GEMINI_API_URL", "messagesSubject", "messages$", "asObservable", "isOpenSubject", "isOpen$", "initializeChat", "welcomeMessage", "id", "generateId", "content", "isUser", "timestamp", "Date", "next", "toggleChat", "value", "closeChat", "openChat", "sendMessage", "userMessage", "trim", "currentMessages", "typingMessage", "isTyping", "contextualPrompt", "buildContextualPrompt", "callGeminiAPI", "pipe", "response", "messagesWithoutTyping", "filter", "msg", "aiMessage", "error", "errorMessage", "prompt", "headers", "body", "contents", "parts", "text", "generationConfig", "temperature", "topK", "topP", "maxOutputTokens", "post", "candidates", "length", "Error", "clearChat", "now", "toString", "Math", "random", "substr", "getCurrentMessages", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\gemini-chat.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { of } from 'rxjs';\n\nexport interface ChatMessage {\n  id: string;\n  content: string;\n  isUser: boolean;\n  timestamp: Date;\n  isTyping?: boolean;\n}\n\nexport interface GeminiResponse {\n  candidates: Array<{\n    content: {\n      parts: Array<{\n        text: string;\n      }>;\n    };\n  }>;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class GeminiChatService {\n  private readonly GEMINI_API_KEY = 'AIzaSyBL2R5ESS0q2DtZMbW6f-aMnk_y3bd4re8';\n  private readonly GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';\n\n  private messagesSubject = new BehaviorSubject<ChatMessage[]>([]);\n  public messages$ = this.messagesSubject.asObservable();\n\n  private isOpenSubject = new BehaviorSubject<boolean>(false);\n  public isOpen$ = this.isOpenSubject.asObservable();\n\n  constructor(private http: HttpClient) {\n    this.initializeChat();\n  }\n\n  private initializeChat(): void {\n    const welcomeMessage: ChatMessage = {\n      id: this.generateId(),\n      content: \"👋 Bonjour ! Je suis votre assistant IA. Je peux vous aider avec vos projets, répondre à vos questions sur la plateforme, ou vous donner des conseils académiques. Comment puis-je vous assister aujourd'hui ?\",\n      isUser: false,\n      timestamp: new Date()\n    };\n    this.messagesSubject.next([welcomeMessage]);\n  }\n\n  toggleChat(): void {\n    this.isOpenSubject.next(!this.isOpenSubject.value);\n  }\n\n  closeChat(): void {\n    this.isOpenSubject.next(false);\n  }\n\n  openChat(): void {\n    this.isOpenSubject.next(true);\n  }\n\n  sendMessage(content: string): Observable<ChatMessage> {\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      content: content.trim(),\n      isUser: true,\n      timestamp: new Date()\n    };\n\n    // Ajouter le message utilisateur\n    const currentMessages = this.messagesSubject.value;\n    this.messagesSubject.next([...currentMessages, userMessage]);\n\n    // Ajouter un indicateur de frappe\n    const typingMessage: ChatMessage = {\n      id: 'typing',\n      content: '',\n      isUser: false,\n      timestamp: new Date(),\n      isTyping: true\n    };\n    this.messagesSubject.next([...this.messagesSubject.value, typingMessage]);\n\n    // Préparer le contexte pour Gemini\n    const contextualPrompt = this.buildContextualPrompt(content);\n\n    return this.callGeminiAPI(contextualPrompt).pipe(\n      map(response => {\n        // Supprimer l'indicateur de frappe\n        const messagesWithoutTyping = this.messagesSubject.value.filter(msg => msg.id !== 'typing');\n        \n        const aiMessage: ChatMessage = {\n          id: this.generateId(),\n          content: response,\n          isUser: false,\n          timestamp: new Date()\n        };\n\n        // Ajouter la réponse IA\n        this.messagesSubject.next([...messagesWithoutTyping, aiMessage]);\n        return aiMessage;\n      }),\n      catchError(error => {\n        // Supprimer l'indicateur de frappe en cas d'erreur\n        const messagesWithoutTyping = this.messagesSubject.value.filter(msg => msg.id !== 'typing');\n        \n        const errorMessage: ChatMessage = {\n          id: this.generateId(),\n          content: \"Désolé, je rencontre des difficultés techniques. Veuillez réessayer dans quelques instants.\",\n          isUser: false,\n          timestamp: new Date()\n        };\n\n        this.messagesSubject.next([...messagesWithoutTyping, errorMessage]);\n        return of(errorMessage);\n      })\n    );\n  }\n\n  private buildContextualPrompt(userMessage: string): string {\n    return `Tu es un assistant IA pour une plateforme de gestion de projets étudiants. Tu aides les professeurs, étudiants et administrateurs.\n\nContexte de la plateforme :\n- Les professeurs peuvent créer des projets, voir les rendus des étudiants, et gérer les évaluations\n- Les étudiants peuvent voir leurs projets assignés, soumettre leurs travaux, et suivre leurs notes\n- Les administrateurs gèrent les utilisateurs et supervisent la plateforme\n\nTon rôle :\n- Répondre aux questions sur l'utilisation de la plateforme\n- Donner des conseils académiques et techniques\n- Aider à résoudre les problèmes courants\n- Être bienveillant, professionnel et pédagogique\n\nRéponds en français, de manière claire et concise. Utilise des emojis appropriés pour rendre tes réponses plus engageantes.\n\nQuestion de l'utilisateur : ${userMessage}`;\n  }\n\n  private callGeminiAPI(prompt: string): Observable<string> {\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n\n    const body = {\n      contents: [\n        {\n          parts: [\n            {\n              text: prompt\n            }\n          ]\n        }\n      ],\n      generationConfig: {\n        temperature: 0.7,\n        topK: 40,\n        topP: 0.95,\n        maxOutputTokens: 1024,\n      }\n    };\n\n    return this.http.post<GeminiResponse>(\n      `${this.GEMINI_API_URL}?key=${this.GEMINI_API_KEY}`,\n      body,\n      { headers }\n    ).pipe(\n      map(response => {\n        if (response.candidates && response.candidates.length > 0) {\n          return response.candidates[0].content.parts[0].text;\n        }\n        throw new Error('Réponse invalide de l\\'API');\n      })\n    );\n  }\n\n  clearChat(): void {\n    this.initializeChat();\n  }\n\n  private generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n\n  getCurrentMessages(): ChatMessage[] {\n    return this.messagesSubject.value;\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAAqBC,eAAe,QAAQ,MAAM;AAClD,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;AAChD,SAASC,EAAE,QAAQ,MAAM;;;AAuBzB,OAAM,MAAOC,iBAAiB;EAU5BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IATP,KAAAC,cAAc,GAAG,yCAAyC;IAC1D,KAAAC,cAAc,GAAG,0FAA0F;IAEpH,KAAAC,eAAe,GAAG,IAAIT,eAAe,CAAgB,EAAE,CAAC;IACzD,KAAAU,SAAS,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;IAE9C,KAAAC,aAAa,GAAG,IAAIZ,eAAe,CAAU,KAAK,CAAC;IACpD,KAAAa,OAAO,GAAG,IAAI,CAACD,aAAa,CAACD,YAAY,EAAE;IAGhD,IAAI,CAACG,cAAc,EAAE;EACvB;EAEQA,cAAcA,CAAA;IACpB,MAAMC,cAAc,GAAgB;MAClCC,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBC,OAAO,EAAE,gNAAgN;MACzNC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACD,IAAI,CAACZ,eAAe,CAACa,IAAI,CAAC,CAACP,cAAc,CAAC,CAAC;EAC7C;EAEAQ,UAAUA,CAAA;IACR,IAAI,CAACX,aAAa,CAACU,IAAI,CAAC,CAAC,IAAI,CAACV,aAAa,CAACY,KAAK,CAAC;EACpD;EAEAC,SAASA,CAAA;IACP,IAAI,CAACb,aAAa,CAACU,IAAI,CAAC,KAAK,CAAC;EAChC;EAEAI,QAAQA,CAAA;IACN,IAAI,CAACd,aAAa,CAACU,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEAK,WAAWA,CAACT,OAAe;IACzB,MAAMU,WAAW,GAAgB;MAC/BZ,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBC,OAAO,EAAEA,OAAO,CAACW,IAAI,EAAE;MACvBV,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE,IAAIC,IAAI;KACpB;IAED;IACA,MAAMS,eAAe,GAAG,IAAI,CAACrB,eAAe,CAACe,KAAK;IAClD,IAAI,CAACf,eAAe,CAACa,IAAI,CAAC,CAAC,GAAGQ,eAAe,EAAEF,WAAW,CAAC,CAAC;IAE5D;IACA,MAAMG,aAAa,GAAgB;MACjCf,EAAE,EAAE,QAAQ;MACZE,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBW,QAAQ,EAAE;KACX;IACD,IAAI,CAACvB,eAAe,CAACa,IAAI,CAAC,CAAC,GAAG,IAAI,CAACb,eAAe,CAACe,KAAK,EAAEO,aAAa,CAAC,CAAC;IAEzE;IACA,MAAME,gBAAgB,GAAG,IAAI,CAACC,qBAAqB,CAAChB,OAAO,CAAC;IAE5D,OAAO,IAAI,CAACiB,aAAa,CAACF,gBAAgB,CAAC,CAACG,IAAI,CAC9CnC,GAAG,CAACoC,QAAQ,IAAG;MACb;MACA,MAAMC,qBAAqB,GAAG,IAAI,CAAC7B,eAAe,CAACe,KAAK,CAACe,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACxB,EAAE,KAAK,QAAQ,CAAC;MAE3F,MAAMyB,SAAS,GAAgB;QAC7BzB,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;QACrBC,OAAO,EAAEmB,QAAQ;QACjBlB,MAAM,EAAE,KAAK;QACbC,SAAS,EAAE,IAAIC,IAAI;OACpB;MAED;MACA,IAAI,CAACZ,eAAe,CAACa,IAAI,CAAC,CAAC,GAAGgB,qBAAqB,EAAEG,SAAS,CAAC,CAAC;MAChE,OAAOA,SAAS;IAClB,CAAC,CAAC,EACFvC,UAAU,CAACwC,KAAK,IAAG;MACjB;MACA,MAAMJ,qBAAqB,GAAG,IAAI,CAAC7B,eAAe,CAACe,KAAK,CAACe,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACxB,EAAE,KAAK,QAAQ,CAAC;MAE3F,MAAM2B,YAAY,GAAgB;QAChC3B,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;QACrBC,OAAO,EAAE,6FAA6F;QACtGC,MAAM,EAAE,KAAK;QACbC,SAAS,EAAE,IAAIC,IAAI;OACpB;MAED,IAAI,CAACZ,eAAe,CAACa,IAAI,CAAC,CAAC,GAAGgB,qBAAqB,EAAEK,YAAY,CAAC,CAAC;MACnE,OAAOxC,EAAE,CAACwC,YAAY,CAAC;IACzB,CAAC,CAAC,CACH;EACH;EAEQT,qBAAqBA,CAACN,WAAmB;IAC/C,OAAO;;;;;;;;;;;;;;;8BAemBA,WAAW,EAAE;EACzC;EAEQO,aAAaA,CAACS,MAAc;IAClC,MAAMC,OAAO,GAAG,IAAI9C,WAAW,CAAC;MAC9B,cAAc,EAAE;KACjB,CAAC;IAEF,MAAM+C,IAAI,GAAG;MACXC,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE,CACL;UACEC,IAAI,EAAEL;SACP;OAEJ,CACF;MACDM,gBAAgB,EAAE;QAChBC,WAAW,EAAE,GAAG;QAChBC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,IAAI;QACVC,eAAe,EAAE;;KAEpB;IAED,OAAO,IAAI,CAAChD,IAAI,CAACiD,IAAI,CACnB,GAAG,IAAI,CAAC/C,cAAc,QAAQ,IAAI,CAACD,cAAc,EAAE,EACnDuC,IAAI,EACJ;MAAED;IAAO,CAAE,CACZ,CAACT,IAAI,CACJnC,GAAG,CAACoC,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACmB,UAAU,IAAInB,QAAQ,CAACmB,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QACzD,OAAOpB,QAAQ,CAACmB,UAAU,CAAC,CAAC,CAAC,CAACtC,OAAO,CAAC8B,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI;;MAErD,MAAM,IAAIS,KAAK,CAAC,4BAA4B,CAAC;IAC/C,CAAC,CAAC,CACH;EACH;EAEAC,SAASA,CAAA;IACP,IAAI,CAAC7C,cAAc,EAAE;EACvB;EAEQG,UAAUA,CAAA;IAChB,OAAOI,IAAI,CAACuC,GAAG,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC,CAAC;EACvE;EAEAC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACxD,eAAe,CAACe,KAAK;EACnC;;;uBAhKWpB,iBAAiB,EAAA8D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAjBjE,iBAAiB;MAAAkE,OAAA,EAAjBlE,iBAAiB,CAAAmE,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}