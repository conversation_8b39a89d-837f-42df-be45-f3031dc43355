{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class ToastService {\n  constructor() {\n    this.toastsSubject = new BehaviorSubject([]);\n    this.toasts$ = this.toastsSubject.asObservable();\n    this.currentId = 0;\n  }\n  generateId() {\n    return Math.random().toString(36).substr(2, 9);\n  }\n  addToast(toast) {\n    const newToast = {\n      ...toast,\n      id: this.generateId(),\n      duration: toast.duration || 5000\n    };\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, newToast]);\n    // Auto-remove toast after duration\n    if (newToast.duration && newToast.duration > 0) {\n      setTimeout(() => {\n        this.removeToast(newToast.id);\n      }, newToast.duration);\n    }\n  }\n  show(message, type = 'info', duration = 5000) {\n    const id = this.generateId();\n    const toast = {\n      id,\n      type,\n      title: '',\n      message,\n      duration\n    };\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, toast]);\n    if (duration > 0) {\n      setTimeout(() => this.dismiss(id), duration);\n    }\n  }\n  showSuccess(message, duration = 3000) {\n    this.show(message, 'success', duration);\n  }\n  showError(message, duration = 5000) {\n    this.show(message, 'error', duration);\n  }\n  showWarning(message, duration = 4000) {\n    this.show(message, 'warning', duration);\n  }\n  showInfo(message, duration = 3000) {\n    this.show(message, 'info', duration);\n  }\n  dismiss(id) {\n    const currentToasts = this.toastsSubject.value.filter(t => t.id !== id);\n    this.toastsSubject.next(currentToasts);\n  }\n  success(title, message, duration) {\n    this.addToast({\n      type: 'success',\n      title,\n      message,\n      duration,\n      icon: 'check-circle'\n    });\n  }\n  error(title, message, duration, action) {\n    this.addToast({\n      type: 'error',\n      title,\n      message,\n      duration: duration || 8000,\n      icon: 'x-circle',\n      action\n    });\n  }\n  warning(title, message, duration) {\n    this.addToast({\n      type: 'warning',\n      title,\n      message,\n      duration,\n      icon: 'exclamation-triangle'\n    });\n  }\n  // Méthodes spécifiques pour les erreurs d'autorisation\n  accessDenied(action = 'effectuer cette action', code) {\n    const codeText = code ? ` (Code: ${code})` : '';\n    this.error('Accès refusé', `Vous n'avez pas les permissions nécessaires pour ${action}${codeText}`, 8000, {\n      label: 'Comprendre les rôles',\n      handler: () => {\n        // Optionnel: rediriger vers une page d'aide\n        console.log(\"Redirection vers l'aide sur les rôles\");\n      }\n    });\n  }\n  ownershipRequired(resource = 'cette ressource') {\n    this.error('Propriétaire requis', `Seul le propriétaire ou un administrateur peut modifier ${resource}`, 8000);\n  }\n  removeToast(id) {\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next(currentToasts.filter(toast => toast.id !== id));\n  }\n  clear() {\n    this.toastsSubject.next([]);\n  }\n  static {\n    this.ɵfac = function ToastService_Factory(t) {\n      return new (t || ToastService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ToastService,\n      factory: ToastService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "ToastService", "constructor", "toastsSubject", "toasts$", "asObservable", "currentId", "generateId", "Math", "random", "toString", "substr", "addToast", "toast", "newToast", "id", "duration", "currentToasts", "value", "next", "setTimeout", "removeToast", "show", "message", "type", "title", "dismiss", "showSuccess", "showError", "showWarning", "showInfo", "filter", "t", "success", "icon", "error", "action", "warning", "accessDenied", "code", "codeText", "label", "handler", "console", "log", "ownershipRequired", "resource", "clear", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\toast.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { Toast } from 'src/app/models/message.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ToastService {\n  private toastsSubject = new BehaviorSubject<Toast[]>([]);\n  toasts$ = this.toastsSubject.asObservable();\n  private currentId = 0;\n\n  constructor() {}\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9);\n  }\n\n  private addToast(toast: Omit<Toast, 'id'>): void {\n    const newToast: Toast = {\n      ...toast,\n      id: this.generateId(),\n      duration: toast.duration || 5000,\n    };\n\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, newToast]);\n\n    // Auto-remove toast after duration\n    if (newToast.duration && newToast.duration > 0) {\n      setTimeout(() => {\n        this.removeToast(newToast.id);\n      }, newToast.duration);\n    }\n  }\n  show(\n    message: string,\n    type: 'success' | 'error' | 'warning' | 'info' = 'info',\n    duration = 5000\n  ) {\n    const id = this.generateId();\n    const toast: Toast = { id, type, title: '', message, duration };\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, toast]);\n\n    if (duration > 0) {\n      setTimeout(() => this.dismiss(id), duration);\n    }\n  }\n\n  showSuccess(message: string, duration = 3000) {\n    this.show(message, 'success', duration);\n  }\n\n  showError(message: string, duration = 5000) {\n    this.show(message, 'error', duration);\n  }\n\n  showWarning(message: string, duration = 4000) {\n    this.show(message, 'warning', duration);\n  }\n\n  showInfo(message: string, duration = 3000) {\n    this.show(message, 'info', duration);\n  }\n\n  dismiss(id: string) {\n    const currentToasts = this.toastsSubject.value.filter((t) => t.id !== id);\n    this.toastsSubject.next(currentToasts);\n  }\n  success(title: string, message: string, duration?: number): void {\n    this.addToast({\n      type: 'success',\n      title,\n      message,\n      duration,\n      icon: 'check-circle',\n    });\n  }\n  error(\n    title: string,\n    message: string,\n    duration?: number,\n    action?: Toast['action']\n  ): void {\n    this.addToast({\n      type: 'error',\n      title,\n      message,\n      duration: duration || 8000, // Longer duration for errors\n      icon: 'x-circle',\n      action,\n    });\n  }\n\n  warning(title: string, message: string, duration?: number): void {\n    this.addToast({\n      type: 'warning',\n      title,\n      message,\n      duration,\n      icon: 'exclamation-triangle',\n    });\n  }\n  // Méthodes spécifiques pour les erreurs d'autorisation\n  accessDenied(action: string = 'effectuer cette action', code?: number): void {\n    const codeText = code ? ` (Code: ${code})` : '';\n    this.error(\n      'Accès refusé',\n      `Vous n'avez pas les permissions nécessaires pour ${action}${codeText}`,\n      8000,\n      {\n        label: 'Comprendre les rôles',\n        handler: () => {\n          // Optionnel: rediriger vers une page d'aide\n          console.log(\"Redirection vers l'aide sur les rôles\");\n        },\n      }\n    );\n  }\n\n  ownershipRequired(resource: string = 'cette ressource'): void {\n    this.error(\n      'Propriétaire requis',\n      `Seul le propriétaire ou un administrateur peut modifier ${resource}`,\n      8000\n    );\n  }\n\n  removeToast(id: string): void {\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next(currentToasts.filter((toast) => toast.id !== id));\n  }\n  clear() {\n    this.toastsSubject.next([]);\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAMtC,OAAM,MAAOC,YAAY;EAKvBC,YAAA;IAJQ,KAAAC,aAAa,GAAG,IAAIH,eAAe,CAAU,EAAE,CAAC;IACxD,KAAAI,OAAO,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE;IACnC,KAAAC,SAAS,GAAG,CAAC;EAEN;EACPC,UAAUA,CAAA;IAChB,OAAOC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EAChD;EAEQC,QAAQA,CAACC,KAAwB;IACvC,MAAMC,QAAQ,GAAU;MACtB,GAAGD,KAAK;MACRE,EAAE,EAAE,IAAI,CAACR,UAAU,EAAE;MACrBS,QAAQ,EAAEH,KAAK,CAACG,QAAQ,IAAI;KAC7B;IAED,MAAMC,aAAa,GAAG,IAAI,CAACd,aAAa,CAACe,KAAK;IAC9C,IAAI,CAACf,aAAa,CAACgB,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAEH,QAAQ,CAAC,CAAC;IAErD;IACA,IAAIA,QAAQ,CAACE,QAAQ,IAAIF,QAAQ,CAACE,QAAQ,GAAG,CAAC,EAAE;MAC9CI,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,WAAW,CAACP,QAAQ,CAACC,EAAE,CAAC;MAC/B,CAAC,EAAED,QAAQ,CAACE,QAAQ,CAAC;;EAEzB;EACAM,IAAIA,CACFC,OAAe,EACfC,IAAA,GAAiD,MAAM,EACvDR,QAAQ,GAAG,IAAI;IAEf,MAAMD,EAAE,GAAG,IAAI,CAACR,UAAU,EAAE;IAC5B,MAAMM,KAAK,GAAU;MAAEE,EAAE;MAAES,IAAI;MAAEC,KAAK,EAAE,EAAE;MAAEF,OAAO;MAAEP;IAAQ,CAAE;IAC/D,MAAMC,aAAa,GAAG,IAAI,CAACd,aAAa,CAACe,KAAK;IAC9C,IAAI,CAACf,aAAa,CAACgB,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAEJ,KAAK,CAAC,CAAC;IAElD,IAAIG,QAAQ,GAAG,CAAC,EAAE;MAChBI,UAAU,CAAC,MAAM,IAAI,CAACM,OAAO,CAACX,EAAE,CAAC,EAAEC,QAAQ,CAAC;;EAEhD;EAEAW,WAAWA,CAACJ,OAAe,EAAEP,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,SAAS,EAAEP,QAAQ,CAAC;EACzC;EAEAY,SAASA,CAACL,OAAe,EAAEP,QAAQ,GAAG,IAAI;IACxC,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,OAAO,EAAEP,QAAQ,CAAC;EACvC;EAEAa,WAAWA,CAACN,OAAe,EAAEP,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,SAAS,EAAEP,QAAQ,CAAC;EACzC;EAEAc,QAAQA,CAACP,OAAe,EAAEP,QAAQ,GAAG,IAAI;IACvC,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,MAAM,EAAEP,QAAQ,CAAC;EACtC;EAEAU,OAAOA,CAACX,EAAU;IAChB,MAAME,aAAa,GAAG,IAAI,CAACd,aAAa,CAACe,KAAK,CAACa,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACjB,EAAE,KAAKA,EAAE,CAAC;IACzE,IAAI,CAACZ,aAAa,CAACgB,IAAI,CAACF,aAAa,CAAC;EACxC;EACAgB,OAAOA,CAACR,KAAa,EAAEF,OAAe,EAAEP,QAAiB;IACvD,IAAI,CAACJ,QAAQ,CAAC;MACZY,IAAI,EAAE,SAAS;MACfC,KAAK;MACLF,OAAO;MACPP,QAAQ;MACRkB,IAAI,EAAE;KACP,CAAC;EACJ;EACAC,KAAKA,CACHV,KAAa,EACbF,OAAe,EACfP,QAAiB,EACjBoB,MAAwB;IAExB,IAAI,CAACxB,QAAQ,CAAC;MACZY,IAAI,EAAE,OAAO;MACbC,KAAK;MACLF,OAAO;MACPP,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BkB,IAAI,EAAE,UAAU;MAChBE;KACD,CAAC;EACJ;EAEAC,OAAOA,CAACZ,KAAa,EAAEF,OAAe,EAAEP,QAAiB;IACvD,IAAI,CAACJ,QAAQ,CAAC;MACZY,IAAI,EAAE,SAAS;MACfC,KAAK;MACLF,OAAO;MACPP,QAAQ;MACRkB,IAAI,EAAE;KACP,CAAC;EACJ;EACA;EACAI,YAAYA,CAACF,MAAA,GAAiB,wBAAwB,EAAEG,IAAa;IACnE,MAAMC,QAAQ,GAAGD,IAAI,GAAG,WAAWA,IAAI,GAAG,GAAG,EAAE;IAC/C,IAAI,CAACJ,KAAK,CACR,cAAc,EACd,oDAAoDC,MAAM,GAAGI,QAAQ,EAAE,EACvE,IAAI,EACJ;MACEC,KAAK,EAAE,sBAAsB;MAC7BC,OAAO,EAAEA,CAAA,KAAK;QACZ;QACAC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD;KACD,CACF;EACH;EAEAC,iBAAiBA,CAACC,QAAA,GAAmB,iBAAiB;IACpD,IAAI,CAACX,KAAK,CACR,qBAAqB,EACrB,2DAA2DW,QAAQ,EAAE,EACrE,IAAI,CACL;EACH;EAEAzB,WAAWA,CAACN,EAAU;IACpB,MAAME,aAAa,GAAG,IAAI,CAACd,aAAa,CAACe,KAAK;IAC9C,IAAI,CAACf,aAAa,CAACgB,IAAI,CAACF,aAAa,CAACc,MAAM,CAAElB,KAAK,IAAKA,KAAK,CAACE,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC3E;EACAgC,KAAKA,CAAA;IACH,IAAI,CAAC5C,aAAa,CAACgB,IAAI,CAAC,EAAE,CAAC;EAC7B;;;uBA/HWlB,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAA+C,OAAA,EAAZ/C,YAAY,CAAAgD,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}