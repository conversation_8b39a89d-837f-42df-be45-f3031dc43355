{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { CallType } from '../../../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/message.service\";\nimport * as i2 from \"../../../../services/toast.service\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = [\"localVideo\"];\nconst _c1 = [\"remoteVideo\"];\nfunction CallInterfaceComponent_div_1_div_3_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"div\", 51)(2, \"div\", 52)(3, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CallInterfaceComponent_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44);\n    i0.ɵɵelement(2, \"img\", 45)(3, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\", 47);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, CallInterfaceComponent_div_1_div_3_div_8_Template, 4, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (ctx_r10.otherParticipant == null ? null : ctx_r10.otherParticipant.image) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r10.otherParticipant == null ? null : ctx_r10.otherParticipant.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.otherParticipant == null ? null : ctx_r10.otherParticipant.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r10.callStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isConnected);\n  }\n}\nfunction CallInterfaceComponent_div_1_video_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 54, 55);\n  }\n}\nfunction CallInterfaceComponent_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"i\", 57);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CallInterfaceComponent_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 58);\n  }\n}\nfunction CallInterfaceComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 59);\n  }\n}\nfunction CallInterfaceComponent_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 60);\n  }\n}\nfunction CallInterfaceComponent_div_1_p_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.formatDuration(ctx_r16.callDuration), \" \");\n  }\n}\nfunction CallInterfaceComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"video\", 19, 20);\n    i0.ɵɵlistener(\"click\", function CallInterfaceComponent_div_1_Template_video_click_1_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.toggleFullscreen());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CallInterfaceComponent_div_1_div_3_Template, 9, 5, \"div\", 21);\n    i0.ɵɵelementStart(4, \"div\", 22);\n    i0.ɵɵtemplate(5, CallInterfaceComponent_div_1_video_5_Template, 2, 0, \"video\", 23);\n    i0.ɵɵtemplate(6, CallInterfaceComponent_div_1_div_6_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementStart(7, \"div\", 25);\n    i0.ɵɵtemplate(8, CallInterfaceComponent_div_1_div_8_Template, 1, 0, \"div\", 26);\n    i0.ɵɵtemplate(9, CallInterfaceComponent_div_1_div_9_Template, 1, 0, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 28)(11, \"div\", 29)(12, \"div\", 30)(13, \"div\", 31);\n    i0.ɵɵelement(14, \"img\", 32);\n    i0.ɵɵtemplate(15, CallInterfaceComponent_div_1_div_15_Template, 1, 0, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\")(17, \"p\", 34);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\", 35);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, CallInterfaceComponent_div_1_p_21_Template, 2, 1, \"p\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 37)(23, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function CallInterfaceComponent_div_1_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.toggleFullscreen());\n    });\n    i0.ɵɵelement(24, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 40);\n    i0.ɵɵelement(26, \"div\", 41);\n    i0.ɵɵelementStart(27, \"span\", 42);\n    i0.ɵɵtext(28, \"HD\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"cursor-pointer\", ctx_r0.remoteStream);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.remoteStream);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoEnabled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isVideoEnabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoEnabled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isVideoEnabled);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", (ctx_r0.otherParticipant == null ? null : ctx_r0.otherParticipant.image) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.otherParticipant == null ? null : ctx_r0.otherParticipant.username);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isConnected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.otherParticipant == null ? null : ctx_r0.otherParticipant.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.callStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isConnected && ctx_r0.callDuration > 0);\n  }\n}\nfunction CallInterfaceComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"i\", 78);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CallInterfaceComponent_div_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵelement(1, \"div\", 80)(2, \"div\", 81)(3, \"div\", 82);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CallInterfaceComponent_div_2_p_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r24.formatDuration(ctx_r24.callDuration), \" \");\n  }\n}\nfunction CallInterfaceComponent_div_2_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"div\", 85);\n    i0.ɵɵelement(2, \"i\", 86);\n    i0.ɵɵelementStart(3, \"span\", 87);\n    i0.ɵɵtext(4, \"Audio HD\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CallInterfaceComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63);\n    i0.ɵɵelement(2, \"div\", 64)(3, \"div\", 65)(4, \"div\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 67)(6, \"div\", 31);\n    i0.ɵɵelement(7, \"img\", 68)(8, \"div\", 69);\n    i0.ɵɵtemplate(9, CallInterfaceComponent_div_2_div_9_Template, 2, 0, \"div\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, CallInterfaceComponent_div_2_div_10_Template, 4, 0, \"div\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 72)(12, \"h2\", 73);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 74);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, CallInterfaceComponent_div_2_p_16_Template, 2, 1, \"p\", 75);\n    i0.ɵɵtemplate(17, CallInterfaceComponent_div_2_div_17_Template, 5, 0, \"div\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"src\", (ctx_r1.otherParticipant == null ? null : ctx_r1.otherParticipant.image) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.otherParticipant == null ? null : ctx_r1.otherParticipant.username);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"animate-pulse\", !ctx_r1.isConnected)(\"animate-spin\", ctx_r1.isConnected);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isConnected);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isConnected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otherParticipant == null ? null : ctx_r1.otherParticipant.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.callStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isConnected && ctx_r1.callDuration > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isConnected);\n  }\n}\nfunction CallInterfaceComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function CallInterfaceComponent_div_10_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.toggleVideo());\n    });\n    i0.ɵɵelement(2, \"i\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(!ctx_r2.isVideoEnabled ? \"bg-red-500 hover:bg-red-600 border-red-400 shadow-red-500/50\" : \"bg-gray-700/80 hover:bg-gray-600/80 border-gray-500 shadow-gray-700/50\");\n    i0.ɵɵproperty(\"title\", ctx_r2.isVideoEnabled ? \"D\\u00E9sactiver la cam\\u00E9ra\" : \"Activer la cam\\u00E9ra\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r2.isVideoEnabled ? \"fa-video\" : \"fa-video-slash\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isVideoEnabled ? \"D\\u00E9sactiver cam\\u00E9ra\" : \"Activer cam\\u00E9ra\", \" \");\n  }\n}\nfunction CallInterfaceComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵelement(1, \"i\", 89);\n    i0.ɵɵelementStart(2, \"span\", 90);\n    i0.ɵɵtext(3, \"Micro coup\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CallInterfaceComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵelementStart(2, \"span\", 90);\n    i0.ɵɵtext(3, \"Cam\\u00E9ra d\\u00E9sactiv\\u00E9e\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CallInterfaceComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵelement(1, \"i\", 93);\n    i0.ɵɵelementStart(2, \"span\", 94);\n    i0.ɵɵtext(3, \"Haut-parleur\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CallInterfaceComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 96)(2, \"div\", 97)(3, \"div\", 98);\n    i0.ɵɵelement(4, \"img\", 99)(5, \"div\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h2\", 101);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 102);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 4)(11, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function CallInterfaceComponent_div_25_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.rejectCall());\n    });\n    i0.ɵɵelement(12, \"i\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function CallInterfaceComponent_div_25_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.acceptCall());\n    });\n    i0.ɵɵelement(14, \"i\", 7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", (ctx_r6.otherParticipant == null ? null : ctx_r6.otherParticipant.image) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r6.otherParticipant == null ? null : ctx_r6.otherParticipant.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.otherParticipant == null ? null : ctx_r6.otherParticipant.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r6.getCallTypeLabel(), \" entrant...\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMap(ctx_r6.callType === \"VIDEO\" ? \"fa-video\" : \"fa-phone\");\n  }\n}\nfunction CallInterfaceComponent_audio_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"audio\", 106, 107);\n  }\n}\nfunction CallInterfaceComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108)(1, \"div\", 109);\n    i0.ɵɵelement(2, \"div\", 110);\n    i0.ɵɵelementStart(3, \"p\", 111);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.callStatus);\n  }\n}\nexport class CallInterfaceComponent {\n  constructor(messageService, toastService, cdr) {\n    this.messageService = messageService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    this.activeCall = null;\n    this.isVisible = false;\n    this.callType = null;\n    this.otherParticipant = null;\n    this.callEnded = new EventEmitter();\n    this.callAccepted = new EventEmitter();\n    this.callRejected = new EventEmitter();\n    // État de l'appel\n    this.isConnected = false;\n    this.isIncoming = false;\n    this.isOutgoing = false;\n    this.callDuration = 0;\n    this.callStatus = 'Connexion...';\n    // Contrôles\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.isSpeakerOn = false;\n    this.isFullscreen = false;\n    // Streams\n    this.localStream = null;\n    this.remoteStream = null;\n    // Timer\n    this.callTimer = null;\n    this.subscriptions = new Subscription();\n    // Types pour le template\n    this.CallType = CallType;\n  }\n  ngOnInit() {\n    this.setupCallSubscriptions();\n    this.initializeMediaDevices();\n  }\n  ngOnDestroy() {\n    this.cleanup();\n  }\n  setupCallSubscriptions() {\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(this.messageService.activeCall$.subscribe({\n      next: call => {\n        if (call) {\n          this.activeCall = call;\n          this.updateCallStatus();\n          this.cdr.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('❌ Error in active call subscription:', error);\n      }\n    }));\n    // S'abonner aux streams\n    this.subscriptions.add(this.messageService.localStream$.subscribe({\n      next: stream => {\n        this.localStream = stream;\n        this.attachLocalStream();\n      }\n    }));\n    this.subscriptions.add(this.messageService.remoteStream$.subscribe({\n      next: stream => {\n        this.remoteStream = stream;\n        this.attachRemoteStream();\n      }\n    }));\n  }\n  initializeMediaDevices() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.activeCall) return;\n      try {\n        const constraints = {\n          audio: true,\n          video: _this.callType === 'VIDEO'\n        };\n        _this.localStream = yield navigator.mediaDevices.getUserMedia(constraints);\n        _this.attachLocalStream();\n        console.log('✅ Media devices initialized');\n      } catch (error) {\n        console.error('❌ Error accessing media devices:', error);\n        _this.toastService.showError(\"Erreur d'accès à la caméra/micro\");\n      }\n    })();\n  }\n  attachLocalStream() {\n    if (this.localStream && this.localVideoRef?.nativeElement) {\n      this.localVideoRef.nativeElement.srcObject = this.localStream;\n      this.localVideoRef.nativeElement.muted = true; // Éviter l'écho\n    }\n  }\n\n  attachRemoteStream() {\n    if (this.remoteStream && this.remoteVideoRef?.nativeElement) {\n      this.remoteVideoRef.nativeElement.srcObject = this.remoteStream;\n    }\n  }\n  updateCallStatus() {\n    if (!this.activeCall) return;\n    switch (this.activeCall.status) {\n      case 'ringing':\n        this.callStatus = this.isIncoming ? 'Appel entrant...' : 'Appel en cours...';\n        break;\n      case 'accepted':\n        this.callStatus = 'Connexion...';\n        break;\n      case 'connected':\n        this.callStatus = 'Connecté';\n        this.isConnected = true;\n        this.startCallTimer();\n        break;\n      case 'ended':\n        this.callStatus = 'Appel terminé';\n        this.endCall();\n        break;\n      case 'rejected':\n        this.callStatus = 'Appel rejeté';\n        this.endCall();\n        break;\n      default:\n        this.callStatus = 'En cours...';\n    }\n  }\n  startCallTimer() {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n  // === CONTRÔLES D'APPEL ===\n  toggleMute() {\n    if (!this.activeCall) return;\n    this.isMuted = !this.isMuted;\n    // Couper/activer le micro dans le stream local\n    if (this.localStream) {\n      const audioTracks = this.localStream.getAudioTracks();\n      audioTracks.forEach(track => {\n        track.enabled = !this.isMuted;\n      });\n    }\n    // Notifier via le service\n    this.messageService.toggleMedia(this.activeCall.id, undefined, !this.isMuted).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isMuted ? 'Micro coupé' : 'Micro activé');\n      },\n      error: error => {\n        console.error('❌ Error toggling mute:', error);\n        // Revert state on error\n        this.isMuted = !this.isMuted;\n        if (this.localStream) {\n          const audioTracks = this.localStream.getAudioTracks();\n          audioTracks.forEach(track => {\n            track.enabled = !this.isMuted;\n          });\n        }\n      }\n    });\n  }\n  toggleVideo() {\n    if (!this.activeCall || this.callType !== 'VIDEO') return;\n    this.isVideoEnabled = !this.isVideoEnabled;\n    // Couper/activer la vidéo dans le stream local\n    if (this.localStream) {\n      const videoTracks = this.localStream.getVideoTracks();\n      videoTracks.forEach(track => {\n        track.enabled = this.isVideoEnabled;\n      });\n    }\n    // Notifier via le service\n    this.messageService.toggleMedia(this.activeCall.id, this.isVideoEnabled, undefined).subscribe({\n      next: () => {\n        this.toastService.showSuccess(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      error: error => {\n        console.error('❌ Error toggling video:', error);\n        // Revert state on error\n        this.isVideoEnabled = !this.isVideoEnabled;\n        if (this.localStream) {\n          const videoTracks = this.localStream.getVideoTracks();\n          videoTracks.forEach(track => {\n            track.enabled = this.isVideoEnabled;\n          });\n        }\n      }\n    });\n  }\n  toggleSpeaker() {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    if (this.remoteVideoRef?.nativeElement) {\n      // Changer la sortie audio (si supporté par le navigateur)\n      this.remoteVideoRef.nativeElement.volume = this.isSpeakerOn ? 1 : 0.7;\n    }\n    this.toastService.showSuccess(this.isSpeakerOn ? 'Haut-parleur activé' : 'Haut-parleur désactivé');\n  }\n  toggleFullscreen() {\n    this.isFullscreen = !this.isFullscreen;\n    if (this.isFullscreen && this.remoteVideoRef?.nativeElement) {\n      this.remoteVideoRef.nativeElement.requestFullscreen?.();\n    } else if (document.fullscreenElement) {\n      document.exitFullscreen?.();\n    }\n  }\n  acceptCall() {\n    if (!this.activeCall) return;\n    this.callAccepted.emit(this.activeCall);\n  }\n  rejectCall() {\n    this.callRejected.emit();\n  }\n  endCall() {\n    this.cleanup();\n    this.callEnded.emit();\n  }\n  cleanup() {\n    // Arrêter le timer\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    // Arrêter les streams\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n    }\n    if (this.remoteStream) {\n      this.remoteStream.getTracks().forEach(track => track.stop());\n      this.remoteStream = null;\n    }\n    // Nettoyer les subscriptions\n    this.subscriptions.unsubscribe();\n    // Réinitialiser l'état\n    this.isConnected = false;\n    this.callDuration = 0;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.isSpeakerOn = false;\n    this.isFullscreen = false;\n  }\n  // === UTILITAIRES ===\n  formatDuration(seconds) {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  }\n  getCallTypeIcon() {\n    return this.callType === 'VIDEO' ? 'videocam' : 'call';\n  }\n  getCallTypeLabel() {\n    return this.callType === 'VIDEO' ? 'Appel vidéo' : 'Appel audio';\n  }\n  static {\n    this.ɵfac = function CallInterfaceComponent_Factory(t) {\n      return new (t || CallInterfaceComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CallInterfaceComponent,\n      selectors: [[\"app-call-interface\"]],\n      viewQuery: function CallInterfaceComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localVideoRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.remoteVideoRef = _t.first);\n        }\n      },\n      inputs: {\n        activeCall: \"activeCall\",\n        isVisible: \"isVisible\",\n        callType: \"callType\",\n        otherParticipant: \"otherParticipant\"\n      },\n      outputs: {\n        callEnded: \"callEnded\",\n        callAccepted: \"callAccepted\",\n        callRejected: \"callRejected\"\n      },\n      decls: 28,\n      vars: 27,\n      consts: [[1, \"fixed\", \"inset-0\", \"z-50\", \"transition-all\", \"duration-300\", \"bg-gray-900\", \"dark:bg-gray-800\", 2, \"background\", \"linear-gradient(135deg, #111827 0%, #1f2937 50%, #374151 100%)\"], [\"class\", \"relative w-full h-full\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center h-full p-8 relative\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-0\", \"left-0\", \"right-0\", \"bg-gradient-to-t\", \"from-black/80\", \"to-transparent\", \"p-8\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-8\"], [1, \"relative\", \"group\"], [1, \"w-20\", \"h-20\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-300\", \"shadow-2xl\", \"border-2\", \"transform\", \"hover:scale-110\", 3, \"title\", \"click\"], [1, \"fas\", \"text-white\", \"text-2xl\"], [1, \"absolute\", \"-top-12\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black/80\", \"text-white\", \"text-xs\", \"px-2\", \"py-1\", \"rounded\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [\"class\", \"relative group\", 4, \"ngIf\"], [\"title\", \"Raccrocher\", 1, \"w-24\", \"h-24\", \"rounded-full\", \"bg-red-500\", \"hover:bg-red-600\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-300\", \"shadow-2xl\", \"border-2\", \"border-red-400\", \"shadow-red-500/50\", \"transform\", \"hover:scale-110\", 3, \"click\"], [1, \"fas\", \"fa-phone-slash\", \"text-white\", \"text-3xl\"], [1, \"flex\", \"items-center\", \"justify-center\", \"mt-4\", \"space-x-4\"], [\"class\", \"flex items-center space-x-1 bg-red-500/20 px-2 py-1 rounded-full\", 4, \"ngIf\"], [\"class\", \"flex items-center space-x-1 bg-blue-500/20 px-2 py-1 rounded-full\", 4, \"ngIf\"], [\"class\", \"absolute inset-0 bg-black/80 flex items-center justify-center p-8\", 4, \"ngIf\"], [\"autoplay\", \"\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 z-60 bg-black/50 flex items-center justify-center\", 4, \"ngIf\"], [1, \"relative\", \"w-full\", \"h-full\"], [\"autoplay\", \"\", \"playsinline\", \"\", 1, \"w-full\", \"h-full\", \"object-cover\", \"bg-gray-800\", \"dark:bg-gray-900\", 3, \"click\"], [\"remoteVideo\", \"\"], [\"class\", \"absolute inset-0 flex flex-col items-center justify-center\", \"style\", \"\\n        background: linear-gradient(\\n          135deg,\\n          #1e40af 0%,\\n          #3b82f6 50%,\\n          #06b6d4 100%\\n        );\\n      \", 4, \"ngIf\"], [1, \"absolute\", \"top-4\", \"right-4\", \"w-36\", \"h-28\", \"bg-gray-800\", \"dark:bg-gray-900\", \"rounded-xl\", \"overflow-hidden\", \"border-2\", \"border-white/30\", \"shadow-2xl\", \"backdrop-blur-sm\"], [\"class\", \"w-full h-full object-cover\", \"autoplay\", \"\", \"playsinline\", \"\", \"muted\", \"\", 4, \"ngIf\"], [\"class\", \"w-full h-full bg-gray-700 dark:bg-gray-800 flex items-center justify-center\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-1\", \"right-1\"], [\"class\", \"w-2 h-2 bg-green-400 rounded-full shadow-lg\", \"title\", \"Cam\\u00E9ra activ\\u00E9e\", 4, \"ngIf\"], [\"class\", \"w-2 h-2 bg-red-400 rounded-full shadow-lg\", \"title\", \"Cam\\u00E9ra d\\u00E9sactiv\\u00E9e\", 4, \"ngIf\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"bg-gradient-to-b\", \"from-black/70\", \"to-transparent\", \"p-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"relative\"], [1, \"w-12\", \"h-12\", \"rounded-full\", \"border-2\", \"border-white/40\", \"shadow-lg\", 3, \"src\", \"alt\"], [\"class\", \"absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white shadow-sm\", \"title\", \"Connect\\u00E9\", 4, \"ngIf\"], [1, \"text-white\", \"font-semibold\", \"text-lg\", \"drop-shadow-md\"], [1, \"text-cyan-200\", \"text-sm\", \"font-medium\"], [\"class\", \"text-green-300 text-sm font-mono\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"title\", \"Plein \\u00E9cran\", 1, \"p-3\", \"rounded-full\", \"bg-white/20\", \"hover:bg-white/30\", \"transition-all\", \"duration-200\", \"backdrop-blur-sm\", \"border\", \"border-white/20\", 3, \"click\"], [1, \"fas\", \"fa-expand\", \"text-white\", \"text-lg\"], [1, \"flex\", \"items-center\", \"space-x-1\", \"bg-black/30\", \"px-2\", \"py-1\", \"rounded-full\"], [1, \"w-2\", \"h-2\", \"bg-green-400\", \"rounded-full\"], [1, \"text-white\", \"text-xs\", \"font-medium\"], [1, \"absolute\", \"inset-0\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", 2, \"background\", \"linear-gradient(\\n          135deg,\\n          #1e40af 0%,\\n          #3b82f6 50%,\\n          #06b6d4 100%\\n        )\"], [1, \"relative\", \"mb-8\"], [1, \"w-32\", \"h-32\", \"rounded-full\", \"border-4\", \"border-white/30\", \"shadow-2xl\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border-4\", \"border-cyan-400\", \"animate-pulse\", \"opacity-70\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\", \"drop-shadow-lg\"], [1, \"text-cyan-200\", \"text-lg\", \"font-medium\"], [\"class\", \"mt-4 flex items-center space-x-2\", 4, \"ngIf\"], [1, \"mt-4\", \"flex\", \"items-center\", \"space-x-2\"], [1, \"w-3\", \"h-3\", \"bg-cyan-400\", \"rounded-full\", \"animate-bounce\"], [1, \"w-3\", \"h-3\", \"bg-cyan-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-3\", \"h-3\", \"bg-cyan-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [\"autoplay\", \"\", \"playsinline\", \"\", \"muted\", \"\", 1, \"w-full\", \"h-full\", \"object-cover\"], [\"localVideo\", \"\"], [1, \"w-full\", \"h-full\", \"bg-gray-700\", \"dark:bg-gray-800\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-video-slash\", \"text-white\", \"text-xl\", \"opacity-80\"], [\"title\", \"Cam\\u00E9ra activ\\u00E9e\", 1, \"w-2\", \"h-2\", \"bg-green-400\", \"rounded-full\", \"shadow-lg\"], [\"title\", \"Cam\\u00E9ra d\\u00E9sactiv\\u00E9e\", 1, \"w-2\", \"h-2\", \"bg-red-400\", \"rounded-full\", \"shadow-lg\"], [\"title\", \"Connect\\u00E9\", 1, \"absolute\", \"-bottom-1\", \"-right-1\", \"w-4\", \"h-4\", \"bg-green-400\", \"rounded-full\", \"border-2\", \"border-white\", \"shadow-sm\"], [1, \"text-green-300\", \"text-sm\", \"font-mono\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"h-full\", \"p-8\", \"relative\"], [1, \"absolute\", \"inset-0\", \"opacity-20\"], [1, \"absolute\", \"top-1/4\", \"left-1/4\", \"w-32\", \"h-32\", \"bg-blue-500\", \"rounded-full\", \"blur-3xl\", \"animate-pulse\"], [1, \"absolute\", \"bottom-1/4\", \"right-1/4\", \"w-40\", \"h-40\", \"bg-cyan-500\", \"rounded-full\", \"blur-3xl\", \"animate-pulse\", 2, \"animation-delay\", \"1s\"], [1, \"absolute\", \"top-1/2\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"-translate-y-1/2\", \"w-24\", \"h-24\", \"bg-purple-500\", \"rounded-full\", \"blur-3xl\", \"animate-pulse\", 2, \"animation-delay\", \"2s\"], [1, \"relative\", \"mb-8\", \"z-10\"], [1, \"w-56\", \"h-56\", \"rounded-full\", \"border-4\", \"border-white/30\", \"shadow-2xl\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border-4\", \"border-cyan-400\", \"opacity-70\", 2, \"animation-duration\", \"3s\"], [\"class\", \"absolute -bottom-2 -right-2 w-8 h-8 bg-green-400 rounded-full border-4 border-white shadow-lg flex items-center justify-center\", 4, \"ngIf\"], [\"class\", \"absolute -inset-8 flex items-center justify-center\", 4, \"ngIf\"], [1, \"text-center\", \"z-10\"], [1, \"text-5xl\", \"font-bold\", \"text-white\", \"mb-4\", \"drop-shadow-lg\"], [1, \"text-cyan-200\", \"text-xl\", \"mb-4\", \"font-medium\"], [\"class\", \"text-green-300 text-3xl font-mono font-bold drop-shadow-md\", 4, \"ngIf\"], [\"class\", \"mt-6 flex items-center justify-center space-x-2\", 4, \"ngIf\"], [1, \"absolute\", \"-bottom-2\", \"-right-2\", \"w-8\", \"h-8\", \"bg-green-400\", \"rounded-full\", \"border-4\", \"border-white\", \"shadow-lg\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-check\", \"text-white\", \"text-sm\"], [1, \"absolute\", \"-inset-8\", \"flex\", \"items-center\", \"justify-center\"], [1, \"absolute\", \"w-72\", \"h-72\", \"border-2\", \"border-cyan-400/30\", \"rounded-full\", \"animate-ping\"], [1, \"absolute\", \"w-80\", \"h-80\", \"border-2\", \"border-blue-400/20\", \"rounded-full\", \"animate-ping\", 2, \"animation-delay\", \"0.5s\"], [1, \"absolute\", \"w-88\", \"h-88\", \"border-2\", \"border-purple-400/10\", \"rounded-full\", \"animate-ping\", 2, \"animation-delay\", \"1s\"], [1, \"text-green-300\", \"text-3xl\", \"font-mono\", \"font-bold\", \"drop-shadow-md\"], [1, \"mt-6\", \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [1, \"flex\", \"items-center\", \"space-x-1\", \"bg-black/30\", \"px-3\", \"py-2\", \"rounded-full\", \"backdrop-blur-sm\"], [1, \"fas\", \"fa-volume-up\", \"text-green-400\"], [1, \"text-white\", \"text-sm\", \"font-medium\"], [1, \"flex\", \"items-center\", \"space-x-1\", \"bg-red-500/20\", \"px-2\", \"py-1\", \"rounded-full\"], [1, \"fas\", \"fa-microphone-slash\", \"text-red-400\", \"text-xs\"], [1, \"text-red-300\", \"text-xs\"], [1, \"fas\", \"fa-video-slash\", \"text-red-400\", \"text-xs\"], [1, \"flex\", \"items-center\", \"space-x-1\", \"bg-blue-500/20\", \"px-2\", \"py-1\", \"rounded-full\"], [1, \"fas\", \"fa-volume-up\", \"text-blue-400\", \"text-xs\"], [1, \"text-blue-300\", \"text-xs\"], [1, \"absolute\", \"inset-0\", \"bg-black/80\", \"flex\", \"items-center\", \"justify-center\", \"p-8\"], [1, \"bg-gray-800\", \"rounded-2xl\", \"p-8\", \"max-w-sm\", \"w-full\", \"text-center\", \"shadow-2xl\", \"border\", \"border-gray-700\"], [1, \"mb-8\"], [1, \"relative\", \"inline-block\", \"mb-4\"], [1, \"w-24\", \"h-24\", \"rounded-full\", \"border-4\", \"border-white/20\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border-4\", \"border-cyan-400\", \"animate-pulse\"], [1, \"text-2xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-cyan-300\", \"text-lg\"], [\"title\", \"Rejeter l'appel\", 1, \"w-20\", \"h-20\", \"rounded-full\", \"bg-red-500\", \"hover:bg-red-600\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-300\", \"shadow-2xl\", \"border-2\", \"border-red-400\", \"transform\", \"hover:scale-110\", 3, \"click\"], [1, \"fas\", \"fa-phone-slash\", \"text-white\", \"text-2xl\"], [\"title\", \"Accepter l'appel\", 1, \"w-20\", \"h-20\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-300\", \"shadow-2xl\", \"border-2\", \"border-green-400\", \"transform\", \"hover:scale-110\", 3, \"click\"], [\"autoplay\", \"\"], [\"remoteAudio\", \"\"], [1, \"fixed\", \"inset-0\", \"z-60\", \"bg-black/50\", \"flex\", \"items-center\", \"justify-center\"], [1, \"bg-gray-800\", \"rounded-xl\", \"p-8\", \"text-center\", \"shadow-2xl\", \"border\", \"border-gray-700\"], [1, \"w-12\", \"h-12\", \"border-4\", \"border-cyan-400\", \"border-t-transparent\", \"rounded-full\", \"animate-spin\", \"mx-auto\", \"mb-4\"], [1, \"text-white\", \"text-lg\"]],\n      template: function CallInterfaceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, CallInterfaceComponent_div_1_Template, 29, 13, \"div\", 1);\n          i0.ɵɵtemplate(2, CallInterfaceComponent_div_2_Template, 18, 12, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function CallInterfaceComponent_Template_button_click_6_listener() {\n            return ctx.toggleMute();\n          });\n          i0.ɵɵelement(7, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(10, CallInterfaceComponent_div_10_Template, 5, 6, \"div\", 9);\n          i0.ɵɵelementStart(11, \"div\", 5)(12, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function CallInterfaceComponent_Template_button_click_12_listener() {\n            return ctx.toggleSpeaker();\n          });\n          i0.ɵɵelement(13, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 8);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 5)(17, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function CallInterfaceComponent_Template_button_click_17_listener() {\n            return ctx.endCall();\n          });\n          i0.ɵɵelement(18, \"i\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 8);\n          i0.ɵɵtext(20, \" Raccrocher \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 12);\n          i0.ɵɵtemplate(22, CallInterfaceComponent_div_22_Template, 4, 0, \"div\", 13);\n          i0.ɵɵtemplate(23, CallInterfaceComponent_div_23_Template, 4, 0, \"div\", 13);\n          i0.ɵɵtemplate(24, CallInterfaceComponent_div_24_Template, 4, 0, \"div\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(25, CallInterfaceComponent_div_25_Template, 15, 6, \"div\", 15);\n          i0.ɵɵtemplate(26, CallInterfaceComponent_audio_26_Template, 2, 0, \"audio\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, CallInterfaceComponent_div_27_Template, 5, 1, \"div\", 17);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"opacity-100\", ctx.isVisible)(\"opacity-0\", !ctx.isVisible)(\"pointer-events-none\", !ctx.isVisible);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.callType === \"VIDEO\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.callType === \"AUDIO\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassMap(ctx.isMuted ? \"bg-red-500 hover:bg-red-600 border-red-400 shadow-red-500/50\" : \"bg-gray-700/80 hover:bg-gray-600/80 border-gray-500 shadow-gray-700/50\");\n          i0.ɵɵproperty(\"title\", ctx.isMuted ? \"Activer le micro\" : \"Couper le micro\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.isMuted ? \"fa-microphone-slash\" : \"fa-microphone\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isMuted ? \"Activer micro\" : \"Couper micro\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.callType === \"VIDEO\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.isSpeakerOn ? \"bg-blue-500 hover:bg-blue-600 border-blue-400 shadow-blue-500/50\" : \"bg-gray-700/80 hover:bg-gray-600/80 border-gray-500 shadow-gray-700/50\");\n          i0.ɵɵproperty(\"title\", ctx.isSpeakerOn ? \"D\\u00E9sactiver haut-parleur\" : \"Activer haut-parleur\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.isSpeakerOn ? \"fa-volume-up\" : \"fa-volume-down\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isSpeakerOn ? \"D\\u00E9sactiver haut-parleur\" : \"Activer haut-parleur\", \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isMuted);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isVideoEnabled && ctx.callType === \"VIDEO\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSpeakerOn);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isIncoming && !ctx.isConnected);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.callType === \"AUDIO\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isVisible && !ctx.isConnected && !ctx.isIncoming);\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\"\\n\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImNhbGwtaW50ZXJmYWNlLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsc0ZBQXNGIiwiZmlsZSI6ImNhbGwtaW50ZXJmYWNlLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBDU1MgcG91ciBsZSBjb21wb3NhbnQgZCdpbnRlcmZhY2UgZCdhcHBlbCAtIFV0aWxpc2UgVGFpbHdpbmQgQ1NTIGRhbnMgbGUgdGVtcGxhdGUgKi9cbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbWVzc2FnZXMvY2FsbC1pbnRlcmZhY2UvY2FsbC1pbnRlcmZhY2UuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxzRkFBc0Y7O0FBRXRGLHdYQUF3WCIsInNvdXJjZXNDb250ZW50IjpbIi8qIENTUyBwb3VyIGxlIGNvbXBvc2FudCBkJ2ludGVyZmFjZSBkJ2FwcGVsIC0gVXRpbGlzZSBUYWlsd2luZCBDU1MgZGFucyBsZSB0ZW1wbGF0ZSAqL1xuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Subscription", "CallType", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵtemplate", "CallInterfaceComponent_div_1_div_3_div_8_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r10", "otherParticipant", "image", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "callStatus", "isConnected", "ctx_r16", "formatDuration", "callDuration", "ɵɵlistener", "CallInterfaceComponent_div_1_Template_video_click_1_listener", "ɵɵrestoreView", "_r20", "ctx_r19", "ɵɵnextContext", "ɵɵresetView", "toggleFullscreen", "CallInterfaceComponent_div_1_div_3_Template", "CallInterfaceComponent_div_1_video_5_Template", "CallInterfaceComponent_div_1_div_6_Template", "CallInterfaceComponent_div_1_div_8_Template", "CallInterfaceComponent_div_1_div_9_Template", "CallInterfaceComponent_div_1_div_15_Template", "CallInterfaceComponent_div_1_p_21_Template", "CallInterfaceComponent_div_1_Template_button_click_23_listener", "ctx_r21", "ɵɵclassProp", "ctx_r0", "remoteStream", "isVideoEnabled", "ctx_r24", "CallInterfaceComponent_div_2_div_9_Template", "CallInterfaceComponent_div_2_div_10_Template", "CallInterfaceComponent_div_2_p_16_Template", "CallInterfaceComponent_div_2_div_17_Template", "ctx_r1", "CallInterfaceComponent_div_10_Template_button_click_1_listener", "_r27", "ctx_r26", "toggleVideo", "ɵɵclassMap", "ctx_r2", "CallInterfaceComponent_div_25_Template_button_click_11_listener", "_r29", "ctx_r28", "rejectCall", "CallInterfaceComponent_div_25_Template_button_click_13_listener", "ctx_r30", "acceptCall", "ctx_r6", "getCallTypeLabel", "callType", "ctx_r8", "CallInterfaceComponent", "constructor", "messageService", "toastService", "cdr", "activeCall", "isVisible", "callEnded", "callAccepted", "callRejected", "isIncoming", "isOutgoing", "isMuted", "isSpeakerOn", "isFullscreen", "localStream", "callTimer", "subscriptions", "ngOnInit", "setupCallSubscriptions", "initializeMediaDevices", "ngOnDestroy", "cleanup", "add", "activeCall$", "subscribe", "next", "call", "updateCallStatus", "detectChanges", "error", "console", "localStream$", "stream", "attachLocalStream", "remoteStream$", "attachRemoteStream", "_this", "_asyncToGenerator", "constraints", "audio", "video", "navigator", "mediaDevices", "getUserMedia", "log", "showError", "localVideoRef", "nativeElement", "srcObject", "muted", "remoteVideoRef", "status", "startCallTimer", "endCall", "setInterval", "toggleMute", "audioTracks", "getAudioTracks", "for<PERSON>ach", "track", "enabled", "toggleMedia", "id", "undefined", "showSuccess", "videoTracks", "getVideoTracks", "toggleSpeaker", "volume", "requestFullscreen", "document", "fullscreenElement", "exitFullscreen", "emit", "clearInterval", "getTracks", "stop", "unsubscribe", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "getCallTypeIcon", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "ToastService", "ChangeDetectorRef", "selectors", "viewQuery", "CallInterfaceComponent_Query", "rf", "ctx", "CallInterfaceComponent_div_1_Template", "CallInterfaceComponent_div_2_Template", "CallInterfaceComponent_Template_button_click_6_listener", "CallInterfaceComponent_div_10_Template", "CallInterfaceComponent_Template_button_click_12_listener", "CallInterfaceComponent_Template_button_click_17_listener", "CallInterfaceComponent_div_22_Template", "CallInterfaceComponent_div_23_Template", "CallInterfaceComponent_div_24_Template", "CallInterfaceComponent_div_25_Template", "CallInterfaceComponent_audio_26_Template", "CallInterfaceComponent_div_27_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\call-interface\\call-interface.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\call-interface\\call-interface.component.html"], "sourcesContent": ["import {\n  Component,\n  OnInit,\n  OnDestroy,\n  Input,\n  Output,\n  EventEmitter,\n  ViewChild,\n  ElementRef,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { MessageService } from '../../../../services/message.service';\nimport { ToastService } from '../../../../services/toast.service';\nimport { Call, CallType, User } from '../../../../models/message.model';\n\n@Component({\n  selector: 'app-call-interface',\n  templateUrl: './call-interface.component.html',\n  styleUrls: ['./call-interface.component.css'],\n})\nexport class CallInterfaceComponent implements OnInit, OnDestroy {\n  @Input() activeCall: Call | null = null;\n  @Input() isVisible: boolean = false;\n  @Input() callType: 'VIDEO' | 'AUDIO' | null = null;\n  @Input() otherParticipant: User | null = null;\n\n  @Output() callEnded = new EventEmitter<void>();\n  @Output() callAccepted = new EventEmitter<Call>();\n  @Output() callRejected = new EventEmitter<void>();\n\n  // Références aux éléments vidéo\n  @ViewChild('localVideo', { static: false })\n  localVideoRef!: ElementRef<HTMLVideoElement>;\n  @ViewChild('remoteVideo', { static: false })\n  remoteVideoRef!: ElementRef<HTMLVideoElement>;\n\n  // État de l'appel\n  isConnected = false;\n  isIncoming = false;\n  isOutgoing = false;\n  callDuration = 0;\n  callStatus = 'Connexion...';\n\n  // Contrôles\n  isMuted = false;\n  isVideoEnabled = true;\n  isSpeakerOn = false;\n  isFullscreen = false;\n\n  // Streams\n  localStream: MediaStream | null = null;\n  remoteStream: MediaStream | null = null;\n\n  // Timer\n  private callTimer: any = null;\n  private subscriptions = new Subscription();\n\n  // Types pour le template\n  CallType = CallType;\n\n  constructor(\n    private messageService: MessageService,\n    private toastService: ToastService,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnInit(): void {\n    this.setupCallSubscriptions();\n    this.initializeMediaDevices();\n  }\n\n  ngOnDestroy(): void {\n    this.cleanup();\n  }\n\n  private setupCallSubscriptions(): void {\n    // S'abonner aux changements d'état d'appel\n    this.subscriptions.add(\n      this.messageService.activeCall$.subscribe({\n        next: (call) => {\n          if (call) {\n            this.activeCall = call;\n            this.updateCallStatus();\n            this.cdr.detectChanges();\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error in active call subscription:', error);\n        },\n      })\n    );\n\n    // S'abonner aux streams\n    this.subscriptions.add(\n      this.messageService.localStream$.subscribe({\n        next: (stream) => {\n          this.localStream = stream;\n          this.attachLocalStream();\n        },\n      })\n    );\n\n    this.subscriptions.add(\n      this.messageService.remoteStream$.subscribe({\n        next: (stream) => {\n          this.remoteStream = stream;\n          this.attachRemoteStream();\n        },\n      })\n    );\n  }\n\n  private async initializeMediaDevices(): Promise<void> {\n    if (!this.activeCall) return;\n\n    try {\n      const constraints = {\n        audio: true,\n        video: this.callType === 'VIDEO',\n      };\n\n      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);\n      this.attachLocalStream();\n\n      console.log('✅ Media devices initialized');\n    } catch (error) {\n      console.error('❌ Error accessing media devices:', error);\n      this.toastService.showError(\"Erreur d'accès à la caméra/micro\");\n    }\n  }\n\n  private attachLocalStream(): void {\n    if (this.localStream && this.localVideoRef?.nativeElement) {\n      this.localVideoRef.nativeElement.srcObject = this.localStream;\n      this.localVideoRef.nativeElement.muted = true; // Éviter l'écho\n    }\n  }\n\n  private attachRemoteStream(): void {\n    if (this.remoteStream && this.remoteVideoRef?.nativeElement) {\n      this.remoteVideoRef.nativeElement.srcObject = this.remoteStream;\n    }\n  }\n\n  private updateCallStatus(): void {\n    if (!this.activeCall) return;\n\n    switch (this.activeCall.status as string) {\n      case 'ringing':\n        this.callStatus = this.isIncoming\n          ? 'Appel entrant...'\n          : 'Appel en cours...';\n        break;\n      case 'accepted':\n        this.callStatus = 'Connexion...';\n        break;\n      case 'connected':\n        this.callStatus = 'Connecté';\n        this.isConnected = true;\n        this.startCallTimer();\n        break;\n      case 'ended':\n        this.callStatus = 'Appel terminé';\n        this.endCall();\n        break;\n      case 'rejected':\n        this.callStatus = 'Appel rejeté';\n        this.endCall();\n        break;\n      default:\n        this.callStatus = 'En cours...';\n    }\n  }\n\n  private startCallTimer(): void {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n  }\n\n  // === CONTRÔLES D'APPEL ===\n  toggleMute(): void {\n    if (!this.activeCall) return;\n\n    this.isMuted = !this.isMuted;\n\n    // Couper/activer le micro dans le stream local\n    if (this.localStream) {\n      const audioTracks = this.localStream.getAudioTracks();\n      audioTracks.forEach((track) => {\n        track.enabled = !this.isMuted;\n      });\n    }\n\n    // Notifier via le service\n    this.messageService\n      .toggleMedia(this.activeCall.id, undefined, !this.isMuted)\n      .subscribe({\n        next: () => {\n          this.toastService.showSuccess(\n            this.isMuted ? 'Micro coupé' : 'Micro activé'\n          );\n        },\n        error: (error) => {\n          console.error('❌ Error toggling mute:', error);\n          // Revert state on error\n          this.isMuted = !this.isMuted;\n          if (this.localStream) {\n            const audioTracks = this.localStream.getAudioTracks();\n            audioTracks.forEach((track) => {\n              track.enabled = !this.isMuted;\n            });\n          }\n        },\n      });\n  }\n\n  toggleVideo(): void {\n    if (!this.activeCall || this.callType !== 'VIDEO') return;\n\n    this.isVideoEnabled = !this.isVideoEnabled;\n\n    // Couper/activer la vidéo dans le stream local\n    if (this.localStream) {\n      const videoTracks = this.localStream.getVideoTracks();\n      videoTracks.forEach((track) => {\n        track.enabled = this.isVideoEnabled;\n      });\n    }\n\n    // Notifier via le service\n    this.messageService\n      .toggleMedia(this.activeCall.id, this.isVideoEnabled, undefined)\n      .subscribe({\n        next: () => {\n          this.toastService.showSuccess(\n            this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\n          );\n        },\n        error: (error) => {\n          console.error('❌ Error toggling video:', error);\n          // Revert state on error\n          this.isVideoEnabled = !this.isVideoEnabled;\n          if (this.localStream) {\n            const videoTracks = this.localStream.getVideoTracks();\n            videoTracks.forEach((track) => {\n              track.enabled = this.isVideoEnabled;\n            });\n          }\n        },\n      });\n  }\n\n  toggleSpeaker(): void {\n    this.isSpeakerOn = !this.isSpeakerOn;\n\n    if (this.remoteVideoRef?.nativeElement) {\n      // Changer la sortie audio (si supporté par le navigateur)\n      this.remoteVideoRef.nativeElement.volume = this.isSpeakerOn ? 1 : 0.7;\n    }\n\n    this.toastService.showSuccess(\n      this.isSpeakerOn ? 'Haut-parleur activé' : 'Haut-parleur désactivé'\n    );\n  }\n\n  toggleFullscreen(): void {\n    this.isFullscreen = !this.isFullscreen;\n\n    if (this.isFullscreen && this.remoteVideoRef?.nativeElement) {\n      this.remoteVideoRef.nativeElement.requestFullscreen?.();\n    } else if (document.fullscreenElement) {\n      document.exitFullscreen?.();\n    }\n  }\n\n  acceptCall(): void {\n    if (!this.activeCall) return;\n\n    this.callAccepted.emit(this.activeCall);\n  }\n\n  rejectCall(): void {\n    this.callRejected.emit();\n  }\n\n  endCall(): void {\n    this.cleanup();\n    this.callEnded.emit();\n  }\n\n  private cleanup(): void {\n    // Arrêter le timer\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n\n    // Arrêter les streams\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n    }\n\n    if (this.remoteStream) {\n      this.remoteStream.getTracks().forEach((track) => track.stop());\n      this.remoteStream = null;\n    }\n\n    // Nettoyer les subscriptions\n    this.subscriptions.unsubscribe();\n\n    // Réinitialiser l'état\n    this.isConnected = false;\n    this.callDuration = 0;\n    this.isMuted = false;\n    this.isVideoEnabled = true;\n    this.isSpeakerOn = false;\n    this.isFullscreen = false;\n  }\n\n  // === UTILITAIRES ===\n  formatDuration(seconds: number): string {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs\n      .toString()\n      .padStart(2, '0')}`;\n  }\n\n  getCallTypeIcon(): string {\n    return this.callType === 'VIDEO' ? 'videocam' : 'call';\n  }\n\n  getCallTypeLabel(): string {\n    return this.callType === 'VIDEO' ? 'Appel vidéo' : 'Appel audio';\n  }\n}\n", "<!-- Interface d'appel WebRTC avec Tailwind CSS -->\n<div\n  class=\"fixed inset-0 z-50 transition-all duration-300 bg-gray-900 dark:bg-gray-800\"\n  [class.opacity-100]=\"isVisible\"\n  [class.opacity-0]=\"!isVisible\"\n  [class.pointer-events-none]=\"!isVisible\"\n  style=\"\n    background: linear-gradient(135deg, #111827 0%, #1f2937 50%, #374151 100%);\n  \"\n>\n  <!-- === APPEL VIDÉO === -->\n  <div *ngIf=\"callType === 'VIDEO'\" class=\"relative w-full h-full\">\n    <!-- Vidéo distante (plein écran) -->\n    <video\n      #remoteVideo\n      class=\"w-full h-full object-cover bg-gray-800 dark:bg-gray-900\"\n      [class.cursor-pointer]=\"remoteStream\"\n      autoplay\n      playsinline\n      (click)=\"toggleFullscreen()\"\n    ></video>\n\n    <!-- Placeholder si pas de vidéo distante -->\n    <div\n      *ngIf=\"!remoteStream\"\n      class=\"absolute inset-0 flex flex-col items-center justify-center\"\n      style=\"\n        background: linear-gradient(\n          135deg,\n          #1e40af 0%,\n          #3b82f6 50%,\n          #06b6d4 100%\n        );\n      \"\n    >\n      <div class=\"relative mb-8\">\n        <img\n          [src]=\"otherParticipant?.image || '/assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-32 h-32 rounded-full border-4 border-white/30 shadow-2xl\"\n        />\n        <div\n          class=\"absolute inset-0 rounded-full border-4 border-cyan-400 animate-pulse opacity-70\"\n        ></div>\n      </div>\n      <h2 class=\"text-3xl font-bold text-white mb-2 drop-shadow-lg\">\n        {{ otherParticipant?.username }}\n      </h2>\n      <p class=\"text-cyan-200 text-lg font-medium\">{{ callStatus }}</p>\n\n      <!-- Indicateur de connexion -->\n      <div *ngIf=\"!isConnected\" class=\"mt-4 flex items-center space-x-2\">\n        <div class=\"w-3 h-3 bg-cyan-400 rounded-full animate-bounce\"></div>\n        <div\n          class=\"w-3 h-3 bg-cyan-400 rounded-full animate-bounce\"\n          style=\"animation-delay: 0.1s\"\n        ></div>\n        <div\n          class=\"w-3 h-3 bg-cyan-400 rounded-full animate-bounce\"\n          style=\"animation-delay: 0.2s\"\n        ></div>\n      </div>\n    </div>\n\n    <!-- Vidéo locale (picture-in-picture) -->\n    <div\n      class=\"absolute top-4 right-4 w-36 h-28 bg-gray-800 dark:bg-gray-900 rounded-xl overflow-hidden border-2 border-white/30 shadow-2xl backdrop-blur-sm\"\n    >\n      <video\n        #localVideo\n        *ngIf=\"isVideoEnabled\"\n        class=\"w-full h-full object-cover\"\n        autoplay\n        playsinline\n        muted\n      ></video>\n\n      <!-- Placeholder vidéo locale désactivée -->\n      <div\n        *ngIf=\"!isVideoEnabled\"\n        class=\"w-full h-full bg-gray-700 dark:bg-gray-800 flex items-center justify-center\"\n      >\n        <i class=\"fas fa-video-slash text-white text-xl opacity-80\"></i>\n      </div>\n\n      <!-- Indicateur de statut -->\n      <div class=\"absolute bottom-1 right-1\">\n        <div\n          *ngIf=\"isVideoEnabled\"\n          class=\"w-2 h-2 bg-green-400 rounded-full shadow-lg\"\n          title=\"Caméra activée\"\n        ></div>\n        <div\n          *ngIf=\"!isVideoEnabled\"\n          class=\"w-2 h-2 bg-red-400 rounded-full shadow-lg\"\n          title=\"Caméra désactivée\"\n        ></div>\n      </div>\n    </div>\n\n    <!-- En-tête d'appel vidéo -->\n    <div\n      class=\"absolute top-0 left-0 right-0 bg-gradient-to-b from-black/70 to-transparent p-4 backdrop-blur-sm\"\n    >\n      <div class=\"flex items-center justify-between\">\n        <div class=\"flex items-center space-x-3\">\n          <div class=\"relative\">\n            <img\n              [src]=\"\n                otherParticipant?.image || '/assets/images/default-avatar.png'\n              \"\n              [alt]=\"otherParticipant?.username\"\n              class=\"w-12 h-12 rounded-full border-2 border-white/40 shadow-lg\"\n            />\n            <div\n              *ngIf=\"isConnected\"\n              class=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white shadow-sm\"\n              title=\"Connecté\"\n            ></div>\n          </div>\n          <div>\n            <p class=\"text-white font-semibold text-lg drop-shadow-md\">\n              {{ otherParticipant?.username }}\n            </p>\n            <p class=\"text-cyan-200 text-sm font-medium\">{{ callStatus }}</p>\n            <p\n              *ngIf=\"isConnected && callDuration > 0\"\n              class=\"text-green-300 text-sm font-mono\"\n            >\n              {{ formatDuration(callDuration) }}\n            </p>\n          </div>\n        </div>\n\n        <div class=\"flex items-center space-x-2\">\n          <!-- Bouton plein écran -->\n          <button\n            (click)=\"toggleFullscreen()\"\n            class=\"p-3 rounded-full bg-white/20 hover:bg-white/30 transition-all duration-200 backdrop-blur-sm border border-white/20\"\n            title=\"Plein écran\"\n          >\n            <i class=\"fas fa-expand text-white text-lg\"></i>\n          </button>\n\n          <!-- Indicateur de qualité -->\n          <div\n            class=\"flex items-center space-x-1 bg-black/30 px-2 py-1 rounded-full\"\n          >\n            <div class=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n            <span class=\"text-white text-xs font-medium\">HD</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- === APPEL AUDIO === -->\n  <div\n    *ngIf=\"callType === 'AUDIO'\"\n    class=\"flex flex-col items-center justify-center h-full p-8 relative\"\n  >\n    <!-- Effet de fond animé -->\n    <div class=\"absolute inset-0 opacity-20\">\n      <div\n        class=\"absolute top-1/4 left-1/4 w-32 h-32 bg-blue-500 rounded-full blur-3xl animate-pulse\"\n      ></div>\n      <div\n        class=\"absolute bottom-1/4 right-1/4 w-40 h-40 bg-cyan-500 rounded-full blur-3xl animate-pulse\"\n        style=\"animation-delay: 1s\"\n      ></div>\n      <div\n        class=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-purple-500 rounded-full blur-3xl animate-pulse\"\n        style=\"animation-delay: 2s\"\n      ></div>\n    </div>\n\n    <!-- Avatar principal -->\n    <div class=\"relative mb-8 z-10\">\n      <div class=\"relative\">\n        <img\n          [src]=\"otherParticipant?.image || '/assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-56 h-56 rounded-full border-4 border-white/30 shadow-2xl\"\n        />\n        <!-- Cercle d'animation -->\n        <div\n          class=\"absolute inset-0 rounded-full border-4 border-cyan-400 opacity-70\"\n          [class.animate-pulse]=\"!isConnected\"\n          [class.animate-spin]=\"isConnected\"\n          style=\"animation-duration: 3s\"\n        ></div>\n        <!-- Indicateur de connexion -->\n        <div\n          *ngIf=\"isConnected\"\n          class=\"absolute -bottom-2 -right-2 w-8 h-8 bg-green-400 rounded-full border-4 border-white shadow-lg flex items-center justify-center\"\n        >\n          <i class=\"fas fa-check text-white text-sm\"></i>\n        </div>\n      </div>\n\n      <!-- Ondes sonores animées -->\n      <div\n        *ngIf=\"isConnected\"\n        class=\"absolute -inset-8 flex items-center justify-center\"\n      >\n        <div\n          class=\"absolute w-72 h-72 border-2 border-cyan-400/30 rounded-full animate-ping\"\n        ></div>\n        <div\n          class=\"absolute w-80 h-80 border-2 border-blue-400/20 rounded-full animate-ping\"\n          style=\"animation-delay: 0.5s\"\n        ></div>\n        <div\n          class=\"absolute w-88 h-88 border-2 border-purple-400/10 rounded-full animate-ping\"\n          style=\"animation-delay: 1s\"\n        ></div>\n      </div>\n    </div>\n\n    <!-- Informations utilisateur -->\n    <div class=\"text-center z-10\">\n      <h2 class=\"text-5xl font-bold text-white mb-4 drop-shadow-lg\">\n        {{ otherParticipant?.username }}\n      </h2>\n      <p class=\"text-cyan-200 text-xl mb-4 font-medium\">{{ callStatus }}</p>\n      <p\n        *ngIf=\"isConnected && callDuration > 0\"\n        class=\"text-green-300 text-3xl font-mono font-bold drop-shadow-md\"\n      >\n        {{ formatDuration(callDuration) }}\n      </p>\n\n      <!-- Indicateur de qualité audio -->\n      <div\n        *ngIf=\"isConnected\"\n        class=\"mt-6 flex items-center justify-center space-x-2\"\n      >\n        <div\n          class=\"flex items-center space-x-1 bg-black/30 px-3 py-2 rounded-full backdrop-blur-sm\"\n        >\n          <i class=\"fas fa-volume-up text-green-400\"></i>\n          <span class=\"text-white text-sm font-medium\">Audio HD</span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- === CONTRÔLES D'APPEL === -->\n  <div\n    class=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-8 backdrop-blur-sm\"\n  >\n    <div class=\"flex items-center justify-center space-x-8\">\n      <!-- Bouton Micro -->\n      <div class=\"relative group\">\n        <button\n          (click)=\"toggleMute()\"\n          class=\"w-20 h-20 rounded-full flex items-center justify-center transition-all duration-300 shadow-2xl border-2 transform hover:scale-110\"\n          [class]=\"\n            isMuted\n              ? 'bg-red-500 hover:bg-red-600 border-red-400 shadow-red-500/50'\n              : 'bg-gray-700/80 hover:bg-gray-600/80 border-gray-500 shadow-gray-700/50'\n          \"\n          [title]=\"isMuted ? 'Activer le micro' : 'Couper le micro'\"\n        >\n          <i\n            class=\"fas text-white text-2xl\"\n            [class]=\"isMuted ? 'fa-microphone-slash' : 'fa-microphone'\"\n          ></i>\n        </button>\n        <!-- Tooltip -->\n        <div\n          class=\"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity\"\n        >\n          {{ isMuted ? \"Activer micro\" : \"Couper micro\" }}\n        </div>\n      </div>\n\n      <!-- Bouton Vidéo (seulement pour appels vidéo) -->\n      <div *ngIf=\"callType === 'VIDEO'\" class=\"relative group\">\n        <button\n          (click)=\"toggleVideo()\"\n          class=\"w-20 h-20 rounded-full flex items-center justify-center transition-all duration-300 shadow-2xl border-2 transform hover:scale-110\"\n          [class]=\"\n            !isVideoEnabled\n              ? 'bg-red-500 hover:bg-red-600 border-red-400 shadow-red-500/50'\n              : 'bg-gray-700/80 hover:bg-gray-600/80 border-gray-500 shadow-gray-700/50'\n          \"\n          [title]=\"\n            isVideoEnabled ? 'Désactiver la caméra' : 'Activer la caméra'\n          \"\n        >\n          <i\n            class=\"fas text-white text-2xl\"\n            [class]=\"isVideoEnabled ? 'fa-video' : 'fa-video-slash'\"\n          ></i>\n        </button>\n        <!-- Tooltip -->\n        <div\n          class=\"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity\"\n        >\n          {{ isVideoEnabled ? \"Désactiver caméra\" : \"Activer caméra\" }}\n        </div>\n      </div>\n\n      <!-- Bouton Haut-parleur -->\n      <div class=\"relative group\">\n        <button\n          (click)=\"toggleSpeaker()\"\n          class=\"w-20 h-20 rounded-full flex items-center justify-center transition-all duration-300 shadow-2xl border-2 transform hover:scale-110\"\n          [class]=\"\n            isSpeakerOn\n              ? 'bg-blue-500 hover:bg-blue-600 border-blue-400 shadow-blue-500/50'\n              : 'bg-gray-700/80 hover:bg-gray-600/80 border-gray-500 shadow-gray-700/50'\n          \"\n          [title]=\"\n            isSpeakerOn ? 'Désactiver haut-parleur' : 'Activer haut-parleur'\n          \"\n        >\n          <i\n            class=\"fas text-white text-2xl\"\n            [class]=\"isSpeakerOn ? 'fa-volume-up' : 'fa-volume-down'\"\n          ></i>\n        </button>\n        <!-- Tooltip -->\n        <div\n          class=\"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity\"\n        >\n          {{ isSpeakerOn ? \"Désactiver haut-parleur\" : \"Activer haut-parleur\" }}\n        </div>\n      </div>\n\n      <!-- Bouton Raccrocher -->\n      <div class=\"relative group\">\n        <button\n          (click)=\"endCall()\"\n          class=\"w-24 h-24 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all duration-300 shadow-2xl border-2 border-red-400 shadow-red-500/50 transform hover:scale-110\"\n          title=\"Raccrocher\"\n        >\n          <i class=\"fas fa-phone-slash text-white text-3xl\"></i>\n        </button>\n        <!-- Tooltip -->\n        <div\n          class=\"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity\"\n        >\n          Raccrocher\n        </div>\n      </div>\n    </div>\n\n    <!-- Indicateurs de statut -->\n    <div class=\"flex items-center justify-center mt-4 space-x-4\">\n      <div\n        *ngIf=\"isMuted\"\n        class=\"flex items-center space-x-1 bg-red-500/20 px-2 py-1 rounded-full\"\n      >\n        <i class=\"fas fa-microphone-slash text-red-400 text-xs\"></i>\n        <span class=\"text-red-300 text-xs\">Micro coupé</span>\n      </div>\n      <div\n        *ngIf=\"!isVideoEnabled && callType === 'VIDEO'\"\n        class=\"flex items-center space-x-1 bg-red-500/20 px-2 py-1 rounded-full\"\n      >\n        <i class=\"fas fa-video-slash text-red-400 text-xs\"></i>\n        <span class=\"text-red-300 text-xs\">Caméra désactivée</span>\n      </div>\n      <div\n        *ngIf=\"isSpeakerOn\"\n        class=\"flex items-center space-x-1 bg-blue-500/20 px-2 py-1 rounded-full\"\n      >\n        <i class=\"fas fa-volume-up text-blue-400 text-xs\"></i>\n        <span class=\"text-blue-300 text-xs\">Haut-parleur</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- === MODAL APPEL ENTRANT === -->\n  <div\n    *ngIf=\"isIncoming && !isConnected\"\n    class=\"absolute inset-0 bg-black/80 flex items-center justify-center p-8\"\n  >\n    <div\n      class=\"bg-gray-800 rounded-2xl p-8 max-w-sm w-full text-center shadow-2xl border border-gray-700\"\n    >\n      <!-- Avatar et info -->\n      <div class=\"mb-8\">\n        <div class=\"relative inline-block mb-4\">\n          <img\n            [src]=\"\n              otherParticipant?.image || '/assets/images/default-avatar.png'\n            \"\n            [alt]=\"otherParticipant?.username\"\n            class=\"w-24 h-24 rounded-full border-4 border-white/20\"\n          />\n          <div\n            class=\"absolute inset-0 rounded-full border-4 border-cyan-400 animate-pulse\"\n          ></div>\n        </div>\n\n        <h2 class=\"text-2xl font-bold text-white mb-2\">\n          {{ otherParticipant?.username }}\n        </h2>\n        <p class=\"text-cyan-300 text-lg\">{{ getCallTypeLabel() }} entrant...</p>\n      </div>\n\n      <!-- Boutons d'action -->\n      <div class=\"flex items-center justify-center space-x-8\">\n        <button\n          (click)=\"rejectCall()\"\n          class=\"w-20 h-20 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all duration-300 shadow-2xl border-2 border-red-400 transform hover:scale-110\"\n          title=\"Rejeter l'appel\"\n        >\n          <i class=\"fas fa-phone-slash text-white text-2xl\"></i>\n        </button>\n\n        <button\n          (click)=\"acceptCall()\"\n          class=\"w-20 h-20 rounded-full bg-green-500 hover:bg-green-600 flex items-center justify-center transition-all duration-300 shadow-2xl border-2 border-green-400 transform hover:scale-110\"\n          title=\"Accepter l'appel\"\n        >\n          <i\n            class=\"fas text-white text-2xl\"\n            [class]=\"callType === 'VIDEO' ? 'fa-video' : 'fa-phone'\"\n          ></i>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Audio éléments (cachés) -->\n  <audio #remoteAudio autoplay *ngIf=\"callType === 'AUDIO'\"></audio>\n</div>\n\n<!-- Overlay de chargement -->\n<div\n  *ngIf=\"isVisible && !isConnected && !isIncoming\"\n  class=\"fixed inset-0 z-60 bg-black/50 flex items-center justify-center\"\n>\n  <div\n    class=\"bg-gray-800 rounded-xl p-8 text-center shadow-2xl border border-gray-700\"\n  >\n    <div\n      class=\"w-12 h-12 border-4 border-cyan-400 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n    ></div>\n    <p class=\"text-white text-lg\">{{ callStatus }}</p>\n  </div>\n</div>\n"], "mappings": ";AAAA,SAMEA,YAAY,QAIP,eAAe;AACtB,SAASC,YAAY,QAAQ,MAAM;AAGnC,SAAeC,QAAQ,QAAc,kCAAkC;;;;;;;;;ICqCjEC,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAE,SAAA,cAAmE;IASrEF,EAAA,CAAAG,YAAA,EAAM;;;;;IAtCRH,EAAA,CAAAC,cAAA,cAWC;IAEGD,EAAA,CAAAE,SAAA,cAIE;IAIJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA8D;IAC5DD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAA6C;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGjEH,EAAA,CAAAK,UAAA,IAAAC,iDAAA,kBAUM;IACRN,EAAA,CAAAG,YAAA,EAAM;;;;IAzBAH,EAAA,CAAAO,SAAA,GAAsE;IAAtEP,EAAA,CAAAQ,UAAA,SAAAC,OAAA,CAAAC,gBAAA,kBAAAD,OAAA,CAAAC,gBAAA,CAAAC,KAAA,0CAAAX,EAAA,CAAAY,aAAA,CAAsE,QAAAH,OAAA,CAAAC,gBAAA,kBAAAD,OAAA,CAAAC,gBAAA,CAAAG,QAAA;IASxEb,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAL,OAAA,CAAAC,gBAAA,kBAAAD,OAAA,CAAAC,gBAAA,CAAAG,QAAA,MACF;IAC6Cb,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAe,iBAAA,CAAAN,OAAA,CAAAO,UAAA,CAAgB;IAGvDhB,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,CAAAQ,WAAA,CAAkB;;;;;IAiBxBjB,EAAA,CAAAE,SAAA,oBAOS;;;;;IAGTF,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAAgE;IAClEF,EAAA,CAAAG,YAAA,EAAM;;;;;IAIJH,EAAA,CAAAE,SAAA,cAIO;;;;;IACPF,EAAA,CAAAE,SAAA,cAIO;;;;;IAkBHF,EAAA,CAAAE,SAAA,cAIO;;;;;IAOPF,EAAA,CAAAC,cAAA,YAGC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAI,OAAA,CAAAC,cAAA,CAAAD,OAAA,CAAAE,YAAA,OACF;;;;;;IAvHVpB,EAAA,CAAAC,cAAA,cAAiE;IAQ7DD,EAAA,CAAAqB,UAAA,mBAAAC,6DAAA;MAAAtB,EAAA,CAAAuB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAF,OAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAC7B5B,EAAA,CAAAG,YAAA,EAAQ;IAGTH,EAAA,CAAAK,UAAA,IAAAwB,2CAAA,kBAuCM;IAGN7B,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAK,UAAA,IAAAyB,6CAAA,oBAOS;IAGT9B,EAAA,CAAAK,UAAA,IAAA0B,2CAAA,kBAKM;IAGN/B,EAAA,CAAAC,cAAA,cAAuC;IACrCD,EAAA,CAAAK,UAAA,IAAA2B,2CAAA,kBAIO;IACPhC,EAAA,CAAAK,UAAA,IAAA4B,2CAAA,kBAIO;IACTjC,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAEC;IAIOD,EAAA,CAAAE,SAAA,eAME;IACFF,EAAA,CAAAK,UAAA,KAAA6B,4CAAA,kBAIO;IACTlC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAA6C;IAAAD,EAAA,CAAAI,MAAA,IAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACjEH,EAAA,CAAAK,UAAA,KAAA8B,0CAAA,gBAKI;IACNnC,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAAyC;IAGrCD,EAAA,CAAAqB,UAAA,mBAAAe,+DAAA;MAAApC,EAAA,CAAAuB,aAAA,CAAAC,IAAA;MAAA,MAAAa,OAAA,GAAArC,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAU,OAAA,CAAAT,gBAAA,EAAkB;IAAA,EAAC;IAI5B5B,EAAA,CAAAE,SAAA,aAAgD;IAClDF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,eAAqD;IACrDF,EAAA,CAAAC,cAAA,gBAA6C;IAAAD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IArI5DH,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAsC,WAAA,mBAAAC,MAAA,CAAAC,YAAA,CAAqC;IAQpCxC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAQ,UAAA,UAAA+B,MAAA,CAAAC,YAAA,CAAmB;IA8CjBxC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,UAAA,SAAA+B,MAAA,CAAAE,cAAA,CAAoB;IASpBzC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,UAAA,UAAA+B,MAAA,CAAAE,cAAA,CAAqB;IASnBzC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,UAAA,SAAA+B,MAAA,CAAAE,cAAA,CAAoB;IAKpBzC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,UAAA,UAAA+B,MAAA,CAAAE,cAAA,CAAqB;IAelBzC,EAAA,CAAAO,SAAA,GAEC;IAFDP,EAAA,CAAAQ,UAAA,SAAA+B,MAAA,CAAA7B,gBAAA,kBAAA6B,MAAA,CAAA7B,gBAAA,CAAAC,KAAA,0CAAAX,EAAA,CAAAY,aAAA,CAEC,QAAA2B,MAAA,CAAA7B,gBAAA,kBAAA6B,MAAA,CAAA7B,gBAAA,CAAAG,QAAA;IAKAb,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAQ,UAAA,SAAA+B,MAAA,CAAAtB,WAAA,CAAiB;IAOlBjB,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAyB,MAAA,CAAA7B,gBAAA,kBAAA6B,MAAA,CAAA7B,gBAAA,CAAAG,QAAA,MACF;IAC6Cb,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAe,iBAAA,CAAAwB,MAAA,CAAAvB,UAAA,CAAgB;IAE1DhB,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAQ,UAAA,SAAA+B,MAAA,CAAAtB,WAAA,IAAAsB,MAAA,CAAAnB,YAAA,KAAqC;;;;;IAkE5CpB,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAM;;;;;IAIRH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,cAEO;IASTF,EAAA,CAAAG,YAAA,EAAM;;;;;IASNH,EAAA,CAAAC,cAAA,YAGC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAA4B,OAAA,CAAAvB,cAAA,CAAAuB,OAAA,CAAAtB,YAAA,OACF;;;;;IAGApB,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,SAAA,YAA+C;IAC/CF,EAAA,CAAAC,cAAA,eAA6C;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IApFpEH,EAAA,CAAAC,cAAA,cAGC;IAGGD,EAAA,CAAAE,SAAA,cAEO;IASTF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAgC;IAE5BD,EAAA,CAAAE,SAAA,cAIE;IASFF,EAAA,CAAAK,UAAA,IAAAsC,2CAAA,kBAKM;IACR3C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAK,UAAA,KAAAuC,4CAAA,kBAeM;IACR5C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA8B;IAE1BD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAkD;IAAAD,EAAA,CAAAI,MAAA,IAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACtEH,EAAA,CAAAK,UAAA,KAAAwC,0CAAA,gBAKI;IAGJ7C,EAAA,CAAAK,UAAA,KAAAyC,4CAAA,kBAUM;IACR9C,EAAA,CAAAG,YAAA,EAAM;;;;IAhEAH,EAAA,CAAAO,SAAA,GAAsE;IAAtEP,EAAA,CAAAQ,UAAA,SAAAuC,MAAA,CAAArC,gBAAA,kBAAAqC,MAAA,CAAArC,gBAAA,CAAAC,KAAA,0CAAAX,EAAA,CAAAY,aAAA,CAAsE,QAAAmC,MAAA,CAAArC,gBAAA,kBAAAqC,MAAA,CAAArC,gBAAA,CAAAG,QAAA;IAOtEb,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAsC,WAAA,mBAAAS,MAAA,CAAA9B,WAAA,CAAoC,iBAAA8B,MAAA,CAAA9B,WAAA;IAMnCjB,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAQ,UAAA,SAAAuC,MAAA,CAAA9B,WAAA,CAAiB;IASnBjB,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAQ,UAAA,SAAAuC,MAAA,CAAA9B,WAAA,CAAiB;IAoBlBjB,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAiC,MAAA,CAAArC,gBAAA,kBAAAqC,MAAA,CAAArC,gBAAA,CAAAG,QAAA,MACF;IACkDb,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAe,iBAAA,CAAAgC,MAAA,CAAA/B,UAAA,CAAgB;IAE/DhB,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAQ,UAAA,SAAAuC,MAAA,CAAA9B,WAAA,IAAA8B,MAAA,CAAA3B,YAAA,KAAqC;IAQrCpB,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAQ,UAAA,SAAAuC,MAAA,CAAA9B,WAAA,CAAiB;;;;;;IA4CpBjB,EAAA,CAAAC,cAAA,aAAyD;IAErDD,EAAA,CAAAqB,UAAA,mBAAA2B,+DAAA;MAAAhD,EAAA,CAAAuB,aAAA,CAAA0B,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAuB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAWvBnD,EAAA,CAAAE,SAAA,WAGK;IACPF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IAnBJH,EAAA,CAAAO,SAAA,GAIC;IAJDP,EAAA,CAAAoD,UAAA,EAAAC,MAAA,CAAAZ,cAAA,6IAIC;IACDzC,EAAA,CAAAQ,UAAA,UAAA6C,MAAA,CAAAZ,cAAA,+DAEC;IAICzC,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAoD,UAAA,CAAAC,MAAA,CAAAZ,cAAA,iCAAwD;IAO1DzC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAuC,MAAA,CAAAZ,cAAA,8DACF;;;;;IAkDFzC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAA4D;IAC5DF,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAI,MAAA,uBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAEvDH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAAuD;IACvDF,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAI,MAAA,uCAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAE7DH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAAsD;IACtDF,EAAA,CAAAC,cAAA,eAAoC;IAAAD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAM7DH,EAAA,CAAAC,cAAA,cAGC;IAOOD,EAAA,CAAAE,SAAA,cAME;IAIJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAI,MAAA,GAAmC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAI1EH,EAAA,CAAAC,cAAA,cAAwD;IAEpDD,EAAA,CAAAqB,UAAA,mBAAAiC,gEAAA;MAAAtD,EAAA,CAAAuB,aAAA,CAAAgC,IAAA;MAAA,MAAAC,OAAA,GAAAxD,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAA6B,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAItBzD,EAAA,CAAAE,SAAA,cAAsD;IACxDF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,mBAIC;IAHCD,EAAA,CAAAqB,UAAA,mBAAAqC,gEAAA;MAAA1D,EAAA,CAAAuB,aAAA,CAAAgC,IAAA;MAAA,MAAAI,OAAA,GAAA3D,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAgC,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAItB5D,EAAA,CAAAE,SAAA,YAGK;IACPF,EAAA,CAAAG,YAAA,EAAS;;;;IApCLH,EAAA,CAAAO,SAAA,GAEC;IAFDP,EAAA,CAAAQ,UAAA,SAAAqD,MAAA,CAAAnD,gBAAA,kBAAAmD,MAAA,CAAAnD,gBAAA,CAAAC,KAAA,0CAAAX,EAAA,CAAAY,aAAA,CAEC,QAAAiD,MAAA,CAAAnD,gBAAA,kBAAAmD,MAAA,CAAAnD,gBAAA,CAAAG,QAAA;IAUHb,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAA+C,MAAA,CAAAnD,gBAAA,kBAAAmD,MAAA,CAAAnD,gBAAA,CAAAG,QAAA,MACF;IACiCb,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAc,kBAAA,KAAA+C,MAAA,CAAAC,gBAAA,kBAAmC;IAoBhE9D,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAoD,UAAA,CAAAS,MAAA,CAAAE,QAAA,uCAAwD;;;;;IAQlE/D,EAAA,CAAAE,SAAA,sBAAkE;;;;;IAIpEF,EAAA,CAAAC,cAAA,eAGC;IAIGD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAApBH,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAe,iBAAA,CAAAiD,MAAA,CAAAhD,UAAA,CAAgB;;;ADtalD,OAAM,MAAOiD,sBAAsB;EAwCjCC,YACUC,cAA8B,EAC9BC,YAA0B,EAC1BC,GAAsB;IAFtB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IA1CJ,KAAAC,UAAU,GAAgB,IAAI;IAC9B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAR,QAAQ,GAA6B,IAAI;IACzC,KAAArD,gBAAgB,GAAgB,IAAI;IAEnC,KAAA8D,SAAS,GAAG,IAAI3E,YAAY,EAAQ;IACpC,KAAA4E,YAAY,GAAG,IAAI5E,YAAY,EAAQ;IACvC,KAAA6E,YAAY,GAAG,IAAI7E,YAAY,EAAQ;IAQjD;IACA,KAAAoB,WAAW,GAAG,KAAK;IACnB,KAAA0D,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAxD,YAAY,GAAG,CAAC;IAChB,KAAAJ,UAAU,GAAG,cAAc;IAE3B;IACA,KAAA6D,OAAO,GAAG,KAAK;IACf,KAAApC,cAAc,GAAG,IAAI;IACrB,KAAAqC,WAAW,GAAG,KAAK;IACnB,KAAAC,YAAY,GAAG,KAAK;IAEpB;IACA,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAxC,YAAY,GAAuB,IAAI;IAEvC;IACQ,KAAAyC,SAAS,GAAQ,IAAI;IACrB,KAAAC,aAAa,GAAG,IAAIpF,YAAY,EAAE;IAE1C;IACA,KAAAC,QAAQ,GAAGA,QAAQ;EAMhB;EAEHoF,QAAQA,CAAA;IACN,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACC,OAAO,EAAE;EAChB;EAEQH,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAACF,aAAa,CAACM,GAAG,CACpB,IAAI,CAACrB,cAAc,CAACsB,WAAW,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAACtB,UAAU,GAAGsB,IAAI;UACtB,IAAI,CAACC,gBAAgB,EAAE;UACvB,IAAI,CAACxB,GAAG,CAACyB,aAAa,EAAE;;MAE5B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC9D;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACb,aAAa,CAACM,GAAG,CACpB,IAAI,CAACrB,cAAc,CAAC8B,YAAY,CAACP,SAAS,CAAC;MACzCC,IAAI,EAAGO,MAAM,IAAI;QACf,IAAI,CAAClB,WAAW,GAAGkB,MAAM;QACzB,IAAI,CAACC,iBAAiB,EAAE;MAC1B;KACD,CAAC,CACH;IAED,IAAI,CAACjB,aAAa,CAACM,GAAG,CACpB,IAAI,CAACrB,cAAc,CAACiC,aAAa,CAACV,SAAS,CAAC;MAC1CC,IAAI,EAAGO,MAAM,IAAI;QACf,IAAI,CAAC1D,YAAY,GAAG0D,MAAM;QAC1B,IAAI,CAACG,kBAAkB,EAAE;MAC3B;KACD,CAAC,CACH;EACH;EAEchB,sBAAsBA,CAAA;IAAA,IAAAiB,KAAA;IAAA,OAAAC,iBAAA;MAClC,IAAI,CAACD,KAAI,CAAChC,UAAU,EAAE;MAEtB,IAAI;QACF,MAAMkC,WAAW,GAAG;UAClBC,KAAK,EAAE,IAAI;UACXC,KAAK,EAAEJ,KAAI,CAACvC,QAAQ,KAAK;SAC1B;QAEDuC,KAAI,CAACtB,WAAW,SAAS2B,SAAS,CAACC,YAAY,CAACC,YAAY,CAACL,WAAW,CAAC;QACzEF,KAAI,CAACH,iBAAiB,EAAE;QAExBH,OAAO,CAACc,GAAG,CAAC,6BAA6B,CAAC;OAC3C,CAAC,OAAOf,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDO,KAAI,CAAClC,YAAY,CAAC2C,SAAS,CAAC,kCAAkC,CAAC;;IAChE;EACH;EAEQZ,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACnB,WAAW,IAAI,IAAI,CAACgC,aAAa,EAAEC,aAAa,EAAE;MACzD,IAAI,CAACD,aAAa,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI,CAAClC,WAAW;MAC7D,IAAI,CAACgC,aAAa,CAACC,aAAa,CAACE,KAAK,GAAG,IAAI,CAAC,CAAC;;EAEnD;;EAEQd,kBAAkBA,CAAA;IACxB,IAAI,IAAI,CAAC7D,YAAY,IAAI,IAAI,CAAC4E,cAAc,EAAEH,aAAa,EAAE;MAC3D,IAAI,CAACG,cAAc,CAACH,aAAa,CAACC,SAAS,GAAG,IAAI,CAAC1E,YAAY;;EAEnE;EAEQqD,gBAAgBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACvB,UAAU,EAAE;IAEtB,QAAQ,IAAI,CAACA,UAAU,CAAC+C,MAAgB;MACtC,KAAK,SAAS;QACZ,IAAI,CAACrG,UAAU,GAAG,IAAI,CAAC2D,UAAU,GAC7B,kBAAkB,GAClB,mBAAmB;QACvB;MACF,KAAK,UAAU;QACb,IAAI,CAAC3D,UAAU,GAAG,cAAc;QAChC;MACF,KAAK,WAAW;QACd,IAAI,CAACA,UAAU,GAAG,UAAU;QAC5B,IAAI,CAACC,WAAW,GAAG,IAAI;QACvB,IAAI,CAACqG,cAAc,EAAE;QACrB;MACF,KAAK,OAAO;QACV,IAAI,CAACtG,UAAU,GAAG,eAAe;QACjC,IAAI,CAACuG,OAAO,EAAE;QACd;MACF,KAAK,UAAU;QACb,IAAI,CAACvG,UAAU,GAAG,cAAc;QAChC,IAAI,CAACuG,OAAO,EAAE;QACd;MACF;QACE,IAAI,CAACvG,UAAU,GAAG,aAAa;;EAErC;EAEQsG,cAAcA,CAAA;IACpB,IAAI,CAAClG,YAAY,GAAG,CAAC;IACrB,IAAI,CAAC6D,SAAS,GAAGuC,WAAW,CAAC,MAAK;MAChC,IAAI,CAACpG,YAAY,EAAE;MACnB,IAAI,CAACiD,GAAG,CAACyB,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACA2B,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACnD,UAAU,EAAE;IAEtB,IAAI,CAACO,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,IAAI,IAAI,CAACG,WAAW,EAAE;MACpB,MAAM0C,WAAW,GAAG,IAAI,CAAC1C,WAAW,CAAC2C,cAAc,EAAE;MACrDD,WAAW,CAACE,OAAO,CAAEC,KAAK,IAAI;QAC5BA,KAAK,CAACC,OAAO,GAAG,CAAC,IAAI,CAACjD,OAAO;MAC/B,CAAC,CAAC;;IAGJ;IACA,IAAI,CAACV,cAAc,CAChB4D,WAAW,CAAC,IAAI,CAACzD,UAAU,CAAC0D,EAAE,EAAEC,SAAS,EAAE,CAAC,IAAI,CAACpD,OAAO,CAAC,CACzDa,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvB,YAAY,CAAC8D,WAAW,CAC3B,IAAI,CAACrD,OAAO,GAAG,aAAa,GAAG,cAAc,CAC9C;MACH,CAAC;MACDkB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA,IAAI,CAAClB,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;QAC5B,IAAI,IAAI,CAACG,WAAW,EAAE;UACpB,MAAM0C,WAAW,GAAG,IAAI,CAAC1C,WAAW,CAAC2C,cAAc,EAAE;UACrDD,WAAW,CAACE,OAAO,CAAEC,KAAK,IAAI;YAC5BA,KAAK,CAACC,OAAO,GAAG,CAAC,IAAI,CAACjD,OAAO;UAC/B,CAAC,CAAC;;MAEN;KACD,CAAC;EACN;EAEA1B,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACmB,UAAU,IAAI,IAAI,CAACP,QAAQ,KAAK,OAAO,EAAE;IAEnD,IAAI,CAACtB,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C;IACA,IAAI,IAAI,CAACuC,WAAW,EAAE;MACpB,MAAMmD,WAAW,GAAG,IAAI,CAACnD,WAAW,CAACoD,cAAc,EAAE;MACrDD,WAAW,CAACP,OAAO,CAAEC,KAAK,IAAI;QAC5BA,KAAK,CAACC,OAAO,GAAG,IAAI,CAACrF,cAAc;MACrC,CAAC,CAAC;;IAGJ;IACA,IAAI,CAAC0B,cAAc,CAChB4D,WAAW,CAAC,IAAI,CAACzD,UAAU,CAAC0D,EAAE,EAAE,IAAI,CAACvF,cAAc,EAAEwF,SAAS,CAAC,CAC/DvC,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvB,YAAY,CAAC8D,WAAW,CAC3B,IAAI,CAACzF,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDsD,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;QACA,IAAI,CAACtD,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,IAAI,CAACuC,WAAW,EAAE;UACpB,MAAMmD,WAAW,GAAG,IAAI,CAACnD,WAAW,CAACoD,cAAc,EAAE;UACrDD,WAAW,CAACP,OAAO,CAAEC,KAAK,IAAI;YAC5BA,KAAK,CAACC,OAAO,GAAG,IAAI,CAACrF,cAAc;UACrC,CAAC,CAAC;;MAEN;KACD,CAAC;EACN;EAEA4F,aAAaA,CAAA;IACX,IAAI,CAACvD,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,IAAI,CAACsC,cAAc,EAAEH,aAAa,EAAE;MACtC;MACA,IAAI,CAACG,cAAc,CAACH,aAAa,CAACqB,MAAM,GAAG,IAAI,CAACxD,WAAW,GAAG,CAAC,GAAG,GAAG;;IAGvE,IAAI,CAACV,YAAY,CAAC8D,WAAW,CAC3B,IAAI,CAACpD,WAAW,GAAG,qBAAqB,GAAG,wBAAwB,CACpE;EACH;EAEAlD,gBAAgBA,CAAA;IACd,IAAI,CAACmD,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IAEtC,IAAI,IAAI,CAACA,YAAY,IAAI,IAAI,CAACqC,cAAc,EAAEH,aAAa,EAAE;MAC3D,IAAI,CAACG,cAAc,CAACH,aAAa,CAACsB,iBAAiB,GAAE,CAAE;KACxD,MAAM,IAAIC,QAAQ,CAACC,iBAAiB,EAAE;MACrCD,QAAQ,CAACE,cAAc,GAAE,CAAE;;EAE/B;EAEA9E,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACU,UAAU,EAAE;IAEtB,IAAI,CAACG,YAAY,CAACkE,IAAI,CAAC,IAAI,CAACrE,UAAU,CAAC;EACzC;EAEAb,UAAUA,CAAA;IACR,IAAI,CAACiB,YAAY,CAACiE,IAAI,EAAE;EAC1B;EAEApB,OAAOA,CAAA;IACL,IAAI,CAAChC,OAAO,EAAE;IACd,IAAI,CAACf,SAAS,CAACmE,IAAI,EAAE;EACvB;EAEQpD,OAAOA,CAAA;IACb;IACA,IAAI,IAAI,CAACN,SAAS,EAAE;MAClB2D,aAAa,CAAC,IAAI,CAAC3D,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB;IACA,IAAI,IAAI,CAACD,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC6D,SAAS,EAAE,CAACjB,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACiB,IAAI,EAAE,CAAC;MAC7D,IAAI,CAAC9D,WAAW,GAAG,IAAI;;IAGzB,IAAI,IAAI,CAACxC,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACqG,SAAS,EAAE,CAACjB,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACiB,IAAI,EAAE,CAAC;MAC9D,IAAI,CAACtG,YAAY,GAAG,IAAI;;IAG1B;IACA,IAAI,CAAC0C,aAAa,CAAC6D,WAAW,EAAE;IAEhC;IACA,IAAI,CAAC9H,WAAW,GAAG,KAAK;IACxB,IAAI,CAACG,YAAY,GAAG,CAAC;IACrB,IAAI,CAACyD,OAAO,GAAG,KAAK;IACpB,IAAI,CAACpC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACqC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,YAAY,GAAG,KAAK;EAC3B;EAEA;EACA5D,cAAcA,CAAC6H,OAAe;IAC5B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAC/CC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACvB;EAEAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACxF,QAAQ,KAAK,OAAO,GAAG,UAAU,GAAG,MAAM;EACxD;EAEAD,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACC,QAAQ,KAAK,OAAO,GAAG,aAAa,GAAG,aAAa;EAClE;;;uBA9TWE,sBAAsB,EAAAjE,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA1J,EAAA,CAAAwJ,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAA5J,EAAA,CAAAwJ,iBAAA,CAAAxJ,EAAA,CAAA6J,iBAAA;IAAA;EAAA;;;YAAtB5F,sBAAsB;MAAA6F,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;UCpBnCjK,EAAA,CAAAC,cAAA,aAQC;UAECD,EAAA,CAAAK,UAAA,IAAA8J,qCAAA,mBA+IM;UAGNnK,EAAA,CAAAK,UAAA,IAAA+J,qCAAA,mBAwFM;UAGNpK,EAAA,CAAAC,cAAA,aAEC;UAKOD,EAAA,CAAAqB,UAAA,mBAAAgJ,wDAAA;YAAA,OAASH,GAAA,CAAAzC,UAAA,EAAY;UAAA,EAAC;UAStBzH,EAAA,CAAAE,SAAA,WAGK;UACPF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,aAEC;UACCD,EAAA,CAAAI,MAAA,GACF;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAK,UAAA,KAAAiK,sCAAA,iBAwBM;UAGNtK,EAAA,CAAAC,cAAA,cAA4B;UAExBD,EAAA,CAAAqB,UAAA,mBAAAkJ,yDAAA;YAAA,OAASL,GAAA,CAAA7B,aAAA,EAAe;UAAA,EAAC;UAWzBrI,EAAA,CAAAE,SAAA,YAGK;UACPF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,cAEC;UACCD,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAA4B;UAExBD,EAAA,CAAAqB,UAAA,mBAAAmJ,yDAAA;YAAA,OAASN,GAAA,CAAA3C,OAAA,EAAS;UAAA,EAAC;UAInBvH,EAAA,CAAAE,SAAA,aAAsD;UACxDF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,cAEC;UACCD,EAAA,CAAAI,MAAA,oBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAA6D;UAC3DD,EAAA,CAAAK,UAAA,KAAAoK,sCAAA,kBAMM;UACNzK,EAAA,CAAAK,UAAA,KAAAqK,sCAAA,kBAMM;UACN1K,EAAA,CAAAK,UAAA,KAAAsK,sCAAA,kBAMM;UACR3K,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAK,UAAA,KAAAuK,sCAAA,mBAkDM;UAGN5K,EAAA,CAAAK,UAAA,KAAAwK,wCAAA,oBAAkE;UACpE7K,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAK,UAAA,KAAAyK,sCAAA,kBAYM;;;UA1bJ9K,EAAA,CAAAsC,WAAA,gBAAA4H,GAAA,CAAA3F,SAAA,CAA+B,eAAA2F,GAAA,CAAA3F,SAAA,0BAAA2F,GAAA,CAAA3F,SAAA;UAQzBvE,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAQ,UAAA,SAAA0J,GAAA,CAAAnG,QAAA,aAA0B;UAmJ7B/D,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAQ,UAAA,SAAA0J,GAAA,CAAAnG,QAAA,aAA0B;UAmGrB/D,EAAA,CAAAO,SAAA,GAIC;UAJDP,EAAA,CAAAoD,UAAA,CAAA8G,GAAA,CAAArF,OAAA,6IAIC;UACD7E,EAAA,CAAAQ,UAAA,UAAA0J,GAAA,CAAArF,OAAA,0CAA0D;UAIxD7E,EAAA,CAAAO,SAAA,GAA2D;UAA3DP,EAAA,CAAAoD,UAAA,CAAA8G,GAAA,CAAArF,OAAA,2CAA2D;UAO7D7E,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAc,kBAAA,MAAAoJ,GAAA,CAAArF,OAAA,yCACF;UAII7E,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAQ,UAAA,SAAA0J,GAAA,CAAAnG,QAAA,aAA0B;UA+B5B/D,EAAA,CAAAO,SAAA,GAIC;UAJDP,EAAA,CAAAoD,UAAA,CAAA8G,GAAA,CAAApF,WAAA,iJAIC;UACD9E,EAAA,CAAAQ,UAAA,UAAA0J,GAAA,CAAApF,WAAA,2DAEC;UAIC9E,EAAA,CAAAO,SAAA,GAAyD;UAAzDP,EAAA,CAAAoD,UAAA,CAAA8G,GAAA,CAAApF,WAAA,qCAAyD;UAO3D9E,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAc,kBAAA,MAAAoJ,GAAA,CAAApF,WAAA,gEACF;UAwBC9E,EAAA,CAAAO,SAAA,GAAa;UAAbP,EAAA,CAAAQ,UAAA,SAAA0J,GAAA,CAAArF,OAAA,CAAa;UAOb7E,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAAQ,UAAA,UAAA0J,GAAA,CAAAzH,cAAA,IAAAyH,GAAA,CAAAnG,QAAA,aAA6C;UAO7C/D,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAQ,UAAA,SAAA0J,GAAA,CAAApF,WAAA,CAAiB;UAWrB9E,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAQ,UAAA,SAAA0J,GAAA,CAAAvF,UAAA,KAAAuF,GAAA,CAAAjJ,WAAA,CAAgC;UAoDLjB,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAQ,UAAA,SAAA0J,GAAA,CAAAnG,QAAA,aAA0B;UAKvD/D,EAAA,CAAAO,SAAA,GAA8C;UAA9CP,EAAA,CAAAQ,UAAA,SAAA0J,GAAA,CAAA3F,SAAA,KAAA2F,GAAA,CAAAjJ,WAAA,KAAAiJ,GAAA,CAAAvF,UAAA,CAA8C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}