{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { trigger, style, transition, animate } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/gemini-chat.service\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"messageInput\"];\nfunction AiChatbotComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function AiChatbotComponent_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleFullscreen());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AiChatbotComponent__svg_svg_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 11);\n    i0.ɵɵelement(1, \"path\", 12);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AiChatbotComponent__svg_svg_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 13);\n    i0.ɵɵelement(1, \"path\", 14);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AiChatbotComponent_div_9__svg_path_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 52);\n  }\n}\nfunction AiChatbotComponent_div_9__svg_path_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 53);\n  }\n}\nfunction AiChatbotComponent_div_9_div_27_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(message_r13.content);\n  }\n}\nfunction AiChatbotComponent_div_9_div_27_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61);\n    i0.ɵɵelement(2, \"div\", 62)(3, \"div\", 63)(4, \"div\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 65);\n    i0.ɵɵtext(6, \"L'IA r\\u00E9fl\\u00E9chit...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"text-right text-white/80\": a0,\n    \"text-[#6d6870] dark:text-[#a0a0a0]\": a1\n  };\n};\nfunction AiChatbotComponent_div_9_div_27_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c2, message_r13.isUser, !message_r13.isUser));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.formatTime(message_r13.timestamp), \" \");\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    \"justify-end\": a0\n  };\n};\nconst _c4 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"max-w-sm\": a0,\n    \"max-w-2xl\": a1,\n    \"bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] text-white shadow-lg\": a2,\n    \"bg-[#edf1f4]/80 dark:bg-[#2a2a2a]/80 text-[#3d4a85] dark:text-[#6d78c9] backdrop-blur-sm border border-[#edf1f4] dark:border-[#2a2a2a]\": a3,\n    \"bg-[#edf1f4]/80 dark:bg-[#2a2a2a]/80 backdrop-blur-sm\": a4\n  };\n};\nfunction AiChatbotComponent_div_9_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtemplate(2, AiChatbotComponent_div_9_div_27_div_2_Template, 2, 1, \"div\", 56);\n    i0.ɵɵtemplate(3, AiChatbotComponent_div_9_div_27_div_3_Template, 7, 0, \"div\", 57);\n    i0.ɵɵtemplate(4, AiChatbotComponent_div_9_div_27_div_4_Template, 2, 5, \"div\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r13 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c3, message_r13.isUser));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(7, _c4, !ctx_r9.isFullscreen, ctx_r9.isFullscreen, message_r13.isUser, !message_r13.isUser && !message_r13.isTyping, message_r13.isTyping));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !message_r13.isTyping);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r13.isTyping);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !message_r13.isTyping);\n  }\n}\nfunction AiChatbotComponent_div_9_div_28_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function AiChatbotComponent_div_9_div_28_button_8_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r22);\n      const quickMsg_r20 = restoredCtx.$implicit;\n      const ctx_r21 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r21.sendQuickMessage(quickMsg_r20));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const quickMsg_r20 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", quickMsg_r20, \" \");\n  }\n}\nfunction AiChatbotComponent_div_9_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68)(2, \"div\", 69);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 70);\n    i0.ɵɵelement(4, \"path\", 71);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"span\", 72);\n    i0.ɵɵtext(6, \"Suggestions rapides :\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 73);\n    i0.ɵɵtemplate(8, AiChatbotComponent_div_9_div_28_button_8_Template, 2, 1, \"button\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.quickMessages);\n  }\n}\nfunction AiChatbotComponent_div_9_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"div\", 77);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c5 = function (a0, a1) {\n  return {\n    \"fixed inset-4 w-auto h-auto\": a0,\n    \"absolute bottom-16 right-0 w-96 h-[32rem]\": a1\n  };\n};\nconst _c6 = function (a0) {\n  return {\n    \"height\": a0\n  };\n};\nfunction AiChatbotComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17)(3, \"div\", 18)(4, \"div\", 19);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 20);\n    i0.ɵɵelement(6, \"path\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"div\", 22)(8, \"h3\", 23);\n    i0.ɵɵtext(9, \"Assistant IA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 24);\n    i0.ɵɵelement(11, \"div\", 25);\n    i0.ɵɵelementStart(12, \"span\", 26);\n    i0.ɵɵtext(13, \"En ligne\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"div\", 27)(15, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function AiChatbotComponent_div_9_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.toggleFullscreen());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(16, \"svg\", 29);\n    i0.ɵɵtemplate(17, AiChatbotComponent_div_9__svg_path_17_Template, 1, 0, \"path\", 30);\n    i0.ɵɵtemplate(18, AiChatbotComponent_div_9__svg_path_18_Template, 1, 0, \"path\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(19, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function AiChatbotComponent_div_9_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.clearChat());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(20, \"svg\", 29);\n    i0.ɵɵelement(21, \"path\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(22, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function AiChatbotComponent_div_9_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.closeChat());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(23, \"svg\", 29);\n    i0.ɵɵelement(24, \"path\", 14);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(25, \"div\", 35, 36);\n    i0.ɵɵtemplate(27, AiChatbotComponent_div_9_div_27_Template, 5, 13, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, AiChatbotComponent_div_9_div_28_Template, 9, 1, \"div\", 38);\n    i0.ɵɵelementStart(29, \"div\", 39)(30, \"form\", 40);\n    i0.ɵɵlistener(\"ngSubmit\", function AiChatbotComponent_div_9_Template_form_ngSubmit_30_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.sendMessage());\n    });\n    i0.ɵɵelementStart(31, \"div\", 41)(32, \"textarea\", 42, 43);\n    i0.ɵɵlistener(\"keypress\", function AiChatbotComponent_div_9_Template_textarea_keypress_32_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.onKeyPress($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, AiChatbotComponent_div_9_div_34_Template, 2, 0, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 45);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(36, \"svg\", 46);\n    i0.ɵɵelement(37, \"path\", 47);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(38, \"div\", 48)(39, \"span\");\n    i0.ɵɵtext(40, \"Appuyez sur Entr\\u00E9e pour envoyer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 49)(42, \"div\", 50)(43, \"span\", 51);\n    i0.ɵɵtext(44, \"\\u23CE\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"span\");\n    i0.ɵɵtext(46, \"Envoyer\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(13, _c5, ctx_r3.isFullscreen, !ctx_r3.isFullscreen))(\"@slideInOut\", undefined);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"title\", ctx_r3.isFullscreen ? \"Mode fen\\u00EAtre\" : \"Plein \\u00E9cran\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isFullscreen);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isFullscreen);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(16, _c6, ctx_r3.isFullscreen ? \"calc(100vh - 280px)\" : \"calc(32rem - 200px)\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.messages)(\"ngForTrackBy\", ctx_r3.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.messages.length <= 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.chatForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.chatForm.invalid || ctx_r3.isLoading);\n  }\n}\nexport class AiChatbotComponent {\n  constructor(fb, geminiService) {\n    this.fb = fb;\n    this.geminiService = geminiService;\n    this.messages = [];\n    this.isOpen = false;\n    this.isLoading = false;\n    this.isFullscreen = false;\n    this.destroy$ = new Subject();\n    this.shouldScrollToBottom = false;\n    // Suggestions de messages rapides\n    this.quickMessages = ['Comment soumettre un projet ?', 'Comment voir mes notes ?', 'Problème de connexion', \"Aide pour l'interface\"];\n    this.chatForm = this.fb.group({\n      message: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  ngOnInit() {\n    // S'abonner aux messages\n    this.geminiService.messages$.pipe(takeUntil(this.destroy$)).subscribe(messages => {\n      this.messages = messages;\n      this.shouldScrollToBottom = true;\n    });\n    // S'abonner à l'état d'ouverture\n    this.geminiService.isOpen$.pipe(takeUntil(this.destroy$)).subscribe(isOpen => {\n      this.isOpen = isOpen;\n      if (isOpen) {\n        setTimeout(() => {\n          this.focusInput();\n          this.scrollToBottom();\n        }, 100);\n      }\n    });\n  }\n  ngAfterViewChecked() {\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  toggleChat() {\n    this.geminiService.toggleChat();\n  }\n  closeChat() {\n    this.geminiService.closeChat();\n    this.isFullscreen = false; // Reset fullscreen when closing\n  }\n\n  toggleFullscreen() {\n    this.isFullscreen = !this.isFullscreen;\n    // Scroll to bottom after fullscreen toggle\n    setTimeout(() => {\n      this.scrollToBottom();\n    }, 100);\n  }\n  sendMessage() {\n    if (this.chatForm.valid && !this.isLoading) {\n      const message = this.chatForm.get('message')?.value?.trim();\n      if (message) {\n        this.isLoading = true;\n        this.geminiService.sendMessage(message).pipe(takeUntil(this.destroy$)).subscribe({\n          next: () => {\n            this.isLoading = false;\n            this.chatForm.reset();\n            this.focusInput();\n          },\n          error: () => {\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  clearChat() {\n    this.geminiService.clearChat();\n  }\n  scrollToBottom() {\n    try {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    } catch (err) {\n      console.error('Erreur lors du scroll:', err);\n    }\n  }\n  focusInput() {\n    try {\n      if (this.messageInput) {\n        this.messageInput.nativeElement.focus();\n      }\n    } catch (err) {\n      console.error('Erreur lors du focus:', err);\n    }\n  }\n  formatTime(date) {\n    return new Intl.DateTimeFormat('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    }).format(date);\n  }\n  sendQuickMessage(message) {\n    this.chatForm.patchValue({\n      message\n    });\n    this.sendMessage();\n  }\n  trackByMessageId(index, message) {\n    return message.id;\n  }\n  static {\n    this.ɵfac = function AiChatbotComponent_Factory(t) {\n      return new (t || AiChatbotComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.GeminiChatService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AiChatbotComponent,\n      selectors: [[\"app-ai-chatbot\"]],\n      viewQuery: function AiChatbotComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messageInput = _t.first);\n        }\n      },\n      decls: 10,\n      vars: 6,\n      consts: [[\"class\", \"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300\", 3, \"click\", 4, \"ngIf\"], [1, \"fixed\", \"bottom-6\", \"right-6\", \"z-50\"], [1, \"group\", \"relative\", \"w-16\", \"h-16\", \"bg-gradient-to-br\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"text-white\", \"rounded-2xl\", \"shadow-xl\", \"hover:shadow-2xl\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"focus:outline-none\", \"focus:ring-4\", \"focus:ring-[#4f5fad]/30\", \"dark:focus:ring-[#6d78c9]/30\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-br\", \"from-[#4f5fad]/40\", \"to-[#3d4a85]/40\", \"rounded-2xl\", \"opacity-0\", \"group-hover:opacity-30\", \"blur-xl\", \"transition-opacity\", \"duration-300\", \"scale-150\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\", \"justify-center\", \"w-full\", \"h-full\"], [\"class\", \"w-8 h-8 transition-transform duration-300 group-hover:scale-110\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [\"class\", \"w-7 h-7 transition-transform duration-300 group-hover:rotate-90\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"absolute\", \"-top-2\", \"-right-2\", \"w-5\", \"h-5\", \"bg-gradient-to-r\", \"from-green-400\", \"to-green-500\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"animate-pulse\"], [1, \"w-2\", \"h-2\", \"bg-white\", \"rounded-full\"], [\"class\", \"bg-white/90 dark:bg-[#1e1e1e]/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-[#edf1f4]/50 dark:border-[#2a2a2a]/50 overflow-hidden transform transition-all duration-300 ease-out z-50\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"fixed\", \"inset-0\", \"bg-black/50\", \"backdrop-blur-sm\", \"z-40\", \"transition-opacity\", \"duration-300\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"transition-transform\", \"duration-300\", \"group-hover:scale-110\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-7\", \"h-7\", \"transition-transform\", \"duration-300\", \"group-hover:rotate-90\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"bg-white/90\", \"dark:bg-[#1e1e1e]/90\", \"backdrop-blur-xl\", \"rounded-2xl\", \"shadow-2xl\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]/50\", \"overflow-hidden\", \"transform\", \"transition-all\", \"duration-300\", \"ease-out\", \"z-50\", 3, \"ngClass\"], [1, \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"px-4\", \"py-3\", \"text-white\", \"rounded-t-2xl\"], [1, \"flex\", \"items-center\", \"justify-between\", \"gap-3\"], [1, \"flex\", \"items-center\", \"gap-3\", \"flex-1\", \"min-w-0\"], [1, \"w-8\", \"h-8\", \"bg-white/20\", \"rounded-lg\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"d\", \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"], [1, \"flex-1\", \"min-w-0\"], [1, \"text-sm\", \"font-semibold\", \"text-white\", \"truncate\"], [1, \"flex\", \"items-center\", \"gap-1.5\"], [1, \"w-1.5\", \"h-1.5\", \"bg-green-400\", \"rounded-full\"], [1, \"text-xs\", \"text-white/80\"], [1, \"flex\", \"items-center\", \"gap-1\"], [1, \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-white/20\", \"rounded-lg\", \"transition-colors\", 3, \"title\", \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\", 4, \"ngIf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 9V4.5M9 9H4.5M9 9L3.5 3.5M15 9h4.5M15 9V4.5M15 9l5.5-5.5M9 15v4.5M9 15H4.5M9 15l-5.5 5.5M15 15h4.5M15 15v4.5m0-4.5l5.5 5.5\", 4, \"ngIf\"], [\"title\", \"Nouvelle conversation\", 1, \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-white/20\", \"rounded-lg\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"], [\"title\", \"Fermer le chat\", 1, \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-white/20\", \"rounded-lg\", \"transition-colors\", 3, \"click\"], [1, \"flex-1\", \"overflow-y-auto\", \"p-6\", \"space-y-4\", \"messages-container\", 3, \"ngStyle\"], [\"messagesContainer\", \"\"], [\"class\", \"flex\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"px-6 pb-4\", 4, \"ngIf\"], [1, \"p-6\", \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]/50\", \"bg-[#edf1f4]/30\", \"dark:bg-[#2a2a2a]/30\"], [1, \"flex\", \"items-end\", \"space-x-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex-1\", \"relative\"], [\"formControlName\", \"message\", \"placeholder\", \"Posez votre question \\u00E0 l'assistant IA...\", \"rows\", \"1\", 1, \"w-full\", \"px-4\", \"py-3\", \"pr-12\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-2\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"rounded-2xl\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-4\", \"focus:ring-[#4f5fad]/10\", \"dark:focus:ring-[#6d78c9]/20\", \"text-base\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"placeholder-[#6d6870]\", \"dark:placeholder-[#a0a0a0]\", \"resize-none\", \"transition-all\", \"duration-200\", \"font-medium\", \"leading-relaxed\", 2, \"min-height\", \"48px\", \"max-height\", \"120px\", 3, \"disabled\", \"keypress\"], [\"messageInput\", \"\"], [\"class\", \"absolute right-4 bottom-3\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"p-3\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"hover:shadow-lg\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"text-white\", \"rounded-2xl\", \"transition-all\", \"duration-200\", \"hover:scale-105\", \"focus:outline-none\", \"focus:ring-4\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"group\", 3, \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"group-hover:translate-x-0.5\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mt-3\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"px-2\", \"py-1\", \"rounded\"], [1, \"font-mono\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 9V4.5M9 9H4.5M9 9L3.5 3.5M15 9h4.5M15 9V4.5M15 9l5.5-5.5M9 15v4.5M9 15H4.5M9 15l-5.5 5.5M15 15h4.5M15 15v4.5m0-4.5l5.5 5.5\"], [1, \"flex\", 3, \"ngClass\"], [1, \"px-4\", \"py-3\", \"rounded-2xl\", \"text-base\", \"leading-relaxed\", \"font-medium\", 3, \"ngClass\"], [\"class\", \"whitespace-pre-wrap leading-relaxed\", 4, \"ngIf\"], [\"class\", \"flex items-center space-x-3 py-2\", 4, \"ngIf\"], [\"class\", \"text-xs opacity-75 mt-2 font-medium\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"whitespace-pre-wrap\", \"leading-relaxed\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"py-2\"], [1, \"flex\", \"space-x-1\"], [1, \"w-3\", \"h-3\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0ms\"], [1, \"w-3\", \"h-3\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"150ms\"], [1, \"w-3\", \"h-3\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"300ms\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"font-medium\"], [1, \"text-xs\", \"opacity-75\", \"mt-2\", \"font-medium\", 3, \"ngClass\"], [1, \"px-6\", \"pb-4\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mb-4\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"p-1.5\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 10V3L4 14h7v7l9-11h-7z\"], [1, \"text-sm\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [1, \"grid\", \"grid-cols-1\", \"gap-2\"], [\"class\", \"text-sm px-4 py-3 bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 text-[#3d4a85] dark:text-[#6d78c9] rounded-xl hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 hover:border-[#4f5fad] dark:hover:border-[#6d78c9] border border-transparent transition-all duration-200 font-medium text-left hover:scale-105 hover:shadow-md\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-sm\", \"px-4\", \"py-3\", \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"rounded-xl\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"hover:border-[#4f5fad]\", \"dark:hover:border-[#6d78c9]\", \"border\", \"border-transparent\", \"transition-all\", \"duration-200\", \"font-medium\", \"text-left\", \"hover:scale-105\", \"hover:shadow-md\", 3, \"click\"], [1, \"absolute\", \"right-4\", \"bottom-3\"], [1, \"w-5\", \"h-5\", \"border-2\", \"border-[#4f5fad]/30\", \"dark:border-[#6d78c9]/30\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"]],\n      template: function AiChatbotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AiChatbotComponent_div_0_Template, 1, 0, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function AiChatbotComponent_Template_button_click_2_listener() {\n            return ctx.toggleChat();\n          });\n          i0.ɵɵelement(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵtemplate(5, AiChatbotComponent__svg_svg_5_Template, 2, 0, \"svg\", 5);\n          i0.ɵɵtemplate(6, AiChatbotComponent__svg_svg_6_Template, 2, 0, \"svg\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 7);\n          i0.ɵɵelement(8, \"div\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, AiChatbotComponent_div_9_Template, 47, 18, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen && ctx.isFullscreen);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"rotate-180\", ctx.isOpen);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isOpen);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgStyle, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"\\n\\n@keyframes _ngcontent-%COMP%_slideInOut {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px) scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 80%, 100% {\\n    transform: scale(0);\\n  }\\n  40% {\\n    transform: scale(1);\\n  }\\n}\\n\\n.animate-bounce-dot[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_bounce 1.4s infinite ease-in-out both;\\n}\\n\\n\\n\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(156, 163, 175, 0.5);\\n  border-radius: 2px;\\n}\\n\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(156, 163, 175, 0.7);\\n}\\n\\n\\n\\n.dark[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(75, 85, 99, 0.5);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(75, 85, 99, 0.7);\\n}\\n\\n\\n\\n.floating-button[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.floating-button[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n.notification-badge[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n\\n\\n\\n.chat-window[_ngcontent-%COMP%] {\\n  backdrop-filter: blur(16px);\\n  -webkit-backdrop-filter: blur(16px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n\\n\\n.message-enter[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_messageSlideIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_messageSlideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n.quick-message-btn[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n.quick-message-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 12px;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%] {\\n  width: 6px;\\n  height: 6px;\\n  border-radius: 50%;\\n  background-color: #9CA3AF;\\n  margin: 0 1px;\\n  animation: _ngcontent-%COMP%_typingAnimation 1.4s infinite ease-in-out;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: -0.32s;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: -0.16s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typingAnimation {\\n  0%, 80%, 100% {\\n    transform: scale(0.8);\\n    opacity: 0.5;\\n  }\\n  40% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n\\n.fullscreen-chat[_ngcontent-%COMP%] {\\n  position: fixed !important;\\n  top: 1rem !important;\\n  left: 1rem !important;\\n  right: 1rem !important;\\n  bottom: 1rem !important;\\n  width: auto !important;\\n  height: auto !important;\\n  z-index: 9999 !important;\\n}\\n\\n.fullscreen-messages[_ngcontent-%COMP%] {\\n  height: calc(100vh - 280px) !important;\\n}\\n\\n\\n\\n@media (max-width: 640px) {\\n  .chat-window[_ngcontent-%COMP%]:not(.fullscreen-chat) {\\n    width: calc(100vw - 1rem);\\n    height: calc(100vh - 8rem);\\n    right: 0.5rem;\\n    left: 0.5rem;\\n    bottom: 5rem;\\n  }\\n\\n  .messages-container[_ngcontent-%COMP%]:not(.fullscreen-messages) {\\n    height: calc(100vh - 16rem) !important;\\n  }\\n\\n  .fullscreen-chat[_ngcontent-%COMP%] {\\n    top: 0.5rem !important;\\n    left: 0.5rem !important;\\n    right: 0.5rem !important;\\n    bottom: 0.5rem !important;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-window[_ngcontent-%COMP%]:not(.fullscreen-chat) {\\n    width: calc(100vw - 0.5rem);\\n    height: calc(100vh - 6rem);\\n    right: 0.25rem;\\n    left: 0.25rem;\\n    bottom: 4rem;\\n  }\\n\\n  .fullscreen-chat[_ngcontent-%COMP%] {\\n    top: 0.25rem !important;\\n    left: 0.25rem !important;\\n    right: 0.25rem !important;\\n    bottom: 0.25rem !important;\\n  }\\n}\\n\\n\\n\\n.focus\\\\:ring-purple-500[_ngcontent-%COMP%]:focus {\\n  --tw-ring-color: rgb(168 85 247 / 0.5);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .focus\\\\:ring-purple-400[_ngcontent-%COMP%]:focus {\\n  --tw-ring-color: rgb(196 181 253 / 0.5);\\n}\\n\\n\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 1rem;\\n  background: linear-gradient(to right, #3d4a85, #4f5fad);\\n  color: white;\\n  border-radius: 1rem 1rem 0 0;\\n}\\n\\n.chat-header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n.chat-header-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  flex-shrink: 0;\\n}\\n\\n.ai-icon[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 0.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n\\n.ai-info[_ngcontent-%COMP%] {\\n  min-width: 0;\\n  flex: 1;\\n}\\n\\n.ai-title-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.ai-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n  color: white;\\n  margin: 0;\\n}\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n\\n.status-dot[_ngcontent-%COMP%] {\\n  width: 0.375rem;\\n  height: 0.375rem;\\n  background-color: #4ade80;\\n  border-radius: 50%;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n\\n.header-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  background: transparent;\\n  border: none;\\n  color: white;\\n  border-radius: 0.5rem;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n\\n.header-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\n\\n\\n*[_ngcontent-%COMP%] {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      data: {\n        animation: [trigger('slideInOut', [transition(':enter', [style({\n          opacity: 0,\n          transform: 'translateY(20px) scale(0.95)'\n        }), animate('300ms ease-out', style({\n          opacity: 1,\n          transform: 'translateY(0) scale(1)'\n        }))]), transition(':leave', [animate('200ms ease-in', style({\n          opacity: 0,\n          transform: 'translateY(20px) scale(0.95)'\n        }))])])]\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "trigger", "style", "transition", "animate", "i0", "ɵɵelementStart", "ɵɵlistener", "AiChatbotComponent_div_0_Template_div_click_0_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "toggleFullscreen", "ɵɵelementEnd", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "message_r13", "content", "ɵɵproperty", "ɵɵpureFunction2", "_c2", "isUser", "ɵɵtextInterpolate1", "ctx_r16", "formatTime", "timestamp", "ɵɵtemplate", "AiChatbotComponent_div_9_div_27_div_2_Template", "AiChatbotComponent_div_9_div_27_div_3_Template", "AiChatbotComponent_div_9_div_27_div_4_Template", "ɵɵpureFunction1", "_c3", "ɵɵpureFunction5", "_c4", "ctx_r9", "isFullscreen", "isTyping", "AiChatbotComponent_div_9_div_28_button_8_Template_button_click_0_listener", "restoredCtx", "_r22", "quickMsg_r20", "$implicit", "ctx_r21", "sendQuickMessage", "ɵɵnamespaceHTML", "AiChatbotComponent_div_9_div_28_button_8_Template", "ctx_r10", "quickMessages", "AiChatbotComponent_div_9_Template_button_click_15_listener", "_r24", "ctx_r23", "AiChatbotComponent_div_9__svg_path_17_Template", "AiChatbotComponent_div_9__svg_path_18_Template", "AiChatbotComponent_div_9_Template_button_click_19_listener", "ctx_r25", "clearChat", "AiChatbotComponent_div_9_Template_button_click_22_listener", "ctx_r26", "closeChat", "AiChatbotComponent_div_9_div_27_Template", "AiChatbotComponent_div_9_div_28_Template", "AiChatbotComponent_div_9_Template_form_ngSubmit_30_listener", "ctx_r27", "sendMessage", "AiChatbotComponent_div_9_Template_textarea_keypress_32_listener", "$event", "ctx_r28", "onKeyPress", "AiChatbotComponent_div_9_div_34_Template", "_c5", "ctx_r3", "undefined", "_c6", "messages", "trackByMessageId", "length", "chatForm", "isLoading", "invalid", "AiChatbotComponent", "constructor", "fb", "geminiService", "isOpen", "destroy$", "shouldScrollToBottom", "group", "message", "required", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "messages$", "pipe", "subscribe", "isOpen$", "setTimeout", "focusInput", "scrollToBottom", "ngAfterViewChecked", "ngOnDestroy", "next", "complete", "toggleChat", "valid", "get", "value", "trim", "reset", "error", "event", "key", "shift<PERSON>ey", "preventDefault", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "err", "console", "messageInput", "focus", "date", "Intl", "DateTimeFormat", "hour", "minute", "format", "patchValue", "index", "id", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "GeminiChatService", "selectors", "viewQuery", "AiChatbotComponent_Query", "rf", "ctx", "AiChatbotComponent_div_0_Template", "AiChatbotComponent_Template_button_click_2_listener", "AiChatbotComponent__svg_svg_5_Template", "AiChatbotComponent__svg_svg_6_Template", "AiChatbotComponent_div_9_Template", "ɵɵclassProp", "opacity", "transform"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\ai-chatbot\\ai-chatbot.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\ai-chatbot\\ai-chatbot.component.html"], "sourcesContent": ["import {\n  <PERSON>mpo<PERSON>,\n  <PERSON><PERSON><PERSON>t,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>Child,\n  ElementRef,\n  AfterViewChecked,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport {\n  trigger,\n  state,\n  style,\n  transition,\n  animate,\n} from '@angular/animations';\nimport {\n  GeminiChatService,\n  ChatMessage,\n} from '../../services/gemini-chat.service';\n\n@Component({\n  selector: 'app-ai-chatbot',\n  templateUrl: './ai-chatbot.component.html',\n  styleUrls: ['./ai-chatbot.component.css'],\n  animations: [\n    trigger('slideInOut', [\n      transition(':enter', [\n        style({ opacity: 0, transform: 'translateY(20px) scale(0.95)' }),\n        animate(\n          '300ms ease-out',\n          style({ opacity: 1, transform: 'translateY(0) scale(1)' })\n        ),\n      ]),\n      transition(':leave', [\n        animate(\n          '200ms ease-in',\n          style({ opacity: 0, transform: 'translateY(20px) scale(0.95)' })\n        ),\n      ]),\n    ]),\n  ],\n})\nexport class AiChatbotComponent implements OnInit, OnDestroy, AfterViewChecked {\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('messageInput') private messageInput!: ElementRef;\n\n  chatForm: FormGroup;\n  messages: ChatMessage[] = [];\n  isOpen = false;\n  isLoading = false;\n  isFullscreen = false;\n  private destroy$ = new Subject<void>();\n  private shouldScrollToBottom = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private geminiService: GeminiChatService\n  ) {\n    this.chatForm = this.fb.group({\n      message: ['', [Validators.required, Validators.minLength(1)]],\n    });\n  }\n\n  ngOnInit(): void {\n    // S'abonner aux messages\n    this.geminiService.messages$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe((messages) => {\n        this.messages = messages;\n        this.shouldScrollToBottom = true;\n      });\n\n    // S'abonner à l'état d'ouverture\n    this.geminiService.isOpen$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe((isOpen) => {\n        this.isOpen = isOpen;\n        if (isOpen) {\n          setTimeout(() => {\n            this.focusInput();\n            this.scrollToBottom();\n          }, 100);\n        }\n      });\n  }\n\n  ngAfterViewChecked(): void {\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  toggleChat(): void {\n    this.geminiService.toggleChat();\n  }\n\n  closeChat(): void {\n    this.geminiService.closeChat();\n    this.isFullscreen = false; // Reset fullscreen when closing\n  }\n\n  toggleFullscreen(): void {\n    this.isFullscreen = !this.isFullscreen;\n    // Scroll to bottom after fullscreen toggle\n    setTimeout(() => {\n      this.scrollToBottom();\n    }, 100);\n  }\n\n  sendMessage(): void {\n    if (this.chatForm.valid && !this.isLoading) {\n      const message = this.chatForm.get('message')?.value?.trim();\n      if (message) {\n        this.isLoading = true;\n        this.geminiService\n          .sendMessage(message)\n          .pipe(takeUntil(this.destroy$))\n          .subscribe({\n            next: () => {\n              this.isLoading = false;\n              this.chatForm.reset();\n              this.focusInput();\n            },\n            error: () => {\n              this.isLoading = false;\n            },\n          });\n      }\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  clearChat(): void {\n    this.geminiService.clearChat();\n  }\n\n  private scrollToBottom(): void {\n    try {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    } catch (err) {\n      console.error('Erreur lors du scroll:', err);\n    }\n  }\n\n  private focusInput(): void {\n    try {\n      if (this.messageInput) {\n        this.messageInput.nativeElement.focus();\n      }\n    } catch (err) {\n      console.error('Erreur lors du focus:', err);\n    }\n  }\n\n  formatTime(date: Date): string {\n    return new Intl.DateTimeFormat('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    }).format(date);\n  }\n\n  // Suggestions de messages rapides\n  quickMessages = [\n    'Comment soumettre un projet ?',\n    'Comment voir mes notes ?',\n    'Problème de connexion',\n    \"Aide pour l'interface\",\n  ];\n\n  sendQuickMessage(message: string): void {\n    this.chatForm.patchValue({ message });\n    this.sendMessage();\n  }\n\n  trackByMessageId(index: number, message: ChatMessage): string {\n    return message.id;\n  }\n}\n", "<!-- Overlay pour mode plein écran -->\n<div\n  *ngIf=\"isOpen && isFullscreen\"\n  class=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300\"\n  (click)=\"toggleFullscreen()\"\n></div>\n\n<!-- Bouton flottant IA -->\n<div class=\"fixed bottom-6 right-6 z-50\">\n  <!-- Bouton principal modernisé -->\n  <button\n    (click)=\"toggleChat()\"\n    class=\"group relative w-16 h-16 bg-gradient-to-br from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] text-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-4 focus:ring-[#4f5fad]/30 dark:focus:ring-[#6d78c9]/30\"\n    [class.rotate-180]=\"isOpen\"\n  >\n    <!-- Glow effect amélioré -->\n    <div class=\"absolute inset-0 bg-gradient-to-br from-[#4f5fad]/40 to-[#3d4a85]/40 rounded-2xl opacity-0 group-hover:opacity-30 blur-xl transition-opacity duration-300 scale-150\"></div>\n\n    <!-- Icône IA modernisée -->\n    <div class=\"relative z-10 flex items-center justify-center w-full h-full\">\n      <svg *ngIf=\"!isOpen\" class=\"w-8 h-8 transition-transform duration-300 group-hover:scale-110\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\n      </svg>\n      <svg *ngIf=\"isOpen\" class=\"w-7 h-7 transition-transform duration-300 group-hover:rotate-90\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n      </svg>\n    </div>\n\n    <!-- Badge de notification modernisé -->\n    <div class=\"absolute -top-2 -right-2 w-5 h-5 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center shadow-lg animate-pulse\">\n      <div class=\"w-2 h-2 bg-white rounded-full\"></div>\n    </div>\n  </button>\n\n  <!-- Fenêtre de chat avec mode plein écran -->\n  <div\n    *ngIf=\"isOpen\"\n    [ngClass]=\"{\n      'fixed inset-4 w-auto h-auto': isFullscreen,\n      'absolute bottom-16 right-0 w-96 h-[32rem]': !isFullscreen\n    }\"\n    class=\"bg-white/90 dark:bg-[#1e1e1e]/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-[#edf1f4]/50 dark:border-[#2a2a2a]/50 overflow-hidden transform transition-all duration-300 ease-out z-50\"\n    [@slideInOut]\n  >\n    <!-- Header du chat ultra-simplifié -->\n    <div class=\"bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] px-4 py-3 text-white rounded-t-2xl\">\n      <div class=\"flex items-center justify-between gap-3\">\n        <!-- Info IA -->\n        <div class=\"flex items-center gap-3 flex-1 min-w-0\">\n          <div class=\"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\">\n            <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n            </svg>\n          </div>\n          <div class=\"flex-1 min-w-0\">\n            <h3 class=\"text-sm font-semibold text-white truncate\">Assistant IA</h3>\n            <div class=\"flex items-center gap-1.5\">\n              <div class=\"w-1.5 h-1.5 bg-green-400 rounded-full\"></div>\n              <span class=\"text-xs text-white/80\">En ligne</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Boutons -->\n        <div class=\"flex items-center gap-1\">\n          <button\n            (click)=\"toggleFullscreen()\"\n            class=\"w-8 h-8 flex items-center justify-center hover:bg-white/20 rounded-lg transition-colors\"\n            [title]=\"isFullscreen ? 'Mode fenêtre' : 'Plein écran'\"\n          >\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path *ngIf=\"!isFullscreen\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"></path>\n              <path *ngIf=\"isFullscreen\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 9V4.5M9 9H4.5M9 9L3.5 3.5M15 9h4.5M15 9V4.5M15 9l5.5-5.5M9 15v4.5M9 15H4.5M9 15l-5.5 5.5M15 15h4.5M15 15v4.5m0-4.5l5.5 5.5\"></path>\n            </svg>\n          </button>\n          <button\n            (click)=\"clearChat()\"\n            class=\"w-8 h-8 flex items-center justify-center hover:bg-white/20 rounded-lg transition-colors\"\n            title=\"Nouvelle conversation\"\n          >\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n            </svg>\n          </button>\n          <button\n            (click)=\"closeChat()\"\n            class=\"w-8 h-8 flex items-center justify-center hover:bg-white/20 rounded-lg transition-colors\"\n            title=\"Fermer le chat\"\n          >\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Messages avec hauteur adaptative -->\n    <div\n      #messagesContainer\n      class=\"flex-1 overflow-y-auto p-6 space-y-4 messages-container\"\n      [ngStyle]=\"{\n        'height': isFullscreen ? 'calc(100vh - 280px)' : 'calc(32rem - 200px)'\n      }\"\n    >\n      <div *ngFor=\"let message of messages; trackBy: trackByMessageId\" class=\"flex\" [ngClass]=\"{'justify-end': message.isUser}\">\n        <div\n          [ngClass]=\"{\n            'max-w-sm': !isFullscreen,\n            'max-w-2xl': isFullscreen,\n            'bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] text-white shadow-lg': message.isUser,\n            'bg-[#edf1f4]/80 dark:bg-[#2a2a2a]/80 text-[#3d4a85] dark:text-[#6d78c9] backdrop-blur-sm border border-[#edf1f4] dark:border-[#2a2a2a]': !message.isUser && !message.isTyping,\n            'bg-[#edf1f4]/80 dark:bg-[#2a2a2a]/80 backdrop-blur-sm': message.isTyping\n          }\"\n          class=\"px-4 py-3 rounded-2xl text-base leading-relaxed font-medium\"\n        >\n          <!-- Message normal avec meilleure typographie -->\n          <div *ngIf=\"!message.isTyping\" class=\"whitespace-pre-wrap leading-relaxed\">{{ message.content }}</div>\n\n          <!-- Indicateur de frappe amélioré -->\n          <div *ngIf=\"message.isTyping\" class=\"flex items-center space-x-3 py-2\">\n            <div class=\"flex space-x-1\">\n              <div class=\"w-3 h-3 bg-[#4f5fad] dark:bg-[#6d78c9] rounded-full animate-bounce\" style=\"animation-delay: 0ms\"></div>\n              <div class=\"w-3 h-3 bg-[#4f5fad] dark:bg-[#6d78c9] rounded-full animate-bounce\" style=\"animation-delay: 150ms\"></div>\n              <div class=\"w-3 h-3 bg-[#4f5fad] dark:bg-[#6d78c9] rounded-full animate-bounce\" style=\"animation-delay: 300ms\"></div>\n            </div>\n            <span class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] font-medium\">L'IA réfléchit...</span>\n          </div>\n\n          <!-- Timestamp amélioré -->\n          <div *ngIf=\"!message.isTyping\" class=\"text-xs opacity-75 mt-2 font-medium\" [ngClass]=\"{'text-right text-white/80': message.isUser, 'text-[#6d6870] dark:text-[#a0a0a0]': !message.isUser}\">\n            {{ formatTime(message.timestamp) }}\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Messages rapides modernisés -->\n    <div *ngIf=\"messages.length <= 1\" class=\"px-6 pb-4\">\n      <div class=\"flex items-center space-x-2 mb-4\">\n        <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-1.5 rounded-lg\">\n          <svg class=\"w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\n          </svg>\n        </div>\n        <span class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Suggestions rapides :</span>\n      </div>\n      <div class=\"grid grid-cols-1 gap-2\">\n        <button\n          *ngFor=\"let quickMsg of quickMessages\"\n          (click)=\"sendQuickMessage(quickMsg)\"\n          class=\"text-sm px-4 py-3 bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 text-[#3d4a85] dark:text-[#6d78c9] rounded-xl hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 hover:border-[#4f5fad] dark:hover:border-[#6d78c9] border border-transparent transition-all duration-200 font-medium text-left hover:scale-105 hover:shadow-md\"\n        >\n          {{ quickMsg }}\n        </button>\n      </div>\n    </div>\n\n    <!-- Input de message modernisé -->\n    <div class=\"p-6 border-t border-[#edf1f4]/50 dark:border-[#2a2a2a]/50 bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30\">\n      <form [formGroup]=\"chatForm\" (ngSubmit)=\"sendMessage()\" class=\"flex items-end space-x-3\">\n        <div class=\"flex-1 relative\">\n          <textarea\n            #messageInput\n            formControlName=\"message\"\n            placeholder=\"Posez votre question à l'assistant IA...\"\n            (keypress)=\"onKeyPress($event)\"\n            rows=\"1\"\n            class=\"w-full px-4 py-3 pr-12 bg-white dark:bg-[#1e1e1e] border-2 border-[#edf1f4] dark:border-[#2a2a2a] rounded-2xl focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-4 focus:ring-[#4f5fad]/10 dark:focus:ring-[#6d78c9]/20 text-base text-[#3d4a85] dark:text-[#6d78c9] placeholder-[#6d6870] dark:placeholder-[#a0a0a0] resize-none transition-all duration-200 font-medium leading-relaxed\"\n            [disabled]=\"isLoading\"\n            style=\"min-height: 48px; max-height: 120px;\"\n          ></textarea>\n          <div *ngIf=\"isLoading\" class=\"absolute right-4 bottom-3\">\n            <div class=\"w-5 h-5 border-2 border-[#4f5fad]/30 dark:border-[#6d78c9]/30 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"></div>\n          </div>\n        </div>\n        <button\n          type=\"submit\"\n          [disabled]=\"chatForm.invalid || isLoading\"\n          class=\"p-3 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-2xl transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 group\"\n        >\n          <svg class=\"w-5 h-5 group-hover:translate-x-0.5 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"></path>\n          </svg>\n        </button>\n      </form>\n\n      <!-- Indication de raccourci -->\n      <div class=\"flex items-center justify-between mt-3 text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n        <span>Appuyez sur Entrée pour envoyer</span>\n        <div class=\"flex items-center space-x-1\">\n          <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 px-2 py-1 rounded\">\n            <span class=\"font-mono\">⏎</span>\n          </div>\n          <span>Envoyer</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAQA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SACEC,OAAO,EAEPC,KAAK,EACLC,UAAU,EACVC,OAAO,QACF,qBAAqB;;;;;;;;;;IChB5BC,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAC,uDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAC7BT,EAAA,CAAAU,YAAA,EAAM;;;;;IAeDV,EAAA,CAAAW,cAAA,EAAmJ;IAAnJX,EAAA,CAAAC,cAAA,cAAmJ;IACjJD,EAAA,CAAAY,SAAA,eAAkS;IACpSZ,EAAA,CAAAU,YAAA,EAAM;;;;;IACNV,EAAA,CAAAW,cAAA,EAAkJ;IAAlJX,EAAA,CAAAC,cAAA,cAAkJ;IAChJD,EAAA,CAAAY,SAAA,eAAsG;IACxGZ,EAAA,CAAAU,YAAA,EAAM;;;;;;IA8CEV,EAAA,CAAAY,SAAA,eAAiM;;;;;;IACjMZ,EAAA,CAAAY,SAAA,eAAoO;;;;;IA6CxOZ,EAAA,CAAAC,cAAA,cAA2E;IAAAD,EAAA,CAAAa,MAAA,GAAqB;IAAAb,EAAA,CAAAU,YAAA,EAAM;;;;IAA3BV,EAAA,CAAAc,SAAA,GAAqB;IAArBd,EAAA,CAAAe,iBAAA,CAAAC,WAAA,CAAAC,OAAA,CAAqB;;;;;IAGhGjB,EAAA,CAAAC,cAAA,cAAuE;IAEnED,EAAA,CAAAY,SAAA,cAAmH;IAGrHZ,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAAqE;IAAAD,EAAA,CAAAa,MAAA,kCAAiB;IAAAb,EAAA,CAAAU,YAAA,EAAO;;;;;;;;;;;IAI/FV,EAAA,CAAAC,cAAA,cAA2L;IACzLD,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAU,YAAA,EAAM;;;;;IAFqEV,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAmB,eAAA,IAAAC,GAAA,EAAAJ,WAAA,CAAAK,MAAA,GAAAL,WAAA,CAAAK,MAAA,EAA+G;IACxLrB,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAsB,kBAAA,MAAAC,OAAA,CAAAC,UAAA,CAAAR,WAAA,CAAAS,SAAA,OACF;;;;;;;;;;;;;;;;;;;IA3BJzB,EAAA,CAAAC,cAAA,cAA0H;IAYtHD,EAAA,CAAA0B,UAAA,IAAAC,8CAAA,kBAAsG;IAGtG3B,EAAA,CAAA0B,UAAA,IAAAE,8CAAA,kBAOM;IAGN5B,EAAA,CAAA0B,UAAA,IAAAG,8CAAA,kBAEM;IACR7B,EAAA,CAAAU,YAAA,EAAM;;;;;IA5BsEV,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAAAf,WAAA,CAAAK,MAAA,EAA2C;IAErHrB,EAAA,CAAAc,SAAA,GAME;IANFd,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,YAAA,EAAAD,MAAA,CAAAC,YAAA,EAAAnB,WAAA,CAAAK,MAAA,GAAAL,WAAA,CAAAK,MAAA,KAAAL,WAAA,CAAAoB,QAAA,EAAApB,WAAA,CAAAoB,QAAA,EAME;IAIIpC,EAAA,CAAAc,SAAA,GAAuB;IAAvBd,EAAA,CAAAkB,UAAA,UAAAF,WAAA,CAAAoB,QAAA,CAAuB;IAGvBpC,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAAkB,UAAA,SAAAF,WAAA,CAAAoB,QAAA,CAAsB;IAUtBpC,EAAA,CAAAc,SAAA,GAAuB;IAAvBd,EAAA,CAAAkB,UAAA,UAAAF,WAAA,CAAAoB,QAAA,CAAuB;;;;;;IAkB/BpC,EAAA,CAAAC,cAAA,iBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAmC,0EAAA;MAAA,MAAAC,WAAA,GAAAtC,EAAA,CAAAI,aAAA,CAAAmC,IAAA;MAAA,MAAAC,YAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAkC,OAAA,CAAAC,gBAAA,CAAAH,YAAA,CAA0B;IAAA,EAAC;IAGpCxC,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAU,YAAA,EAAS;;;;IADPV,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAsB,kBAAA,MAAAkB,YAAA,MACF;;;;;IAhBJxC,EAAA,CAAAC,cAAA,cAAoD;IAG9CD,EAAA,CAAAW,cAAA,EAA8G;IAA9GX,EAAA,CAAAC,cAAA,cAA8G;IAC5GD,EAAA,CAAAY,SAAA,eAA4G;IAC9GZ,EAAA,CAAAU,YAAA,EAAM;IAERV,EAAA,CAAA4C,eAAA,EAAuE;IAAvE5C,EAAA,CAAAC,cAAA,eAAuE;IAAAD,EAAA,CAAAa,MAAA,4BAAqB;IAAAb,EAAA,CAAAU,YAAA,EAAO;IAErGV,EAAA,CAAAC,cAAA,cAAoC;IAClCD,EAAA,CAAA0B,UAAA,IAAAmB,iDAAA,qBAMS;IACX7C,EAAA,CAAAU,YAAA,EAAM;;;;IANmBV,EAAA,CAAAc,SAAA,GAAgB;IAAhBd,EAAA,CAAAkB,UAAA,YAAA4B,OAAA,CAAAC,aAAA,CAAgB;;;;;IAuBrC/C,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAY,SAAA,cAAsJ;IACxJZ,EAAA,CAAAU,YAAA,EAAM;;;;;;;;;;;;;;;;;IA3IdV,EAAA,CAAAC,cAAA,cAQC;IAOSD,EAAA,CAAAW,cAAA,EAA6D;IAA7DX,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAY,SAAA,eAAiI;IACnIZ,EAAA,CAAAU,YAAA,EAAM;IAERV,EAAA,CAAA4C,eAAA,EAA4B;IAA5B5C,EAAA,CAAAC,cAAA,cAA4B;IAC4BD,EAAA,CAAAa,MAAA,mBAAY;IAAAb,EAAA,CAAAU,YAAA,EAAK;IACvEV,EAAA,CAAAC,cAAA,eAAuC;IACrCD,EAAA,CAAAY,SAAA,eAAyD;IACzDZ,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAa,MAAA,gBAAQ;IAAAb,EAAA,CAAAU,YAAA,EAAO;IAMzDV,EAAA,CAAAC,cAAA,eAAqC;IAEjCD,EAAA,CAAAE,UAAA,mBAAA8C,2DAAA;MAAAhD,EAAA,CAAAI,aAAA,CAAA6C,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA0C,OAAA,CAAAzC,gBAAA,EAAkB;IAAA,EAAC;IAI5BT,EAAA,CAAAW,cAAA,EAA2E;IAA3EX,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAA0B,UAAA,KAAAyB,8CAAA,mBAAiM;IACjMnD,EAAA,CAAA0B,UAAA,KAAA0B,8CAAA,mBAAoO;IACtOpD,EAAA,CAAAU,YAAA,EAAM;IAERV,EAAA,CAAA4C,eAAA,EAIC;IAJD5C,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAE,UAAA,mBAAAmD,2DAAA;MAAArD,EAAA,CAAAI,aAAA,CAAA6C,IAAA;MAAA,MAAAK,OAAA,GAAAtD,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA8C,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAIrBvD,EAAA,CAAAW,cAAA,EAA2E;IAA3EX,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAY,SAAA,gBAA6L;IAC/LZ,EAAA,CAAAU,YAAA,EAAM;IAERV,EAAA,CAAA4C,eAAA,EAIC;IAJD5C,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAE,UAAA,mBAAAsD,2DAAA;MAAAxD,EAAA,CAAAI,aAAA,CAAA6C,IAAA;MAAA,MAAAQ,OAAA,GAAAzD,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAiD,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAIrB1D,EAAA,CAAAW,cAAA,EAA2E;IAA3EX,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAY,SAAA,gBAAsG;IACxGZ,EAAA,CAAAU,YAAA,EAAM;IAOdV,EAAA,CAAA4C,eAAA,EAMC;IAND5C,EAAA,CAAAC,cAAA,mBAMC;IACCD,EAAA,CAAA0B,UAAA,KAAAiC,wCAAA,mBA6BM;IACR3D,EAAA,CAAAU,YAAA,EAAM;IAGNV,EAAA,CAAA0B,UAAA,KAAAkC,wCAAA,kBAkBM;IAGN5D,EAAA,CAAAC,cAAA,eAA4G;IAC7ED,EAAA,CAAAE,UAAA,sBAAA2D,4DAAA;MAAA7D,EAAA,CAAAI,aAAA,CAAA6C,IAAA;MAAA,MAAAa,OAAA,GAAA9D,EAAA,CAAAO,aAAA;MAAA,OAAYP,EAAA,CAAAQ,WAAA,CAAAsD,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACrD/D,EAAA,CAAAC,cAAA,eAA6B;IAKzBD,EAAA,CAAAE,UAAA,sBAAA8D,gEAAAC,MAAA;MAAAjE,EAAA,CAAAI,aAAA,CAAA6C,IAAA;MAAA,MAAAiB,OAAA,GAAAlE,EAAA,CAAAO,aAAA;MAAA,OAAYP,EAAA,CAAAQ,WAAA,CAAA0D,OAAA,CAAAC,UAAA,CAAAF,MAAA,CAAkB;IAAA,EAAC;IAKhCjE,EAAA,CAAAU,YAAA,EAAW;IACZV,EAAA,CAAA0B,UAAA,KAAA0C,wCAAA,kBAEM;IACRpE,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,kBAIC;IACCD,EAAA,CAAAW,cAAA,EAA4H;IAA5HX,EAAA,CAAAC,cAAA,eAA4H;IAC1HD,EAAA,CAAAY,SAAA,gBAAkH;IACpHZ,EAAA,CAAAU,YAAA,EAAM;IAKVV,EAAA,CAAA4C,eAAA,EAA+F;IAA/F5C,EAAA,CAAAC,cAAA,eAA+F;IACvFD,EAAA,CAAAa,MAAA,4CAA+B;IAAAb,EAAA,CAAAU,YAAA,EAAO;IAC5CV,EAAA,CAAAC,cAAA,eAAyC;IAEbD,EAAA,CAAAa,MAAA,cAAC;IAAAb,EAAA,CAAAU,YAAA,EAAO;IAElCV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,eAAO;IAAAb,EAAA,CAAAU,YAAA,EAAO;;;;IA7J1BV,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAmB,eAAA,KAAAkD,GAAA,EAAAC,MAAA,CAAAnC,YAAA,GAAAmC,MAAA,CAAAnC,YAAA,EAGE,gBAAAoC,SAAA;IA4BMvE,EAAA,CAAAc,SAAA,IAAuD;IAAvDd,EAAA,CAAAkB,UAAA,UAAAoD,MAAA,CAAAnC,YAAA,4CAAuD;IAG9CnC,EAAA,CAAAc,SAAA,GAAmB;IAAnBd,EAAA,CAAAkB,UAAA,UAAAoD,MAAA,CAAAnC,YAAA,CAAmB;IACnBnC,EAAA,CAAAc,SAAA,GAAkB;IAAlBd,EAAA,CAAAkB,UAAA,SAAAoD,MAAA,CAAAnC,YAAA,CAAkB;IA6BjCnC,EAAA,CAAAc,SAAA,GAEE;IAFFd,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAA8B,eAAA,KAAA0C,GAAA,EAAAF,MAAA,CAAAnC,YAAA,kDAEE;IAEuBnC,EAAA,CAAAc,SAAA,GAAa;IAAbd,EAAA,CAAAkB,UAAA,YAAAoD,MAAA,CAAAG,QAAA,CAAa,iBAAAH,MAAA,CAAAI,gBAAA;IAiClC1E,EAAA,CAAAc,SAAA,GAA0B;IAA1Bd,EAAA,CAAAkB,UAAA,SAAAoD,MAAA,CAAAG,QAAA,CAAAE,MAAA,MAA0B;IAsBxB3E,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAAkB,UAAA,cAAAoD,MAAA,CAAAM,QAAA,CAAsB;IAStB5E,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAAkB,UAAA,aAAAoD,MAAA,CAAAO,SAAA,CAAsB;IAGlB7E,EAAA,CAAAc,SAAA,GAAe;IAAfd,EAAA,CAAAkB,UAAA,SAAAoD,MAAA,CAAAO,SAAA,CAAe;IAMrB7E,EAAA,CAAAc,SAAA,GAA0C;IAA1Cd,EAAA,CAAAkB,UAAA,aAAAoD,MAAA,CAAAM,QAAA,CAAAE,OAAA,IAAAR,MAAA,CAAAO,SAAA,CAA0C;;;ADrIpD,OAAM,MAAOE,kBAAkB;EAY7BC,YACUC,EAAe,EACfC,aAAgC;IADhC,KAAAD,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IATvB,KAAAT,QAAQ,GAAkB,EAAE;IAC5B,KAAAU,MAAM,GAAG,KAAK;IACd,KAAAN,SAAS,GAAG,KAAK;IACjB,KAAA1C,YAAY,GAAG,KAAK;IACZ,KAAAiD,QAAQ,GAAG,IAAI1F,OAAO,EAAQ;IAC9B,KAAA2F,oBAAoB,GAAG,KAAK;IA4HpC;IACA,KAAAtC,aAAa,GAAG,CACd,+BAA+B,EAC/B,0BAA0B,EAC1B,uBAAuB,EACvB,uBAAuB,CACxB;IA5HC,IAAI,CAAC6B,QAAQ,GAAG,IAAI,CAACK,EAAE,CAACK,KAAK,CAAC;MAC5BC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC9F,UAAU,CAAC+F,QAAQ,EAAE/F,UAAU,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,aAAa,CAACS,SAAS,CACzBC,IAAI,CAACjG,SAAS,CAAC,IAAI,CAACyF,QAAQ,CAAC,CAAC,CAC9BS,SAAS,CAAEpB,QAAQ,IAAI;MACtB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACY,oBAAoB,GAAG,IAAI;IAClC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACH,aAAa,CAACY,OAAO,CACvBF,IAAI,CAACjG,SAAS,CAAC,IAAI,CAACyF,QAAQ,CAAC,CAAC,CAC9BS,SAAS,CAAEV,MAAM,IAAI;MACpB,IAAI,CAACA,MAAM,GAAGA,MAAM;MACpB,IAAIA,MAAM,EAAE;QACVY,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,UAAU,EAAE;UACjB,IAAI,CAACC,cAAc,EAAE;QACvB,CAAC,EAAE,GAAG,CAAC;;IAEX,CAAC,CAAC;EACN;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACb,oBAAoB,EAAE;MAC7B,IAAI,CAACY,cAAc,EAAE;MACrB,IAAI,CAACZ,oBAAoB,GAAG,KAAK;;EAErC;EAEAc,WAAWA,CAAA;IACT,IAAI,CAACf,QAAQ,CAACgB,IAAI,EAAE;IACpB,IAAI,CAAChB,QAAQ,CAACiB,QAAQ,EAAE;EAC1B;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACpB,aAAa,CAACoB,UAAU,EAAE;EACjC;EAEA5C,SAASA,CAAA;IACP,IAAI,CAACwB,aAAa,CAACxB,SAAS,EAAE;IAC9B,IAAI,CAACvB,YAAY,GAAG,KAAK,CAAC,CAAC;EAC7B;;EAEA1B,gBAAgBA,CAAA;IACd,IAAI,CAAC0B,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC;IACA4D,UAAU,CAAC,MAAK;MACd,IAAI,CAACE,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAlC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACa,QAAQ,CAAC2B,KAAK,IAAI,CAAC,IAAI,CAAC1B,SAAS,EAAE;MAC1C,MAAMU,OAAO,GAAG,IAAI,CAACX,QAAQ,CAAC4B,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK,EAAEC,IAAI,EAAE;MAC3D,IAAInB,OAAO,EAAE;QACX,IAAI,CAACV,SAAS,GAAG,IAAI;QACrB,IAAI,CAACK,aAAa,CACfnB,WAAW,CAACwB,OAAO,CAAC,CACpBK,IAAI,CAACjG,SAAS,CAAC,IAAI,CAACyF,QAAQ,CAAC,CAAC,CAC9BS,SAAS,CAAC;UACTO,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAACvB,SAAS,GAAG,KAAK;YACtB,IAAI,CAACD,QAAQ,CAAC+B,KAAK,EAAE;YACrB,IAAI,CAACX,UAAU,EAAE;UACnB,CAAC;UACDY,KAAK,EAAEA,CAAA,KAAK;YACV,IAAI,CAAC/B,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;;;EAGV;EAEAV,UAAUA,CAAC0C,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACjD,WAAW,EAAE;;EAEtB;EAEAR,SAASA,CAAA;IACP,IAAI,CAAC2B,aAAa,CAAC3B,SAAS,EAAE;EAChC;EAEQ0C,cAAcA,CAAA;IACpB,IAAI;MACF,IAAI,IAAI,CAACgB,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;KAE3C,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACX,KAAK,CAAC,wBAAwB,EAAEU,GAAG,CAAC;;EAEhD;EAEQtB,UAAUA,CAAA;IAChB,IAAI;MACF,IAAI,IAAI,CAACwB,YAAY,EAAE;QACrB,IAAI,CAACA,YAAY,CAACL,aAAa,CAACM,KAAK,EAAE;;KAE1C,CAAC,OAAOH,GAAG,EAAE;MACZC,OAAO,CAACX,KAAK,CAAC,uBAAuB,EAAEU,GAAG,CAAC;;EAE/C;EAEA9F,UAAUA,CAACkG,IAAU;IACnB,OAAO,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC,CAACC,MAAM,CAACL,IAAI,CAAC;EACjB;EAUA/E,gBAAgBA,CAAC4C,OAAe;IAC9B,IAAI,CAACX,QAAQ,CAACoD,UAAU,CAAC;MAAEzC;IAAO,CAAE,CAAC;IACrC,IAAI,CAACxB,WAAW,EAAE;EACpB;EAEAW,gBAAgBA,CAACuD,KAAa,EAAE1C,OAAoB;IAClD,OAAOA,OAAO,CAAC2C,EAAE;EACnB;;;uBArJWnD,kBAAkB,EAAA/E,EAAA,CAAAmI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArI,EAAA,CAAAmI,iBAAA,CAAAG,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAlBxD,kBAAkB;MAAAyD,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UC5C/B3I,EAAA,CAAA0B,UAAA,IAAAmH,iCAAA,iBAIO;UAGP7I,EAAA,CAAAC,cAAA,aAAyC;UAGrCD,EAAA,CAAAE,UAAA,mBAAA4I,oDAAA;YAAA,OAASF,GAAA,CAAAtC,UAAA,EAAY;UAAA,EAAC;UAKtBtG,EAAA,CAAAY,SAAA,aAAuL;UAGvLZ,EAAA,CAAAC,cAAA,aAA0E;UACxED,EAAA,CAAA0B,UAAA,IAAAqH,sCAAA,iBAEM;UACN/I,EAAA,CAAA0B,UAAA,IAAAsH,sCAAA,iBAEM;UACRhJ,EAAA,CAAAU,YAAA,EAAM;UAGNV,EAAA,CAAAC,cAAA,aAAiK;UAC/JD,EAAA,CAAAY,SAAA,aAAiD;UACnDZ,EAAA,CAAAU,YAAA,EAAM;UAIRV,EAAA,CAAA0B,UAAA,IAAAuH,iCAAA,mBAmKM;UACRjJ,EAAA,CAAAU,YAAA,EAAM;;;UArMHV,EAAA,CAAAkB,UAAA,SAAA0H,GAAA,CAAAzD,MAAA,IAAAyD,GAAA,CAAAzG,YAAA,CAA4B;UAW3BnC,EAAA,CAAAc,SAAA,GAA2B;UAA3Bd,EAAA,CAAAkJ,WAAA,eAAAN,GAAA,CAAAzD,MAAA,CAA2B;UAOnBnF,EAAA,CAAAc,SAAA,GAAa;UAAbd,EAAA,CAAAkB,UAAA,UAAA0H,GAAA,CAAAzD,MAAA,CAAa;UAGbnF,EAAA,CAAAc,SAAA,GAAY;UAAZd,EAAA,CAAAkB,UAAA,SAAA0H,GAAA,CAAAzD,MAAA,CAAY;UAanBnF,EAAA,CAAAc,SAAA,GAAY;UAAZd,EAAA,CAAAkB,UAAA,SAAA0H,GAAA,CAAAzD,MAAA,CAAY;;;;;;mBDTH,CACVvF,OAAO,CAAC,YAAY,EAAE,CACpBE,UAAU,CAAC,QAAQ,EAAE,CACnBD,KAAK,CAAC;UAAEsJ,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAA8B,CAAE,CAAC,EAChErJ,OAAO,CACL,gBAAgB,EAChBF,KAAK,CAAC;UAAEsJ,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAwB,CAAE,CAAC,CAC3D,CACF,CAAC,EACFtJ,UAAU,CAAC,QAAQ,EAAE,CACnBC,OAAO,CACL,eAAe,EACfF,KAAK,CAAC;UAAEsJ,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAA8B,CAAE,CAAC,CACjE,CACF,CAAC,CACH,CAAC;MACH;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}