<!-- Interface d'appel simplifiée et fonctionnelle -->
<div
  *ngIf="activeCall"
  class="fixed inset-0 z-50 bg-gradient-to-br from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] flex flex-col"
>
  <!-- Header avec informations d'appel -->
  <div class="flex-shrink-0 p-6 text-center">
    <div
      class="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-black/30 backdrop-blur-sm border border-blue-500/20"
    >
      <div class="w-2 h-2 rounded-full bg-blue-400 animate-pulse"></div>
      <span class="text-white text-sm font-medium">{{
        getCallStatusText()
      }}</span>
    </div>
  </div>

  <!-- Zone principale d'appel -->
  <div class="flex-1 flex flex-col items-center justify-center px-6">
    <!-- Appel vidéo -->
    <div *ngIf="isVideoCall()" class="w-full max-w-4xl mx-auto relative">
      <!-- Vidéo locale (grande) -->
      <div
        class="relative w-full aspect-video rounded-2xl overflow-hidden bg-gray-900 border border-blue-500/20"
      >
        <video
          #localVideo
          autoplay
          playsinline
          muted
          class="w-full h-full object-cover"
        ></video>

        <!-- Overlay si pas de vidéo -->
        <div
          *ngIf="!hasLocalVideo"
          class="absolute inset-0 flex items-center justify-center bg-gray-900"
        >
          <div class="text-center">
            <div
              class="w-24 h-24 rounded-full bg-blue-500/20 flex items-center justify-center mx-auto mb-4"
            >
              <i class="fas fa-user text-3xl text-blue-400"></i>
            </div>
            <p class="text-white text-lg">Vous</p>
          </div>
        </div>
      </div>

      <!-- Vidéo distante (petite, en overlay) -->
      <div
        class="absolute top-4 right-4 w-48 h-36 rounded-xl overflow-hidden bg-gray-800 border border-blue-500/30"
      >
        <video
          #remoteVideo
          autoplay
          playsinline
          class="w-full h-full object-cover"
        ></video>

        <!-- Avatar si pas de vidéo distante -->
        <div
          *ngIf="!hasRemoteVideo"
          class="absolute inset-0 flex items-center justify-center bg-gray-800"
        >
          <div class="text-center">
            <img
              [src]="getOtherParticipantAvatar()"
              [alt]="getOtherParticipantName()"
              class="w-16 h-16 rounded-full object-cover mx-auto mb-2 border-2 border-blue-400"
            />
            <p class="text-white text-sm">{{ getOtherParticipantName() }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Appel audio -->
    <div *ngIf="!isVideoCall()" class="text-center">
      <!-- Avatar de l'autre participant -->
      <div class="relative mb-8">
        <div
          class="w-40 h-40 rounded-full overflow-hidden border-4 border-blue-500/50 mx-auto"
        >
          <img
            [src]="getOtherParticipantAvatar()"
            [alt]="getOtherParticipantName()"
            class="w-full h-full object-cover"
          />
        </div>

        <!-- Effet de pulsation -->
        <div
          class="absolute inset-0 rounded-full border-4 border-blue-400/30 animate-ping"
        ></div>
        <div
          class="absolute -inset-4 rounded-full border-2 border-blue-400/20 animate-ping"
          style="animation-delay: 0.5s"
        ></div>
      </div>

      <!-- Nom du participant -->
      <h2 class="text-3xl font-bold text-white mb-2">
        {{ getOtherParticipantName() }}
      </h2>

      <!-- Visualiseur audio -->
      <div class="flex items-center justify-center space-x-1 h-12 mb-8">
        <div
          *ngFor="let i of audioLevels"
          class="w-1 bg-gradient-to-t from-blue-600 to-blue-400 rounded-full transition-all duration-150"
          [style.height.px]="i"
        ></div>
      </div>
    </div>
  </div>

  <!-- Contrôles d'appel simplifiés -->
  <div class="flex-shrink-0 p-6">
    <div class="flex justify-center space-x-6">
      <!-- Bouton Microphone -->
      <button
        (click)="toggleMicrophone()"
        class="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 relative group"
        [ngClass]="{
          'bg-blue-500 text-white shadow-lg shadow-blue-500/25': !isAudioMuted,
          'bg-red-500 text-white shadow-lg shadow-red-500/25': isAudioMuted
        }"
      >
        <i
          class="fas text-xl"
          [ngClass]="isAudioMuted ? 'fa-microphone-slash' : 'fa-microphone'"
        ></i>
        <div
          class="absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity"
        ></div>
      </button>

      <!-- Bouton Caméra (seulement pour appels vidéo) -->
      <button
        *ngIf="isVideoCall()"
        (click)="toggleCamera()"
        class="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 relative group"
        [ngClass]="{
          'bg-blue-500 text-white shadow-lg shadow-blue-500/25': !isVideoMuted,
          'bg-red-500 text-white shadow-lg shadow-red-500/25': isVideoMuted
        }"
      >
        <i
          class="fas text-xl"
          [ngClass]="isVideoMuted ? 'fa-video-slash' : 'fa-video'"
        ></i>
        <div
          class="absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity"
        ></div>
      </button>

      <!-- Bouton Haut-parleur -->
      <button
        (click)="toggleSpeaker()"
        class="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 relative group"
        [ngClass]="{
          'bg-blue-500 text-white shadow-lg shadow-blue-500/25': isSpeakerOn,
          'bg-gray-600 text-gray-300 shadow-lg shadow-gray-600/25': !isSpeakerOn
        }"
      >
        <i
          class="fas text-xl"
          [ngClass]="isSpeakerOn ? 'fa-volume-up' : 'fa-volume-mute'"
        ></i>
        <div
          class="absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity"
        ></div>
      </button>

      <!-- Bouton Raccrocher -->
      <button
        (click)="endCall()"
        class="w-16 h-16 rounded-full flex items-center justify-center bg-red-500 text-white transition-all duration-200 relative group shadow-lg shadow-red-500/25"
      >
        <i class="fas fa-phone-slash text-xl"></i>
        <div
          class="absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity"
        ></div>
      </button>
    </div>
  </div>
</div>
