{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/logger.service\";\nimport * as i2 from \"./services/theme.service\";\nconst _c0 = function (a0) {\n  return {\n    dark: a0\n  };\n};\nexport class AppComponent {\n  constructor(logger, themeService) {\n    this.logger = logger;\n    this.themeService = themeService;\n    this.title = 'frontend';\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n  ngOnInit() {\n    // Activer les logs\n    this.logger.setLogsEnabled(true);\n    // Activer les logs pour certains composants spécifiques\n    this.logger.enableComponentLogs('MessageService');\n    this.logger.enableComponentLogs('MessageChat');\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.LoggerService), i0.ɵɵdirectiveInject(i2.ThemeService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 8,\n      vars: 5,\n      consts: [[1, \"app-container\", 3, \"ngClass\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelement(2, \"router-outlet\")(3, \"app-incoming-call\")(4, \"app-active-call\")(5, \"app-connection-status\")(6, \"app-graphql-status\")(7, \"app-ai-chatbot\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, i0.ɵɵpipeBind1(1, 1, ctx.isDarkMode$)));\n        }\n      },\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  width: 100%;\\n}\\n\\n.dark[_ngcontent-%COMP%] {\\n  color-scheme: dark;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFwcC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsaUJBQWlCO0VBQ2pCLFdBQVc7QUFDYjs7QUFFQTtFQUNFLGtCQUFrQjtBQUNwQiIsImZpbGUiOiJhcHAuY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi5hcHAtY29udGFpbmVyIHtcbiAgbWluLWhlaWdodDogMTAwdmg7XG4gIHdpZHRoOiAxMDAlO1xufVxuXG4uZGFyayB7XG4gIGNvbG9yLXNjaGVtZTogZGFyaztcbn1cbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxpQkFBaUI7RUFDakIsV0FBVztBQUNiOztBQUVBO0VBQ0Usa0JBQWtCO0FBQ3BCOztBQUVBLGdhQUFnYSIsInNvdXJjZXNDb250ZW50IjpbIi5hcHAtY29udGFpbmVyIHtcbiAgbWluLWhlaWdodDogMTAwdmg7XG4gIHdpZHRoOiAxMDAlO1xufVxuXG4uZGFyayB7XG4gIGNvbG9yLXNjaGVtZTogZGFyaztcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "logger", "themeService", "title", "isDarkMode$", "darkMode$", "ngOnInit", "setLogs<PERSON>nabled", "enableComponentLogs", "i0", "ɵɵdirectiveInject", "i1", "LoggerService", "i2", "ThemeService", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ɵɵpipeBind1"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { LoggerService } from './services/logger.service';\nimport { Observable } from 'rxjs';\nimport { ThemeService } from './services/theme.service';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css'],\n})\nexport class AppComponent implements OnInit {\n  title = 'frontend';\n  isDarkMode$: Observable<boolean>;\n  constructor(\n    private logger: LoggerService,\n    private themeService: ThemeService\n  ) {\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n\n  ngOnInit() {\n    // Activer les logs\n    this.logger.setLogsEnabled(true);\n\n    // Activer les logs pour certains composants spécifiques\n    this.logger.enableComponentLogs('MessageService');\n    this.logger.enableComponentLogs('MessageChat');\n  }\n}\n", "<div class=\"app-container\" [ngClass]=\"{ dark: isDarkMode$ | async }\">\n  <router-outlet></router-outlet>\n  <app-incoming-call></app-incoming-call>\n  <app-active-call></app-active-call>\n  <app-connection-status></app-connection-status>\n  <app-graphql-status></app-graphql-status>\n\n  <!-- Chatbot IA disponible sur toutes les pages -->\n  <app-ai-chatbot></app-ai-chatbot>\n</div>\n"], "mappings": ";;;;;;;;AAUA,OAAM,MAAOA,YAAY;EAGvBC,YACUC,MAAqB,EACrBC,YAA0B;IAD1B,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IAJtB,KAAAC,KAAK,GAAG,UAAU;IAMhB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACF,YAAY,CAACG,SAAS;EAChD;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACL,MAAM,CAACM,cAAc,CAAC,IAAI,CAAC;IAEhC;IACA,IAAI,CAACN,MAAM,CAACO,mBAAmB,CAAC,gBAAgB,CAAC;IACjD,IAAI,CAACP,MAAM,CAACO,mBAAmB,CAAC,aAAa,CAAC;EAChD;;;uBAjBWT,YAAY,EAAAU,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAZf,YAAY;MAAAgB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVzBZ,EAAA,CAAAc,cAAA,aAAqE;;UACnEd,EAAA,CAAAe,SAAA,oBAA+B;UAQjCf,EAAA,CAAAgB,YAAA,EAAM;;;UATqBhB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAAnB,EAAA,CAAAoB,WAAA,OAAAP,GAAA,CAAAlB,WAAA,GAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}