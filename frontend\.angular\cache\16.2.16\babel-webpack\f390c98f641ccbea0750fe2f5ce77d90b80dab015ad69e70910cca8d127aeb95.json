{"ast": null, "code": "import { BehaviorSubject, Observable, of, throwError, retry, EMP<PERSON> } from 'rxjs';\nimport { map, catchError, tap, filter, switchMap } from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport { MessageType, CallType, CallStatus } from '../models/message.model';\nimport { GET_CONVERSATIONS_QUERY, GET_NOTIFICATIONS_QUERY, NOTIFICATION_SUBSCRIPTION, GET_CONVERSATION_QUERY, SEND_MESSAGE_MUTATION, MARK_AS_READ_MUTATION, MESSAGE_SENT_SUBSCRIPTION, USER_STATUS_SUBSCRIPTION, GET_USER_QUERY, GET_ALL_USER_QUERY, CONVERSATION_UPDATED_SUBSCRIPTION, SEARCH_MESSAGES_QUERY, GET_UNREAD_MESSAGES_QUERY, SET_USER_ONLINE_MUTATION, SET_USER_OFFLINE_MUTATION, START_TYPING_MUTATION, STOP_TYPING_MUTATION, TYPING_INDICATOR_SUBSCRIPTION, GET_CURRENT_USER_QUERY, REACT_TO_MESSAGE_MUTATION, FORWARD_MESSAGE_MUTATION, PIN_MESSAGE_MUTATION, CREATE_GROUP_MUTATION, UPDATE_GROUP_MUTATION, DELETE_GROUP_MUTATION, LEAVE_GROUP_MUTATION, GET_GROUP_QUERY, GET_USER_GROUPS_QUERY, EDIT_MESSAGE_MUTATION, DELETE_MESSAGE_MUTATION, GET_MESSAGES_QUERY, GET_NOTIFICATIONS_ATTACHAMENTS, MARK_NOTIFICATION_READ_MUTATION, NOTIFICATIONS_READ_SUBSCRIPTION, CREATE_CONVERSATION_MUTATION, DELETE_NOTIFICATION_MUTATION, DELETE_MULTIPLE_NOTIFICATIONS_MUTATION, DELETE_ALL_NOTIFICATIONS_MUTATION,\n// Requêtes et mutations pour les appels\nCALL_HISTORY_QUERY, CALL_DETAILS_QUERY, CALL_STATS_QUERY, INITIATE_CALL_MUTATION, SEND_CALL_SIGNAL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, CALL_SIGNAL_SUBSCRIPTION, INCOMING_CALL_SUBSCRIPTION, GET_VOICE_MESSAGES_QUERY } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class MessageService {\n  constructor(apollo, logger, zone) {\n    this.apollo = apollo;\n    this.logger = logger;\n    this.zone = zone;\n    // État partagé\n    this.activeConversation = new BehaviorSubject(null);\n    this.notifications = new BehaviorSubject([]);\n    this.notificationCache = new Map();\n    this.notificationCount = new BehaviorSubject(0);\n    this.onlineUsers = new Map();\n    this.subscriptions = [];\n    this.CACHE_DURATION = 300000;\n    this.lastFetchTime = 0;\n    // Propriétés pour les appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    this.callSignals = new BehaviorSubject(null);\n    this.localStream = null;\n    this.remoteStream = null;\n    this.peerConnection = null;\n    // Observables publics pour les appels\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    this.callSignals$ = this.callSignals.asObservable();\n    this.localStream$ = new BehaviorSubject(null);\n    this.remoteStream$ = new BehaviorSubject(null);\n    // Configuration WebRTC\n    this.rtcConfig = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    this.usersCache = [];\n    // Pagination metadata for user list\n    this.currentUserPagination = {\n      totalCount: 0,\n      totalPages: 0,\n      currentPage: 1,\n      hasNextPage: false,\n      hasPreviousPage: false\n    };\n    // Observables publics\n    this.activeConversation$ = this.activeConversation.asObservable();\n    this.notifications$ = this.notifications.asObservable();\n    this.notificationCount$ = this.notificationCount.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // --------------------------------------------------------------------------\n    // Section 2: Méthodes pour les Notifications\n    // --------------------------------------------------------------------------\n    // Propriétés pour la pagination des notifications\n    this.notificationPagination = {\n      currentPage: 1,\n      limit: 10,\n      hasMoreNotifications: true\n    };\n    this.toSafeISOString = date => {\n      if (!date) return undefined;\n      return typeof date === 'string' ? date : date.toISOString();\n    };\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  loadNotificationsFromLocalStorage() {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications);\n        this.notificationCache.clear();\n        notifications.forEach(notification => {\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  initSubscriptions() {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n      // 🔥 AJOUT: Subscription générale pour l'utilisateur\n    });\n\n    this.subscribeToUserStatus();\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    return this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.incomingCall) {\n        return null;\n      }\n      // Gérer l'appel entrant\n      this.handleIncomingCall(data.incomingCall);\n      return data.incomingCall;\n    }), catchError(error => {\n      this.logger.error('Error in incoming call subscription', error);\n      return of(null);\n    }));\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      return;\n    }\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n      sound.loop = loop;\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(error => {\n          // Handle error silently\n        });\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted) {\n    this.muted = muted;\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted() {\n    return this.muted;\n  }\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound() {\n    console.log('MessageService: Tentative de lecture du son de notification');\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n    // Créer une mélodie agréable avec l'API Web Audio\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // 🎵 TESTEZ DIFFÉRENTS SONS - Décommentez celui que vous voulez tester !\n      // SON 1: Mélodie douce (WhatsApp style) - ACTUEL\n      this.playNotificationMelody1(audioContext);\n      // SON 2: Mélodie montante (iPhone style) - Décommentez pour tester\n      // this.playNotificationMelody2(audioContext);\n      // SON 3: Mélodie descendante (Messenger style) - Décommentez pour tester\n      // this.playNotificationMelody3(audioContext);\n      // SON 4: Triple note (Discord style) - Décommentez pour tester\n      // this.playNotificationMelody4(audioContext);\n      // SON 5: Cloche douce (Slack style) - Décommentez pour tester\n      // this.playNotificationMelody5(audioContext);\n      console.log('MessageService: Son de notification mélodieux généré avec succès');\n    } catch (error) {\n      console.error('MessageService: Erreur lors de la génération du son:', error);\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 0.7; // Volume plus doux\n        audio.play().catch(err => {\n          console.error('MessageService: Erreur lors de la lecture du fichier son:', err);\n        });\n      } catch (audioError) {\n        console.error('MessageService: Exception lors de la lecture du fichier son:', audioError);\n      }\n    }\n  }\n  // 🎵 SON 1: Mélodie douce (WhatsApp style)\n  playNotificationMelody1(audioContext) {\n    this.playNotificationTone(audioContext, 0, 659.25, 0.15); // E5\n    this.playNotificationTone(audioContext, 0.15, 523.25, 0.15); // C5\n  }\n  // 🎵 SON 2: Mélodie montante (iPhone style)\n  playNotificationMelody2(audioContext) {\n    this.playNotificationTone(audioContext, 0, 523.25, 0.12); // C5\n    this.playNotificationTone(audioContext, 0.12, 659.25, 0.12); // E5\n    this.playNotificationTone(audioContext, 0.24, 783.99, 0.16); // G5\n  }\n  // 🎵 SON 3: Mélodie descendante (Messenger style)\n  playNotificationMelody3(audioContext) {\n    this.playNotificationTone(audioContext, 0, 880, 0.1); // A5\n    this.playNotificationTone(audioContext, 0.1, 659.25, 0.1); // E5\n    this.playNotificationTone(audioContext, 0.2, 523.25, 0.15); // C5\n  }\n  // 🎵 SON 4: Triple note (Discord style)\n  playNotificationMelody4(audioContext) {\n    this.playNotificationTone(audioContext, 0, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.08, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.16, 880, 0.12); // A5\n  }\n  // 🎵 SON 5: Cloche douce (Slack style)\n  playNotificationMelody5(audioContext) {\n    this.playBellTone(audioContext, 0, 1046.5, 0.4); // C6 - son de cloche\n  }\n  /**\n   * Joue une note individuelle pour la mélodie de notification\n   */\n  playNotificationTone(audioContext, startTime, frequency, duration) {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n    // Configurer l'oscillateur pour un son plus doux\n    oscillator.type = 'sine';\n    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + startTime);\n    // Configurer le volume avec une enveloppe douce\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + startTime + 0.02);\n    gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + startTime + duration * 0.7);\n    gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + startTime + duration);\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n  /**\n   * Joue un son de cloche pour les notifications\n   */\n  playBellTone(audioContext, startTime, frequency, duration) {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n    // Configurer l'oscillateur pour un son de cloche\n    oscillator.type = 'triangle'; // Son plus doux que sine\n    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + startTime);\n    // Enveloppe de cloche (attaque rapide, déclin lent)\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(0.4, audioContext.currentTime + startTime + 0.01);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + startTime + duration);\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl) {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n      audio.onended = () => {\n        resolve();\n      };\n      audio.onerror = error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n      audio.play().catch(error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages() {\n    this.logger.debug('[MessageService] Getting voice messages');\n    return this.apollo.watchQuery({\n      query: GET_VOICE_MESSAGES_QUERY,\n      fetchPolicy: 'network-only' // Ne pas utiliser le cache pour cette requête\n    }).valueChanges.pipe(map(result => {\n      const voiceMessages = result.data?.getVoiceMessages || [];\n      this.logger.debug(`[MessageService] Retrieved ${voiceMessages.length} voice messages`);\n      return voiceMessages;\n    }), catchError(error => {\n      this.logger.error('[MessageService] Error fetching voice messages:', error);\n      return throwError(() => new Error('Failed to fetch voice messages'));\n    }));\n  }\n  // Message methods\n  getMessages(senderId, receiverId, conversationId, page = 1, limit = 10) {\n    return this.apollo.watchQuery({\n      query: GET_MESSAGES_QUERY,\n      variables: {\n        senderId,\n        receiverId,\n        conversationId,\n        limit,\n        page\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const messages = result.data?.getMessages || [];\n      return messages.map(msg => this.normalizeMessage(msg));\n    }), catchError(error => {\n      this.logger.error('Error fetching messages:', error);\n      return throwError(() => new Error('Failed to fetch messages'));\n    }));\n  }\n  editMessage(messageId, newContent) {\n    return this.apollo.mutate({\n      mutation: EDIT_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        newContent\n      }\n    }).pipe(map(result => {\n      if (!result.data?.editMessage) {\n        throw new Error('Failed to edit message');\n      }\n      return this.normalizeMessage(result.data.editMessage);\n    }), catchError(error => {\n      this.logger.error('Error editing message:', error);\n      return throwError(() => new Error('Failed to edit message'));\n    }));\n  }\n  deleteMessage(messageId) {\n    return this.apollo.mutate({\n      mutation: DELETE_MESSAGE_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.deleteMessage) {\n        throw new Error('Failed to delete message');\n      }\n      return this.normalizeMessage(result.data.deleteMessage);\n    }), catchError(error => {\n      this.logger.error('Error deleting message:', error);\n      return throwError(() => new Error('Failed to delete message'));\n    }));\n  }\n  markMessageAsRead(messageId) {\n    return this.apollo.mutate({\n      mutation: MARK_AS_READ_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.markMessageAsRead) throw new Error('Failed to mark message as read');\n      return {\n        ...result.data.markMessageAsRead,\n        readAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error marking message as read:', error);\n      return throwError(() => new Error('Failed to mark message as read'));\n    }));\n  }\n  reactToMessage(messageId, emoji) {\n    return this.apollo.mutate({\n      mutation: REACT_TO_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        emoji\n      }\n    }).pipe(map(result => {\n      if (!result.data?.reactToMessage) throw new Error('Failed to react to message');\n      return result.data.reactToMessage;\n    }), catchError(error => {\n      console.error('Error reacting to message:', error);\n      return throwError(() => new Error('Failed to react to message'));\n    }));\n  }\n  forwardMessage(messageId, conversationIds) {\n    return this.apollo.mutate({\n      mutation: FORWARD_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationIds\n      }\n    }).pipe(map(result => {\n      if (!result.data?.forwardMessage) throw new Error('Failed to forward message');\n      return result.data.forwardMessage.map(msg => ({\n        ...msg,\n        timestamp: msg.timestamp ? this.normalizeDate(msg.timestamp) : new Date()\n      }));\n    }), catchError(error => {\n      console.error('Error forwarding message:', error);\n      return throwError(() => new Error('Failed to forward message'));\n    }));\n  }\n  pinMessage(messageId, conversationId) {\n    return this.apollo.mutate({\n      mutation: PIN_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.pinMessage) throw new Error('Failed to pin message');\n      return {\n        ...result.data.pinMessage,\n        pinnedAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error pinning message:', error);\n      return throwError(() => new Error('Failed to pin message'));\n    }));\n  }\n  searchMessages(query, conversationId, filters = {}) {\n    return this.apollo.watchQuery({\n      query: SEARCH_MESSAGES_QUERY,\n      variables: {\n        query,\n        conversationId,\n        ...filters,\n        dateFrom: this.toSafeISOString(filters.dateFrom),\n        dateTo: this.toSafeISOString(filters.dateTo)\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.searchMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error searching messages:', error);\n      return throwError(() => new Error('Failed to search messages'));\n    }));\n  }\n  getUnreadMessages(userId) {\n    return this.apollo.watchQuery({\n      query: GET_UNREAD_MESSAGES_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.getUnreadMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error fetching unread messages:', error);\n      return throwError(() => new Error('Failed to fetch unread messages'));\n    }));\n  }\n  setActiveConversation(conversationId) {\n    this.activeConversation.next(conversationId);\n  }\n  getConversations() {\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATIONS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const conversations = result.data?.getConversations || [];\n      return conversations.map(conv => this.normalizeConversation(conv));\n    }), catchError(error => {\n      console.error('Error fetching conversations:', error);\n      return throwError(() => new Error('Failed to load conversations'));\n    }));\n  }\n  getConversation(conversationId, limit, page) {\n    this.logger.info(`[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`);\n    const variables = {\n      conversationId\n    };\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(`[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`);\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(`[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`);\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATION_QUERY,\n      variables: variables,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug(`[MessageService] Conversation response received:`, result);\n      const conv = result.data?.getConversation;\n      if (!conv) {\n        this.logger.error(`[MessageService] Conversation not found: ${conversationId}`);\n        throw new Error('Conversation not found');\n      }\n      this.logger.debug(`[MessageService] Normalizing conversation: ${conversationId}`);\n      const normalizedConversation = this.normalizeConversation(conv);\n      this.logger.info(`[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${normalizedConversation.participants?.length || 0}, messages: ${normalizedConversation.messages?.length || 0}`);\n      return normalizedConversation;\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error fetching conversation:`, error);\n      return throwError(() => new Error('Failed to load conversation'));\n    }));\n  }\n  createConversation(userId) {\n    this.logger.info(`[MessageService] Creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to create a conversation'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_CONVERSATION_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      this.logger.debug(`[MessageService] Conversation creation response:`, result);\n      const conversation = result.data?.createConversation;\n      if (!conversation) {\n        this.logger.error(`[MessageService] Failed to create conversation with user: ${userId}`);\n        throw new Error('Failed to create conversation');\n      }\n      try {\n        const normalizedConversation = this.normalizeConversation(conversation);\n        this.logger.info(`[MessageService] Conversation created successfully: ${normalizedConversation.id}`);\n        return normalizedConversation;\n      } catch (error) {\n        this.logger.error(`[MessageService] Error normalizing created conversation:`, error);\n        throw new Error('Error processing created conversation');\n      }\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error creating conversation with user ${userId}:`, error);\n      return throwError(() => new Error(`Failed to create conversation: ${error.message}`));\n    }));\n  }\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId) {\n    this.logger.info(`[MessageService] Getting or creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot get/create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to get/create a conversation'));\n    }\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(map(conversations => {\n      // Récupérer l'ID de l'utilisateur actuel\n      const currentUserId = this.getCurrentUserId();\n      // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n      const existingConversation = conversations.find(conv => {\n        if (conv.isGroup) return false;\n        // Vérifier si la conversation contient les deux utilisateurs\n        const participantIds = conv.participants?.map(p => p.id || p._id) || [];\n        return participantIds.includes(userId) && participantIds.includes(currentUserId);\n      });\n      if (existingConversation) {\n        this.logger.info(`[MessageService] Found existing conversation: ${existingConversation.id}`);\n        return existingConversation;\n      }\n      // Si aucune conversation n'est trouvée, en créer une nouvelle\n      throw new Error('No existing conversation found');\n    }), catchError(error => {\n      this.logger.info(`[MessageService] No existing conversation found, creating new one: ${error.message}`);\n      return this.createConversation(userId);\n    }));\n  }\n  getNotifications(refresh = false, page = 1, limit = 10) {\n    this.logger.info('MessageService', `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`);\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY\n    });\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug('MessageService', 'Resetting pagination due to refresh');\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug('MessageService', `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`);\n    return this.apollo.watchQuery({\n      query: GET_NOTIFICATIONS_QUERY,\n      variables: {\n        page: page,\n        limit: limit\n      },\n      fetchPolicy: refresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Notifications response received');\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      const notifications = result.data?.getUserNotifications || [];\n      this.logger.debug('MessageService', `Received ${notifications.length} notifications from server for page ${page}`);\n      // Vérifier s'il y a plus de notifications à charger\n      this.notificationPagination.hasMoreNotifications = notifications.length >= limit;\n      if (notifications.length === 0) {\n        this.logger.info('MessageService', 'No notifications received from server');\n        this.notificationPagination.hasMoreNotifications = false;\n      }\n      // Filtrer les notifications supprimées\n      const filteredNotifications = notifications.filter(notif => !deletedNotificationIds.has(notif.id));\n      this.logger.debug('MessageService', `Filtered out ${notifications.length - filteredNotifications.length} deleted notifications`);\n      // Afficher les notifications reçues pour le débogage\n      filteredNotifications.forEach((notif, index) => {\n        console.log(`Notification ${index + 1} (page ${page}):`, {\n          id: notif.id || notif._id,\n          type: notif.type,\n          content: notif.content,\n          isRead: notif.isRead\n        });\n      });\n      // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n      // Mettre à jour le cache avec les nouvelles notifications\n      this.updateCache(filteredNotifications);\n      // Récupérer toutes les notifications du cache et les TRIER\n      const cachedNotifications = Array.from(this.notificationCache.values());\n      // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n      const sortedNotifications = this.sortNotificationsByDate(cachedNotifications);\n      console.log(`📊 SORTED: ${sortedNotifications.length} notifications triées (plus récentes en premier)`);\n      // Mettre à jour le BehaviorSubject avec les notifications TRIÉES\n      this.notifications.next(sortedNotifications);\n      // Mettre à jour le compteur de notifications non lues\n      this.updateUnreadCount();\n      // Sauvegarder les notifications dans le localStorage\n      this.saveNotificationsToLocalStorage();\n      return cachedNotifications;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error loading notifications:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      return throwError(() => new Error('Failed to load notifications'));\n    }));\n  }\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  getDeletedNotificationIds() {\n    try {\n      const deletedIds = new Set();\n      const savedNotifications = localStorage.getItem('notifications');\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(JSON.parse(savedNotifications).map(n => n.id));\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications = this.apollo.client.readQuery({\n        query: GET_NOTIFICATIONS_QUERY\n      })?.getUserNotifications || [];\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach(notification => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n      return deletedIds;\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la récupération des IDs de notifications supprimées:', error);\n      return new Set();\n    }\n  }\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications() {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications() {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(false, nextPage, this.notificationPagination.limit);\n  }\n  getNotificationById(id) {\n    return this.notifications$.pipe(map(notifications => notifications.find(n => n.id === id)), catchError(error => {\n      this.logger.error('Error finding notification:', error);\n      return throwError(() => new Error('Failed to find notification'));\n    }));\n  }\n  getNotificationCount() {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId) {\n    return this.apollo.query({\n      query: GET_NOTIFICATIONS_ATTACHAMENTS,\n      variables: {\n        id: notificationId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => result.data?.getNotificationAttachments || []), catchError(error => {\n      this.logger.error('Error fetching notification attachments:', error);\n      return throwError(() => new Error('Failed to fetch attachments'));\n    }));\n  }\n  getUnreadNotifications() {\n    return this.notifications$.pipe(map(notifications => notifications.filter(n => !n.isRead)));\n  }\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(notificationId) {\n    this.logger.debug('MessageService', `Suppression de la notification ${notificationId}`);\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const removedCount = this.removeNotificationsFromCache([notificationId]);\n    // Appeler le backend pour supprimer la notification\n    return this.apollo.mutate({\n      mutation: DELETE_NOTIFICATION_MUTATION,\n      variables: {\n        notificationId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteNotification;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression de la notification', {\n      success: true,\n      message: 'Notification supprimée localement (erreur serveur)'\n    })));\n  }\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  saveNotificationsToLocalStorage() {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug('MessageService', 'Notifications sauvegardées localement');\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la sauvegarde des notifications:', error);\n    }\n  }\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications() {\n    this.logger.debug('MessageService', 'Suppression de toutes les notifications');\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    const allNotificationIds = Array.from(this.notificationCache.keys());\n    this.removeNotificationsFromCache(allNotificationIds);\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_ALL_NOTIFICATIONS_MUTATION\n    }).pipe(map(result => {\n      const response = result.data?.deleteAllNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression de toutes les notifications:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression de toutes les notifications', {\n      success: true,\n      count,\n      message: `${count} notifications supprimées localement (erreur serveur)`\n    })));\n  }\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(notificationIds) {\n    this.logger.debug('MessageService', `Suppression de ${notificationIds.length} notifications`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.removeNotificationsFromCache(notificationIds);\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n      variables: {\n        notificationIds\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteMultipleNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression multiple:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression multiple de notifications', {\n      success: count > 0,\n      count,\n      message: `${count} notifications supprimées localement (erreur serveur)`\n    })));\n  }\n  groupNotificationsByType() {\n    return this.notifications$.pipe(map(notifications => {\n      const groups = new Map();\n      notifications.forEach(notif => {\n        if (!groups.has(notif.type)) {\n          groups.set(notif.type, []);\n        }\n        groups.get(notif.type)?.push(notif);\n      });\n      return groups;\n    }));\n  }\n  markAsRead(notificationIds) {\n    this.logger.debug('MessageService', `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value\n      });\n    }\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n    this.logger.debug('MessageService', 'Sending mutation to mark notifications as read', validIds);\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      }\n    };\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n    return this.apollo.mutate({\n      mutation: MARK_NOTIFICATION_READ_MUTATION,\n      variables: {\n        notificationIds: validIds\n      },\n      optimisticResponse: optimisticResponse,\n      errorPolicy: 'all',\n      fetchPolicy: 'no-cache' // Ne pas utiliser le cache pour cette mutation\n    }).pipe(map(result => {\n      this.logger.debug('MessageService', 'Mutation result', result);\n      console.log('Mutation result:', result);\n      // Si nous avons des erreurs GraphQL, les logger mais continuer\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        console.error('GraphQL errors:', result.errors);\n      }\n      // Utiliser la réponse du serveur ou notre réponse optimiste\n      const response = result.data?.markNotificationsAsRead ?? optimisticResponse.markNotificationsAsRead;\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error marking notifications as read:', error);\n      console.error('Error in markAsRead:', error);\n      // En cas d'erreur, retourner quand même un succès simulé\n      // puisque nous avons déjà mis à jour l'interface utilisateur\n      return of({\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      });\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels\n  // --------------------------------------------------------------------------\n  /**\n   * Initie un appel avec un autre utilisateur\n   * @param recipientId ID de l'utilisateur à appeler\n   * @param callType Type d'appel (audio, vidéo)\n   * @param conversationId ID de la conversation (optionnel)\n   * @param options Options d'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  initiateCall(recipientId, callType, conversationId, options) {\n    return this.setupMediaDevices(callType).pipe(switchMap(stream => {\n      this.localStream = stream;\n      this.localStream$.next(stream);\n      // Créer une connexion peer\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      // Ajouter les pistes audio/vidéo\n      stream.getTracks().forEach(track => {\n        this.peerConnection.addTrack(track, stream);\n      });\n      // Écouter les candidats ICE\n      this.peerConnection.onicecandidate = event => {\n        if (event.candidate) {\n          this.sendCallSignal(this.generateCallId(), 'ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n      // Écouter les pistes distantes\n      this.peerConnection.ontrack = event => {\n        if (!this.remoteStream) {\n          this.remoteStream = new MediaStream();\n          this.remoteStream$.next(this.remoteStream);\n        }\n        event.streams[0].getTracks().forEach(track => {\n          this.remoteStream.addTrack(track);\n        });\n      };\n      // Créer l'offre SDP\n      return from(this.peerConnection.createOffer()).pipe(switchMap(offer => {\n        return from(this.peerConnection.setLocalDescription(offer)).pipe(map(() => offer));\n      }));\n    }), switchMap(offer => {\n      // Générer un ID d'appel unique\n      const callId = this.generateCallId();\n      // Envoyer l'offre au serveur\n      return this.apollo.mutate({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: {\n          recipientId,\n          callType,\n          callId,\n          offer: JSON.stringify(offer),\n          conversationId,\n          options\n        }\n      }).pipe(map(result => {\n        const call = result.data?.initiateCall;\n        if (!call) {\n          throw new Error('Failed to initiate call');\n        }\n        // Mettre à jour l'état de l'appel actif\n        this.activeCall.next(call);\n        // S'abonner aux signaux d'appel\n        const signalSub = this.subscribeToCallSignals(call.id).subscribe();\n        this.subscriptions.push(signalSub);\n        return call;\n      }));\n    }), catchError(error => {\n      this.logger.error('Error initiating call', error);\n      this.cleanupCall();\n      return throwError(() => new Error('Failed to initiate call'));\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   * @param incomingCall Appel entrant à accepter\n   * @returns Observable avec les informations de l'appel\n   */\n  acceptCall(incomingCall) {\n    this.stop('ringtone');\n    return this.setupMediaDevices(incomingCall.type).pipe(switchMap(stream => {\n      this.localStream = stream;\n      this.localStream$.next(stream);\n      // Créer une connexion peer\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      // Ajouter les pistes audio/vidéo\n      stream.getTracks().forEach(track => {\n        this.peerConnection.addTrack(track, stream);\n      });\n      // Écouter les candidats ICE\n      this.peerConnection.onicecandidate = event => {\n        if (event.candidate) {\n          this.sendCallSignal(incomingCall.id, 'ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n      // Écouter les pistes distantes\n      this.peerConnection.ontrack = event => {\n        if (!this.remoteStream) {\n          this.remoteStream = new MediaStream();\n          this.remoteStream$.next(this.remoteStream);\n        }\n        event.streams[0].getTracks().forEach(track => {\n          this.remoteStream.addTrack(track);\n        });\n      };\n      // Définir l'offre distante\n      const offer = JSON.parse(incomingCall.offer);\n      return from(this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer))).pipe(switchMap(() => from(this.peerConnection.createAnswer())), switchMap(answer => {\n        return from(this.peerConnection.setLocalDescription(answer)).pipe(map(() => answer));\n      }));\n    }), switchMap(answer => {\n      // Envoyer la réponse au serveur\n      return this.apollo.mutate({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer: JSON.stringify(answer)\n        }\n      }).pipe(map(result => {\n        const call = result.data?.acceptCall;\n        if (!call) {\n          throw new Error('Failed to accept call');\n        }\n        // Jouer le son de connexion\n        this.play('call-connected');\n        // Mettre à jour l'état de l'appel actif\n        this.activeCall.next({\n          ...call,\n          caller: incomingCall.caller,\n          type: incomingCall.type,\n          conversationId: incomingCall.conversationId\n        });\n        // S'abonner aux signaux d'appel\n        const signalSub = this.subscribeToCallSignals(incomingCall.id).subscribe();\n        this.subscriptions.push(signalSub);\n        // Effacer l'appel entrant\n        this.incomingCall.next(null);\n        return call;\n      }));\n    }), catchError(error => {\n      this.logger.error('Error accepting call', error);\n      this.cleanupCall();\n      return throwError(() => new Error('Failed to accept call'));\n    }));\n  }\n  /**\n   * Rejette un appel entrant\n   * @param callId ID de l'appel à rejeter\n   * @param reason Raison du rejet (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  rejectCall(callId, reason) {\n    this.stop('ringtone');\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason\n      }\n    }).pipe(map(result => {\n      const call = result.data?.rejectCall;\n      if (!call) {\n        throw new Error('Failed to reject call');\n      }\n      // Effacer l'appel entrant\n      this.incomingCall.next(null);\n      return call;\n    }), catchError(error => {\n      this.logger.error('Error rejecting call', error);\n      return throwError(() => new Error('Failed to reject call'));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   * @param callId ID de l'appel à terminer\n   * @param feedback Commentaires sur l'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  endCall(callId, feedback) {\n    this.stop('ringtone');\n    this.play('call-end');\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback\n      }\n    }).pipe(map(result => {\n      const call = result.data?.endCall;\n      if (!call) {\n        throw new Error('Failed to end call');\n      }\n      // Nettoyer les ressources\n      this.cleanupCall();\n      // Mettre à jour l'état de l'appel actif\n      this.activeCall.next(null);\n      return call;\n    }), catchError(error => {\n      this.logger.error('Error ending call', error);\n      this.cleanupCall();\n      return throwError(() => new Error('Failed to end call'));\n    }));\n  }\n  /**\n   * Active ou désactive la caméra ou le micro\n   * @param callId ID de l'appel\n   * @param video État de la caméra (optionnel)\n   * @param audio État du micro (optionnel)\n   * @returns Observable avec le résultat de l'opération\n   */\n  toggleMedia(callId, video, audio) {\n    if (this.localStream) {\n      // Mettre à jour les pistes locales\n      if (video !== undefined) {\n        this.localStream.getVideoTracks().forEach(track => {\n          track.enabled = video;\n        });\n      }\n      if (audio !== undefined) {\n        this.localStream.getAudioTracks().forEach(track => {\n          track.enabled = audio;\n        });\n      }\n    }\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video,\n        audio\n      }\n    }).pipe(map(result => {\n      const success = result.data?.toggleCallMedia;\n      if (!success) {\n        throw new Error('Failed to toggle media');\n      }\n      return success;\n    }), catchError(error => {\n      this.logger.error('Error toggling media', error);\n      return throwError(() => new Error('Failed to toggle media'));\n    }));\n  }\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId) {\n    return this.apollo.subscribe({\n      query: CALL_SIGNAL_SUBSCRIPTION,\n      variables: {\n        callId\n      }\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.callSignal) {\n        throw new Error('No call signal received');\n      }\n      return data.callSignal;\n    }), tap(signal => {\n      this.callSignals.next(signal);\n      this.handleCallSignal(signal);\n    }), catchError(error => {\n      this.logger.error('Error in call signal subscription', error);\n      return throwError(() => new Error('Call signal subscription failed'));\n    }));\n  }\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(callId, signalType, signalData) {\n    return this.apollo.mutate({\n      mutation: SEND_CALL_SIGNAL_MUTATION,\n      variables: {\n        callId,\n        signalType,\n        signalData\n      }\n    }).pipe(map(result => {\n      const success = result.data?.sendCallSignal;\n      if (!success) {\n        throw new Error('Failed to send call signal');\n      }\n      return success;\n    }), catchError(error => {\n      this.logger.error('Error sending call signal', error);\n      return throwError(() => new Error('Failed to send call signal'));\n    }));\n  }\n  /**\n   * Récupère l'historique des appels avec filtres\n   * @param limit Nombre d'appels à récupérer\n   * @param offset Décalage pour la pagination\n   * @param status Filtres de statut\n   * @param type Filtres de type\n   * @param startDate Date de début\n   * @param endDate Date de fin\n   * @returns Observable avec l'historique des appels\n   */\n  getCallHistory(limit = 20, offset = 0, status, type, startDate, endDate) {\n    return this.apollo.watchQuery({\n      query: CALL_HISTORY_QUERY,\n      variables: {\n        limit,\n        offset,\n        status,\n        type,\n        startDate,\n        endDate\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const history = result.data?.callHistory || [];\n      this.logger.debug(`Retrieved ${history.length} call history items`);\n      return history;\n    }), catchError(error => {\n      this.logger.error('Error fetching call history:', error);\n      return throwError(() => new Error('Failed to fetch call history'));\n    }));\n  }\n  /**\n   * Récupère les détails d'un appel spécifique\n   * @param callId ID de l'appel\n   * @returns Observable avec les détails de l'appel\n   */\n  getCallDetails(callId) {\n    return this.apollo.watchQuery({\n      query: CALL_DETAILS_QUERY,\n      variables: {\n        callId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const details = result.data?.callDetails;\n      if (!details) {\n        throw new Error('Call details not found');\n      }\n      this.logger.debug(`Retrieved call details for: ${callId}`);\n      return details;\n    }), catchError(error => {\n      this.logger.error('Error fetching call details:', error);\n      return throwError(() => new Error('Failed to fetch call details'));\n    }));\n  }\n  /**\n   * Récupère les statistiques d'appels\n   * @returns Observable avec les statistiques d'appels\n   */\n  getCallStats() {\n    return this.apollo.watchQuery({\n      query: CALL_STATS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const stats = result.data?.callStats;\n      if (!stats) {\n        throw new Error('Call stats not found');\n      }\n      this.logger.debug('Retrieved call stats:', stats);\n      return stats;\n    }), catchError(error => {\n      this.logger.error('Error fetching call stats:', error);\n      return throwError(() => new Error('Failed to fetch call stats'));\n    }));\n  }\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  handleCallSignal(signal) {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  handleIceCandidate(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate)).catch(error => {\n        this.logger.error('Error adding ICE candidate', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error);\n    }\n  }\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  handleAnswer(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer)).catch(error => {\n        this.logger.error('Error setting remote description', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error);\n    }\n  }\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  handleEndCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  handleRejectCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Nettoie les ressources d'appel\n   */\n  cleanupCall() {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  setupMediaDevices(callType) {\n    const constraints = {\n      audio: true,\n      video: callType !== CallType.AUDIO ? {\n        width: {\n          ideal: 1280\n        },\n        height: {\n          ideal: 720\n        }\n      } : false\n    };\n    return new Observable(observer => {\n      navigator.mediaDevices.getUserMedia(constraints).then(stream => {\n        observer.next(stream);\n        observer.complete();\n      }).catch(error => {\n        this.logger.error('Error accessing media devices', error);\n        observer.error(new Error('Failed to access media devices'));\n      });\n    });\n  }\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  generateCallId() {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(forceRefresh = false, search, page = 1, limit = 10, sortBy = 'username', sortOrder = 'asc', isOnline) {\n    this.logger.info('MessageService', `Getting users with params: forceRefresh=${forceRefresh}, search=${search || '(empty)'}, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`);\n    const now = Date.now();\n    const cacheValid = !forceRefresh && this.usersCache.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION && !search && page === 1 && limit >= this.usersCache.length;\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug('MessageService', `Using cached users (${this.usersCache.length} users)`);\n      return of([...this.usersCache]);\n    }\n    this.logger.debug('MessageService', `Fetching users from server with pagination, fetchPolicy=${forceRefresh ? 'network-only' : 'cache-first'}`);\n    return this.apollo.watchQuery({\n      query: GET_ALL_USER_QUERY,\n      variables: {\n        search,\n        page,\n        limit,\n        sortBy,\n        sortOrder,\n        isOnline: isOnline !== undefined ? isOnline : null\n      },\n      fetchPolicy: forceRefresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Users response received', result);\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors in getAllUsers:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      if (!result.data?.getAllUsers) {\n        this.logger.warn('MessageService', 'No users data received from server');\n        return [];\n      }\n      const paginatedResponse = result.data.getAllUsers;\n      // Log pagination metadata\n      this.logger.debug('MessageService', 'Pagination metadata:', {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      });\n      // Normalize users with error handling\n      const users = [];\n      for (const user of paginatedResponse.users) {\n        try {\n          if (user) {\n            users.push(this.normalizeUser(user));\n          }\n        } catch (error) {\n          this.logger.warn('MessageService', `Error normalizing user, skipping:`, error);\n        }\n      }\n      this.logger.info('MessageService', `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`);\n      // Update cache only for first page with no filters\n      if (!search && page === 1 && !isOnline) {\n        this.usersCache = [...users];\n        this.lastFetchTime = Date.now();\n        this.logger.debug('MessageService', `User cache updated with ${users.length} users`);\n      }\n      // Store pagination metadata in a property for component access\n      this.currentUserPagination = {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      };\n      return users;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching users:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      // Return cache if available (only for first page)\n      if (this.usersCache.length > 0 && page === 1 && !search && !isOnline) {\n        this.logger.warn('MessageService', `Returning ${this.usersCache.length} cached users due to fetch error`);\n        return of([...this.usersCache]);\n      }\n      return throwError(() => new Error(`Failed to fetch users: ${error.message || 'Unknown error'}`));\n    }));\n  }\n  getOneUser(userId) {\n    return this.apollo.watchQuery({\n      query: GET_USER_QUERY,\n      variables: {\n        id: userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getOneUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching user:', error);\n      return throwError(() => new Error('Failed to fetch user'));\n    }));\n  }\n  getCurrentUser() {\n    return this.apollo.watchQuery({\n      query: GET_CURRENT_USER_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getCurrentUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching current user:', error);\n      return throwError(() => new Error('Failed to fetch current user'));\n    }));\n  }\n  setUserOnline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_ONLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOnline) throw new Error('Failed to set user online');\n      return this.normalizeUser(result.data.setUserOnline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user online:', error);\n      return throwError(() => new Error('Failed to set user online'));\n    }));\n  }\n  setUserOffline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_OFFLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOffline) throw new Error('Failed to set user offline');\n      return this.normalizeUser(result.data.setUserOffline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user offline:', error);\n      return throwError(() => new Error('Failed to set user offline'));\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des Groupes\n  // --------------------------------------------------------------------------\n  /**\n   * Crée un nouveau groupe\n   */\n  createGroup(name, participantIds, photo, description) {\n    this.logger.debug('MessageService', `Creating group: ${name} with ${participantIds.length} participants`);\n    if (!name || !participantIds || participantIds.length === 0) {\n      return throwError(() => new Error('Nom du groupe et participants requis'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_GROUP_MUTATION,\n      variables: {\n        name,\n        participantIds,\n        photo,\n        description\n      }\n    }).pipe(map(result => {\n      const group = result.data?.createGroup;\n      if (!group) {\n        throw new Error('Échec de la création du groupe');\n      }\n      this.logger.info('MessageService', `Group created successfully: ${group.id}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error creating group:', error);\n      return throwError(() => new Error('Échec de la création du groupe'));\n    }));\n  }\n  /**\n   * Met à jour un groupe existant\n   */\n  updateGroup(groupId, input) {\n    this.logger.debug('MessageService', `Updating group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: UPDATE_GROUP_MUTATION,\n      variables: {\n        id: groupId,\n        input\n      }\n    }).pipe(map(result => {\n      const group = result.data?.updateGroup;\n      if (!group) {\n        throw new Error('Échec de la mise à jour du groupe');\n      }\n      this.logger.info('MessageService', `Group updated successfully: ${group.id}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error updating group:', error);\n      return throwError(() => new Error('Échec de la mise à jour du groupe'));\n    }));\n  }\n  /**\n   * Supprime un groupe\n   */\n  deleteGroup(groupId) {\n    this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: DELETE_GROUP_MUTATION,\n      variables: {\n        id: groupId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteGroup;\n      if (!response) {\n        throw new Error('Échec de la suppression du groupe');\n      }\n      this.logger.info('MessageService', `Group deleted successfully: ${groupId}`);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error deleting group:', error);\n      return throwError(() => new Error('Échec de la suppression du groupe'));\n    }));\n  }\n  /**\n   * Quitte un groupe\n   */\n  leaveGroup(groupId) {\n    this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: LEAVE_GROUP_MUTATION,\n      variables: {\n        groupId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.leaveGroup;\n      if (!response) {\n        throw new Error('Échec de la sortie du groupe');\n      }\n      this.logger.info('MessageService', `Left group successfully: ${groupId}`);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error leaving group:', error);\n      return throwError(() => new Error('Échec de la sortie du groupe'));\n    }));\n  }\n  /**\n   * Récupère les informations d'un groupe\n   */\n  getGroup(groupId) {\n    this.logger.debug('MessageService', `Getting group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.query({\n      query: GET_GROUP_QUERY,\n      variables: {\n        id: groupId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      const group = result.data?.getGroup;\n      if (!group) {\n        throw new Error('Groupe non trouvé');\n      }\n      this.logger.info('MessageService', `Group retrieved successfully: ${groupId}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error getting group:', error);\n      return throwError(() => new Error('Échec de la récupération du groupe'));\n    }));\n  }\n  /**\n   * Récupère les groupes d'un utilisateur\n   */\n  getUserGroups(userId) {\n    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n    if (!userId) {\n      return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n    }\n    return this.apollo.query({\n      query: GET_USER_GROUPS_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      const groups = result.data?.getUserGroups || [];\n      this.logger.info('MessageService', `Retrieved ${groups.length} groups for user: ${userId}`);\n      return groups;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error getting user groups:', error);\n      return throwError(() => new Error('Échec de la récupération des groupes'));\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section 4: Subscriptions et Gestion Temps Réel\n  // --------------------------------------------------------------------------\n  subscribeToNewMessages(conversationId) {\n    console.log(`🔍 DEBUG: subscribeToNewMessages called with conversationId: ${conversationId}`);\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    const tokenValid = this.isTokenValid();\n    console.log(`🔍 DEBUG: Token validation result: ${tokenValid}`);\n    if (!tokenValid) {\n      console.warn('❌ DEBUG: Token invalid - subscription will not be established');\n      this.logger.warn(\"Tentative d'abonnement aux messages avec un token invalide ou expiré\");\n      return of(null);\n    }\n    console.log(`✅ DEBUG: Token valid - proceeding with subscription setup for conversation: ${conversationId}`);\n    this.logger.debug(`🚀 INSTANT MESSAGE: Setting up real-time subscription for conversation: ${conversationId}`);\n    console.log(`🔍 DEBUG: Creating Apollo subscription with variables:`, {\n      conversationId\n    });\n    console.log(`🔍 DEBUG: MESSAGE_SENT_SUBSCRIPTION query:`, MESSAGE_SENT_SUBSCRIPTION);\n    console.log(`🔍 DEBUG: Subscription variables:`, {\n      conversationId\n    });\n    const sub$ = this.apollo.subscribe({\n      query: MESSAGE_SENT_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(tap(result => {\n      console.log(`🔍 DEBUG: Raw subscription result received:`, result);\n      console.log(`🔍 DEBUG: result.data:`, result.data);\n      console.log(`🔍 DEBUG: result.data?.messageSent:`, result.data?.messageSent);\n    }), map(result => {\n      const msg = result.data?.messageSent;\n      if (!msg) {\n        console.log(`❌ DEBUG: No message payload received in result:`, result);\n        this.logger.warn('⚠️ No message payload received');\n        throw new Error('No message payload received');\n      }\n      this.logger.debug('⚡ INSTANT: New message received via WebSocket', msg);\n      // Vérifier que l'ID est présent\n      if (!msg.id && !msg._id) {\n        this.logger.warn('⚠️ Message without ID received, generating temp ID');\n        msg.id = `temp-${Date.now()}`;\n      }\n      try {\n        // NORMALISATION RAPIDE du message\n        const normalizedMessage = this.normalizeMessage(msg);\n        this.logger.debug('✅ INSTANT: Message normalized successfully', normalizedMessage);\n        // TRAITEMENT INSTANTANÉ selon le type\n        if (normalizedMessage.type === MessageType.AUDIO || normalizedMessage.type === MessageType.VOICE_MESSAGE || normalizedMessage.attachments && normalizedMessage.attachments.some(att => att.type === 'AUDIO')) {\n          this.logger.debug('🎤 INSTANT: Voice message received in real-time');\n        }\n        // MISE À JOUR IMMÉDIATE de l'UI\n        this.zone.run(() => {\n          this.logger.debug('📡 INSTANT: Updating conversation UI immediately');\n          this.updateConversationWithNewMessage(conversationId, normalizedMessage);\n        });\n        return normalizedMessage;\n      } catch (err) {\n        this.logger.error('❌ Error normalizing message:', err);\n        // Créer un message minimal mais valide pour éviter les erreurs\n        const minimalMessage = {\n          id: msg.id || msg._id || `temp-${Date.now()}`,\n          content: msg.content || '',\n          type: msg.type || MessageType.TEXT,\n          timestamp: this.safeDate(msg.timestamp),\n          isRead: false,\n          sender: msg.sender ? this.normalizeUser(msg.sender) : {\n            id: this.getCurrentUserId(),\n            username: 'Unknown'\n          }\n        };\n        this.logger.debug('🔧 FALLBACK: Created minimal message', minimalMessage);\n        return minimalMessage;\n      }\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Message subscription error:', error);\n      // Retourner un observable vide au lieu de null\n      return EMPTY;\n    }),\n    // Filtrer les valeurs null\n    filter(message => !!message),\n    // Réessayer après un délai en cas d'erreur\n    retry(3));\n    console.log(`🔍 DEBUG: Setting up subscription observer...`);\n    const sub = sub$.subscribe({\n      next: message => {\n        console.log(`✅ DEBUG: Message received via subscription:`, message);\n        // Traitement supplémentaire pour s'assurer que le message est bien affiché\n        this.logger.debug('MessageService', 'New message received:', message);\n        // Mettre à jour la conversation avec le nouveau message\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: err => {\n        console.error(`❌ DEBUG: Subscription error:`, err);\n        this.logger.error('Error in message subscription:', err);\n      },\n      complete: () => {\n        console.log(`🔚 DEBUG: Subscription completed`);\n      }\n    });\n    // Log pour confirmer que la subscription est créée\n    console.log(`🔗 DEBUG: Subscription object created:`, sub);\n    console.log(`🔗 DEBUG: Apollo client state:`, this.apollo);\n    this.subscriptions.push(sub);\n    console.log(`✅ DEBUG: Subscription established and added to subscriptions list. Total subscriptions: ${this.subscriptions.length}`);\n    return sub$;\n  }\n  /**\n   * Met à jour une conversation avec un nouveau message INSTANTANÉMENT\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  updateConversationWithNewMessage(conversationId, message) {\n    this.logger.debug(`⚡ INSTANT: Updating conversation ${conversationId} with new message ${message.id}`);\n    // MISE À JOUR IMMÉDIATE sans attendre la requête\n    this.zone.run(() => {\n      // Émettre IMMÉDIATEMENT l'événement de conversation active\n      this.activeConversation.next(conversationId);\n      this.logger.debug('📡 INSTANT: Conversation event emitted immediately');\n    });\n    // Mise à jour en arrière-plan (non-bloquante)\n    setTimeout(() => {\n      this.getConversation(conversationId).subscribe({\n        next: conversation => {\n          this.logger.debug(`✅ BACKGROUND: Conversation ${conversationId} refreshed with ${conversation?.messages?.length || 0} messages`);\n        },\n        error: error => {\n          this.logger.error(`⚠️ BACKGROUND: Error refreshing conversation ${conversationId}:`, error);\n        }\n      });\n    }, 0); // Exécution asynchrone immédiate\n  }\n  /**\n   * Rafraîchit les notifications du sender après envoi d'un message\n   */\n  refreshSenderNotifications() {\n    console.log('🔄 SENDER: Refreshing notifications after message sent');\n    // Recharger les notifications en arrière-plan\n    this.getNotifications(true).subscribe({\n      next: notifications => {\n        console.log('🔄 SENDER: Notifications refreshed successfully', notifications.length);\n      },\n      error: error => {\n        console.error('🔄 SENDER: Error refreshing notifications:', error);\n      }\n    });\n  }\n  subscribeToUserStatus() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\");\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n    const sub$ = this.apollo.subscribe({\n      query: USER_STATUS_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement au statut utilisateur:\", result)), map(result => {\n      const user = result.data?.userStatusChanged;\n      if (!user) {\n        this.logger.error('No status payload received');\n        throw new Error('No status payload received');\n      }\n      return this.normalizeUser(user);\n    }), catchError(error => {\n      this.logger.error('Status subscription error:', error);\n      return throwError(() => new Error('Status subscription failed'));\n    }), retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: CONVERSATION_UPDATED_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => {\n      const conv = result.data?.conversationUpdated;\n      if (!conv) throw new Error('No conversation payload received');\n      const normalizedConversation = {\n        ...conv,\n        participants: conv.participants?.map(p => this.normalizeUser(p)) || [],\n        lastMessage: conv.lastMessage ? {\n          ...conv.lastMessage,\n          sender: this.normalizeUser(conv.lastMessage.sender),\n          timestamp: this.safeDate(conv.lastMessage.timestamp),\n          readAt: conv.lastMessage.readAt ? this.safeDate(conv.lastMessage.readAt) : undefined,\n          // Conservez toutes les autres propriétés du message\n          id: conv.lastMessage.id,\n          content: conv.lastMessage.content,\n          type: conv.lastMessage.type,\n          isRead: conv.lastMessage.isRead\n          // ... autres propriétés nécessaires\n        } : null // On conserve null comme dans votre version originale\n      };\n\n      return normalizedConversation; // Assertion de type si nécessaire\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Conversation subscription error:', error);\n      return throwError(() => new Error('Conversation subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: TYPING_INDICATOR_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => result.data?.typingIndicator), filter(Boolean), catchError(error => {\n      this.logger.error('MessageService', 'Typing indicator subscription error:', error);\n      return throwError(() => new Error('Typing indicator subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  isTokenValid() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString()\n        });\n        return false;\n      }\n      return true;\n    } catch (error) {\n      this.logger.error('Erreur lors de la vérification du token:', error);\n      return false;\n    }\n  }\n  subscribeToNotificationsRead() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications avec un token invalide ou expiré\");\n      return of([]);\n    }\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n    const sub$ = this.apollo.subscribe({\n      query: NOTIFICATIONS_READ_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement aux notifications lues:\", result)), map(result => {\n      const notificationIds = result.data?.notificationsRead || [];\n      this.logger.debug('Notifications marquées comme lues:', notificationIds);\n      this.updateNotificationStatus(notificationIds, true);\n      return notificationIds;\n    }), catchError(err => {\n      this.logger.error('Notifications read subscription error:', err);\n      // Retourner un tableau vide au lieu de propager l'erreur\n      return of([]);\n    }),\n    // Réessayer après un délai en cas d'erreur\n    retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications() {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications sans être connecté\");\n      return EMPTY;\n    }\n    this.logger.debug('🚀 INSTANT NOTIFICATION: Setting up real-time subscription');\n    const source$ = this.apollo.subscribe({\n      query: NOTIFICATION_SUBSCRIPTION\n    });\n    const processed$ = source$.pipe(map(result => {\n      const notification = result.data?.notificationReceived;\n      if (!notification) {\n        throw new Error('No notification payload received');\n      }\n      this.logger.debug('⚡ INSTANT: New notification received', notification);\n      const normalized = this.normalizeNotification(notification);\n      // Vérification rapide du cache\n      if (this.notificationCache.has(normalized.id)) {\n        this.logger.debug(`🔄 Notification ${normalized.id} already in cache, skipping`);\n        throw new Error('Notification already exists in cache');\n      }\n      // TRAITEMENT INSTANTANÉ\n      this.logger.debug('📡 INSTANT: Processing notification immediately');\n      // Vérifier si la notification existe déjà pour éviter les doublons\n      const currentNotifications = this.notifications.value;\n      const existingNotification = currentNotifications.find(n => n.id === normalized.id);\n      if (existingNotification) {\n        this.logger.debug('🔄 DUPLICATE: Notification already exists, skipping:', normalized.id);\n        return normalized;\n      }\n      // Son de notification IMMÉDIAT\n      this.playNotificationSound();\n      // Mise à jour INSTANTANÉE du cache\n      this.updateNotificationCache(normalized);\n      // Émettre IMMÉDIATEMENT la notification EN PREMIER\n      this.zone.run(() => {\n        // 🚀 INSERTION EN PREMIER: Nouvelle notification en tête de liste\n        const updatedNotifications = [normalized, ...currentNotifications];\n        this.logger.debug(`⚡ INSTANT: Nouvelle notification ajoutée en PREMIER (${updatedNotifications.length} total)`);\n        this.notifications.next(updatedNotifications);\n        this.notificationCount.next(this.notificationCount.value + 1);\n      });\n      this.logger.debug('✅ INSTANT: Notification processed and emitted', normalized);\n      return normalized;\n    }),\n    // Gestion d'erreurs optimisée\n    catchError(err => {\n      if (err instanceof Error && err.message === 'Notification already exists in cache') {\n        return EMPTY;\n      }\n      this.logger.error('❌ Notification subscription error:', err);\n      return EMPTY;\n    }),\n    // Optimisation: traitement en temps réel\n    tap(notification => {\n      this.logger.debug('⚡ INSTANT: Notification ready for UI update', notification);\n    }));\n    const sub = processed$.subscribe({\n      next: notification => {\n        this.logger.debug('✅ INSTANT: Notification delivered to UI', notification);\n      },\n      error: error => {\n        this.logger.error('❌ CRITICAL: Notification subscription error', error);\n      }\n    });\n    this.subscriptions.push(sub);\n    this.logger.debug('🔗 INSTANT: Notification subscription established');\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n  startCleanupInterval() {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  cleanupExpiredNotifications() {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n    let expiredCount = 0;\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n      // 🚀 TRI OPTIMISÉ: Maintenir l'ordre après nettoyage\n      const remainingNotifications = Array.from(this.notificationCache.values());\n      const sortedNotifications = this.sortNotificationsByDate(remainingNotifications);\n      this.notifications.next(sortedNotifications);\n      this.updateUnreadCount();\n    }\n  }\n  /**\n   * Trie les notifications par date (plus récentes en premier)\n   * @param notifications Array de notifications à trier\n   * @returns Array de notifications triées\n   */\n  sortNotificationsByDate(notifications) {\n    return notifications.sort((a, b) => {\n      // Utiliser timestamp ou une date par défaut si manquant\n      const dateA = new Date(a.timestamp || 0);\n      const dateB = new Date(b.timestamp || 0);\n      return dateB.getTime() - dateA.getTime(); // Ordre décroissant (plus récent en premier)\n    });\n  }\n\n  getCurrentUserId() {\n    return localStorage.getItem('userId') || '';\n  }\n  normalizeMessage(message) {\n    if (!message) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined message');\n      throw new Error('Message object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error('[MessageService] Message ID is missing', undefined, message);\n        throw new Error('Message ID is required');\n      }\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender ? this.normalizeUser(message.sender) : undefined;\n      } catch (error) {\n        this.logger.warn('[MessageService] Error normalizing message sender, using default values', error);\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true\n        };\n      }\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing message receiver, using default values', error);\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true\n          };\n        }\n      }\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments = message.attachments?.map(att => ({\n        id: att.id || att._id || `attachment-${Date.now()}`,\n        url: att.url || '',\n        type: att.type || 'unknown',\n        name: att.name || 'attachment',\n        size: att.size || 0,\n        duration: att.duration || 0\n      })) || [];\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null\n      };\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id\n      });\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing message:', error instanceof Error ? error : new Error(String(error)), message);\n      throw new Error(`Failed to normalize message: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeUser(user) {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive = user.isActive !== undefined && user.isActive !== null ? user.isActive : true;\n    const role = user.role || 'user';\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount\n    };\n  }\n  normalizeConversation(conv) {\n    if (!conv) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined conversation');\n      throw new Error('Conversation object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error('[MessageService] Conversation ID is missing', undefined, conv);\n        throw new Error('Conversation ID is required');\n      }\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing participant, skipping', error);\n          }\n        }\n      } else {\n        this.logger.warn('[MessageService] Conversation has no participants or invalid participants array', conv);\n      }\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length\n        });\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug('[MessageService] Successfully normalized message', {\n                messageId: normalizedMessage.id,\n                content: normalizedMessage.content?.substring(0, 20),\n                sender: normalizedMessage.sender?.username\n              });\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing message in conversation, skipping', error);\n          }\n        }\n      } else {\n        this.logger.debug('[MessageService] No messages found in conversation or invalid messages array');\n      }\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing last message, using null', error);\n        }\n      }\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt)\n      };\n      this.logger.debug('[MessageService] Conversation normalized successfully', {\n        conversationId: normalizedConversation.id,\n        participantCount: normalizedParticipants.length,\n        messageCount: normalizedMessages.length\n      });\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing conversation:', error instanceof Error ? error : new Error(String(error)), conv);\n      throw new Error(`Failed to normalize conversation: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  safeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  normalizeNotification(notification) {\n    this.logger.debug('MessageService', 'Normalizing notification', notification);\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || notification._id;\n    if (!notificationId) {\n      this.logger.error('MessageService', 'Notification ID is missing', notification);\n      throw new Error('Notification ID is required');\n    }\n    if (!notification.timestamp) {\n      this.logger.warn('MessageService', 'Notification timestamp is missing, using current time', notification);\n      notification.timestamp = new Date();\n    }\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId,\n        id: notificationId,\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId)\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message)\n        })\n      };\n      this.logger.debug('MessageService', 'Normalized notification result', normalized);\n      return normalized;\n    } catch (error) {\n      this.logger.error('MessageService', 'Error in normalizeNotification', error);\n      throw error;\n    }\n  }\n  normalizeSender(sender) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && {\n        image: sender.image\n      })\n    };\n  }\n  /**\n   * Normalise un message de notification\n   * @param message Message à normaliser\n   * @returns Message normalisé\n   */\n  normalizeNotMessage(message) {\n    if (!message) return null;\n    return {\n      id: message.id || message._id,\n      content: message.content || '',\n      type: message.type || 'TEXT',\n      timestamp: this.safeDate(message.timestamp),\n      attachments: message.attachments || [],\n      ...(message.sender && {\n        sender: this.normalizeSender(message.sender)\n      })\n    };\n  }\n  /**\n   * Met à jour le cache de notifications avec une ou plusieurs notifications\n   * @param notifications Notification(s) à ajouter au cache\n   * @param skipDuplicates Si true, ignore les notifications déjà présentes dans le cache\n   */\n  updateCache(notifications, skipDuplicates = true) {\n    const notificationArray = Array.isArray(notifications) ? notifications : [notifications];\n    this.logger.debug('MessageService', `Updating notification cache with ${notificationArray.length} notifications`);\n    if (notificationArray.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notificationArray.filter(notif => notif && (notif.id || notif._id));\n    if (validNotifications.length !== notificationArray.length) {\n      this.logger.warn('MessageService', `Found ${notificationArray.length - validNotifications.length} notifications without valid IDs`);\n    }\n    let addedCount = 0;\n    let skippedCount = 0;\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || notif._id;\n        if (!notifId) {\n          this.logger.error('MessageService', 'Notification without ID:', notif);\n          return;\n        }\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n        // Vérifier si cette notification existe déjà dans le cache\n        if (skipDuplicates && this.notificationCache.has(normalized.id)) {\n          this.logger.debug('MessageService', `Notification ${normalized.id} already exists in cache, skipping`);\n          skippedCount++;\n          return;\n        }\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n        addedCount++;\n        this.logger.debug('MessageService', `Added notification ${normalized.id} to cache`);\n      } catch (error) {\n        this.logger.error('MessageService', `Error processing notification ${index + 1}:`, error);\n      }\n    });\n    this.logger.debug('MessageService', `Cache update complete: ${addedCount} added, ${skippedCount} skipped, total: ${this.notificationCache.size}`);\n    // Mettre à jour les observables et sauvegarder\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Met à jour les observables de notifications et sauvegarde dans le localStorage\n   * OPTIMISÉ: Trie les notifications par date (plus récentes en premier)\n   */\n  refreshNotificationObservables() {\n    const allNotifications = Array.from(this.notificationCache.values());\n    // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n    const sortedNotifications = this.sortNotificationsByDate(allNotifications);\n    this.logger.debug(`📊 SORTED: ${sortedNotifications.length} notifications triées par date (plus récentes en premier)`);\n    this.notifications.next(sortedNotifications);\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n  }\n  /**\n   * Met à jour le compteur de notifications non lues\n   */\n  updateUnreadCount() {\n    const allNotifications = Array.from(this.notificationCache.values());\n    const unreadNotifications = allNotifications.filter(n => !n.isRead);\n    const count = unreadNotifications.length;\n    // Forcer la mise à jour dans la zone Angular\n    this.zone.run(() => {\n      this.notificationCount.next(count);\n      // Émettre un événement global pour forcer la mise à jour du layout\n      window.dispatchEvent(new CustomEvent('notificationCountChanged', {\n        detail: {\n          count\n        }\n      }));\n    });\n  }\n  /**\n   * Met à jour le cache avec une seule notification (méthode simplifiée)\n   * @param notification Notification à ajouter\n   */\n  updateNotificationCache(notification) {\n    this.updateCache(notification, true);\n  }\n  /**\n   * Met à jour le statut de lecture des notifications\n   * @param ids IDs des notifications à mettre à jour\n   * @param isRead Nouveau statut de lecture\n   */\n  updateNotificationStatus(ids, isRead) {\n    ids.forEach(id => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, {\n          ...notif,\n          isRead,\n          readAt: isRead ? new Date().toISOString() : undefined\n        });\n      }\n    });\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Méthode générique pour supprimer des notifications du cache local\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Nombre de notifications supprimées\n   */\n  removeNotificationsFromCache(notificationIds) {\n    console.log('🗑️ REMOVE FROM CACHE: Starting removal of', notificationIds.length, 'notifications');\n    console.log('🗑️ REMOVE FROM CACHE: Cache size before:', this.notificationCache.size);\n    let removedCount = 0;\n    notificationIds.forEach(id => {\n      if (this.notificationCache.has(id)) {\n        console.log('🗑️ REMOVE FROM CACHE: Removing notification:', id);\n        this.notificationCache.delete(id);\n        removedCount++;\n      } else {\n        console.log('🗑️ REMOVE FROM CACHE: Notification not found in cache:', id);\n      }\n    });\n    console.log('🗑️ REMOVE FROM CACHE: Removed', removedCount, 'notifications');\n    console.log('🗑️ REMOVE FROM CACHE: Cache size after:', this.notificationCache.size);\n    if (removedCount > 0) {\n      console.log('🗑️ REMOVE FROM CACHE: Refreshing observables...');\n      this.refreshNotificationObservables();\n    }\n    return removedCount;\n  }\n  /**\n   * Méthode générique pour gérer les erreurs de suppression\n   * @param error Erreur survenue\n   * @param operation Nom de l'opération\n   * @param fallbackResponse Réponse de fallback en cas d'erreur\n   */\n  handleDeletionError(error, operation, fallbackResponse) {\n    this.logger.error('MessageService', `Erreur lors de ${operation}:`, error);\n    return of(fallbackResponse);\n  }\n  // Typing indicators\n  startTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: START_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.startTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error starting typing indicator', error);\n      return throwError(() => new Error('Failed to start typing indicator'));\n    }));\n  }\n  stopTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: STOP_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.stopTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error stopping typing indicator', error);\n      return throwError(() => new Error('Failed to stop typing indicator'));\n    }));\n  }\n  // ========================================\n  // MÉTHODE SENDMESSAGE MANQUANTE\n  // ========================================\n  /**\n   * Envoie un message (texte, fichier, audio, etc.)\n   * @param receiverId ID du destinataire\n   * @param content Contenu du message (texte)\n   * @param file Fichier à envoyer (optionnel)\n   * @param messageType Type de message (TEXT, AUDIO, IMAGE, etc.)\n   * @param conversationId ID de la conversation\n   * @returns Observable avec le message envoyé\n   */\n  sendMessage(receiverId, content, file, messageType = 'TEXT', conversationId) {\n    console.log('🚀 [MessageService] sendMessage called with:', {\n      receiverId,\n      content: content?.substring(0, 50),\n      hasFile: !!file,\n      fileName: file?.name,\n      fileType: file?.type,\n      fileSize: file?.size,\n      messageType,\n      conversationId\n    });\n    if (!receiverId) {\n      const error = new Error('Receiver ID is required');\n      console.error('❌ [MessageService] sendMessage error:', error);\n      return throwError(() => error);\n    }\n    // Préparer les variables pour la mutation\n    const variables = {\n      receiverId,\n      content: content || '',\n      type: messageType\n    };\n    // Ajouter l'ID de conversation si fourni\n    if (conversationId) {\n      variables.conversationId = conversationId;\n    }\n    // Si un fichier est fourni, l'ajouter aux variables\n    if (file) {\n      variables.file = file;\n      console.log('📁 [MessageService] Adding file to mutation:', {\n        name: file.name,\n        type: file.type,\n        size: file.size\n      });\n    }\n    console.log('📤 [MessageService] Sending mutation with variables:', variables);\n    return this.apollo.mutate({\n      mutation: SEND_MESSAGE_MUTATION,\n      variables,\n      context: {\n        useMultipart: !!file // Utiliser multipart si un fichier est présent\n      }\n    }).pipe(map(result => {\n      console.log('✅ [MessageService] sendMessage mutation result:', result);\n      if (!result.data?.sendMessage) {\n        throw new Error('No message data received from server');\n      }\n      const message = result.data.sendMessage;\n      console.log('📨 [MessageService] Message sent successfully:', {\n        id: message.id,\n        type: message.type,\n        content: message.content?.substring(0, 50),\n        hasAttachments: !!message.attachments?.length\n      });\n      // Normaliser le message reçu\n      const normalizedMessage = this.normalizeMessage(message);\n      console.log('🔧 [MessageService] Message normalized:', normalizedMessage);\n      return normalizedMessage;\n    }), catchError(error => {\n      console.error('❌ [MessageService] sendMessage error:', error);\n      this.logger.error('Error sending message:', error);\n      // Fournir un message d'erreur plus spécifique\n      let errorMessage = \"Erreur lors de l'envoi du message\";\n      if (error.networkError) {\n        errorMessage = 'Erreur de connexion réseau';\n      } else if (error.graphQLErrors?.length > 0) {\n        errorMessage = error.graphQLErrors[0].message || errorMessage;\n      }\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  // ========================================\n  // MÉTHODES UTILITAIRES CONSOLIDÉES\n  // ========================================\n  /**\n   * Formate l'heure d'un message\n   */\n  formatMessageTime(timestamp) {\n    if (!timestamp) return 'Unknown time';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n    } catch (error) {\n      return 'Invalid time';\n    }\n  }\n  /**\n   * Formate la dernière activité d'un utilisateur\n   */\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate = lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours = Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n  /**\n   * Formate la date d'un message\n   */\n  formatMessageDate(timestamp) {\n    if (!timestamp) return 'Unknown date';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      }\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })}`;\n      }\n      const day = date.toLocaleDateString('fr-FR', {\n        weekday: 'short'\n      }).toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    } catch (error) {\n      return 'Invalid date';\n    }\n  }\n  /**\n   * Détermine si un en-tête de date doit être affiché\n   */\n  shouldShowDateHeader(messages, index) {\n    if (index === 0) return true;\n    try {\n      const currentMsg = messages[index];\n      const prevMsg = messages[index - 1];\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) return true;\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n      return currentDate !== prevDate;\n    } catch (error) {\n      return false;\n    }\n  }\n  getDateFromTimestamp(timestamp) {\n    if (!timestamp) return 'unknown-date';\n    try {\n      return (timestamp instanceof Date ? timestamp : new Date(timestamp)).toDateString();\n    } catch (error) {\n      return 'invalid-date';\n    }\n  }\n  /**\n   * Obtient l'icône d'un fichier selon son type MIME\n   */\n  getFileIcon(mimeType) {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword')) return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed')) return 'fa-file-archive';\n    return 'fa-file';\n  }\n  /**\n   * Obtient le type d'un fichier selon son type MIME\n   */\n  getFileType(mimeType) {\n    if (!mimeType) return 'File';\n    const typeMap = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive'\n    };\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n  /**\n   * Vérifie si un message contient une image\n   */\n  hasImage(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message) {\n    if (!message) return false;\n    // Vérifier le type du message\n    if (message.type === MessageType.VOICE_MESSAGE || message.type === MessageType.VOICE_MESSAGE) {\n      return true;\n    }\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || message.metadata?.isVoiceMessage && (type === 'AUDIO' || type === 'audio');\n      });\n    }\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    const voiceAttachment = message.attachments.find(att => {\n      const type = att.type?.toString();\n      return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n    });\n    return voiceAttachment?.url || '';\n  }\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message) {\n    if (!message) return 0;\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n      });\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n    return 0;\n  }\n  /**\n   * Génère la hauteur des barres de la forme d'onde moderne\n   */\n  getVoiceBarHeight(index) {\n    const pattern = [8, 12, 6, 15, 10, 18, 7, 14, 9, 16, 5, 13, 11, 17, 8, 12, 6, 15, 10, 18];\n    return pattern[index % pattern.length];\n  }\n  /**\n   * Formate la durée du message vocal en format MM:SS\n   */\n  formatVoiceDuration(seconds) {\n    if (!seconds || seconds === 0) {\n      return '0:00';\n    }\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  /**\n   * Obtient l'URL de l'image en toute sécurité\n   */\n  getImageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n  /**\n   * Détermine le type d'un message\n   */\n  getMessageType(message) {\n    if (!message) return MessageType.TEXT;\n    try {\n      if (message.type) {\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (attachmentTypeStr === 'file' || attachmentTypeStr === 'FILE') {\n            return MessageType.FILE;\n          } else if (attachmentTypeStr === 'audio' || attachmentTypeStr === 'AUDIO') {\n            return MessageType.AUDIO;\n          } else if (attachmentTypeStr === 'video' || attachmentTypeStr === 'VIDEO') {\n            return MessageType.VIDEO;\n          }\n        }\n        return MessageType.FILE;\n      }\n      return MessageType.TEXT;\n    } catch (error) {\n      return MessageType.TEXT;\n    }\n  }\n  /**\n   * Retourne la liste des emojis communs\n   */\n  getCommonEmojis() {\n    return ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✌️', '🤞', '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '💯', '💢'];\n  }\n  /**\n   * Obtient les classes CSS pour un message\n   */\n  getMessageTypeClass(message, currentUserId) {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n    try {\n      const isCurrentUser = message.sender?.id === currentUserId || message.sender?._id === currentUserId || message.senderId === currentUserId;\n      const baseClass = isCurrentUser ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm' : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n      const messageType = this.getMessageType(message);\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            return `p-1 max-w-xs`;\n          } else if (attachmentTypeStr === 'FILE' || attachmentTypeStr === 'file') {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n      // Les vérifications de type sont déjà faites avec les attachments ci-dessus\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n  // ========================================\n  // MÉTHODES POUR LES APPELS WEBRTC\n  // ========================================\n  /**\n   * Initie un appel WebRTC\n   * @param recipientId ID du destinataire\n   * @param callType Type d'appel (VIDEO ou AUDIO)\n   * @param conversationId ID de la conversation (optionnel)\n   * @returns Observable avec les détails de l'appel\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    console.log('🔄 [MessageService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [MessageService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n    const variables = {\n      recipientId,\n      type: callType,\n      conversationId\n    };\n    console.log('📤 [MessageService] Sending initiate call mutation:', variables);\n    return this.apollo.mutate({\n      mutation: INITIATE_CALL_MUTATION,\n      variables\n    }).pipe(map(result => {\n      console.log('✅ [MessageService] Call initiated successfully:', result);\n      if (!result.data?.initiateCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.initiateCall;\n      console.log('📞 [MessageService] Call details:', {\n        id: call.id,\n        type: call.type,\n        status: call.status,\n        caller: call.caller?.username,\n        recipient: call.recipient?.username\n      });\n      // Mettre à jour l'état local\n      this.activeCall.next(call);\n      return call;\n    }), catchError(error => {\n      console.error('❌ [MessageService] initiateCall error:', error);\n      this.logger.error('Error initiating call:', error);\n      let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n      if (error.networkError) {\n        errorMessage = 'Erreur de connexion réseau';\n      } else if (error.graphQLErrors?.length > 0) {\n        errorMessage = error.graphQLErrors[0].message || errorMessage;\n      }\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   * @param incomingCall Appel entrant à accepter\n   * @returns Observable avec les détails de l'appel accepté\n   */\n  acceptCall(incomingCall) {\n    console.log('🔄 [MessageService] Accepting call:', incomingCall.id);\n    return this.apollo.mutate({\n      mutation: ACCEPT_CALL_MUTATION,\n      variables: {\n        callId: incomingCall.id\n      }\n    }).pipe(map(result => {\n      console.log('✅ [MessageService] Call accepted successfully:', result);\n      if (!result.data?.acceptCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.acceptCall;\n      // Mettre à jour l'état local\n      this.activeCall.next(call);\n      this.incomingCall.next(null); // Supprimer l'appel entrant\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      this.play('call-connected');\n      return call;\n    }), catchError(error => {\n      console.error('❌ [MessageService] acceptCall error:', error);\n      this.logger.error('Error accepting call:', error);\n      return throwError(() => new Error(\"Erreur lors de l'acceptation de l'appel\"));\n    }));\n  }\n  /**\n   * Rejette un appel entrant\n   * @param callId ID de l'appel à rejeter\n   * @param reason Raison du rejet\n   * @returns Observable avec le résultat\n   */\n  rejectCall(callId, reason) {\n    console.log('🔄 [MessageService] Rejecting call:', callId, reason);\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      console.log('✅ [MessageService] Call rejected successfully:', result);\n      if (!result.data?.rejectCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      return result.data.rejectCall;\n    }), catchError(error => {\n      console.error('❌ [MessageService] rejectCall error:', error);\n      this.logger.error('Error rejecting call:', error);\n      return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   * @param callId ID de l'appel à terminer\n   * @returns Observable avec le résultat\n   */\n  endCall(callId) {\n    console.log('🔄 [MessageService] Ending call:', callId);\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId\n      }\n    }).pipe(map(result => {\n      console.log('✅ [MessageService] Call ended successfully:', result);\n      if (!result.data?.endCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.activeCall.next(null);\n      this.incomingCall.next(null);\n      // Arrêter tous les sons\n      this.stopAllSounds();\n      this.play('call-end');\n      return result.data.endCall;\n    }), catchError(error => {\n      console.error('❌ [MessageService] endCall error:', error);\n      this.logger.error('Error ending call:', error);\n      return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n    }));\n  }\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   * @param callId ID de l'appel\n   * @param enableVideo État de la vidéo (optionnel)\n   * @param enableAudio État de l'audio (optionnel)\n   * @returns Observable avec le résultat\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    console.log('🔄 [MessageService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio\n    });\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        enableVideo,\n        enableAudio\n      }\n    }).pipe(map(result => {\n      console.log('✅ [MessageService] Media toggled successfully:', result);\n      if (!result.data?.toggleCallMedia) {\n        throw new Error('No response received from server');\n      }\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('❌ [MessageService] toggleMedia error:', error);\n      this.logger.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  // destroy\n  cleanupSubscriptions() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n  static {\n    this.ɵfac = function MessageService_Factory(t) {\n      return new (t || MessageService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MessageService,\n      factory: MessageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Observable", "of", "throwError", "retry", "EMPTY", "map", "catchError", "tap", "filter", "switchMap", "from", "MessageType", "CallType", "CallStatus", "GET_CONVERSATIONS_QUERY", "GET_NOTIFICATIONS_QUERY", "NOTIFICATION_SUBSCRIPTION", "GET_CONVERSATION_QUERY", "SEND_MESSAGE_MUTATION", "MARK_AS_READ_MUTATION", "MESSAGE_SENT_SUBSCRIPTION", "USER_STATUS_SUBSCRIPTION", "GET_USER_QUERY", "GET_ALL_USER_QUERY", "CONVERSATION_UPDATED_SUBSCRIPTION", "SEARCH_MESSAGES_QUERY", "GET_UNREAD_MESSAGES_QUERY", "SET_USER_ONLINE_MUTATION", "SET_USER_OFFLINE_MUTATION", "START_TYPING_MUTATION", "STOP_TYPING_MUTATION", "TYPING_INDICATOR_SUBSCRIPTION", "GET_CURRENT_USER_QUERY", "REACT_TO_MESSAGE_MUTATION", "FORWARD_MESSAGE_MUTATION", "PIN_MESSAGE_MUTATION", "CREATE_GROUP_MUTATION", "UPDATE_GROUP_MUTATION", "DELETE_GROUP_MUTATION", "LEAVE_GROUP_MUTATION", "GET_GROUP_QUERY", "GET_USER_GROUPS_QUERY", "EDIT_MESSAGE_MUTATION", "DELETE_MESSAGE_MUTATION", "GET_MESSAGES_QUERY", "GET_NOTIFICATIONS_ATTACHAMENTS", "MARK_NOTIFICATION_READ_MUTATION", "NOTIFICATIONS_READ_SUBSCRIPTION", "CREATE_CONVERSATION_MUTATION", "DELETE_NOTIFICATION_MUTATION", "DELETE_MULTIPLE_NOTIFICATIONS_MUTATION", "DELETE_ALL_NOTIFICATIONS_MUTATION", "CALL_HISTORY_QUERY", "CALL_DETAILS_QUERY", "CALL_STATS_QUERY", "INITIATE_CALL_MUTATION", "SEND_CALL_SIGNAL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "CALL_SIGNAL_SUBSCRIPTION", "INCOMING_CALL_SUBSCRIPTION", "GET_VOICE_MESSAGES_QUERY", "MessageService", "constructor", "apollo", "logger", "zone", "activeConversation", "notifications", "notificationCache", "Map", "notificationCount", "onlineUsers", "subscriptions", "CACHE_DURATION", "lastFetchTime", "activeCall", "incomingCall", "callSignals", "localStream", "remoteStream", "peerConnection", "activeCall$", "asObservable", "incomingCall$", "callSignals$", "localStream$", "remoteStream$", "rtcConfig", "iceServers", "urls", "usersCache", "currentUserPagination", "totalCount", "totalPages", "currentPage", "hasNextPage", "hasPreviousPage", "activeConversation$", "notifications$", "notificationCount$", "sounds", "isPlaying", "muted", "notificationPagination", "limit", "hasMoreNotifications", "toSafeISOString", "date", "undefined", "toISOString", "loadNotificationsFromLocalStorage", "initSubscriptions", "startCleanupInterval", "preloadSounds", "savedNotifications", "localStorage", "getItem", "JSON", "parse", "clear", "for<PERSON>ach", "notification", "id", "set", "next", "Array", "values", "updateUnreadCount", "error", "runOutsideAngular", "subscribeToNewNotifications", "subscribe", "subscribeToNotificationsRead", "subscribeToIncomingCalls", "subscribeToUserStatus", "query", "pipe", "data", "handleIncomingCall", "call", "play", "loadSound", "name", "path", "audio", "Audio", "load", "addEventListener", "loop", "sound", "currentTime", "catch", "stop", "pause", "stopAllSounds", "Object", "keys", "setMuted", "isMuted", "playNotificationSound", "console", "log", "audioContext", "window", "AudioContext", "webkitAudioContext", "playNotificationMelody1", "volume", "err", "audioError", "playNotificationTone", "playNotificationMelody2", "playNotificationMelody3", "playNotificationMelody4", "playNotificationMelody5", "playBellTone", "startTime", "frequency", "duration", "oscillator", "createOscillator", "gainNode", "createGain", "type", "setValueAtTime", "gain", "linearRampToValueAtTime", "connect", "destination", "start", "exponentialRampToValueAtTime", "playAudio", "audioUrl", "Promise", "resolve", "reject", "onended", "onerror", "getVoiceMessages", "debug", "watch<PERSON><PERSON>y", "fetchPolicy", "valueChanges", "result", "voiceMessages", "length", "Error", "getMessages", "senderId", "receiverId", "conversationId", "page", "variables", "messages", "msg", "normalizeMessage", "editMessage", "messageId", "newContent", "mutate", "mutation", "deleteMessage", "markMessageAsRead", "readAt", "Date", "reactToMessage", "emoji", "forwardMessage", "conversationIds", "timestamp", "normalizeDate", "pinMessage", "pinnedAt", "searchMessages", "filters", "dateFrom", "dateTo", "safeDate", "sender", "normalizeUser", "getUnreadMessages", "userId", "setActiveConversation", "getConversations", "conversations", "conv", "normalizeConversation", "getConversation", "info", "offset", "normalizedConversation", "participants", "createConversation", "conversation", "message", "getOrCreateConversation", "currentUserId", "getCurrentUserId", "existingConversation", "find", "isGroup", "participantIds", "p", "_id", "includes", "getNotifications", "refresh", "deletedNotificationIds", "getDeletedNotificationIds", "size", "errors", "e", "join", "getUserNotifications", "filteredNotifications", "notif", "has", "index", "content", "isRead", "updateCache", "cachedNotifications", "sortedNotifications", "sortNotificationsByDate", "saveNotificationsToLocalStorage", "graphQLErrors", "networkError", "deletedIds", "Set", "savedNotificationIds", "n", "serverNotifications", "client", "readQuery", "add", "loadMoreNotifications", "nextPage", "getNotificationById", "getNotificationCount", "value", "getNotificationAttachments", "notificationId", "getUnreadNotifications", "deleteNotification", "warn", "removedCount", "removeNotificationsFromCache", "response", "handleDeletionError", "success", "setItem", "stringify", "deleteAllNotifications", "count", "allNotificationIds", "deleteMultipleNotifications", "notificationIds", "groupNotificationsByType", "groups", "get", "push", "mark<PERSON><PERSON><PERSON>", "readCount", "remainingCount", "validIds", "trim", "provided", "valid", "updateNotificationStatus", "optimisticResponse", "markNotificationsAsRead", "Math", "max", "errorPolicy", "initiateCall", "recipientId", "callType", "options", "setupMediaDevices", "stream", "RTCPeerConnection", "getTracks", "track", "addTrack", "onicecandidate", "event", "candidate", "sendCallSignal", "generateCallId", "ontrack", "MediaStream", "streams", "createOffer", "offer", "setLocalDescription", "callId", "signalSub", "subscribeToCallSignals", "cleanupCall", "acceptCall", "setRemoteDescription", "RTCSessionDescription", "createAnswer", "answer", "caller", "rejectCall", "reason", "endCall", "feedback", "toggleMedia", "video", "getVideoTracks", "enabled", "getAudioTracks", "toggleCallMedia", "callSignal", "signal", "handleCallSignal", "signalType", "signalData", "getCallHistory", "status", "startDate", "endDate", "history", "callHistory", "getCallDetails", "details", "callDetails", "getCallStats", "stats", "callStats", "handleIceCandidate", "handleAnswer", "handleEndCall", "handleRejectCall", "addIceCandidate", "RTCIceCandidate", "currentCall", "ENDED", "endTime", "REJECTED", "close", "constraints", "AUDIO", "width", "ideal", "height", "observer", "navigator", "mediaDevices", "getUserMedia", "then", "complete", "now", "toString", "random", "substring", "getAllUsers", "forceRefresh", "search", "sortBy", "sortOrder", "isOnline", "cacheValid", "paginatedResponse", "users", "user", "getOneUser", "getCurrentUser", "setUserOnline", "setUserOffline", "createGroup", "photo", "description", "group", "updateGroup", "groupId", "input", "deleteGroup", "leaveGroup", "getGroup", "getUserGroups", "subscribeToNewMessages", "tokenValid", "isTokenValid", "sub$", "messageSent", "normalizedMessage", "VOICE_MESSAGE", "attachments", "some", "att", "run", "updateConversationWithNewMessage", "minimalMessage", "TEXT", "username", "sub", "setTimeout", "refreshSenderNotifications", "userStatusChanged", "subscribeToConversationUpdates", "conversationUpdated", "lastMessage", "subscribeToTypingIndicator", "typingIndicator", "Boolean", "token", "parts", "split", "payload", "atob", "exp", "expirationDate", "expiration", "notificationsRead", "source$", "processed$", "notificationReceived", "normalized", "normalizeNotification", "currentNotifications", "existingNotification", "updateNotificationCache", "updatedNotifications", "cleanupInterval", "setInterval", "cleanupExpiredNotifications", "thirtyDaysAgo", "getTime", "expiredCount", "notificationDate", "delete", "remainingNotifications", "sort", "a", "b", "dateA", "dateB", "normalizedSender", "email", "role", "isActive", "normalizedReceiver", "receiver", "normalizedAttachments", "url", "metadata", "String", "image", "bio", "lastActive", "createdAt", "updatedAt", "followingCount", "followersCount", "postCount", "normalizedParticipants", "isArray", "participant", "normalizedMessages", "normalizedLastMessage", "unreadCount", "participantCount", "messageCount", "normalizeSender", "normalizeNotMessage", "skipDuplicates", "notificationArray", "validNotifications", "addedCount", "skippedCount", "notifId", "refreshNotificationObservables", "allNotifications", "unreadNotifications", "dispatchEvent", "CustomEvent", "detail", "ids", "operation", "fallbackResponse", "startTyping", "stopTyping", "sendMessage", "file", "messageType", "hasFile", "fileName", "fileType", "fileSize", "context", "useMultipart", "hasAttachments", "errorMessage", "formatMessageTime", "toLocaleTimeString", "hour", "minute", "hour12", "formatLastActive", "lastActiveDate", "diffHours", "abs", "toLocaleDateString", "formatMessageDate", "today", "toDateString", "yesterday", "setDate", "getDate", "day", "weekday", "toUpperCase", "shouldShowDateHeader", "currentMsg", "prevMsg", "currentDate", "getDateFromTimestamp", "prevDate", "getFileIcon", "mimeType", "startsWith", "getFileType", "typeMap", "key", "entries", "hasImage", "attachment", "isVoiceMessage", "getVoiceMessageUrl", "voiceAttachment", "getVoiceMessageDuration", "getVoiceBarHeight", "pattern", "formatVoiceDuration", "seconds", "minutes", "floor", "remainingSeconds", "padStart", "getImageUrl", "getMessageType", "msgType", "IMAGE", "FILE", "VIDEO", "SYSTEM", "attachmentTypeStr", "getCommonEmojis", "getMessageTypeClass", "isCurrentUser", "baseClass", "recipient", "enableVideo", "enableAudio", "cleanupSubscriptions", "unsubscribe", "clearInterval", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\message.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/core';\nimport { <PERSON> } from 'apollo-angular';\nimport {\n  BehaviorSubject,\n  Observable,\n  of,\n  Subscription,\n  throwError,\n  retry,\n  EMPTY,\n} from 'rxjs';\nimport {\n  map,\n  catchError,\n  tap,\n  filter,\n  switchMap,\n  concatMap,\n  toArray,\n} from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport {\n  MessageType,\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSignal,\n  CallOptions,\n  CallFeedback,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  GET_CONVERSATIONS_QUERY,\n  GET_NOTIFICATIONS_QUERY,\n  NOTIFICATION_SUBSCRIPTION,\n  GET_CONVERSATION_QUERY,\n  SEND_MESSAGE_MUTATION,\n  MARK_AS_READ_MUTATION,\n  MESSAGE_SENT_SUBSCRIPTION,\n  USER_STATUS_SUBSCRIPTION,\n  GET_USER_QUERY,\n  GET_ALL_USER_QUERY,\n  CONVERSATION_UPDATED_SUBSCRIPTION,\n  SEARCH_MESSAGES_QUERY,\n  GET_UNREAD_MESSAGES_QUERY,\n  SET_USER_ONLINE_MUTATION,\n  SET_USER_OFFLINE_MUTATION,\n  START_TYPING_MUTATION,\n  STOP_TYPING_MUTATION,\n  TYPING_INDICATOR_SUBSCRIPTION,\n  GET_CURRENT_USER_QUERY,\n  REACT_TO_MESSAGE_MUTATION,\n  FORWARD_MESSAGE_MUTATION,\n  PIN_MESSAGE_MUTATION,\n  CREATE_GROUP_MUTATION,\n  UPDATE_GROUP_MUTATION,\n  DELETE_GROUP_MUTATION,\n  ADD_GROUP_PARTICIPANTS_MUTATION,\n  REMOVE_GROUP_PARTICIPANTS_MUTATION,\n  LEAVE_GROUP_MUTATION,\n  GET_GROUP_QUERY,\n  GET_USER_GROUPS_QUERY,\n  EDIT_MESSAGE_MUTATION,\n  DELETE_MESSAGE_MUTATION,\n  GET_MESSAGES_QUERY,\n  GET_NOTIFICATIONS_ATTACHAMENTS,\n  MARK_NOTIFICATION_READ_MUTATION,\n  NOTIFICATIONS_READ_SUBSCRIPTION,\n  CREATE_CONVERSATION_MUTATION,\n  DELETE_NOTIFICATION_MUTATION,\n  DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n  DELETE_ALL_NOTIFICATIONS_MUTATION,\n  // Requêtes et mutations pour les appels\n  CALL_HISTORY_QUERY,\n  CALL_DETAILS_QUERY,\n  CALL_STATS_QUERY,\n  INITIATE_CALL_MUTATION,\n  SEND_CALL_SIGNAL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  CALL_SIGNAL_SUBSCRIPTION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  GET_VOICE_MESSAGES_QUERY,\n} from '../graphql/message.graphql';\nimport {\n  Conversation,\n  Message,\n  Notification,\n  User,\n  Attachment,\n  getNotificationAttachmentsEvent,\n  Group,\n  MessageFilter,\n  TypingIndicatorEvent,\n  GetConversationsResponse,\n  GetConversationResponse,\n  MarkAsReadResponse,\n  ReactToMessageResponse,\n  ForwardMessageResponse,\n  PinMessageResponse,\n  SearchMessagesResponse,\n  SendMessageResponse,\n  GetUnreadMessagesResponse,\n  GetAllUsersResponse,\n  GetOneUserResponse,\n  getCurrentUserResponse,\n  SetUserOnlineResponse,\n  SetUserOfflineResponse,\n  GetGroupResponse,\n  GetUserGroupsResponse,\n  CreateGroupResponse,\n  UpdateGroupResponse,\n  StartTupingResponse,\n  StopTypingResponse,\n  TypingIndicatorEvents,\n  getUserNotificationsResponse,\n  NotificationType,\n  MarkNotificationsAsReadResponse,\n  NotificationReceivedEvent,\n  NotificationsReadEvent,\n} from '../models/message.model';\nimport { LoggerService } from './logger.service';\n@Injectable({\n  providedIn: 'root',\n})\nexport class MessageService implements OnDestroy {\n  // État partagé\n  private activeConversation = new BehaviorSubject<string | null>(null);\n  private notifications = new BehaviorSubject<Notification[]>([]);\n  private notificationCache = new Map<string, Notification>();\n  private cleanupInterval: any;\n  private notificationCount = new BehaviorSubject<number>(0);\n  private onlineUsers = new Map<string, User>();\n  private subscriptions: Subscription[] = [];\n  private readonly CACHE_DURATION = 300000;\n  private lastFetchTime = 0;\n\n  // Propriétés pour les appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n  private callSignals = new BehaviorSubject<CallSignal | null>(null);\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private peerConnection: RTCPeerConnection | null = null;\n\n  // Observables publics pour les appels\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n  public callSignals$ = this.callSignals.asObservable();\n  public localStream$ = new BehaviorSubject<MediaStream | null>(null);\n  public remoteStream$ = new BehaviorSubject<MediaStream | null>(null);\n\n  // Configuration WebRTC\n  private readonly rtcConfig: RTCConfiguration = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n    ],\n  };\n  private usersCache: User[] = [];\n\n  // Pagination metadata for user list\n  public currentUserPagination: {\n    totalCount: number;\n    totalPages: number;\n    currentPage: number;\n    hasNextPage: boolean;\n    hasPreviousPage: boolean;\n  } = {\n    totalCount: 0,\n    totalPages: 0,\n    currentPage: 1,\n    hasNextPage: false,\n    hasPreviousPage: false,\n  };\n\n  // Observables publics\n  public activeConversation$ = this.activeConversation.asObservable();\n  public notifications$ = this.notifications.asObservable();\n  public notificationCount$ = this.notificationCount.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  constructor(\n    private apollo: Apollo,\n    private logger: LoggerService,\n    private zone: NgZone\n  ) {\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  private loadNotificationsFromLocalStorage(): void {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications) as Notification[];\n\n        this.notificationCache.clear();\n\n        notifications.forEach((notification) => {\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  private initSubscriptions(): void {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n      // 🔥 AJOUT: Subscription générale pour l'utilisateur\n    });\n    this.subscribeToUserStatus();\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): Observable<IncomingCall | null> {\n    return this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.incomingCall) {\n            return null;\n          }\n\n          // Gérer l'appel entrant\n          this.handleIncomingCall(data.incomingCall);\n          return data.incomingCall;\n        }),\n        catchError((error) => {\n          this.logger.error('Error in incoming call subscription', error);\n          return of(null);\n        })\n      );\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      return;\n    }\n\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n\n      sound.loop = loop;\n\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch((error) => {\n          // Handle error silently\n        });\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted: boolean): void {\n    this.muted = muted;\n\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted(): boolean {\n    return this.muted;\n  }\n\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound(): void {\n    console.log('MessageService: Tentative de lecture du son de notification');\n\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n\n    // Créer une mélodie agréable avec l'API Web Audio\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // 🎵 TESTEZ DIFFÉRENTS SONS - Décommentez celui que vous voulez tester !\n\n      // SON 1: Mélodie douce (WhatsApp style) - ACTUEL\n      this.playNotificationMelody1(audioContext);\n\n      // SON 2: Mélodie montante (iPhone style) - Décommentez pour tester\n      // this.playNotificationMelody2(audioContext);\n\n      // SON 3: Mélodie descendante (Messenger style) - Décommentez pour tester\n      // this.playNotificationMelody3(audioContext);\n\n      // SON 4: Triple note (Discord style) - Décommentez pour tester\n      // this.playNotificationMelody4(audioContext);\n\n      // SON 5: Cloche douce (Slack style) - Décommentez pour tester\n      // this.playNotificationMelody5(audioContext);\n\n      console.log(\n        'MessageService: Son de notification mélodieux généré avec succès'\n      );\n    } catch (error) {\n      console.error(\n        'MessageService: Erreur lors de la génération du son:',\n        error\n      );\n\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 0.7; // Volume plus doux\n        audio.play().catch((err) => {\n          console.error(\n            'MessageService: Erreur lors de la lecture du fichier son:',\n            err\n          );\n        });\n      } catch (audioError) {\n        console.error(\n          'MessageService: Exception lors de la lecture du fichier son:',\n          audioError\n        );\n      }\n    }\n  }\n\n  // 🎵 SON 1: Mélodie douce (WhatsApp style)\n  private playNotificationMelody1(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 659.25, 0.15); // E5\n    this.playNotificationTone(audioContext, 0.15, 523.25, 0.15); // C5\n  }\n\n  // 🎵 SON 2: Mélodie montante (iPhone style)\n  private playNotificationMelody2(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 523.25, 0.12); // C5\n    this.playNotificationTone(audioContext, 0.12, 659.25, 0.12); // E5\n    this.playNotificationTone(audioContext, 0.24, 783.99, 0.16); // G5\n  }\n\n  // 🎵 SON 3: Mélodie descendante (Messenger style)\n  private playNotificationMelody3(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 880, 0.1); // A5\n    this.playNotificationTone(audioContext, 0.1, 659.25, 0.1); // E5\n    this.playNotificationTone(audioContext, 0.2, 523.25, 0.15); // C5\n  }\n\n  // 🎵 SON 4: Triple note (Discord style)\n  private playNotificationMelody4(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.08, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.16, 880, 0.12); // A5\n  }\n\n  // 🎵 SON 5: Cloche douce (Slack style)\n  private playNotificationMelody5(audioContext: AudioContext): void {\n    this.playBellTone(audioContext, 0, 1046.5, 0.4); // C6 - son de cloche\n  }\n\n  /**\n   * Joue une note individuelle pour la mélodie de notification\n   */\n  private playNotificationTone(\n    audioContext: AudioContext,\n    startTime: number,\n    frequency: number,\n    duration: number\n  ): void {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n\n    // Configurer l'oscillateur pour un son plus doux\n    oscillator.type = 'sine';\n    oscillator.frequency.setValueAtTime(\n      frequency,\n      audioContext.currentTime + startTime\n    );\n\n    // Configurer le volume avec une enveloppe douce\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(\n      0.3,\n      audioContext.currentTime + startTime + 0.02\n    );\n    gainNode.gain.linearRampToValueAtTime(\n      0.2,\n      audioContext.currentTime + startTime + duration * 0.7\n    );\n    gainNode.gain.linearRampToValueAtTime(\n      0,\n      audioContext.currentTime + startTime + duration\n    );\n\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n\n  /**\n   * Joue un son de cloche pour les notifications\n   */\n  private playBellTone(\n    audioContext: AudioContext,\n    startTime: number,\n    frequency: number,\n    duration: number\n  ): void {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n\n    // Configurer l'oscillateur pour un son de cloche\n    oscillator.type = 'triangle'; // Son plus doux que sine\n    oscillator.frequency.setValueAtTime(\n      frequency,\n      audioContext.currentTime + startTime\n    );\n\n    // Enveloppe de cloche (attaque rapide, déclin lent)\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(\n      0.4,\n      audioContext.currentTime + startTime + 0.01\n    );\n    gainNode.gain.exponentialRampToValueAtTime(\n      0.01,\n      audioContext.currentTime + startTime + duration\n    );\n\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n\n      audio.onended = () => {\n        resolve();\n      };\n\n      audio.onerror = (error) => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n\n      audio.play().catch((error) => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages(): Observable<Call[]> {\n    this.logger.debug('[MessageService] Getting voice messages');\n\n    return this.apollo\n      .watchQuery<{ getVoiceMessages: Call[] }>({\n        query: GET_VOICE_MESSAGES_QUERY,\n        fetchPolicy: 'network-only', // Ne pas utiliser le cache pour cette requête\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const voiceMessages = result.data?.getVoiceMessages || [];\n          this.logger.debug(\n            `[MessageService] Retrieved ${voiceMessages.length} voice messages`\n          );\n          return voiceMessages;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            '[MessageService] Error fetching voice messages:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch voice messages'));\n        })\n      );\n  }\n  // Message methods\n  getMessages(\n    senderId: string,\n    receiverId: string,\n    conversationId: string,\n    page: number = 1,\n    limit: number = 10\n  ): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<{ getMessages: Message[] }>({\n        query: GET_MESSAGES_QUERY,\n        variables: { senderId, receiverId, conversationId, limit, page },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const messages = result.data?.getMessages || [];\n          return messages.map((msg) => this.normalizeMessage(msg));\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching messages:', error);\n          return throwError(() => new Error('Failed to fetch messages'));\n        })\n      );\n  }\n  editMessage(messageId: string, newContent: string): Observable<Message> {\n    return this.apollo\n      .mutate<{ editMessage: Message }>({\n        mutation: EDIT_MESSAGE_MUTATION,\n        variables: { messageId, newContent },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.editMessage) {\n            throw new Error('Failed to edit message');\n          }\n          return this.normalizeMessage(result.data.editMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error editing message:', error);\n          return throwError(() => new Error('Failed to edit message'));\n        })\n      );\n  }\n\n  deleteMessage(messageId: string): Observable<Message> {\n    return this.apollo\n      .mutate<{ deleteMessage: Message }>({\n        mutation: DELETE_MESSAGE_MUTATION,\n        variables: { messageId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.deleteMessage) {\n            throw new Error('Failed to delete message');\n          }\n          return this.normalizeMessage(result.data.deleteMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error deleting message:', error);\n          return throwError(() => new Error('Failed to delete message'));\n        })\n      );\n  }\n\n  markMessageAsRead(messageId: string): Observable<Message> {\n    return this.apollo\n      .mutate<MarkAsReadResponse>({\n        mutation: MARK_AS_READ_MUTATION,\n        variables: { messageId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.markMessageAsRead)\n            throw new Error('Failed to mark message as read');\n          return {\n            ...result.data.markMessageAsRead,\n            readAt: new Date(),\n          };\n        }),\n        catchError((error) => {\n          console.error('Error marking message as read:', error);\n          return throwError(() => new Error('Failed to mark message as read'));\n        })\n      );\n  }\n\n  reactToMessage(messageId: string, emoji: string): Observable<Message> {\n    return this.apollo\n      .mutate<ReactToMessageResponse>({\n        mutation: REACT_TO_MESSAGE_MUTATION,\n        variables: { messageId, emoji },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.reactToMessage)\n            throw new Error('Failed to react to message');\n          return result.data.reactToMessage;\n        }),\n        catchError((error) => {\n          console.error('Error reacting to message:', error);\n          return throwError(() => new Error('Failed to react to message'));\n        })\n      );\n  }\n\n  forwardMessage(\n    messageId: string,\n    conversationIds: string[]\n  ): Observable<Message[]> {\n    return this.apollo\n      .mutate<ForwardMessageResponse>({\n        mutation: FORWARD_MESSAGE_MUTATION,\n        variables: { messageId, conversationIds },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.forwardMessage)\n            throw new Error('Failed to forward message');\n          return result.data.forwardMessage.map((msg) => ({\n            ...msg,\n            timestamp: msg.timestamp\n              ? this.normalizeDate(msg.timestamp)\n              : new Date(),\n          }));\n        }),\n        catchError((error) => {\n          console.error('Error forwarding message:', error);\n          return throwError(() => new Error('Failed to forward message'));\n        })\n      );\n  }\n\n  pinMessage(messageId: string, conversationId: string): Observable<Message> {\n    return this.apollo\n      .mutate<PinMessageResponse>({\n        mutation: PIN_MESSAGE_MUTATION,\n        variables: { messageId, conversationId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.pinMessage)\n            throw new Error('Failed to pin message');\n          return {\n            ...result.data.pinMessage,\n            pinnedAt: new Date(),\n          };\n        }),\n        catchError((error) => {\n          console.error('Error pinning message:', error);\n          return throwError(() => new Error('Failed to pin message'));\n        })\n      );\n  }\n\n  searchMessages(\n    query: string,\n    conversationId?: string,\n    filters: MessageFilter = {}\n  ): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<SearchMessagesResponse>({\n        query: SEARCH_MESSAGES_QUERY,\n        variables: {\n          query,\n          conversationId,\n          ...filters,\n          dateFrom: this.toSafeISOString(filters.dateFrom),\n          dateTo: this.toSafeISOString(filters.dateTo),\n        },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map(\n          (result) =>\n            result.data?.searchMessages?.map((msg) => ({\n              ...msg,\n              timestamp: this.safeDate(msg.timestamp),\n              sender: this.normalizeUser(msg.sender),\n            })) || []\n        ),\n        catchError((error) => {\n          console.error('Error searching messages:', error);\n          return throwError(() => new Error('Failed to search messages'));\n        })\n      );\n  }\n\n  getUnreadMessages(userId: string): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<GetUnreadMessagesResponse>({\n        query: GET_UNREAD_MESSAGES_QUERY,\n        variables: { userId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map(\n          (result) =>\n            result.data?.getUnreadMessages?.map((msg) => ({\n              ...msg,\n              timestamp: this.safeDate(msg.timestamp),\n              sender: this.normalizeUser(msg.sender),\n            })) || []\n        ),\n        catchError((error) => {\n          console.error('Error fetching unread messages:', error);\n          return throwError(() => new Error('Failed to fetch unread messages'));\n        })\n      );\n  }\n\n  setActiveConversation(conversationId: string): void {\n    this.activeConversation.next(conversationId);\n  }\n\n  getConversations(): Observable<Conversation[]> {\n    return this.apollo\n      .watchQuery<GetConversationsResponse>({\n        query: GET_CONVERSATIONS_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const conversations = result.data?.getConversations || [];\n          return conversations.map((conv) => this.normalizeConversation(conv));\n        }),\n        catchError((error) => {\n          console.error('Error fetching conversations:', error);\n          return throwError(() => new Error('Failed to load conversations'));\n        })\n      );\n  }\n\n  getConversation(\n    conversationId: string,\n    limit?: number,\n    page?: number\n  ): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`\n    );\n\n    const variables: any = { conversationId };\n\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(\n        `[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`\n      );\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(\n      `[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`\n    );\n\n    return this.apollo\n      .watchQuery<GetConversationResponse>({\n        query: GET_CONVERSATION_QUERY,\n        variables: variables,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] Conversation response received:`,\n            result\n          );\n\n          const conv = result.data?.getConversation;\n          if (!conv) {\n            this.logger.error(\n              `[MessageService] Conversation not found: ${conversationId}`\n            );\n            throw new Error('Conversation not found');\n          }\n\n          this.logger.debug(\n            `[MessageService] Normalizing conversation: ${conversationId}`\n          );\n          const normalizedConversation = this.normalizeConversation(conv);\n\n          this.logger.info(\n            `[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${\n              normalizedConversation.participants?.length || 0\n            }, messages: ${normalizedConversation.messages?.length || 0}`\n          );\n          return normalizedConversation;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            `[MessageService] Error fetching conversation:`,\n            error\n          );\n          return throwError(() => new Error('Failed to load conversation'));\n        })\n      );\n  }\n\n  createConversation(userId: string): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Creating conversation with user: ${userId}`\n    );\n\n    if (!userId) {\n      this.logger.error(\n        `[MessageService] Cannot create conversation: userId is undefined`\n      );\n      return throwError(\n        () => new Error('User ID is required to create a conversation')\n      );\n    }\n\n    return this.apollo\n      .mutate<{ createConversation: Conversation }>({\n        mutation: CREATE_CONVERSATION_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] Conversation creation response:`,\n            result\n          );\n\n          const conversation = result.data?.createConversation;\n          if (!conversation) {\n            this.logger.error(\n              `[MessageService] Failed to create conversation with user: ${userId}`\n            );\n            throw new Error('Failed to create conversation');\n          }\n\n          try {\n            const normalizedConversation =\n              this.normalizeConversation(conversation);\n            this.logger.info(\n              `[MessageService] Conversation created successfully: ${normalizedConversation.id}`\n            );\n            return normalizedConversation;\n          } catch (error) {\n            this.logger.error(\n              `[MessageService] Error normalizing created conversation:`,\n              error\n            );\n            throw new Error('Error processing created conversation');\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(\n            `[MessageService] Error creating conversation with user ${userId}:`,\n            error\n          );\n          return throwError(\n            () => new Error(`Failed to create conversation: ${error.message}`)\n          );\n        })\n      );\n  }\n\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId: string): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Getting or creating conversation with user: ${userId}`\n    );\n\n    if (!userId) {\n      this.logger.error(\n        `[MessageService] Cannot get/create conversation: userId is undefined`\n      );\n      return throwError(\n        () => new Error('User ID is required to get/create a conversation')\n      );\n    }\n\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(\n      map((conversations) => {\n        // Récupérer l'ID de l'utilisateur actuel\n        const currentUserId = this.getCurrentUserId();\n\n        // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n        const existingConversation = conversations.find((conv) => {\n          if (conv.isGroup) return false;\n\n          // Vérifier si la conversation contient les deux utilisateurs\n          const participantIds =\n            conv.participants?.map((p) => p.id || p._id) || [];\n          return (\n            participantIds.includes(userId) &&\n            participantIds.includes(currentUserId)\n          );\n        });\n\n        if (existingConversation) {\n          this.logger.info(\n            `[MessageService] Found existing conversation: ${existingConversation.id}`\n          );\n          return existingConversation;\n        }\n\n        // Si aucune conversation n'est trouvée, en créer une nouvelle\n        throw new Error('No existing conversation found');\n      }),\n      catchError((error) => {\n        this.logger.info(\n          `[MessageService] No existing conversation found, creating new one: ${error.message}`\n        );\n        return this.createConversation(userId);\n      })\n    );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 2: Méthodes pour les Notifications\n  // --------------------------------------------------------------------------\n  // Propriétés pour la pagination des notifications\n  private notificationPagination = {\n    currentPage: 1,\n    limit: 10,\n    hasMoreNotifications: true,\n  };\n\n  getNotifications(\n    refresh = false,\n    page = 1,\n    limit = 10\n  ): Observable<Notification[]> {\n    this.logger.info(\n      'MessageService',\n      `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`\n    );\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY,\n    });\n\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug(\n        'MessageService',\n        'Resetting pagination due to refresh'\n      );\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug(\n      'MessageService',\n      `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`\n    );\n\n    return this.apollo\n      .watchQuery<getUserNotificationsResponse>({\n        query: GET_NOTIFICATIONS_QUERY,\n        variables: {\n          page: page,\n          limit: limit,\n        },\n        fetchPolicy: refresh ? 'network-only' : 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Notifications response received'\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          const notifications = result.data?.getUserNotifications || [];\n          this.logger.debug(\n            'MessageService',\n            `Received ${notifications.length} notifications from server for page ${page}`\n          );\n\n          // Vérifier s'il y a plus de notifications à charger\n          this.notificationPagination.hasMoreNotifications =\n            notifications.length >= limit;\n\n          if (notifications.length === 0) {\n            this.logger.info(\n              'MessageService',\n              'No notifications received from server'\n            );\n            this.notificationPagination.hasMoreNotifications = false;\n          }\n\n          // Filtrer les notifications supprimées\n          const filteredNotifications = notifications.filter(\n            (notif) => !deletedNotificationIds.has(notif.id)\n          );\n\n          this.logger.debug(\n            'MessageService',\n            `Filtered out ${\n              notifications.length - filteredNotifications.length\n            } deleted notifications`\n          );\n\n          // Afficher les notifications reçues pour le débogage\n          filteredNotifications.forEach((notif, index) => {\n            console.log(`Notification ${index + 1} (page ${page}):`, {\n              id: notif.id || (notif as any)._id,\n              type: notif.type,\n              content: notif.content,\n              isRead: notif.isRead,\n            });\n          });\n\n          // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n          // Mettre à jour le cache avec les nouvelles notifications\n          this.updateCache(filteredNotifications);\n\n          // Récupérer toutes les notifications du cache et les TRIER\n          const cachedNotifications = Array.from(\n            this.notificationCache.values()\n          );\n\n          // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n          const sortedNotifications =\n            this.sortNotificationsByDate(cachedNotifications);\n\n          console.log(\n            `📊 SORTED: ${sortedNotifications.length} notifications triées (plus récentes en premier)`\n          );\n\n          // Mettre à jour le BehaviorSubject avec les notifications TRIÉES\n          this.notifications.next(sortedNotifications);\n\n          // Mettre à jour le compteur de notifications non lues\n          this.updateUnreadCount();\n\n          // Sauvegarder les notifications dans le localStorage\n          this.saveNotificationsToLocalStorage();\n\n          return cachedNotifications;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error loading notifications:',\n            error\n          );\n\n          if (error.graphQLErrors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              error.graphQLErrors\n            );\n          }\n\n          if (error.networkError) {\n            this.logger.error(\n              'MessageService',\n              'Network error:',\n              error.networkError\n            );\n          }\n\n          return throwError(() => new Error('Failed to load notifications'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  private getDeletedNotificationIds(): Set<string> {\n    try {\n      const deletedIds = new Set<string>();\n      const savedNotifications = localStorage.getItem('notifications');\n\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(\n        JSON.parse(savedNotifications).map((n: Notification) => n.id)\n      );\n\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications =\n        this.apollo.client.readQuery<getUserNotificationsResponse>({\n          query: GET_NOTIFICATIONS_QUERY,\n        })?.getUserNotifications || [];\n\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach((notification) => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n\n      return deletedIds;\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors de la récupération des IDs de notifications supprimées:',\n        error\n      );\n      return new Set<string>();\n    }\n  }\n\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications(): boolean {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications(): Observable<Notification[]> {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(\n      false,\n      nextPage,\n      this.notificationPagination.limit\n    );\n  }\n  getNotificationById(id: string): Observable<Notification | undefined> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications.find((n) => n.id === id)),\n      catchError((error) => {\n        this.logger.error('Error finding notification:', error);\n        return throwError(() => new Error('Failed to find notification'));\n      })\n    );\n  }\n  getNotificationCount(): number {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId: string): Observable<Attachment[]> {\n    return this.apollo\n      .query<getNotificationAttachmentsEvent>({\n        query: GET_NOTIFICATIONS_ATTACHAMENTS,\n        variables: { id: notificationId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result) => result.data?.getNotificationAttachments || []),\n        catchError((error) => {\n          this.logger.error('Error fetching notification attachments:', error);\n          return throwError(() => new Error('Failed to fetch attachments'));\n        })\n      );\n  }\n  getUnreadNotifications(): Observable<Notification[]> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications.filter((n) => !n.isRead))\n    );\n  }\n\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(\n    notificationId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug(\n      'MessageService',\n      `Suppression de la notification ${notificationId}`\n    );\n\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const removedCount = this.removeNotificationsFromCache([notificationId]);\n\n    // Appeler le backend pour supprimer la notification\n    return this.apollo\n      .mutate<{ deleteNotification: { success: boolean; message: string } }>({\n        mutation: DELETE_NOTIFICATION_MUTATION,\n        variables: { notificationId },\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteNotification;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(error, 'la suppression de la notification', {\n            success: true,\n            message: 'Notification supprimée localement (erreur serveur)',\n          })\n        )\n      );\n  }\n\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  private saveNotificationsToLocalStorage(): void {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug(\n        'MessageService',\n        'Notifications sauvegardées localement'\n      );\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors de la sauvegarde des notifications:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications(): Observable<{\n    success: boolean;\n    count: number;\n    message: string;\n  }> {\n    this.logger.debug(\n      'MessageService',\n      'Suppression de toutes les notifications'\n    );\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    const allNotificationIds = Array.from(this.notificationCache.keys());\n    this.removeNotificationsFromCache(allNotificationIds);\n\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo\n      .mutate<{\n        deleteAllNotifications: {\n          success: boolean;\n          count: number;\n          message: string;\n        };\n      }>({\n        mutation: DELETE_ALL_NOTIFICATIONS_MUTATION,\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteAllNotifications;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression de toutes les notifications:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(\n            error,\n            'la suppression de toutes les notifications',\n            {\n              success: true,\n              count,\n              message: `${count} notifications supprimées localement (erreur serveur)`,\n            }\n          )\n        )\n      );\n  }\n\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(\n    notificationIds: string[]\n  ): Observable<{ success: boolean; count: number; message: string }> {\n    this.logger.debug(\n      'MessageService',\n      `Suppression de ${notificationIds.length} notifications`\n    );\n\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.removeNotificationsFromCache(notificationIds);\n\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo\n      .mutate<{\n        deleteMultipleNotifications: {\n          success: boolean;\n          count: number;\n          message: string;\n        };\n      }>({\n        mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n        variables: { notificationIds },\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteMultipleNotifications;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression multiple:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(\n            error,\n            'la suppression multiple de notifications',\n            {\n              success: count > 0,\n              count,\n              message: `${count} notifications supprimées localement (erreur serveur)`,\n            }\n          )\n        )\n      );\n  }\n  groupNotificationsByType(): Observable<\n    Map<NotificationType, Notification[]>\n  > {\n    return this.notifications$.pipe(\n      map((notifications) => {\n        const groups = new Map<NotificationType, Notification[]>();\n        notifications.forEach((notif) => {\n          if (!groups.has(notif.type)) {\n            groups.set(notif.type, []);\n          }\n          groups.get(notif.type)?.push(notif);\n        });\n        return groups;\n      })\n    );\n  }\n  markAsRead(notificationIds: string[]): Observable<{\n    success: boolean;\n    readCount: number;\n    remainingCount: number;\n  }> {\n    this.logger.debug(\n      'MessageService',\n      `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`\n    );\n\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value,\n      });\n    }\n\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(\n      (id) => id && typeof id === 'string' && id.trim() !== ''\n    );\n\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds,\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n\n    this.logger.debug(\n      'MessageService',\n      'Sending mutation to mark notifications as read',\n      validIds\n    );\n\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(\n          0,\n          this.notificationCount.value - validIds.length\n        ),\n      },\n    };\n\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds,\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n\n    return this.apollo\n      .mutate<MarkNotificationsAsReadResponse>({\n        mutation: MARK_NOTIFICATION_READ_MUTATION,\n        variables: { notificationIds: validIds },\n        optimisticResponse: optimisticResponse,\n        errorPolicy: 'all', // Continuer même en cas d'erreur\n        fetchPolicy: 'no-cache', // Ne pas utiliser le cache pour cette mutation\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug('MessageService', 'Mutation result', result);\n          console.log('Mutation result:', result);\n\n          // Si nous avons des erreurs GraphQL, les logger mais continuer\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              result.errors\n            );\n            console.error('GraphQL errors:', result.errors);\n          }\n\n          // Utiliser la réponse du serveur ou notre réponse optimiste\n          const response =\n            result.data?.markNotificationsAsRead ??\n            optimisticResponse.markNotificationsAsRead;\n\n          return response;\n        }),\n        catchError((error: Error) => {\n          this.logger.error(\n            'MessageService',\n            'Error marking notifications as read:',\n            error\n          );\n          console.error('Error in markAsRead:', error);\n\n          // En cas d'erreur, retourner quand même un succès simulé\n          // puisque nous avons déjà mis à jour l'interface utilisateur\n          return of({\n            success: true,\n            readCount: validIds.length,\n            remainingCount: Math.max(\n              0,\n              this.notificationCount.value - validIds.length\n            ),\n          });\n        })\n      );\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels\n  // --------------------------------------------------------------------------\n\n  /**\n   * Initie un appel avec un autre utilisateur\n   * @param recipientId ID de l'utilisateur à appeler\n   * @param callType Type d'appel (audio, vidéo)\n   * @param conversationId ID de la conversation (optionnel)\n   * @param options Options d'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string,\n    options?: CallOptions\n  ): Observable<Call> {\n    return this.setupMediaDevices(callType).pipe(\n      switchMap((stream) => {\n        this.localStream = stream;\n        this.localStream$.next(stream);\n\n        // Créer une connexion peer\n        this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n\n        // Ajouter les pistes audio/vidéo\n        stream.getTracks().forEach((track) => {\n          this.peerConnection!.addTrack(track, stream);\n        });\n\n        // Écouter les candidats ICE\n        this.peerConnection.onicecandidate = (event) => {\n          if (event.candidate) {\n            this.sendCallSignal(\n              this.generateCallId(),\n              'ice-candidate',\n              JSON.stringify(event.candidate)\n            );\n          }\n        };\n\n        // Écouter les pistes distantes\n        this.peerConnection.ontrack = (event) => {\n          if (!this.remoteStream) {\n            this.remoteStream = new MediaStream();\n            this.remoteStream$.next(this.remoteStream);\n          }\n          event.streams[0].getTracks().forEach((track) => {\n            this.remoteStream!.addTrack(track);\n          });\n        };\n\n        // Créer l'offre SDP\n        return from(this.peerConnection.createOffer()).pipe(\n          switchMap((offer) => {\n            return from(this.peerConnection!.setLocalDescription(offer)).pipe(\n              map(() => offer)\n            );\n          })\n        );\n      }),\n      switchMap((offer) => {\n        // Générer un ID d'appel unique\n        const callId = this.generateCallId();\n\n        // Envoyer l'offre au serveur\n        return this.apollo\n          .mutate<{ initiateCall: Call }>({\n            mutation: INITIATE_CALL_MUTATION,\n            variables: {\n              recipientId,\n              callType,\n              callId,\n              offer: JSON.stringify(offer),\n              conversationId,\n              options,\n            },\n          })\n          .pipe(\n            map((result) => {\n              const call = result.data?.initiateCall;\n              if (!call) {\n                throw new Error('Failed to initiate call');\n              }\n\n              // Mettre à jour l'état de l'appel actif\n              this.activeCall.next(call);\n\n              // S'abonner aux signaux d'appel\n              const signalSub = this.subscribeToCallSignals(\n                call.id\n              ).subscribe();\n              this.subscriptions.push(signalSub);\n\n              return call;\n            })\n          );\n      }),\n      catchError((error) => {\n        this.logger.error('Error initiating call', error);\n        this.cleanupCall();\n        return throwError(() => new Error('Failed to initiate call'));\n      })\n    );\n  }\n\n  /**\n   * Accepte un appel entrant\n   * @param incomingCall Appel entrant à accepter\n   * @returns Observable avec les informations de l'appel\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    this.stop('ringtone');\n\n    return this.setupMediaDevices(incomingCall.type).pipe(\n      switchMap((stream) => {\n        this.localStream = stream;\n        this.localStream$.next(stream);\n\n        // Créer une connexion peer\n        this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n\n        // Ajouter les pistes audio/vidéo\n        stream.getTracks().forEach((track) => {\n          this.peerConnection!.addTrack(track, stream);\n        });\n\n        // Écouter les candidats ICE\n        this.peerConnection.onicecandidate = (event) => {\n          if (event.candidate) {\n            this.sendCallSignal(\n              incomingCall.id,\n              'ice-candidate',\n              JSON.stringify(event.candidate)\n            );\n          }\n        };\n\n        // Écouter les pistes distantes\n        this.peerConnection.ontrack = (event) => {\n          if (!this.remoteStream) {\n            this.remoteStream = new MediaStream();\n            this.remoteStream$.next(this.remoteStream);\n          }\n          event.streams[0].getTracks().forEach((track) => {\n            this.remoteStream!.addTrack(track);\n          });\n        };\n\n        // Définir l'offre distante\n        const offer = JSON.parse(incomingCall.offer);\n        return from(\n          this.peerConnection.setRemoteDescription(\n            new RTCSessionDescription(offer)\n          )\n        ).pipe(\n          switchMap(() => from(this.peerConnection!.createAnswer())),\n          switchMap((answer) => {\n            return from(this.peerConnection!.setLocalDescription(answer)).pipe(\n              map(() => answer)\n            );\n          })\n        );\n      }),\n      switchMap((answer) => {\n        // Envoyer la réponse au serveur\n        return this.apollo\n          .mutate<{ acceptCall: Call }>({\n            mutation: ACCEPT_CALL_MUTATION,\n            variables: {\n              callId: incomingCall.id,\n              answer: JSON.stringify(answer),\n            },\n          })\n          .pipe(\n            map((result) => {\n              const call = result.data?.acceptCall;\n              if (!call) {\n                throw new Error('Failed to accept call');\n              }\n\n              // Jouer le son de connexion\n              this.play('call-connected');\n\n              // Mettre à jour l'état de l'appel actif\n              this.activeCall.next({\n                ...call,\n                caller: incomingCall.caller,\n                type: incomingCall.type,\n                conversationId: incomingCall.conversationId,\n              });\n\n              // S'abonner aux signaux d'appel\n              const signalSub = this.subscribeToCallSignals(\n                incomingCall.id\n              ).subscribe();\n              this.subscriptions.push(signalSub);\n\n              // Effacer l'appel entrant\n              this.incomingCall.next(null);\n\n              return call;\n            })\n          );\n      }),\n      catchError((error) => {\n        this.logger.error('Error accepting call', error);\n        this.cleanupCall();\n        return throwError(() => new Error('Failed to accept call'));\n      })\n    );\n  }\n\n  /**\n   * Rejette un appel entrant\n   * @param callId ID de l'appel à rejeter\n   * @param reason Raison du rejet (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  rejectCall(callId: string, reason?: string): Observable<Call> {\n    this.stop('ringtone');\n\n    return this.apollo\n      .mutate<{ rejectCall: Call }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const call = result.data?.rejectCall;\n          if (!call) {\n            throw new Error('Failed to reject call');\n          }\n\n          // Effacer l'appel entrant\n          this.incomingCall.next(null);\n\n          return call;\n        }),\n        catchError((error) => {\n          this.logger.error('Error rejecting call', error);\n          return throwError(() => new Error('Failed to reject call'));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   * @param callId ID de l'appel à terminer\n   * @param feedback Commentaires sur l'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  endCall(callId: string, feedback?: CallFeedback): Observable<Call> {\n    this.stop('ringtone');\n    this.play('call-end');\n\n    return this.apollo\n      .mutate<{ endCall: Call }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const call = result.data?.endCall;\n          if (!call) {\n            throw new Error('Failed to end call');\n          }\n\n          // Nettoyer les ressources\n          this.cleanupCall();\n\n          // Mettre à jour l'état de l'appel actif\n          this.activeCall.next(null);\n\n          return call;\n        }),\n        catchError((error) => {\n          this.logger.error('Error ending call', error);\n          this.cleanupCall();\n          return throwError(() => new Error('Failed to end call'));\n        })\n      );\n  }\n\n  /**\n   * Active ou désactive la caméra ou le micro\n   * @param callId ID de l'appel\n   * @param video État de la caméra (optionnel)\n   * @param audio État du micro (optionnel)\n   * @returns Observable avec le résultat de l'opération\n   */\n  toggleMedia(\n    callId: string,\n    video?: boolean,\n    audio?: boolean\n  ): Observable<CallSuccess> {\n    if (this.localStream) {\n      // Mettre à jour les pistes locales\n      if (video !== undefined) {\n        this.localStream.getVideoTracks().forEach((track) => {\n          track.enabled = video;\n        });\n      }\n\n      if (audio !== undefined) {\n        this.localStream.getAudioTracks().forEach((track) => {\n          track.enabled = audio;\n        });\n      }\n    }\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video,\n          audio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.toggleCallMedia;\n          if (!success) {\n            throw new Error('Failed to toggle media');\n          }\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('Error toggling media', error);\n          return throwError(() => new Error('Failed to toggle media'));\n        })\n      );\n  }\n\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId: string): Observable<CallSignal> {\n    return this.apollo\n      .subscribe<{ callSignal: CallSignal }>({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        variables: { callId },\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.callSignal) {\n            throw new Error('No call signal received');\n          }\n          return data.callSignal;\n        }),\n        tap((signal) => {\n          this.callSignals.next(signal);\n          this.handleCallSignal(signal);\n        }),\n        catchError((error) => {\n          this.logger.error('Error in call signal subscription', error);\n          return throwError(() => new Error('Call signal subscription failed'));\n        })\n      );\n  }\n\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(\n    callId: string,\n    signalType: string,\n    signalData: string\n  ): Observable<CallSuccess> {\n    return this.apollo\n      .mutate<{ sendCallSignal: CallSuccess }>({\n        mutation: SEND_CALL_SIGNAL_MUTATION,\n        variables: {\n          callId,\n          signalType,\n          signalData,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.sendCallSignal;\n          if (!success) {\n            throw new Error('Failed to send call signal');\n          }\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('Error sending call signal', error);\n          return throwError(() => new Error('Failed to send call signal'));\n        })\n      );\n  }\n\n  /**\n   * Récupère l'historique des appels avec filtres\n   * @param limit Nombre d'appels à récupérer\n   * @param offset Décalage pour la pagination\n   * @param status Filtres de statut\n   * @param type Filtres de type\n   * @param startDate Date de début\n   * @param endDate Date de fin\n   * @returns Observable avec l'historique des appels\n   */\n  getCallHistory(\n    limit: number = 20,\n    offset: number = 0,\n    status?: string[],\n    type?: string[],\n    startDate?: string | null,\n    endDate?: string | null\n  ): Observable<Call[]> {\n    return this.apollo\n      .watchQuery<{ callHistory: Call[] }>({\n        query: CALL_HISTORY_QUERY,\n        variables: {\n          limit,\n          offset,\n          status,\n          type,\n          startDate,\n          endDate,\n        },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const history = result.data?.callHistory || [];\n          this.logger.debug(`Retrieved ${history.length} call history items`);\n          return history;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call history:', error);\n          return throwError(() => new Error('Failed to fetch call history'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les détails d'un appel spécifique\n   * @param callId ID de l'appel\n   * @returns Observable avec les détails de l'appel\n   */\n  getCallDetails(callId: string): Observable<Call> {\n    return this.apollo\n      .watchQuery<{ callDetails: Call }>({\n        query: CALL_DETAILS_QUERY,\n        variables: { callId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const details = result.data?.callDetails;\n          if (!details) {\n            throw new Error('Call details not found');\n          }\n          this.logger.debug(`Retrieved call details for: ${callId}`);\n          return details;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call details:', error);\n          return throwError(() => new Error('Failed to fetch call details'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les statistiques d'appels\n   * @returns Observable avec les statistiques d'appels\n   */\n  getCallStats(): Observable<any> {\n    return this.apollo\n      .watchQuery<{ callStats: any }>({\n        query: CALL_STATS_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const stats = result.data?.callStats;\n          if (!stats) {\n            throw new Error('Call stats not found');\n          }\n          this.logger.debug('Retrieved call stats:', stats);\n          return stats;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call stats:', error);\n          return throwError(() => new Error('Failed to fetch call stats'));\n        })\n      );\n  }\n\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  private handleCallSignal(signal: CallSignal): void {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  private handleIceCandidate(signal: CallSignal): void {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection\n        .addIceCandidate(new RTCIceCandidate(candidate))\n        .catch((error) => {\n          this.logger.error('Error adding ICE candidate', error as Error);\n        });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error as Error);\n    }\n  }\n\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  private handleAnswer(signal: CallSignal): void {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection\n        .setRemoteDescription(new RTCSessionDescription(answer))\n        .catch((error) => {\n          this.logger.error('Error setting remote description', error as Error);\n        });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error as Error);\n    }\n  }\n\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  private handleEndCall(signal: CallSignal): void {\n    this.stop('ringtone');\n    this.cleanupCall();\n\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString(),\n      });\n    }\n  }\n\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  private handleRejectCall(signal: CallSignal): void {\n    this.stop('ringtone');\n    this.cleanupCall();\n\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString(),\n      });\n    }\n  }\n\n  /**\n   * Nettoie les ressources d'appel\n   */\n  private cleanupCall(): void {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  private setupMediaDevices(callType: CallType): Observable<MediaStream> {\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video:\n        callType !== CallType.AUDIO\n          ? {\n              width: { ideal: 1280 },\n              height: { ideal: 720 },\n            }\n          : false,\n    };\n\n    return new Observable<MediaStream>((observer) => {\n      navigator.mediaDevices\n        .getUserMedia(constraints)\n        .then((stream) => {\n          observer.next(stream);\n          observer.complete();\n        })\n        .catch((error) => {\n          this.logger.error('Error accessing media devices', error);\n          observer.error(new Error('Failed to access media devices'));\n        });\n    });\n  }\n\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  private generateCallId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(\n    forceRefresh = false,\n    search?: string,\n    page: number = 1,\n    limit: number = 10,\n    sortBy: string = 'username',\n    sortOrder: string = 'asc',\n    isOnline?: boolean\n  ): Observable<User[]> {\n    this.logger.info(\n      'MessageService',\n      `Getting users with params: forceRefresh=${forceRefresh}, search=${\n        search || '(empty)'\n      }, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`\n    );\n\n    const now = Date.now();\n    const cacheValid =\n      !forceRefresh &&\n      this.usersCache.length > 0 &&\n      now - this.lastFetchTime <= this.CACHE_DURATION &&\n      !search &&\n      page === 1 &&\n      limit >= this.usersCache.length;\n\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug(\n        'MessageService',\n        `Using cached users (${this.usersCache.length} users)`\n      );\n      return of([...this.usersCache]);\n    }\n\n    this.logger.debug(\n      'MessageService',\n      `Fetching users from server with pagination, fetchPolicy=${\n        forceRefresh ? 'network-only' : 'cache-first'\n      }`\n    );\n\n    return this.apollo\n      .watchQuery<any>({\n        query: GET_ALL_USER_QUERY,\n        variables: {\n          search,\n          page,\n          limit,\n          sortBy,\n          sortOrder,\n          isOnline: isOnline !== undefined ? isOnline : null,\n        },\n        fetchPolicy: forceRefresh ? 'network-only' : 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Users response received',\n            result\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors in getAllUsers:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          if (!result.data?.getAllUsers) {\n            this.logger.warn(\n              'MessageService',\n              'No users data received from server'\n            );\n            return [];\n          }\n\n          const paginatedResponse = result.data.getAllUsers;\n\n          // Log pagination metadata\n          this.logger.debug('MessageService', 'Pagination metadata:', {\n            totalCount: paginatedResponse.totalCount,\n            totalPages: paginatedResponse.totalPages,\n            currentPage: paginatedResponse.currentPage,\n            hasNextPage: paginatedResponse.hasNextPage,\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\n          });\n\n          // Normalize users with error handling\n          const users: User[] = [];\n          for (const user of paginatedResponse.users) {\n            try {\n              if (user) {\n                users.push(this.normalizeUser(user));\n              }\n            } catch (error) {\n              this.logger.warn(\n                'MessageService',\n                `Error normalizing user, skipping:`,\n                error\n              );\n            }\n          }\n\n          this.logger.info(\n            'MessageService',\n            `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`\n          );\n\n          // Update cache only for first page with no filters\n          if (!search && page === 1 && !isOnline) {\n            this.usersCache = [...users];\n            this.lastFetchTime = Date.now();\n            this.logger.debug(\n              'MessageService',\n              `User cache updated with ${users.length} users`\n            );\n          }\n\n          // Store pagination metadata in a property for component access\n          this.currentUserPagination = {\n            totalCount: paginatedResponse.totalCount,\n            totalPages: paginatedResponse.totalPages,\n            currentPage: paginatedResponse.currentPage,\n            hasNextPage: paginatedResponse.hasNextPage,\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\n          };\n\n          return users;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error fetching users:', error);\n\n          if (error.graphQLErrors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              error.graphQLErrors\n            );\n          }\n\n          if (error.networkError) {\n            this.logger.error(\n              'MessageService',\n              'Network error:',\n              error.networkError\n            );\n          }\n\n          // Return cache if available (only for first page)\n          if (\n            this.usersCache.length > 0 &&\n            page === 1 &&\n            !search &&\n            !isOnline\n          ) {\n            this.logger.warn(\n              'MessageService',\n              `Returning ${this.usersCache.length} cached users due to fetch error`\n            );\n            return of([...this.usersCache]);\n          }\n\n          return throwError(\n            () =>\n              new Error(\n                `Failed to fetch users: ${error.message || 'Unknown error'}`\n              )\n          );\n        })\n      );\n  }\n  getOneUser(userId: string): Observable<User> {\n    return this.apollo\n      .watchQuery<GetOneUserResponse>({\n        query: GET_USER_QUERY,\n        variables: { id: userId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => this.normalizeUser(result.data?.getOneUser)),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error fetching user:', error);\n          return throwError(() => new Error('Failed to fetch user'));\n        })\n      );\n  }\n  getCurrentUser(): Observable<User> {\n    return this.apollo\n      .watchQuery<getCurrentUserResponse>({\n        query: GET_CURRENT_USER_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => this.normalizeUser(result.data?.getCurrentUser)),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error fetching current user:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch current user'));\n        })\n      );\n  }\n  setUserOnline(userId: string): Observable<User> {\n    return this.apollo\n      .mutate<SetUserOnlineResponse>({\n        mutation: SET_USER_ONLINE_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.setUserOnline)\n            throw new Error('Failed to set user online');\n          return this.normalizeUser(result.data.setUserOnline);\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error setting user online:',\n            error\n          );\n          return throwError(() => new Error('Failed to set user online'));\n        })\n      );\n  }\n  setUserOffline(userId: string): Observable<User> {\n    return this.apollo\n      .mutate<SetUserOfflineResponse>({\n        mutation: SET_USER_OFFLINE_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.setUserOffline)\n            throw new Error('Failed to set user offline');\n          return this.normalizeUser(result.data.setUserOffline);\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error setting user offline:',\n            error\n          );\n          return throwError(() => new Error('Failed to set user offline'));\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion des Groupes\n  // --------------------------------------------------------------------------\n\n  /**\n   * Crée un nouveau groupe\n   */\n  createGroup(\n    name: string,\n    participantIds: string[],\n    photo?: File,\n    description?: string\n  ): Observable<any> {\n    this.logger.debug(\n      'MessageService',\n      `Creating group: ${name} with ${participantIds.length} participants`\n    );\n\n    if (!name || !participantIds || participantIds.length === 0) {\n      return throwError(\n        () => new Error('Nom du groupe et participants requis')\n      );\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: CREATE_GROUP_MUTATION,\n        variables: { name, participantIds, photo, description },\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.createGroup;\n          if (!group) {\n            throw new Error('Échec de la création du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group created successfully: ${group.id}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error creating group:', error);\n          return throwError(() => new Error('Échec de la création du groupe'));\n        })\n      );\n  }\n\n  /**\n   * Met à jour un groupe existant\n   */\n  updateGroup(groupId: string, input: any): Observable<any> {\n    this.logger.debug('MessageService', `Updating group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: UPDATE_GROUP_MUTATION,\n        variables: { id: groupId, input },\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.updateGroup;\n          if (!group) {\n            throw new Error('Échec de la mise à jour du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group updated successfully: ${group.id}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error updating group:', error);\n          return throwError(\n            () => new Error('Échec de la mise à jour du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Supprime un groupe\n   */\n  deleteGroup(\n    groupId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: DELETE_GROUP_MUTATION,\n        variables: { id: groupId },\n      })\n      .pipe(\n        map((result: any) => {\n          const response = result.data?.deleteGroup;\n          if (!response) {\n            throw new Error('Échec de la suppression du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group deleted successfully: ${groupId}`\n          );\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error deleting group:', error);\n          return throwError(\n            () => new Error('Échec de la suppression du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Quitte un groupe\n   */\n  leaveGroup(\n    groupId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: LEAVE_GROUP_MUTATION,\n        variables: { groupId },\n      })\n      .pipe(\n        map((result: any) => {\n          const response = result.data?.leaveGroup;\n          if (!response) {\n            throw new Error('Échec de la sortie du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Left group successfully: ${groupId}`\n          );\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error leaving group:', error);\n          return throwError(() => new Error('Échec de la sortie du groupe'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les informations d'un groupe\n   */\n  getGroup(groupId: string): Observable<any> {\n    this.logger.debug('MessageService', `Getting group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .query({\n        query: GET_GROUP_QUERY,\n        variables: { id: groupId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.getGroup;\n          if (!group) {\n            throw new Error('Groupe non trouvé');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group retrieved successfully: ${groupId}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error getting group:', error);\n          return throwError(\n            () => new Error('Échec de la récupération du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Récupère les groupes d'un utilisateur\n   */\n  getUserGroups(userId: string): Observable<any[]> {\n    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n\n    if (!userId) {\n      return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n    }\n\n    return this.apollo\n      .query({\n        query: GET_USER_GROUPS_QUERY,\n        variables: { userId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result: any) => {\n          const groups = result.data?.getUserGroups || [];\n          this.logger.info(\n            'MessageService',\n            `Retrieved ${groups.length} groups for user: ${userId}`\n          );\n          return groups;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error getting user groups:',\n            error\n          );\n          return throwError(\n            () => new Error('Échec de la récupération des groupes')\n          );\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 4: Subscriptions et Gestion Temps Réel\n  // --------------------------------------------------------------------------\n  subscribeToNewMessages(conversationId: string): Observable<Message> {\n    console.log(\n      `🔍 DEBUG: subscribeToNewMessages called with conversationId: ${conversationId}`\n    );\n\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    const tokenValid = this.isTokenValid();\n    console.log(`🔍 DEBUG: Token validation result: ${tokenValid}`);\n\n    if (!tokenValid) {\n      console.warn(\n        '❌ DEBUG: Token invalid - subscription will not be established'\n      );\n      this.logger.warn(\n        \"Tentative d'abonnement aux messages avec un token invalide ou expiré\"\n      );\n      return of(null as unknown as Message);\n    }\n\n    console.log(\n      `✅ DEBUG: Token valid - proceeding with subscription setup for conversation: ${conversationId}`\n    );\n    this.logger.debug(\n      `🚀 INSTANT MESSAGE: Setting up real-time subscription for conversation: ${conversationId}`\n    );\n\n    console.log(`🔍 DEBUG: Creating Apollo subscription with variables:`, {\n      conversationId,\n    });\n    console.log(\n      `🔍 DEBUG: MESSAGE_SENT_SUBSCRIPTION query:`,\n      MESSAGE_SENT_SUBSCRIPTION\n    );\n    console.log(`🔍 DEBUG: Subscription variables:`, { conversationId });\n\n    const sub$ = this.apollo\n      .subscribe<{ messageSent: Message }>({\n        query: MESSAGE_SENT_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        tap((result) => {\n          console.log(`🔍 DEBUG: Raw subscription result received:`, result);\n          console.log(`🔍 DEBUG: result.data:`, result.data);\n          console.log(\n            `🔍 DEBUG: result.data?.messageSent:`,\n            result.data?.messageSent\n          );\n        }),\n        map((result) => {\n          const msg = result.data?.messageSent;\n          if (!msg) {\n            console.log(\n              `❌ DEBUG: No message payload received in result:`,\n              result\n            );\n            this.logger.warn('⚠️ No message payload received');\n            throw new Error('No message payload received');\n          }\n\n          this.logger.debug(\n            '⚡ INSTANT: New message received via WebSocket',\n            msg\n          );\n\n          // Vérifier que l'ID est présent\n          if (!msg.id && !msg._id) {\n            this.logger.warn(\n              '⚠️ Message without ID received, generating temp ID'\n            );\n            msg.id = `temp-${Date.now()}`;\n          }\n\n          try {\n            // NORMALISATION RAPIDE du message\n            const normalizedMessage = this.normalizeMessage(msg);\n\n            this.logger.debug(\n              '✅ INSTANT: Message normalized successfully',\n              normalizedMessage\n            );\n\n            // TRAITEMENT INSTANTANÉ selon le type\n            if (\n              normalizedMessage.type === MessageType.AUDIO ||\n              normalizedMessage.type === MessageType.VOICE_MESSAGE ||\n              (normalizedMessage.attachments &&\n                normalizedMessage.attachments.some(\n                  (att) => att.type === 'AUDIO'\n                ))\n            ) {\n              this.logger.debug(\n                '🎤 INSTANT: Voice message received in real-time'\n              );\n            }\n\n            // MISE À JOUR IMMÉDIATE de l'UI\n            this.zone.run(() => {\n              this.logger.debug(\n                '📡 INSTANT: Updating conversation UI immediately'\n              );\n              this.updateConversationWithNewMessage(\n                conversationId,\n                normalizedMessage\n              );\n            });\n\n            return normalizedMessage;\n          } catch (err) {\n            this.logger.error('❌ Error normalizing message:', err);\n\n            // Créer un message minimal mais valide pour éviter les erreurs\n            const minimalMessage: Message = {\n              id: msg.id || msg._id || `temp-${Date.now()}`,\n              content: msg.content || '',\n              type: msg.type || MessageType.TEXT,\n              timestamp: this.safeDate(msg.timestamp),\n              isRead: false,\n              sender: msg.sender\n                ? this.normalizeUser(msg.sender)\n                : {\n                    id: this.getCurrentUserId(),\n                    username: 'Unknown',\n                  },\n            };\n\n            this.logger.debug(\n              '🔧 FALLBACK: Created minimal message',\n              minimalMessage\n            );\n            return minimalMessage;\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Message subscription error:',\n            error\n          );\n          // Retourner un observable vide au lieu de null\n          return EMPTY;\n        }),\n        // Filtrer les valeurs null\n        filter((message) => !!message),\n        // Réessayer après un délai en cas d'erreur\n        retry(3)\n      );\n\n    console.log(`🔍 DEBUG: Setting up subscription observer...`);\n\n    const sub = sub$.subscribe({\n      next: (message) => {\n        console.log(`✅ DEBUG: Message received via subscription:`, message);\n        // Traitement supplémentaire pour s'assurer que le message est bien affiché\n        this.logger.debug('MessageService', 'New message received:', message);\n\n        // Mettre à jour la conversation avec le nouveau message\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: (err) => {\n        console.error(`❌ DEBUG: Subscription error:`, err);\n        this.logger.error('Error in message subscription:', err);\n      },\n      complete: () => {\n        console.log(`🔚 DEBUG: Subscription completed`);\n      },\n    });\n\n    // Log pour confirmer que la subscription est créée\n    console.log(`🔗 DEBUG: Subscription object created:`, sub);\n    console.log(`🔗 DEBUG: Apollo client state:`, this.apollo);\n\n    this.subscriptions.push(sub);\n    console.log(\n      `✅ DEBUG: Subscription established and added to subscriptions list. Total subscriptions: ${this.subscriptions.length}`\n    );\n    return sub$;\n  }\n\n  /**\n   * Met à jour une conversation avec un nouveau message INSTANTANÉMENT\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  private updateConversationWithNewMessage(\n    conversationId: string,\n    message: Message\n  ): void {\n    this.logger.debug(\n      `⚡ INSTANT: Updating conversation ${conversationId} with new message ${message.id}`\n    );\n\n    // MISE À JOUR IMMÉDIATE sans attendre la requête\n    this.zone.run(() => {\n      // Émettre IMMÉDIATEMENT l'événement de conversation active\n      this.activeConversation.next(conversationId);\n\n      this.logger.debug('📡 INSTANT: Conversation event emitted immediately');\n    });\n\n    // Mise à jour en arrière-plan (non-bloquante)\n    setTimeout(() => {\n      this.getConversation(conversationId).subscribe({\n        next: (conversation) => {\n          this.logger.debug(\n            `✅ BACKGROUND: Conversation ${conversationId} refreshed with ${\n              conversation?.messages?.length || 0\n            } messages`\n          );\n        },\n        error: (error) => {\n          this.logger.error(\n            `⚠️ BACKGROUND: Error refreshing conversation ${conversationId}:`,\n            error\n          );\n        },\n      });\n    }, 0); // Exécution asynchrone immédiate\n  }\n\n  /**\n   * Rafraîchit les notifications du sender après envoi d'un message\n   */\n  private refreshSenderNotifications(): void {\n    console.log('🔄 SENDER: Refreshing notifications after message sent');\n\n    // Recharger les notifications en arrière-plan\n    this.getNotifications(true).subscribe({\n      next: (notifications) => {\n        console.log(\n          '🔄 SENDER: Notifications refreshed successfully',\n          notifications.length\n        );\n      },\n      error: (error) => {\n        console.error('🔄 SENDER: Error refreshing notifications:', error);\n      },\n    });\n  }\n\n  subscribeToUserStatus(): Observable<User> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\"\n      );\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n\n    const sub$ = this.apollo\n      .subscribe<{ userStatusChanged: User }>({\n        query: USER_STATUS_SUBSCRIPTION,\n      })\n      .pipe(\n        tap((result) =>\n          this.logger.debug(\n            \"Données reçues de l'abonnement au statut utilisateur:\",\n            result\n          )\n        ),\n        map((result) => {\n          const user = result.data?.userStatusChanged;\n          if (!user) {\n            this.logger.error('No status payload received');\n            throw new Error('No status payload received');\n          }\n          return this.normalizeUser(user);\n        }),\n        catchError((error) => {\n          this.logger.error('Status subscription error:', error as Error);\n          return throwError(() => new Error('Status subscription failed'));\n        }),\n        retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(\n    conversationId: string\n  ): Observable<Conversation> {\n    const sub$ = this.apollo\n      .subscribe<{ conversationUpdated: Conversation }>({\n        query: CONVERSATION_UPDATED_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => {\n          const conv = result.data?.conversationUpdated;\n          if (!conv) throw new Error('No conversation payload received');\n\n          const normalizedConversation: Conversation = {\n            ...conv,\n            participants:\n              conv.participants?.map((p) => this.normalizeUser(p)) || [],\n            lastMessage: conv.lastMessage\n              ? {\n                  ...conv.lastMessage,\n                  sender: this.normalizeUser(conv.lastMessage.sender),\n                  timestamp: this.safeDate(conv.lastMessage.timestamp),\n                  readAt: conv.lastMessage.readAt\n                    ? this.safeDate(conv.lastMessage.readAt)\n                    : undefined,\n                  // Conservez toutes les autres propriétés du message\n                  id: conv.lastMessage.id,\n                  content: conv.lastMessage.content,\n                  type: conv.lastMessage.type,\n                  isRead: conv.lastMessage.isRead,\n                  // ... autres propriétés nécessaires\n                }\n              : null, // On conserve null comme dans votre version originale\n          };\n\n          return normalizedConversation as Conversation; // Assertion de type si nécessaire\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Conversation subscription error:',\n            error\n          );\n          return throwError(\n            () => new Error('Conversation subscription failed')\n          );\n        })\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(\n    conversationId: string\n  ): Observable<TypingIndicatorEvent> {\n    const sub$ = this.apollo\n      .subscribe<TypingIndicatorEvents>({\n        query: TYPING_INDICATOR_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => result.data?.typingIndicator),\n        filter(Boolean),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Typing indicator subscription error:',\n            error\n          );\n          return throwError(\n            () => new Error('Typing indicator subscription failed')\n          );\n        })\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  private isTokenValid(): boolean {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString(),\n        });\n        return false;\n      }\n\n      return true;\n    } catch (error) {\n      this.logger.error(\n        'Erreur lors de la vérification du token:',\n        error as Error\n      );\n      return false;\n    }\n  }\n\n  subscribeToNotificationsRead(): Observable<string[]> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux notifications avec un token invalide ou expiré\"\n      );\n      return of([]);\n    }\n\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n\n    const sub$ = this.apollo\n      .subscribe<NotificationsReadEvent>({\n        query: NOTIFICATIONS_READ_SUBSCRIPTION,\n      })\n      .pipe(\n        tap((result) =>\n          this.logger.debug(\n            \"Données reçues de l'abonnement aux notifications lues:\",\n            result\n          )\n        ),\n        map((result) => {\n          const notificationIds = result.data?.notificationsRead || [];\n          this.logger.debug(\n            'Notifications marquées comme lues:',\n            notificationIds\n          );\n          this.updateNotificationStatus(notificationIds, true);\n          return notificationIds;\n        }),\n        catchError((err) => {\n          this.logger.error(\n            'Notifications read subscription error:',\n            err as Error\n          );\n          // Retourner un tableau vide au lieu de propager l'erreur\n          return of([]);\n        }),\n        // Réessayer après un délai en cas d'erreur\n        retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications(): Observable<Notification> {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux notifications sans être connecté\"\n      );\n      return EMPTY;\n    }\n\n    this.logger.debug(\n      '🚀 INSTANT NOTIFICATION: Setting up real-time subscription'\n    );\n\n    const source$ = this.apollo.subscribe<NotificationReceivedEvent>({\n      query: NOTIFICATION_SUBSCRIPTION,\n    });\n\n    const processed$ = source$.pipe(\n      map((result) => {\n        const notification = result.data?.notificationReceived;\n        if (!notification) {\n          throw new Error('No notification payload received');\n        }\n\n        this.logger.debug(\n          '⚡ INSTANT: New notification received',\n          notification\n        );\n\n        const normalized = this.normalizeNotification(notification);\n\n        // Vérification rapide du cache\n        if (this.notificationCache.has(normalized.id)) {\n          this.logger.debug(\n            `🔄 Notification ${normalized.id} already in cache, skipping`\n          );\n          throw new Error('Notification already exists in cache');\n        }\n\n        // TRAITEMENT INSTANTANÉ\n        this.logger.debug('📡 INSTANT: Processing notification immediately');\n\n        // Vérifier si la notification existe déjà pour éviter les doublons\n        const currentNotifications = this.notifications.value;\n        const existingNotification = currentNotifications.find(\n          (n) => n.id === normalized.id\n        );\n\n        if (existingNotification) {\n          this.logger.debug(\n            '🔄 DUPLICATE: Notification already exists, skipping:',\n            normalized.id\n          );\n          return normalized;\n        }\n\n        // Son de notification IMMÉDIAT\n        this.playNotificationSound();\n\n        // Mise à jour INSTANTANÉE du cache\n        this.updateNotificationCache(normalized);\n\n        // Émettre IMMÉDIATEMENT la notification EN PREMIER\n        this.zone.run(() => {\n          // 🚀 INSERTION EN PREMIER: Nouvelle notification en tête de liste\n          const updatedNotifications = [normalized, ...currentNotifications];\n\n          this.logger.debug(\n            `⚡ INSTANT: Nouvelle notification ajoutée en PREMIER (${updatedNotifications.length} total)`\n          );\n\n          this.notifications.next(updatedNotifications);\n          this.notificationCount.next(this.notificationCount.value + 1);\n        });\n\n        this.logger.debug(\n          '✅ INSTANT: Notification processed and emitted',\n          normalized\n        );\n\n        return normalized;\n      }),\n      // Gestion d'erreurs optimisée\n      catchError((err) => {\n        if (\n          err instanceof Error &&\n          err.message === 'Notification already exists in cache'\n        ) {\n          return EMPTY;\n        }\n\n        this.logger.error('❌ Notification subscription error:', err as Error);\n        return EMPTY;\n      }),\n      // Optimisation: traitement en temps réel\n      tap((notification) => {\n        this.logger.debug(\n          '⚡ INSTANT: Notification ready for UI update',\n          notification\n        );\n      })\n    );\n\n    const sub = processed$.subscribe({\n      next: (notification) => {\n        this.logger.debug(\n          '✅ INSTANT: Notification delivered to UI',\n          notification\n        );\n      },\n      error: (error) => {\n        this.logger.error(\n          '❌ CRITICAL: Notification subscription error',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.push(sub);\n    this.logger.debug('🔗 INSTANT: Notification subscription established');\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n\n  private startCleanupInterval(): void {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  private cleanupExpiredNotifications(): void {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n\n    let expiredCount = 0;\n\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n\n      // 🚀 TRI OPTIMISÉ: Maintenir l'ordre après nettoyage\n      const remainingNotifications = Array.from(\n        this.notificationCache.values()\n      );\n      const sortedNotifications = this.sortNotificationsByDate(\n        remainingNotifications\n      );\n\n      this.notifications.next(sortedNotifications);\n      this.updateUnreadCount();\n    }\n  }\n  /**\n   * Trie les notifications par date (plus récentes en premier)\n   * @param notifications Array de notifications à trier\n   * @returns Array de notifications triées\n   */\n  private sortNotificationsByDate(\n    notifications: Notification[]\n  ): Notification[] {\n    return notifications.sort((a, b) => {\n      // Utiliser timestamp ou une date par défaut si manquant\n      const dateA = new Date(a.timestamp || 0);\n      const dateB = new Date(b.timestamp || 0);\n      return dateB.getTime() - dateA.getTime(); // Ordre décroissant (plus récent en premier)\n    });\n  }\n\n  private getCurrentUserId(): string {\n    return localStorage.getItem('userId') || '';\n  }\n  private normalizeMessage(message: Message): Message {\n    if (!message) {\n      this.logger.error(\n        '[MessageService] Cannot normalize null or undefined message'\n      );\n      throw new Error('Message object is required');\n    }\n\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error(\n          '[MessageService] Message ID is missing',\n          undefined,\n          message\n        );\n        throw new Error('Message ID is required');\n      }\n\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender\n          ? this.normalizeUser(message.sender)\n          : undefined;\n      } catch (error) {\n        this.logger.warn(\n          '[MessageService] Error normalizing message sender, using default values',\n          error\n        );\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true,\n        };\n      }\n\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn(\n            '[MessageService] Error normalizing message receiver, using default values',\n            error\n          );\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true,\n          };\n        }\n      }\n\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments =\n        message.attachments?.map((att) => ({\n          id: att.id || att._id || `attachment-${Date.now()}`,\n          url: att.url || '',\n          type: att.type || 'unknown',\n          name: att.name || 'attachment',\n          size: att.size || 0,\n          duration: att.duration || 0,\n        })) || [];\n\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null,\n      };\n\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id,\n      });\n\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error(\n        '[MessageService] Error normalizing message:',\n        error instanceof Error ? error : new Error(String(error)),\n        message\n      );\n      throw new Error(\n        `Failed to normalize message: ${\n          error instanceof Error ? error.message : String(error)\n        }`\n      );\n    }\n  }\n\n  public normalizeUser(user: any): User {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive =\n      user.isActive !== undefined && user.isActive !== null\n        ? user.isActive\n        : true;\n    const role = user.role || 'user';\n\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount,\n    };\n  }\n  private normalizeConversation(conv: Conversation): Conversation {\n    if (!conv) {\n      this.logger.error(\n        '[MessageService] Cannot normalize null or undefined conversation'\n      );\n      throw new Error('Conversation object is required');\n    }\n\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error(\n          '[MessageService] Conversation ID is missing',\n          undefined,\n          conv\n        );\n        throw new Error('Conversation ID is required');\n      }\n\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn(\n              '[MessageService] Error normalizing participant, skipping',\n              error\n            );\n          }\n        }\n      } else {\n        this.logger.warn(\n          '[MessageService] Conversation has no participants or invalid participants array',\n          conv\n        );\n      }\n\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length,\n        });\n\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug(\n                '[MessageService] Successfully normalized message',\n                {\n                  messageId: normalizedMessage.id,\n                  content: normalizedMessage.content?.substring(0, 20),\n                  sender: normalizedMessage.sender?.username,\n                }\n              );\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn(\n              '[MessageService] Error normalizing message in conversation, skipping',\n              error\n            );\n          }\n        }\n      } else {\n        this.logger.debug(\n          '[MessageService] No messages found in conversation or invalid messages array'\n        );\n      }\n\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn(\n            '[MessageService] Error normalizing last message, using null',\n            error\n          );\n        }\n      }\n\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt),\n      };\n\n      this.logger.debug(\n        '[MessageService] Conversation normalized successfully',\n        {\n          conversationId: normalizedConversation.id,\n          participantCount: normalizedParticipants.length,\n          messageCount: normalizedMessages.length,\n        }\n      );\n\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error(\n        '[MessageService] Error normalizing conversation:',\n        error instanceof Error ? error : new Error(String(error)),\n        conv\n      );\n      throw new Error(\n        `Failed to normalize conversation: ${\n          error instanceof Error ? error.message : String(error)\n        }`\n      );\n    }\n  }\n  private normalizeDate(date: string | Date | undefined): Date {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  private safeDate(date: string | Date | undefined): Date {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  private toSafeISOString = (\n    date: Date | string | undefined\n  ): string | undefined => {\n    if (!date) return undefined;\n    return typeof date === 'string' ? date : date.toISOString();\n  };\n  private normalizeNotification(notification: Notification): Notification {\n    this.logger.debug(\n      'MessageService',\n      'Normalizing notification',\n      notification\n    );\n\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || (notification as any)._id;\n    if (!notificationId) {\n      this.logger.error(\n        'MessageService',\n        'Notification ID is missing',\n        notification\n      );\n      throw new Error('Notification ID is required');\n    }\n\n    if (!notification.timestamp) {\n      this.logger.warn(\n        'MessageService',\n        'Notification timestamp is missing, using current time',\n        notification\n      );\n      notification.timestamp = new Date();\n    }\n\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId, // Conserver l'ID MongoDB original\n        id: notificationId, // Utiliser le même ID pour les deux propriétés\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId),\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message),\n        }),\n      };\n\n      this.logger.debug(\n        'MessageService',\n        'Normalized notification result',\n        normalized\n      );\n      return normalized;\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Error in normalizeNotification',\n        error\n      );\n      throw error;\n    }\n  }\n  private normalizeSender(sender: any) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && { image: sender.image }),\n    };\n  }\n\n  /**\n   * Normalise un message de notification\n   * @param message Message à normaliser\n   * @returns Message normalisé\n   */\n  private normalizeNotMessage(message: any) {\n    if (!message) return null;\n\n    return {\n      id: message.id || message._id,\n      content: message.content || '',\n      type: message.type || 'TEXT',\n      timestamp: this.safeDate(message.timestamp),\n      attachments: message.attachments || [],\n      ...(message.sender && { sender: this.normalizeSender(message.sender) }),\n    };\n  }\n  /**\n   * Met à jour le cache de notifications avec une ou plusieurs notifications\n   * @param notifications Notification(s) à ajouter au cache\n   * @param skipDuplicates Si true, ignore les notifications déjà présentes dans le cache\n   */\n  private updateCache(\n    notifications: Notification | Notification[],\n    skipDuplicates: boolean = true\n  ) {\n    const notificationArray = Array.isArray(notifications)\n      ? notifications\n      : [notifications];\n\n    this.logger.debug(\n      'MessageService',\n      `Updating notification cache with ${notificationArray.length} notifications`\n    );\n\n    if (notificationArray.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notificationArray.filter(\n      (notif) => notif && (notif.id || (notif as any)._id)\n    );\n\n    if (validNotifications.length !== notificationArray.length) {\n      this.logger.warn(\n        'MessageService',\n        `Found ${\n          notificationArray.length - validNotifications.length\n        } notifications without valid IDs`\n      );\n    }\n\n    let addedCount = 0;\n    let skippedCount = 0;\n\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || (notif as any)._id;\n        if (!notifId) {\n          this.logger.error(\n            'MessageService',\n            'Notification without ID:',\n            notif\n          );\n          return;\n        }\n\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n\n        // Vérifier si cette notification existe déjà dans le cache\n        if (skipDuplicates && this.notificationCache.has(normalized.id)) {\n          this.logger.debug(\n            'MessageService',\n            `Notification ${normalized.id} already exists in cache, skipping`\n          );\n          skippedCount++;\n          return;\n        }\n\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n        addedCount++;\n\n        this.logger.debug(\n          'MessageService',\n          `Added notification ${normalized.id} to cache`\n        );\n      } catch (error) {\n        this.logger.error(\n          'MessageService',\n          `Error processing notification ${index + 1}:`,\n          error\n        );\n      }\n    });\n\n    this.logger.debug(\n      'MessageService',\n      `Cache update complete: ${addedCount} added, ${skippedCount} skipped, total: ${this.notificationCache.size}`\n    );\n\n    // Mettre à jour les observables et sauvegarder\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Met à jour les observables de notifications et sauvegarde dans le localStorage\n   * OPTIMISÉ: Trie les notifications par date (plus récentes en premier)\n   */\n  private refreshNotificationObservables(): void {\n    const allNotifications = Array.from(this.notificationCache.values());\n\n    // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n    const sortedNotifications = this.sortNotificationsByDate(allNotifications);\n\n    this.logger.debug(\n      `📊 SORTED: ${sortedNotifications.length} notifications triées par date (plus récentes en premier)`\n    );\n\n    this.notifications.next(sortedNotifications);\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n  }\n\n  /**\n   * Met à jour le compteur de notifications non lues\n   */\n  private updateUnreadCount(): void {\n    const allNotifications = Array.from(this.notificationCache.values());\n    const unreadNotifications = allNotifications.filter((n) => !n.isRead);\n    const count = unreadNotifications.length;\n\n    // Forcer la mise à jour dans la zone Angular\n    this.zone.run(() => {\n      this.notificationCount.next(count);\n\n      // Émettre un événement global pour forcer la mise à jour du layout\n      window.dispatchEvent(\n        new CustomEvent('notificationCountChanged', {\n          detail: { count },\n        })\n      );\n    });\n  }\n\n  /**\n   * Met à jour le cache avec une seule notification (méthode simplifiée)\n   * @param notification Notification à ajouter\n   */\n  private updateNotificationCache(notification: Notification): void {\n    this.updateCache(notification, true);\n  }\n  /**\n   * Met à jour le statut de lecture des notifications\n   * @param ids IDs des notifications à mettre à jour\n   * @param isRead Nouveau statut de lecture\n   */\n  private updateNotificationStatus(ids: string[], isRead: boolean): void {\n    ids.forEach((id) => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, {\n          ...notif,\n          isRead,\n          readAt: isRead ? new Date().toISOString() : undefined,\n        });\n      }\n    });\n    this.refreshNotificationObservables();\n  }\n\n  /**\n   * Méthode générique pour supprimer des notifications du cache local\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Nombre de notifications supprimées\n   */\n  private removeNotificationsFromCache(notificationIds: string[]): number {\n    console.log(\n      '🗑️ REMOVE FROM CACHE: Starting removal of',\n      notificationIds.length,\n      'notifications'\n    );\n    console.log(\n      '🗑️ REMOVE FROM CACHE: Cache size before:',\n      this.notificationCache.size\n    );\n\n    let removedCount = 0;\n    notificationIds.forEach((id) => {\n      if (this.notificationCache.has(id)) {\n        console.log('🗑️ REMOVE FROM CACHE: Removing notification:', id);\n        this.notificationCache.delete(id);\n        removedCount++;\n      } else {\n        console.log(\n          '🗑️ REMOVE FROM CACHE: Notification not found in cache:',\n          id\n        );\n      }\n    });\n\n    console.log('🗑️ REMOVE FROM CACHE: Removed', removedCount, 'notifications');\n    console.log(\n      '🗑️ REMOVE FROM CACHE: Cache size after:',\n      this.notificationCache.size\n    );\n\n    if (removedCount > 0) {\n      console.log('🗑️ REMOVE FROM CACHE: Refreshing observables...');\n      this.refreshNotificationObservables();\n    }\n\n    return removedCount;\n  }\n\n  /**\n   * Méthode générique pour gérer les erreurs de suppression\n   * @param error Erreur survenue\n   * @param operation Nom de l'opération\n   * @param fallbackResponse Réponse de fallback en cas d'erreur\n   */\n  private handleDeletionError(\n    error: any,\n    operation: string,\n    fallbackResponse: any\n  ) {\n    this.logger.error('MessageService', `Erreur lors de ${operation}:`, error);\n    return of(fallbackResponse);\n  }\n  // Typing indicators\n  startTyping(conversationId: string): Observable<boolean> {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n\n    return this.apollo\n      .mutate<StartTupingResponse>({\n        mutation: START_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId,\n          },\n        },\n      })\n      .pipe(\n        map((result) => result.data?.startTyping || false),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error starting typing indicator',\n            error\n          );\n          return throwError(\n            () => new Error('Failed to start typing indicator')\n          );\n        })\n      );\n  }\n\n  stopTyping(conversationId: string): Observable<boolean> {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n\n    return this.apollo\n      .mutate<StopTypingResponse>({\n        mutation: STOP_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId,\n          },\n        },\n      })\n      .pipe(\n        map((result) => result.data?.stopTyping || false),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error stopping typing indicator',\n            error\n          );\n          return throwError(() => new Error('Failed to stop typing indicator'));\n        })\n      );\n  }\n\n  // ========================================\n  // MÉTHODE SENDMESSAGE MANQUANTE\n  // ========================================\n\n  /**\n   * Envoie un message (texte, fichier, audio, etc.)\n   * @param receiverId ID du destinataire\n   * @param content Contenu du message (texte)\n   * @param file Fichier à envoyer (optionnel)\n   * @param messageType Type de message (TEXT, AUDIO, IMAGE, etc.)\n   * @param conversationId ID de la conversation\n   * @returns Observable avec le message envoyé\n   */\n  sendMessage(\n    receiverId: string,\n    content: string,\n    file?: File,\n    messageType: any = 'TEXT',\n    conversationId?: string\n  ): Observable<Message> {\n    console.log('🚀 [MessageService] sendMessage called with:', {\n      receiverId,\n      content: content?.substring(0, 50),\n      hasFile: !!file,\n      fileName: file?.name,\n      fileType: file?.type,\n      fileSize: file?.size,\n      messageType,\n      conversationId,\n    });\n\n    if (!receiverId) {\n      const error = new Error('Receiver ID is required');\n      console.error('❌ [MessageService] sendMessage error:', error);\n      return throwError(() => error);\n    }\n\n    // Préparer les variables pour la mutation\n    const variables: any = {\n      receiverId,\n      content: content || '',\n      type: messageType,\n    };\n\n    // Ajouter l'ID de conversation si fourni\n    if (conversationId) {\n      variables.conversationId = conversationId;\n    }\n\n    // Si un fichier est fourni, l'ajouter aux variables\n    if (file) {\n      variables.file = file;\n      console.log('📁 [MessageService] Adding file to mutation:', {\n        name: file.name,\n        type: file.type,\n        size: file.size,\n      });\n    }\n\n    console.log(\n      '📤 [MessageService] Sending mutation with variables:',\n      variables\n    );\n\n    return this.apollo\n      .mutate<SendMessageResponse>({\n        mutation: SEND_MESSAGE_MUTATION,\n        variables,\n        context: {\n          useMultipart: !!file, // Utiliser multipart si un fichier est présent\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log(\n            '✅ [MessageService] sendMessage mutation result:',\n            result\n          );\n\n          if (!result.data?.sendMessage) {\n            throw new Error('No message data received from server');\n          }\n\n          const message = result.data.sendMessage;\n          console.log('📨 [MessageService] Message sent successfully:', {\n            id: message.id,\n            type: message.type,\n            content: message.content?.substring(0, 50),\n            hasAttachments: !!message.attachments?.length,\n          });\n\n          // Normaliser le message reçu\n          const normalizedMessage = this.normalizeMessage(message);\n          console.log(\n            '🔧 [MessageService] Message normalized:',\n            normalizedMessage\n          );\n\n          return normalizedMessage;\n        }),\n        catchError((error) => {\n          console.error('❌ [MessageService] sendMessage error:', error);\n          this.logger.error('Error sending message:', error);\n\n          // Fournir un message d'erreur plus spécifique\n          let errorMessage = \"Erreur lors de l'envoi du message\";\n          if (error.networkError) {\n            errorMessage = 'Erreur de connexion réseau';\n          } else if (error.graphQLErrors?.length > 0) {\n            errorMessage = error.graphQLErrors[0].message || errorMessage;\n          }\n\n          return throwError(() => new Error(errorMessage));\n        })\n      );\n  }\n\n  // ========================================\n  // MÉTHODES UTILITAIRES CONSOLIDÉES\n  // ========================================\n\n  /**\n   * Formate l'heure d'un message\n   */\n  formatMessageTime(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'Unknown time';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false,\n      });\n    } catch (error) {\n      return 'Invalid time';\n    }\n  }\n\n  /**\n   * Formate la dernière activité d'un utilisateur\n   */\n  formatLastActive(lastActive: string | Date | undefined): string {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate =\n      lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours =\n      Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n\n  /**\n   * Formate la date d'un message\n   */\n  formatMessageDate(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'Unknown date';\n\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n        });\n      }\n\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n        })}`;\n      }\n\n      const day = date\n        .toLocaleDateString('fr-FR', { weekday: 'short' })\n        .toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n      })}`;\n    } catch (error) {\n      return 'Invalid date';\n    }\n  }\n\n  /**\n   * Détermine si un en-tête de date doit être affiché\n   */\n  shouldShowDateHeader(messages: any[], index: number): boolean {\n    if (index === 0) return true;\n\n    try {\n      const currentMsg = messages[index];\n      const prevMsg = messages[index - 1];\n\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) return true;\n\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n\n      return currentDate !== prevDate;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  private getDateFromTimestamp(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'unknown-date';\n    try {\n      return (\n        timestamp instanceof Date ? timestamp : new Date(timestamp)\n      ).toDateString();\n    } catch (error) {\n      return 'invalid-date';\n    }\n  }\n\n  /**\n   * Obtient l'icône d'un fichier selon son type MIME\n   */\n  getFileIcon(mimeType?: string): string {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword'))\n      return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed'))\n      return 'fa-file-archive';\n    return 'fa-file';\n  }\n\n  /**\n   * Obtient le type d'un fichier selon son type MIME\n   */\n  getFileType(mimeType?: string): string {\n    if (!mimeType) return 'File';\n\n    const typeMap: Record<string, string> = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':\n        'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':\n        'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation':\n        'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive',\n    };\n\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n\n  /**\n   * Vérifie si un message contient une image\n   */\n  hasImage(message: any): boolean {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message: any): boolean {\n    if (!message) return false;\n\n    // Vérifier le type du message\n    if (\n      message.type === MessageType.VOICE_MESSAGE ||\n      message.type === MessageType.VOICE_MESSAGE\n    ) {\n      return true;\n    }\n\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some((att: any) => {\n        const type = att.type?.toString();\n        return (\n          type === 'VOICE_MESSAGE' ||\n          type === 'voice_message' ||\n          (message.metadata?.isVoiceMessage &&\n            (type === 'AUDIO' || type === 'audio'))\n        );\n      });\n    }\n\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message: any): string {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n\n    const voiceAttachment = message.attachments.find((att: any) => {\n      const type = att.type?.toString();\n      return (\n        type === 'VOICE_MESSAGE' ||\n        type === 'voice_message' ||\n        type === 'AUDIO' ||\n        type === 'audio'\n      );\n    });\n\n    return voiceAttachment?.url || '';\n  }\n\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message: any): number {\n    if (!message) return 0;\n\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find((att: any) => {\n        const type = att.type?.toString();\n        return (\n          type === 'VOICE_MESSAGE' ||\n          type === 'voice_message' ||\n          type === 'AUDIO' ||\n          type === 'audio'\n        );\n      });\n\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n\n    return 0;\n  }\n\n  /**\n   * Génère la hauteur des barres de la forme d'onde moderne\n   */\n  getVoiceBarHeight(index: number): number {\n    const pattern = [\n      8, 12, 6, 15, 10, 18, 7, 14, 9, 16, 5, 13, 11, 17, 8, 12, 6, 15, 10, 18,\n    ];\n    return pattern[index % pattern.length];\n  }\n\n  /**\n   * Formate la durée du message vocal en format MM:SS\n   */\n  formatVoiceDuration(seconds: number): string {\n    if (!seconds || seconds === 0) {\n      return '0:00';\n    }\n\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  /**\n   * Obtient l'URL de l'image en toute sécurité\n   */\n  getImageUrl(message: any): string {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n\n  /**\n   * Détermine le type d'un message\n   */\n  getMessageType(message: any): MessageType {\n    if (!message) return MessageType.TEXT;\n\n    try {\n      if (message.type) {\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (\n            attachmentTypeStr === 'file' ||\n            attachmentTypeStr === 'FILE'\n          ) {\n            return MessageType.FILE;\n          } else if (\n            attachmentTypeStr === 'audio' ||\n            attachmentTypeStr === 'AUDIO'\n          ) {\n            return MessageType.AUDIO;\n          } else if (\n            attachmentTypeStr === 'video' ||\n            attachmentTypeStr === 'VIDEO'\n          ) {\n            return MessageType.VIDEO;\n          }\n        }\n\n        return MessageType.FILE;\n      }\n\n      return MessageType.TEXT;\n    } catch (error) {\n      return MessageType.TEXT;\n    }\n  }\n\n  /**\n   * Retourne la liste des emojis communs\n   */\n  getCommonEmojis(): string[] {\n    return [\n      '😀',\n      '😃',\n      '😄',\n      '😁',\n      '😆',\n      '😅',\n      '😂',\n      '🤣',\n      '😊',\n      '😇',\n      '🙂',\n      '🙃',\n      '😉',\n      '😌',\n      '😍',\n      '🥰',\n      '😘',\n      '😗',\n      '😙',\n      '😚',\n      '😋',\n      '😛',\n      '😝',\n      '😜',\n      '🤪',\n      '🤨',\n      '🧐',\n      '🤓',\n      '😎',\n      '🤩',\n      '😏',\n      '😒',\n      '😞',\n      '😔',\n      '😟',\n      '😕',\n      '🙁',\n      '☹️',\n      '😣',\n      '😖',\n      '😫',\n      '😩',\n      '🥺',\n      '😢',\n      '😭',\n      '😤',\n      '😠',\n      '😡',\n      '🤬',\n      '🤯',\n      '😳',\n      '🥵',\n      '🥶',\n      '😱',\n      '😨',\n      '😰',\n      '😥',\n      '😓',\n      '🤗',\n      '🤔',\n      '👍',\n      '👎',\n      '👏',\n      '🙌',\n      '👐',\n      '🤲',\n      '🤝',\n      '🙏',\n      '✌️',\n      '🤞',\n      '❤️',\n      '🧡',\n      '💛',\n      '💚',\n      '💙',\n      '💜',\n      '🖤',\n      '💔',\n      '💯',\n      '💢',\n    ];\n  }\n\n  /**\n   * Obtient les classes CSS pour un message\n   */\n  getMessageTypeClass(message: any, currentUserId: string | null): string {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n\n    try {\n      const isCurrentUser =\n        message.sender?.id === currentUserId ||\n        message.sender?._id === currentUserId ||\n        message.senderId === currentUserId;\n\n      const baseClass = isCurrentUser\n        ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm'\n        : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n\n      const messageType = this.getMessageType(message);\n\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            return `p-1 max-w-xs`;\n          } else if (\n            attachmentTypeStr === 'FILE' ||\n            attachmentTypeStr === 'file'\n          ) {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n\n      // Les vérifications de type sont déjà faites avec les attachments ci-dessus\n\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n\n  // ========================================\n  // MÉTHODES POUR LES APPELS WEBRTC\n  // ========================================\n\n  /**\n   * Initie un appel WebRTC\n   * @param recipientId ID du destinataire\n   * @param callType Type d'appel (VIDEO ou AUDIO)\n   * @param conversationId ID de la conversation (optionnel)\n   * @returns Observable avec les détails de l'appel\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    console.log('🔄 [MessageService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [MessageService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n\n    const variables = {\n      recipientId,\n      type: callType,\n      conversationId,\n    };\n\n    console.log(\n      '📤 [MessageService] Sending initiate call mutation:',\n      variables\n    );\n\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables,\n      })\n      .pipe(\n        map((result) => {\n          console.log(\n            '✅ [MessageService] Call initiated successfully:',\n            result\n          );\n\n          if (!result.data?.initiateCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.initiateCall;\n          console.log('📞 [MessageService] Call details:', {\n            id: call.id,\n            type: call.type,\n            status: call.status,\n            caller: call.caller?.username,\n            recipient: call.recipient?.username,\n          });\n\n          // Mettre à jour l'état local\n          this.activeCall.next(call);\n\n          return call;\n        }),\n        catchError((error) => {\n          console.error('❌ [MessageService] initiateCall error:', error);\n          this.logger.error('Error initiating call:', error);\n\n          let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n          if (error.networkError) {\n            errorMessage = 'Erreur de connexion réseau';\n          } else if (error.graphQLErrors?.length > 0) {\n            errorMessage = error.graphQLErrors[0].message || errorMessage;\n          }\n\n          return throwError(() => new Error(errorMessage));\n        })\n      );\n  }\n\n  /**\n   * Accepte un appel entrant\n   * @param incomingCall Appel entrant à accepter\n   * @returns Observable avec les détails de l'appel accepté\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    console.log('🔄 [MessageService] Accepting call:', incomingCall.id);\n\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log(\n            '✅ [MessageService] Call accepted successfully:',\n            result\n          );\n\n          if (!result.data?.acceptCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.acceptCall;\n\n          // Mettre à jour l'état local\n          this.activeCall.next(call);\n          this.incomingCall.next(null); // Supprimer l'appel entrant\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n          this.play('call-connected');\n\n          return call;\n        }),\n        catchError((error) => {\n          console.error('❌ [MessageService] acceptCall error:', error);\n          this.logger.error('Error accepting call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de l'acceptation de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Rejette un appel entrant\n   * @param callId ID de l'appel à rejeter\n   * @param reason Raison du rejet\n   * @returns Observable avec le résultat\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    console.log('🔄 [MessageService] Rejecting call:', callId, reason);\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason: reason || 'User rejected',\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log(\n            '✅ [MessageService] Call rejected successfully:',\n            result\n          );\n\n          if (!result.data?.rejectCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n\n          return result.data.rejectCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [MessageService] rejectCall error:', error);\n          this.logger.error('Error rejecting call:', error);\n          return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   * @param callId ID de l'appel à terminer\n   * @returns Observable avec le résultat\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    console.log('🔄 [MessageService] Ending call:', callId);\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [MessageService] Call ended successfully:', result);\n\n          if (!result.data?.endCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n\n          // Arrêter tous les sons\n          this.stopAllSounds();\n          this.play('call-end');\n\n          return result.data.endCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [MessageService] endCall error:', error);\n          this.logger.error('Error ending call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de la fin de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   * @param callId ID de l'appel\n   * @param enableVideo État de la vidéo (optionnel)\n   * @param enableAudio État de l'audio (optionnel)\n   * @returns Observable avec le résultat\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    console.log('🔄 [MessageService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio,\n    });\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          enableVideo,\n          enableAudio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log(\n            '✅ [MessageService] Media toggled successfully:',\n            result\n          );\n\n          if (!result.data?.toggleCallMedia) {\n            throw new Error('No response received from server');\n          }\n\n          return result.data.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('❌ [MessageService] toggleMedia error:', error);\n          this.logger.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  // destroy\n  cleanupSubscriptions(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n}\n"], "mappings": "AAEA,SACEA,eAAe,EACfC,UAAU,EACVC,EAAE,EAEFC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,MAAM;AACb,SACEC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,SAAS,QAGJ,gBAAgB;AACvB,SAASC,IAAI,QAAQ,MAAM;AAC3B,SACEC,WAAW,EAEXC,QAAQ,EACRC,UAAU,QAML,yBAAyB;AAChC,SACEC,uBAAuB,EACvBC,uBAAuB,EACvBC,yBAAyB,EACzBC,sBAAsB,EACtBC,qBAAqB,EACrBC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,cAAc,EACdC,kBAAkB,EAClBC,iCAAiC,EACjCC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,yBAAyB,EACzBC,qBAAqB,EACrBC,oBAAoB,EACpBC,6BAA6B,EAC7BC,sBAAsB,EACtBC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,EACpBC,qBAAqB,EACrBC,qBAAqB,EACrBC,qBAAqB,EAGrBC,oBAAoB,EACpBC,eAAe,EACfC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,kBAAkB,EAClBC,8BAA8B,EAC9BC,+BAA+B,EAC/BC,+BAA+B,EAC/BC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,sCAAsC,EACtCC,iCAAiC;AACjC;AACAC,kBAAkB,EAClBC,kBAAkB,EAClBC,gBAAgB,EAChBC,sBAAsB,EACtBC,yBAAyB,EACzBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,wBAAwB,EACxBC,0BAA0B,EAE1BC,wBAAwB,QACnB,4BAA4B;;;;AA0CnC,OAAM,MAAOC,cAAc;EA6DzBC,YACUC,MAAc,EACdC,MAAqB,EACrBC,IAAY;IAFZ,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IA/Dd;IACQ,KAAAC,kBAAkB,GAAG,IAAItE,eAAe,CAAgB,IAAI,CAAC;IAC7D,KAAAuE,aAAa,GAAG,IAAIvE,eAAe,CAAiB,EAAE,CAAC;IACvD,KAAAwE,iBAAiB,GAAG,IAAIC,GAAG,EAAwB;IAEnD,KAAAC,iBAAiB,GAAG,IAAI1E,eAAe,CAAS,CAAC,CAAC;IAClD,KAAA2E,WAAW,GAAG,IAAIF,GAAG,EAAgB;IACrC,KAAAG,aAAa,GAAmB,EAAE;IACzB,KAAAC,cAAc,GAAG,MAAM;IAChC,KAAAC,aAAa,GAAG,CAAC;IAEzB;IACQ,KAAAC,UAAU,GAAG,IAAI/E,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAgF,YAAY,GAAG,IAAIhF,eAAe,CAAsB,IAAI,CAAC;IAC7D,KAAAiF,WAAW,GAAG,IAAIjF,eAAe,CAAoB,IAAI,CAAC;IAC1D,KAAAkF,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,cAAc,GAA6B,IAAI;IAEvD;IACO,KAAAC,WAAW,GAAG,IAAI,CAACN,UAAU,CAACO,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACP,YAAY,CAACM,YAAY,EAAE;IAChD,KAAAE,YAAY,GAAG,IAAI,CAACP,WAAW,CAACK,YAAY,EAAE;IAC9C,KAAAG,YAAY,GAAG,IAAIzF,eAAe,CAAqB,IAAI,CAAC;IAC5D,KAAA0F,aAAa,GAAG,IAAI1F,eAAe,CAAqB,IAAI,CAAC;IAEpE;IACiB,KAAA2F,SAAS,GAAqB;MAC7CC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IACO,KAAAC,UAAU,GAAW,EAAE;IAE/B;IACO,KAAAC,qBAAqB,GAMxB;MACFC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,KAAK;MAClBC,eAAe,EAAE;KAClB;IAED;IACO,KAAAC,mBAAmB,GAAG,IAAI,CAAC/B,kBAAkB,CAACgB,YAAY,EAAE;IAC5D,KAAAgB,cAAc,GAAG,IAAI,CAAC/B,aAAa,CAACe,YAAY,EAAE;IAClD,KAAAiB,kBAAkB,GAAG,IAAI,CAAC7B,iBAAiB,CAACY,YAAY,EAAE;IAEjE;IACQ,KAAAkB,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAw1BrB;IACA;IACA;IACA;IACQ,KAAAC,sBAAsB,GAAG;MAC/BT,WAAW,EAAE,CAAC;MACdU,KAAK,EAAE,EAAE;MACTC,oBAAoB,EAAE;KACvB;IA+lFO,KAAAC,eAAe,GACrBC,IAA+B,IACT;MACtB,IAAI,CAACA,IAAI,EAAE,OAAOC,SAAS;MAC3B,OAAO,OAAOD,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACE,WAAW,EAAE;IAC7D,CAAC;IA77GC,IAAI,CAACC,iCAAiC,EAAE;IACxC,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEA;;;;EAIQH,iCAAiCA,CAAA;IACvC,IAAI;MACF,MAAMI,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAChE,IAAIF,kBAAkB,EAAE;QACtB,MAAM/C,aAAa,GAAGkD,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAmB;QAEtE,IAAI,CAAC9C,iBAAiB,CAACmD,KAAK,EAAE;QAE9BpD,aAAa,CAACqD,OAAO,CAAEC,YAAY,IAAI;UACrC,IAAIA,YAAY,IAAIA,YAAY,CAACC,EAAE,EAAE;YACnC,IAAI,CAACtD,iBAAiB,CAACuD,GAAG,CAACF,YAAY,CAACC,EAAE,EAAED,YAAY,CAAC;;QAE7D,CAAC,CAAC;QAEF,IAAI,CAACtD,aAAa,CAACyD,IAAI,CAACC,KAAK,CAACtH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC0D,MAAM,EAAE,CAAC,CAAC;QACpE,IAAI,CAACC,iBAAiB,EAAE;;KAE3B,CAAC,OAAOC,KAAK,EAAE;MACd;IAAA;EAEJ;EACQjB,iBAAiBA,CAAA;IACvB,IAAI,CAAC9C,IAAI,CAACgE,iBAAiB,CAAC,MAAK;MAC/B,IAAI,CAACC,2BAA2B,EAAE,CAACC,SAAS,EAAE;MAC9C,IAAI,CAACC,4BAA4B,EAAE,CAACD,SAAS,EAAE;MAC/C,IAAI,CAACE,wBAAwB,EAAE,CAACF,SAAS,EAAE;MAC3C;IACF,CAAC,CAAC;;IACF,IAAI,CAACG,qBAAqB,EAAE;EAC9B;EAEA;;;EAGQD,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAACtE,MAAM,CACfoE,SAAS,CAAiC;MACzCI,KAAK,EAAE5E;KACR,CAAC,CACD6E,IAAI,CACHtI,GAAG,CAAC,CAAC;MAAEuI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAE7D,YAAY,EAAE;QACvB,OAAO,IAAI;;MAGb;MACA,IAAI,CAAC8D,kBAAkB,CAACD,IAAI,CAAC7D,YAAY,CAAC;MAC1C,OAAO6D,IAAI,CAAC7D,YAAY;IAC1B,CAAC,CAAC,EACFzE,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC/D,OAAOlI,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGQ4I,kBAAkBA,CAACC,IAAkB;IAC3C,IAAI,CAAC/D,YAAY,CAACgD,IAAI,CAACe,IAAI,CAAC;IAC5B,IAAI,CAACC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC7B;EAEA;EACA;EACA;EAEA;;;EAGQ3B,aAAaA,CAAA;IACnB,IAAI,CAAC4B,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;IACpE,IAAI,CAACA,SAAS,CAAC,cAAc,EAAE,gCAAgC,CAAC;EAClE;EAEA;;;;;EAKQA,SAASA,CAACC,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC;MAC7BC,KAAK,CAACE,IAAI,EAAE;MACZ,IAAI,CAAC9C,MAAM,CAAC0C,IAAI,CAAC,GAAGE,KAAK;MACzB,IAAI,CAAC3C,SAAS,CAACyC,IAAI,CAAC,GAAG,KAAK;MAE5BE,KAAK,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAAC9C,SAAS,CAACyC,IAAI,CAAC,GAAG,KAAK;MAC9B,CAAC,CAAC;KACH,CAAC,OAAOd,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;;;EAKAY,IAAIA,CAACE,IAAY,EAAEM,IAAA,GAAgB,KAAK;IACtC,IAAI,IAAI,CAAC9C,KAAK,EAAE;MACd;;IAGF,IAAI;MACF,MAAM+C,KAAK,GAAG,IAAI,CAACjD,MAAM,CAAC0C,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV;;MAGFA,KAAK,CAACD,IAAI,GAAGA,IAAI;MAEjB,IAAI,CAAC,IAAI,CAAC/C,SAAS,CAACyC,IAAI,CAAC,EAAE;QACzBO,KAAK,CAACC,WAAW,GAAG,CAAC;QACrBD,KAAK,CAACT,IAAI,EAAE,CAACW,KAAK,CAAEvB,KAAK,IAAI;UAC3B;QAAA,CACD,CAAC;QACF,IAAI,CAAC3B,SAAS,CAACyC,IAAI,CAAC,GAAG,IAAI;;KAE9B,CAAC,OAAOd,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;;EAIAwB,IAAIA,CAACV,IAAY;IACf,IAAI;MACF,MAAMO,KAAK,GAAG,IAAI,CAACjD,MAAM,CAAC0C,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV;;MAGF,IAAI,IAAI,CAAChD,SAAS,CAACyC,IAAI,CAAC,EAAE;QACxBO,KAAK,CAACI,KAAK,EAAE;QACbJ,KAAK,CAACC,WAAW,GAAG,CAAC;QACrB,IAAI,CAACjD,SAAS,CAACyC,IAAI,CAAC,GAAG,KAAK;;KAE/B,CAAC,OAAOd,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;EAGA0B,aAAaA,CAAA;IACXC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxD,MAAM,CAAC,CAACoB,OAAO,CAAEsB,IAAI,IAAI;MACxC,IAAI,CAACU,IAAI,CAACV,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;;EAIAe,QAAQA,CAACvD,KAAc;IACrB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAElB,IAAIA,KAAK,EAAE;MACT,IAAI,CAACoD,aAAa,EAAE;;EAExB;EAEA;;;;EAIAI,OAAOA,CAAA;IACL,OAAO,IAAI,CAACxD,KAAK;EACnB;EAEA;;;EAGAyD,qBAAqBA,CAAA;IACnBC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;IAE1E,IAAI,IAAI,CAAC3D,KAAK,EAAE;MACd0D,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;;IAGF;IACA,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MAEA;MACA,IAAI,CAACC,uBAAuB,CAACJ,YAAY,CAAC;MAE1C;MACA;MAEA;MACA;MAEA;MACA;MAEA;MACA;MAEAF,OAAO,CAACC,GAAG,CACT,kEAAkE,CACnE;KACF,CAAC,OAAOjC,KAAK,EAAE;MACdgC,OAAO,CAAChC,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;MAED;MACA,IAAI;QACF,MAAMgB,KAAK,GAAG,IAAIC,KAAK,CAAC,gCAAgC,CAAC;QACzDD,KAAK,CAACuB,MAAM,GAAG,GAAG,CAAC,CAAC;QACpBvB,KAAK,CAACJ,IAAI,EAAE,CAACW,KAAK,CAAEiB,GAAG,IAAI;UACzBR,OAAO,CAAChC,KAAK,CACX,2DAA2D,EAC3DwC,GAAG,CACJ;QACH,CAAC,CAAC;OACH,CAAC,OAAOC,UAAU,EAAE;QACnBT,OAAO,CAAChC,KAAK,CACX,8DAA8D,EAC9DyC,UAAU,CACX;;;EAGP;EAEA;EACQH,uBAAuBA,CAACJ,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC/D;EAEA;EACQS,uBAAuBA,CAACT,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC/D;EAEA;EACQU,uBAAuBA,CAACV,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACtD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA;EACQW,uBAAuBA,CAACX,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;EAC5D;EAEA;EACQY,uBAAuBA,CAACZ,YAA0B;IACxD,IAAI,CAACa,YAAY,CAACb,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;EACnD;EAEA;;;EAGQQ,oBAAoBA,CAC1BR,YAA0B,EAC1Bc,SAAiB,EACjBC,SAAiB,EACjBC,QAAgB;IAEhB,MAAMC,UAAU,GAAGjB,YAAY,CAACkB,gBAAgB,EAAE;IAClD,MAAMC,QAAQ,GAAGnB,YAAY,CAACoB,UAAU,EAAE;IAE1C;IACAH,UAAU,CAACI,IAAI,GAAG,MAAM;IACxBJ,UAAU,CAACF,SAAS,CAACO,cAAc,CACjCP,SAAS,EACTf,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CACrC;IAED;IACAK,QAAQ,CAACI,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEtB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACrEK,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAG,IAAI,CAC5C;IACDK,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,GAAG,GAAG,CACtD;IACDG,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,CAAC,EACDxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAChD;IAED;IACAC,UAAU,CAACQ,OAAO,CAACN,QAAQ,CAAC;IAC5BA,QAAQ,CAACM,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;IAE1C;IACAT,UAAU,CAACU,KAAK,CAAC3B,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACtDG,UAAU,CAAC3B,IAAI,CAACU,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAAC;EAClE;EAEA;;;EAGQH,YAAYA,CAClBb,YAA0B,EAC1Bc,SAAiB,EACjBC,SAAiB,EACjBC,QAAgB;IAEhB,MAAMC,UAAU,GAAGjB,YAAY,CAACkB,gBAAgB,EAAE;IAClD,MAAMC,QAAQ,GAAGnB,YAAY,CAACoB,UAAU,EAAE;IAE1C;IACAH,UAAU,CAACI,IAAI,GAAG,UAAU,CAAC,CAAC;IAC9BJ,UAAU,CAACF,SAAS,CAACO,cAAc,CACjCP,SAAS,EACTf,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CACrC;IAED;IACAK,QAAQ,CAACI,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEtB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACrEK,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAG,IAAI,CAC5C;IACDK,QAAQ,CAACI,IAAI,CAACK,4BAA4B,CACxC,IAAI,EACJ5B,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAChD;IAED;IACAC,UAAU,CAACQ,OAAO,CAACN,QAAQ,CAAC;IAC5BA,QAAQ,CAACM,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;IAE1C;IACAT,UAAU,CAACU,KAAK,CAAC3B,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACtDG,UAAU,CAAC3B,IAAI,CAACU,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAAC;EAClE;EACA;EACA;EACA;EAEA;;;;;EAKAa,SAASA,CAACC,QAAgB;IACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMnD,KAAK,GAAG,IAAIC,KAAK,CAAC+C,QAAQ,CAAC;MAEjChD,KAAK,CAACoD,OAAO,GAAG,MAAK;QACnBF,OAAO,EAAE;MACX,CAAC;MAEDlD,KAAK,CAACqD,OAAO,GAAIrE,KAAK,IAAI;QACxB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjEmE,MAAM,CAACnE,KAAK,CAAC;MACf,CAAC;MAEDgB,KAAK,CAACJ,IAAI,EAAE,CAACW,KAAK,CAAEvB,KAAK,IAAI;QAC3B,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjEmE,MAAM,CAACnE,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;;EAIAsE,gBAAgBA,CAAA;IACd,IAAI,CAACtI,MAAM,CAACuI,KAAK,CAAC,yCAAyC,CAAC;IAE5D,OAAO,IAAI,CAACxI,MAAM,CACfyI,UAAU,CAA+B;MACxCjE,KAAK,EAAE3E,wBAAwB;MAC/B6I,WAAW,EAAE,cAAc,CAAE;KAC9B,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMC,aAAa,GAAGD,MAAM,CAAClE,IAAI,EAAE6D,gBAAgB,IAAI,EAAE;MACzD,IAAI,CAACtI,MAAM,CAACuI,KAAK,CACf,8BAA8BK,aAAa,CAACC,MAAM,iBAAiB,CACpE;MACD,OAAOD,aAAa;IACtB,CAAC,CAAC,EACFzM,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,iDAAiD,EACjDA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EACA;EACAC,WAAWA,CACTC,QAAgB,EAChBC,UAAkB,EAClBC,cAAsB,EACtBC,IAAA,GAAe,CAAC,EAChB3G,KAAA,GAAgB,EAAE;IAElB,OAAO,IAAI,CAACzC,MAAM,CACfyI,UAAU,CAA6B;MACtCjE,KAAK,EAAE9F,kBAAkB;MACzB2K,SAAS,EAAE;QAAEJ,QAAQ;QAAEC,UAAU;QAAEC,cAAc;QAAE1G,KAAK;QAAE2G;MAAI,CAAE;MAChEV,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMU,QAAQ,GAAGV,MAAM,CAAClE,IAAI,EAAEsE,WAAW,IAAI,EAAE;MAC/C,OAAOM,QAAQ,CAACnN,GAAG,CAAEoN,GAAG,IAAK,IAAI,CAACC,gBAAgB,CAACD,GAAG,CAAC,CAAC;IAC1D,CAAC,CAAC,EACFnN,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MACpD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EACAU,WAAWA,CAACC,SAAiB,EAAEC,UAAkB;IAC/C,OAAO,IAAI,CAAC3J,MAAM,CACf4J,MAAM,CAA2B;MAChCC,QAAQ,EAAErL,qBAAqB;MAC/B6K,SAAS,EAAE;QAAEK,SAAS;QAAEC;MAAU;KACnC,CAAC,CACDlF,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE+E,WAAW,EAAE;QAC7B,MAAM,IAAIV,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAO,IAAI,CAACS,gBAAgB,CAACZ,MAAM,CAAClE,IAAI,CAAC+E,WAAW,CAAC;IACvD,CAAC,CAAC,EACFrN,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAClD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEAe,aAAaA,CAACJ,SAAiB;IAC7B,OAAO,IAAI,CAAC1J,MAAM,CACf4J,MAAM,CAA6B;MAClCC,QAAQ,EAAEpL,uBAAuB;MACjC4K,SAAS,EAAE;QAAEK;MAAS;KACvB,CAAC,CACDjF,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEoF,aAAa,EAAE;QAC/B,MAAM,IAAIf,KAAK,CAAC,0BAA0B,CAAC;;MAE7C,OAAO,IAAI,CAACS,gBAAgB,CAACZ,MAAM,CAAClE,IAAI,CAACoF,aAAa,CAAC;IACzD,CAAC,CAAC,EACF1N,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACnD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EAEAgB,iBAAiBA,CAACL,SAAiB;IACjC,OAAO,IAAI,CAAC1J,MAAM,CACf4J,MAAM,CAAqB;MAC1BC,QAAQ,EAAE5M,qBAAqB;MAC/BoM,SAAS,EAAE;QAAEK;MAAS;KACvB,CAAC,CACDjF,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEqF,iBAAiB,EACjC,MAAM,IAAIhB,KAAK,CAAC,gCAAgC,CAAC;MACnD,OAAO;QACL,GAAGH,MAAM,CAAClE,IAAI,CAACqF,iBAAiB;QAChCC,MAAM,EAAE,IAAIC,IAAI;OACjB;IACH,CAAC,CAAC,EACF7N,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEAmB,cAAcA,CAACR,SAAiB,EAAES,KAAa;IAC7C,OAAO,IAAI,CAACnK,MAAM,CACf4J,MAAM,CAAyB;MAC9BC,QAAQ,EAAE9L,yBAAyB;MACnCsL,SAAS,EAAE;QAAEK,SAAS;QAAES;MAAK;KAC9B,CAAC,CACD1F,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEwF,cAAc,EAC9B,MAAM,IAAInB,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAOH,MAAM,CAAClE,IAAI,CAACwF,cAAc;IACnC,CAAC,CAAC,EACF9N,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEAqB,cAAcA,CACZV,SAAiB,EACjBW,eAAyB;IAEzB,OAAO,IAAI,CAACrK,MAAM,CACf4J,MAAM,CAAyB;MAC9BC,QAAQ,EAAE7L,wBAAwB;MAClCqL,SAAS,EAAE;QAAEK,SAAS;QAAEW;MAAe;KACxC,CAAC,CACD5F,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE0F,cAAc,EAC9B,MAAM,IAAIrB,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAOH,MAAM,CAAClE,IAAI,CAAC0F,cAAc,CAACjO,GAAG,CAAEoN,GAAG,KAAM;QAC9C,GAAGA,GAAG;QACNe,SAAS,EAAEf,GAAG,CAACe,SAAS,GACpB,IAAI,CAACC,aAAa,CAAChB,GAAG,CAACe,SAAS,CAAC,GACjC,IAAIL,IAAI;OACb,CAAC,CAAC;IACL,CAAC,CAAC,EACF7N,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEAyB,UAAUA,CAACd,SAAiB,EAAEP,cAAsB;IAClD,OAAO,IAAI,CAACnJ,MAAM,CACf4J,MAAM,CAAqB;MAC1BC,QAAQ,EAAE5L,oBAAoB;MAC9BoL,SAAS,EAAE;QAAEK,SAAS;QAAEP;MAAc;KACvC,CAAC,CACD1E,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE8F,UAAU,EAC1B,MAAM,IAAIzB,KAAK,CAAC,uBAAuB,CAAC;MAC1C,OAAO;QACL,GAAGH,MAAM,CAAClE,IAAI,CAAC8F,UAAU;QACzBC,QAAQ,EAAE,IAAIR,IAAI;OACnB;IACH,CAAC,CAAC,EACF7N,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EAEA2B,cAAcA,CACZlG,KAAa,EACb2E,cAAuB,EACvBwB,OAAA,GAAyB,EAAE;IAE3B,OAAO,IAAI,CAAC3K,MAAM,CACfyI,UAAU,CAAyB;MAClCjE,KAAK,EAAEjH,qBAAqB;MAC5B8L,SAAS,EAAE;QACT7E,KAAK;QACL2E,cAAc;QACd,GAAGwB,OAAO;QACVC,QAAQ,EAAE,IAAI,CAACjI,eAAe,CAACgI,OAAO,CAACC,QAAQ,CAAC;QAChDC,MAAM,EAAE,IAAI,CAAClI,eAAe,CAACgI,OAAO,CAACE,MAAM;OAC5C;MACDnC,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CACAyM,MAAM,IACLA,MAAM,CAAClE,IAAI,EAAEgG,cAAc,EAAEvO,GAAG,CAAEoN,GAAG,KAAM;MACzC,GAAGA,GAAG;MACNe,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACvB,GAAG,CAACe,SAAS,CAAC;MACvCS,MAAM,EAAE,IAAI,CAACC,aAAa,CAACzB,GAAG,CAACwB,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACD3O,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEAkC,iBAAiBA,CAACC,MAAc;IAC9B,OAAO,IAAI,CAAClL,MAAM,CACfyI,UAAU,CAA4B;MACrCjE,KAAK,EAAEhH,yBAAyB;MAChC6L,SAAS,EAAE;QAAE6B;MAAM,CAAE;MACrBxC,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CACAyM,MAAM,IACLA,MAAM,CAAClE,IAAI,EAAEuG,iBAAiB,EAAE9O,GAAG,CAAEoN,GAAG,KAAM;MAC5C,GAAGA,GAAG;MACNe,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACvB,GAAG,CAACe,SAAS,CAAC;MACvCS,MAAM,EAAE,IAAI,CAACC,aAAa,CAACzB,GAAG,CAACwB,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACD3O,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEAoC,qBAAqBA,CAAChC,cAAsB;IAC1C,IAAI,CAAChJ,kBAAkB,CAAC0D,IAAI,CAACsF,cAAc,CAAC;EAC9C;EAEAiC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACpL,MAAM,CACfyI,UAAU,CAA2B;MACpCjE,KAAK,EAAE5H,uBAAuB;MAC9B8L,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMyC,aAAa,GAAGzC,MAAM,CAAClE,IAAI,EAAE0G,gBAAgB,IAAI,EAAE;MACzD,OAAOC,aAAa,CAAClP,GAAG,CAAEmP,IAAI,IAAK,IAAI,CAACC,qBAAqB,CAACD,IAAI,CAAC,CAAC;IACtE,CAAC,CAAC,EACFlP,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEAyC,eAAeA,CACbrC,cAAsB,EACtB1G,KAAc,EACd2G,IAAa;IAEb,IAAI,CAACnJ,MAAM,CAACwL,IAAI,CACd,0CAA0CtC,cAAc,YAAY1G,KAAK,WAAW2G,IAAI,EAAE,CAC3F;IAED,MAAMC,SAAS,GAAQ;MAAEF;IAAc,CAAE;IAEzC;IACA,IAAI1G,KAAK,KAAKI,SAAS,EAAE;MACvBwG,SAAS,CAAC5G,KAAK,GAAGA,KAAK;KACxB,MAAM;MACL4G,SAAS,CAAC5G,KAAK,GAAG,EAAE,CAAC,CAAC;;IAGxB;IACA,IAAI2G,IAAI,KAAKvG,SAAS,EAAE;MACtB;MACA,MAAM6I,MAAM,GAAG,CAACtC,IAAI,GAAG,CAAC,IAAIC,SAAS,CAAC5G,KAAK;MAC3C4G,SAAS,CAACqC,MAAM,GAAGA,MAAM;MACzB,IAAI,CAACzL,MAAM,CAACuI,KAAK,CACf,uCAAuCkD,MAAM,eAAetC,IAAI,eAAeC,SAAS,CAAC5G,KAAK,EAAE,CACjG;KACF,MAAM;MACL4G,SAAS,CAACqC,MAAM,GAAG,CAAC,CAAC,CAAC;;;IAGxB,IAAI,CAACzL,MAAM,CAACuI,KAAK,CACf,uDAAuDa,SAAS,CAAC5G,KAAK,YAAY4G,SAAS,CAACqC,MAAM,EAAE,CACrG;IAED,OAAO,IAAI,CAAC1L,MAAM,CACfyI,UAAU,CAA0B;MACnCjE,KAAK,EAAEzH,sBAAsB;MAC7BsM,SAAS,EAAEA,SAAS;MACpBX,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAAC3I,MAAM,CAACuI,KAAK,CACf,kDAAkD,EAClDI,MAAM,CACP;MAED,MAAM0C,IAAI,GAAG1C,MAAM,CAAClE,IAAI,EAAE8G,eAAe;MACzC,IAAI,CAACF,IAAI,EAAE;QACT,IAAI,CAACrL,MAAM,CAACgE,KAAK,CACf,4CAA4CkF,cAAc,EAAE,CAC7D;QACD,MAAM,IAAIJ,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,IAAI,CAAC9I,MAAM,CAACuI,KAAK,CACf,8CAA8CW,cAAc,EAAE,CAC/D;MACD,MAAMwC,sBAAsB,GAAG,IAAI,CAACJ,qBAAqB,CAACD,IAAI,CAAC;MAE/D,IAAI,CAACrL,MAAM,CAACwL,IAAI,CACd,sDAAsDtC,cAAc,mBAClEwC,sBAAsB,CAACC,YAAY,EAAE9C,MAAM,IAAI,CACjD,eAAe6C,sBAAsB,CAACrC,QAAQ,EAAER,MAAM,IAAI,CAAC,EAAE,CAC9D;MACD,OAAO6C,sBAAsB;IAC/B,CAAC,CAAC,EACFvP,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,+CAA+C,EAC/CA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EAEA8C,kBAAkBA,CAACX,MAAc;IAC/B,IAAI,CAACjL,MAAM,CAACwL,IAAI,CACd,qDAAqDP,MAAM,EAAE,CAC9D;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAACjL,MAAM,CAACgE,KAAK,CACf,kEAAkE,CACnE;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,8CAA8C,CAAC,CAChE;;IAGH,OAAO,IAAI,CAAC/I,MAAM,CACf4J,MAAM,CAAuC;MAC5CC,QAAQ,EAAE/K,4BAA4B;MACtCuK,SAAS,EAAE;QAAE6B;MAAM;KACpB,CAAC,CACDzG,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAAC3I,MAAM,CAACuI,KAAK,CACf,kDAAkD,EAClDI,MAAM,CACP;MAED,MAAMkD,YAAY,GAAGlD,MAAM,CAAClE,IAAI,EAAEmH,kBAAkB;MACpD,IAAI,CAACC,YAAY,EAAE;QACjB,IAAI,CAAC7L,MAAM,CAACgE,KAAK,CACf,6DAA6DiH,MAAM,EAAE,CACtE;QACD,MAAM,IAAInC,KAAK,CAAC,+BAA+B,CAAC;;MAGlD,IAAI;QACF,MAAM4C,sBAAsB,GAC1B,IAAI,CAACJ,qBAAqB,CAACO,YAAY,CAAC;QAC1C,IAAI,CAAC7L,MAAM,CAACwL,IAAI,CACd,uDAAuDE,sBAAsB,CAAChI,EAAE,EAAE,CACnF;QACD,OAAOgI,sBAAsB;OAC9B,CAAC,OAAO1H,KAAK,EAAE;QACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,0DAA0D,EAC1DA,KAAK,CACN;QACD,MAAM,IAAI8E,KAAK,CAAC,uCAAuC,CAAC;;IAE5D,CAAC,CAAC,EACF3M,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,0DAA0DiH,MAAM,GAAG,EACnEjH,KAAK,CACN;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,kCAAkC9E,KAAK,CAAC8H,OAAO,EAAE,CAAC,CACnE;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAC,uBAAuBA,CAACd,MAAc;IACpC,IAAI,CAACjL,MAAM,CAACwL,IAAI,CACd,gEAAgEP,MAAM,EAAE,CACzE;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAACjL,MAAM,CAACgE,KAAK,CACf,sEAAsE,CACvE;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,kDAAkD,CAAC,CACpE;;IAGH;IACA,OAAO,IAAI,CAACqC,gBAAgB,EAAE,CAAC3G,IAAI,CACjCtI,GAAG,CAAEkP,aAAa,IAAI;MACpB;MACA,MAAMY,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;MAE7C;MACA,MAAMC,oBAAoB,GAAGd,aAAa,CAACe,IAAI,CAAEd,IAAI,IAAI;QACvD,IAAIA,IAAI,CAACe,OAAO,EAAE,OAAO,KAAK;QAE9B;QACA,MAAMC,cAAc,GAClBhB,IAAI,CAACM,YAAY,EAAEzP,GAAG,CAAEoQ,CAAC,IAAKA,CAAC,CAAC5I,EAAE,IAAI4I,CAAC,CAACC,GAAG,CAAC,IAAI,EAAE;QACpD,OACEF,cAAc,CAACG,QAAQ,CAACvB,MAAM,CAAC,IAC/BoB,cAAc,CAACG,QAAQ,CAACR,aAAa,CAAC;MAE1C,CAAC,CAAC;MAEF,IAAIE,oBAAoB,EAAE;QACxB,IAAI,CAAClM,MAAM,CAACwL,IAAI,CACd,iDAAiDU,oBAAoB,CAACxI,EAAE,EAAE,CAC3E;QACD,OAAOwI,oBAAoB;;MAG7B;MACA,MAAM,IAAIpD,KAAK,CAAC,gCAAgC,CAAC;IACnD,CAAC,CAAC,EACF3M,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACwL,IAAI,CACd,sEAAsExH,KAAK,CAAC8H,OAAO,EAAE,CACtF;MACD,OAAO,IAAI,CAACF,kBAAkB,CAACX,MAAM,CAAC;IACxC,CAAC,CAAC,CACH;EACH;EAYAwB,gBAAgBA,CACdC,OAAO,GAAG,KAAK,EACfvD,IAAI,GAAG,CAAC,EACR3G,KAAK,GAAG,EAAE;IAEV,IAAI,CAACxC,MAAM,CAACwL,IAAI,CACd,gBAAgB,EAChB,oCAAoCkB,OAAO,WAAWvD,IAAI,YAAY3G,KAAK,EAAE,CAC9E;IACD,IAAI,CAACxC,MAAM,CAACuI,KAAK,CAAC,gBAAgB,EAAE,aAAa,EAAE;MACjDhE,KAAK,EAAE3H;KACR,CAAC;IAEF;IACA;IACA,IAAI8P,OAAO,EAAE;MACX,IAAI,CAAC1M,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,qCAAqC,CACtC;MACD,IAAI,CAAChG,sBAAsB,CAACT,WAAW,GAAG,CAAC;MAC3C,IAAI,CAACS,sBAAsB,CAACE,oBAAoB,GAAG,IAAI;;IAGzD;IACA,IAAI,CAACF,sBAAsB,CAACT,WAAW,GAAGqH,IAAI;IAC9C,IAAI,CAAC5G,sBAAsB,CAACC,KAAK,GAAGA,KAAK;IAEzC;IACA,MAAMmK,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/D,IAAI,CAAC5M,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,SAASoE,sBAAsB,CAACE,IAAI,2CAA2C,CAChF;IAED,OAAO,IAAI,CAAC9M,MAAM,CACfyI,UAAU,CAA+B;MACxCjE,KAAK,EAAE3H,uBAAuB;MAC9BwM,SAAS,EAAE;QACTD,IAAI,EAAEA,IAAI;QACV3G,KAAK,EAAEA;OACR;MACDiG,WAAW,EAAEiE,OAAO,GAAG,cAAc,GAAG;KACzC,CAAC,CACDhE,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAAC3I,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,iCAAiC,CAClC;MAED,IAAII,MAAM,CAACmE,MAAM,EAAE;QACjB,IAAI,CAAC9M,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjB2E,MAAM,CAACmE,MAAM,CACd;QACD,MAAM,IAAIhE,KAAK,CAACH,MAAM,CAACmE,MAAM,CAAC5Q,GAAG,CAAE6Q,CAAC,IAAKA,CAAC,CAACjB,OAAO,CAAC,CAACkB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,MAAM7M,aAAa,GAAGwI,MAAM,CAAClE,IAAI,EAAEwI,oBAAoB,IAAI,EAAE;MAC7D,IAAI,CAACjN,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,YAAYpI,aAAa,CAAC0I,MAAM,uCAAuCM,IAAI,EAAE,CAC9E;MAED;MACA,IAAI,CAAC5G,sBAAsB,CAACE,oBAAoB,GAC9CtC,aAAa,CAAC0I,MAAM,IAAIrG,KAAK;MAE/B,IAAIrC,aAAa,CAAC0I,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAC7I,MAAM,CAACwL,IAAI,CACd,gBAAgB,EAChB,uCAAuC,CACxC;QACD,IAAI,CAACjJ,sBAAsB,CAACE,oBAAoB,GAAG,KAAK;;MAG1D;MACA,MAAMyK,qBAAqB,GAAG/M,aAAa,CAAC9D,MAAM,CAC/C8Q,KAAK,IAAK,CAACR,sBAAsB,CAACS,GAAG,CAACD,KAAK,CAACzJ,EAAE,CAAC,CACjD;MAED,IAAI,CAAC1D,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,gBACEpI,aAAa,CAAC0I,MAAM,GAAGqE,qBAAqB,CAACrE,MAC/C,wBAAwB,CACzB;MAED;MACAqE,qBAAqB,CAAC1J,OAAO,CAAC,CAAC2J,KAAK,EAAEE,KAAK,KAAI;QAC7CrH,OAAO,CAACC,GAAG,CAAC,gBAAgBoH,KAAK,GAAG,CAAC,UAAUlE,IAAI,IAAI,EAAE;UACvDzF,EAAE,EAAEyJ,KAAK,CAACzJ,EAAE,IAAKyJ,KAAa,CAACZ,GAAG;UAClChF,IAAI,EAAE4F,KAAK,CAAC5F,IAAI;UAChB+F,OAAO,EAAEH,KAAK,CAACG,OAAO;UACtBC,MAAM,EAAEJ,KAAK,CAACI;SACf,CAAC;MACJ,CAAC,CAAC;MAEF;MACA;MACA,IAAI,CAACC,WAAW,CAACN,qBAAqB,CAAC;MAEvC;MACA,MAAMO,mBAAmB,GAAG5J,KAAK,CAACtH,IAAI,CACpC,IAAI,CAAC6D,iBAAiB,CAAC0D,MAAM,EAAE,CAChC;MAED;MACA,MAAM4J,mBAAmB,GACvB,IAAI,CAACC,uBAAuB,CAACF,mBAAmB,CAAC;MAEnDzH,OAAO,CAACC,GAAG,CACT,cAAcyH,mBAAmB,CAAC7E,MAAM,kDAAkD,CAC3F;MAED;MACA,IAAI,CAAC1I,aAAa,CAACyD,IAAI,CAAC8J,mBAAmB,CAAC;MAE5C;MACA,IAAI,CAAC3J,iBAAiB,EAAE;MAExB;MACA,IAAI,CAAC6J,+BAA+B,EAAE;MAEtC,OAAOH,mBAAmB;IAC5B,CAAC,CAAC,EACFtR,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MAED,IAAIA,KAAK,CAAC6J,aAAa,EAAE;QACvB,IAAI,CAAC7N,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAAC6J,aAAa,CACpB;;MAGH,IAAI7J,KAAK,CAAC8J,YAAY,EAAE;QACtB,IAAI,CAAC9N,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAAC8J,YAAY,CACnB;;MAGH,OAAO/R,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKQ8D,yBAAyBA,CAAA;IAC/B,IAAI;MACF,MAAMmB,UAAU,GAAG,IAAIC,GAAG,EAAU;MACpC,MAAM9K,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAEhE;MACA,IAAI,CAACF,kBAAkB,EAAE;QACvB,OAAO6K,UAAU;;MAGnB;MACA,MAAME,oBAAoB,GAAG,IAAID,GAAG,CAClC3K,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAC,CAAChH,GAAG,CAAEgS,CAAe,IAAKA,CAAC,CAACxK,EAAE,CAAC,CAC9D;MAED;MACA,MAAMyK,mBAAmB,GACvB,IAAI,CAACpO,MAAM,CAACqO,MAAM,CAACC,SAAS,CAA+B;QACzD9J,KAAK,EAAE3H;OACR,CAAC,EAAEqQ,oBAAoB,IAAI,EAAE;MAEhC;MACAkB,mBAAmB,CAAC3K,OAAO,CAAEC,YAAY,IAAI;QAC3C,IAAI,CAACwK,oBAAoB,CAACb,GAAG,CAAC3J,YAAY,CAACC,EAAE,CAAC,EAAE;UAC9CqK,UAAU,CAACO,GAAG,CAAC7K,YAAY,CAACC,EAAE,CAAC;;MAEnC,CAAC,CAAC;MAEF,OAAOqK,UAAU;KAClB,CAAC,OAAO/J,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,qEAAqE,EACrEA,KAAK,CACN;MACD,OAAO,IAAIgK,GAAG,EAAU;;EAE5B;EAEA;EACAvL,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACF,sBAAsB,CAACE,oBAAoB;EACzD;EAEA;EACA8L,qBAAqBA,CAAA;IACnB,MAAMC,QAAQ,GAAG,IAAI,CAACjM,sBAAsB,CAACT,WAAW,GAAG,CAAC;IAC5D,OAAO,IAAI,CAAC2K,gBAAgB,CAC1B,KAAK,EACL+B,QAAQ,EACR,IAAI,CAACjM,sBAAsB,CAACC,KAAK,CAClC;EACH;EACAiM,mBAAmBA,CAAC/K,EAAU;IAC5B,OAAO,IAAI,CAACxB,cAAc,CAACsC,IAAI,CAC7BtI,GAAG,CAAEiE,aAAa,IAAKA,aAAa,CAACgM,IAAI,CAAE+B,CAAC,IAAKA,CAAC,CAACxK,EAAE,KAAKA,EAAE,CAAC,CAAC,EAC9DvH,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACvD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACH;EACA4F,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACvO,aAAa,CAACwO,KAAK,EAAE9F,MAAM,IAAI,CAAC;EAC9C;EACA+F,0BAA0BA,CAACC,cAAsB;IAC/C,OAAO,IAAI,CAAC9O,MAAM,CACfwE,KAAK,CAAkC;MACtCA,KAAK,EAAE7F,8BAA8B;MACrC0K,SAAS,EAAE;QAAE1F,EAAE,EAAEmL;MAAc,CAAE;MACjCpG,WAAW,EAAE;KACd,CAAC,CACDjE,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAKA,MAAM,CAAClE,IAAI,EAAEmK,0BAA0B,IAAI,EAAE,CAAC,EAC9DzS,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MACpE,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EACAgG,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAC5M,cAAc,CAACsC,IAAI,CAC7BtI,GAAG,CAAEiE,aAAa,IAAKA,aAAa,CAAC9D,MAAM,CAAE6R,CAAC,IAAK,CAACA,CAAC,CAACX,MAAM,CAAC,CAAC,CAC/D;EACH;EAEA;;;;;EAKAwB,kBAAkBA,CAChBF,cAAsB;IAEtB,IAAI,CAAC7O,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,kCAAkCsG,cAAc,EAAE,CACnD;IAED,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAAC7O,MAAM,CAACgP,IAAI,CAAC,gBAAgB,EAAE,6BAA6B,CAAC;MACjE,OAAOjT,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,MAAMmG,YAAY,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAACL,cAAc,CAAC,CAAC;IAExE;IACA,OAAO,IAAI,CAAC9O,MAAM,CACf4J,MAAM,CAAgE;MACrEC,QAAQ,EAAE9K,4BAA4B;MACtCsK,SAAS,EAAE;QAAEyF;MAAc;KAC5B,CAAC,CACDrK,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMwG,QAAQ,GAAGxG,MAAM,CAAClE,IAAI,EAAEsK,kBAAkB;MAChD,IAAI,CAACI,QAAQ,EAAE;QACb,MAAM,IAAIrG,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC9I,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7B4G,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFhT,UAAU,CAAE6H,KAAK,IACf,IAAI,CAACoL,mBAAmB,CAACpL,KAAK,EAAE,mCAAmC,EAAE;MACnEqL,OAAO,EAAE,IAAI;MACbvD,OAAO,EAAE;KACV,CAAC,CACH,CACF;EACL;EAEA;;;;EAIQ8B,+BAA+BA,CAAA;IACrC,IAAI;MACF,MAAMzN,aAAa,GAAG0D,KAAK,CAACtH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC0D,MAAM,EAAE,CAAC;MACjEX,YAAY,CAACmM,OAAO,CAAC,eAAe,EAAEjM,IAAI,CAACkM,SAAS,CAACpP,aAAa,CAAC,CAAC;MACpE,IAAI,CAACH,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,uCAAuC,CACxC;KACF,CAAC,OAAOvE,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iDAAiD,EACjDA,KAAK,CACN;;EAEL;EAEA;;;;EAIAwL,sBAAsBA,CAAA;IAKpB,IAAI,CAACxP,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,yCAAyC,CAC1C;IAED;IACA,MAAMkH,KAAK,GAAG,IAAI,CAACrP,iBAAiB,CAACyM,IAAI;IACzC,MAAM6C,kBAAkB,GAAG7L,KAAK,CAACtH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAACwF,IAAI,EAAE,CAAC;IACpE,IAAI,CAACsJ,4BAA4B,CAACQ,kBAAkB,CAAC;IAErD;IACA,OAAO,IAAI,CAAC3P,MAAM,CACf4J,MAAM,CAMJ;MACDC,QAAQ,EAAE5K;KACX,CAAC,CACDwF,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMwG,QAAQ,GAAGxG,MAAM,CAAClE,IAAI,EAAE+K,sBAAsB;MACpD,IAAI,CAACL,QAAQ,EAAE;QACb,MAAM,IAAIrG,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC9I,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,yDAAyD,EACzD4G,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFhT,UAAU,CAAE6H,KAAK,IACf,IAAI,CAACoL,mBAAmB,CACtBpL,KAAK,EACL,4CAA4C,EAC5C;MACEqL,OAAO,EAAE,IAAI;MACbI,KAAK;MACL3D,OAAO,EAAE,GAAG2D,KAAK;KAClB,CACF,CACF,CACF;EACL;EAEA;;;;;EAKAE,2BAA2BA,CACzBC,eAAyB;IAEzB,IAAI,CAAC5P,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,kBAAkBqH,eAAe,CAAC/G,MAAM,gBAAgB,CACzD;IAED,IAAI,CAAC+G,eAAe,IAAIA,eAAe,CAAC/G,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAAC7I,MAAM,CAACgP,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAOjT,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,iCAAiC,CAAC,CAAC;;IAGvE;IACA,MAAM2G,KAAK,GAAG,IAAI,CAACP,4BAA4B,CAACU,eAAe,CAAC;IAEhE;IACA,OAAO,IAAI,CAAC7P,MAAM,CACf4J,MAAM,CAMJ;MACDC,QAAQ,EAAE7K,sCAAsC;MAChDqK,SAAS,EAAE;QAAEwG;MAAe;KAC7B,CAAC,CACDpL,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMwG,QAAQ,GAAGxG,MAAM,CAAClE,IAAI,EAAEkL,2BAA2B;MACzD,IAAI,CAACR,QAAQ,EAAE;QACb,MAAM,IAAIrG,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC9I,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtC4G,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFhT,UAAU,CAAE6H,KAAK,IACf,IAAI,CAACoL,mBAAmB,CACtBpL,KAAK,EACL,0CAA0C,EAC1C;MACEqL,OAAO,EAAEI,KAAK,GAAG,CAAC;MAClBA,KAAK;MACL3D,OAAO,EAAE,GAAG2D,KAAK;KAClB,CACF,CACF,CACF;EACL;EACAI,wBAAwBA,CAAA;IAGtB,OAAO,IAAI,CAAC3N,cAAc,CAACsC,IAAI,CAC7BtI,GAAG,CAAEiE,aAAa,IAAI;MACpB,MAAM2P,MAAM,GAAG,IAAIzP,GAAG,EAAoC;MAC1DF,aAAa,CAACqD,OAAO,CAAE2J,KAAK,IAAI;QAC9B,IAAI,CAAC2C,MAAM,CAAC1C,GAAG,CAACD,KAAK,CAAC5F,IAAI,CAAC,EAAE;UAC3BuI,MAAM,CAACnM,GAAG,CAACwJ,KAAK,CAAC5F,IAAI,EAAE,EAAE,CAAC;;QAE5BuI,MAAM,CAACC,GAAG,CAAC5C,KAAK,CAAC5F,IAAI,CAAC,EAAEyI,IAAI,CAAC7C,KAAK,CAAC;MACrC,CAAC,CAAC;MACF,OAAO2C,MAAM;IACf,CAAC,CAAC,CACH;EACH;EACAG,UAAUA,CAACL,eAAyB;IAKlC,IAAI,CAAC5P,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,kCAAkCqH,eAAe,EAAE5C,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAC1E;IAED,IAAI,CAAC4C,eAAe,IAAIA,eAAe,CAAC/G,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAAC7I,MAAM,CAACgP,IAAI,CAAC,gBAAgB,EAAE,8BAA8B,CAAC;MAClE,OAAOlT,EAAE,CAAC;QACRuT,OAAO,EAAE,KAAK;QACda,SAAS,EAAE,CAAC;QACZC,cAAc,EAAE,IAAI,CAAC7P,iBAAiB,CAACqO;OACxC,CAAC;;IAGJ;IACA,MAAMyB,QAAQ,GAAGR,eAAe,CAACvT,MAAM,CACpCqH,EAAE,IAAKA,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAAC2M,IAAI,EAAE,KAAK,EAAE,CACzD;IAED,IAAID,QAAQ,CAACvH,MAAM,KAAK+G,eAAe,CAAC/G,MAAM,EAAE;MAC9C,IAAI,CAAC7I,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,EAAE;QACvEsM,QAAQ,EAAEV,eAAe;QACzBW,KAAK,EAAEH;OACR,CAAC;MACF,OAAOrU,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,mCAAmC,CAAC,CAAC;;IAGzE,IAAI,CAAC9I,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,gDAAgD,EAChD6H,QAAQ,CACT;IAED;IACA,IAAI,CAACI,wBAAwB,CAACJ,QAAQ,EAAE,IAAI,CAAC;IAE7C;IACA,MAAMK,kBAAkB,GAAG;MACzBC,uBAAuB,EAAE;QACvBrB,OAAO,EAAE,IAAI;QACba,SAAS,EAAEE,QAAQ,CAACvH,MAAM;QAC1BsH,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAACtQ,iBAAiB,CAACqO,KAAK,GAAGyB,QAAQ,CAACvH,MAAM;;KAGnD;IAED;IACA7C,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE;MACtE2J,eAAe,EAAEQ;KAClB,CAAC;IACFpK,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEtH,+BAA+B,CAAC;IAE/D,OAAO,IAAI,CAACoB,MAAM,CACf4J,MAAM,CAAkC;MACvCC,QAAQ,EAAEjL,+BAA+B;MACzCyK,SAAS,EAAE;QAAEwG,eAAe,EAAEQ;MAAQ,CAAE;MACxCK,kBAAkB,EAAEA,kBAAkB;MACtCI,WAAW,EAAE,KAAK;MAClBpI,WAAW,EAAE,UAAU,CAAE;KAC1B,CAAC,CACDjE,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAAC3I,MAAM,CAACuI,KAAK,CAAC,gBAAgB,EAAE,iBAAiB,EAAEI,MAAM,CAAC;MAC9D3C,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE0C,MAAM,CAAC;MAEvC;MACA,IAAIA,MAAM,CAACmE,MAAM,EAAE;QACjB,IAAI,CAAC9M,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjB2E,MAAM,CAACmE,MAAM,CACd;QACD9G,OAAO,CAAChC,KAAK,CAAC,iBAAiB,EAAE2E,MAAM,CAACmE,MAAM,CAAC;;MAGjD;MACA,MAAMqC,QAAQ,GACZxG,MAAM,CAAClE,IAAI,EAAEiM,uBAAuB,IACpCD,kBAAkB,CAACC,uBAAuB;MAE5C,OAAOvB,QAAQ;IACjB,CAAC,CAAC,EACFhT,UAAU,CAAE6H,KAAY,IAAI;MAC1B,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACDgC,OAAO,CAAChC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAE5C;MACA;MACA,OAAOlI,EAAE,CAAC;QACRuT,OAAO,EAAE,IAAI;QACba,SAAS,EAAEE,QAAQ,CAACvH,MAAM;QAC1BsH,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAACtQ,iBAAiB,CAACqO,KAAK,GAAGyB,QAAQ,CAACvH,MAAM;OAEjD,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EACA;EACA;EACA;EAEA;;;;;;;;EAQAiI,YAAYA,CACVC,WAAmB,EACnBC,QAAkB,EAClB9H,cAAuB,EACvB+H,OAAqB;IAErB,OAAO,IAAI,CAACC,iBAAiB,CAACF,QAAQ,CAAC,CAACxM,IAAI,CAC1ClI,SAAS,CAAE6U,MAAM,IAAI;MACnB,IAAI,CAACrQ,WAAW,GAAGqQ,MAAM;MACzB,IAAI,CAAC9P,YAAY,CAACuC,IAAI,CAACuN,MAAM,CAAC;MAE9B;MACA,IAAI,CAACnQ,cAAc,GAAG,IAAIoQ,iBAAiB,CAAC,IAAI,CAAC7P,SAAS,CAAC;MAE3D;MACA4P,MAAM,CAACE,SAAS,EAAE,CAAC7N,OAAO,CAAE8N,KAAK,IAAI;QACnC,IAAI,CAACtQ,cAAe,CAACuQ,QAAQ,CAACD,KAAK,EAAEH,MAAM,CAAC;MAC9C,CAAC,CAAC;MAEF;MACA,IAAI,CAACnQ,cAAc,CAACwQ,cAAc,GAAIC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACC,SAAS,EAAE;UACnB,IAAI,CAACC,cAAc,CACjB,IAAI,CAACC,cAAc,EAAE,EACrB,eAAe,EACfvO,IAAI,CAACkM,SAAS,CAACkC,KAAK,CAACC,SAAS,CAAC,CAChC;;MAEL,CAAC;MAED;MACA,IAAI,CAAC1Q,cAAc,CAAC6Q,OAAO,GAAIJ,KAAK,IAAI;QACtC,IAAI,CAAC,IAAI,CAAC1Q,YAAY,EAAE;UACtB,IAAI,CAACA,YAAY,GAAG,IAAI+Q,WAAW,EAAE;UACrC,IAAI,CAACxQ,aAAa,CAACsC,IAAI,CAAC,IAAI,CAAC7C,YAAY,CAAC;;QAE5C0Q,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACV,SAAS,EAAE,CAAC7N,OAAO,CAAE8N,KAAK,IAAI;UAC7C,IAAI,CAACvQ,YAAa,CAACwQ,QAAQ,CAACD,KAAK,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC;MAED;MACA,OAAO/U,IAAI,CAAC,IAAI,CAACyE,cAAc,CAACgR,WAAW,EAAE,CAAC,CAACxN,IAAI,CACjDlI,SAAS,CAAE2V,KAAK,IAAI;QAClB,OAAO1V,IAAI,CAAC,IAAI,CAACyE,cAAe,CAACkR,mBAAmB,CAACD,KAAK,CAAC,CAAC,CAACzN,IAAI,CAC/DtI,GAAG,CAAC,MAAM+V,KAAK,CAAC,CACjB;MACH,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACF3V,SAAS,CAAE2V,KAAK,IAAI;MAClB;MACA,MAAME,MAAM,GAAG,IAAI,CAACP,cAAc,EAAE;MAEpC;MACA,OAAO,IAAI,CAAC7R,MAAM,CACf4J,MAAM,CAAyB;QAC9BC,QAAQ,EAAExK,sBAAsB;QAChCgK,SAAS,EAAE;UACT2H,WAAW;UACXC,QAAQ;UACRmB,MAAM;UACNF,KAAK,EAAE5O,IAAI,CAACkM,SAAS,CAAC0C,KAAK,CAAC;UAC5B/I,cAAc;UACd+H;;OAEH,CAAC,CACDzM,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;QACb,MAAMhE,IAAI,GAAGgE,MAAM,CAAClE,IAAI,EAAEqM,YAAY;QACtC,IAAI,CAACnM,IAAI,EAAE;UACT,MAAM,IAAImE,KAAK,CAAC,yBAAyB,CAAC;;QAG5C;QACA,IAAI,CAACnI,UAAU,CAACiD,IAAI,CAACe,IAAI,CAAC;QAE1B;QACA,MAAMyN,SAAS,GAAG,IAAI,CAACC,sBAAsB,CAC3C1N,IAAI,CAACjB,EAAE,CACR,CAACS,SAAS,EAAE;QACb,IAAI,CAAC3D,aAAa,CAACwP,IAAI,CAACoC,SAAS,CAAC;QAElC,OAAOzN,IAAI;MACb,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACFxI,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,IAAI,CAACsO,WAAW,EAAE;MAClB,OAAOvW,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/D,CAAC,CAAC,CACH;EACH;EAEA;;;;;EAKAyJ,UAAUA,CAAC3R,YAA0B;IACnC,IAAI,CAAC4E,IAAI,CAAC,UAAU,CAAC;IAErB,OAAO,IAAI,CAAC0L,iBAAiB,CAACtQ,YAAY,CAAC2G,IAAI,CAAC,CAAC/C,IAAI,CACnDlI,SAAS,CAAE6U,MAAM,IAAI;MACnB,IAAI,CAACrQ,WAAW,GAAGqQ,MAAM;MACzB,IAAI,CAAC9P,YAAY,CAACuC,IAAI,CAACuN,MAAM,CAAC;MAE9B;MACA,IAAI,CAACnQ,cAAc,GAAG,IAAIoQ,iBAAiB,CAAC,IAAI,CAAC7P,SAAS,CAAC;MAE3D;MACA4P,MAAM,CAACE,SAAS,EAAE,CAAC7N,OAAO,CAAE8N,KAAK,IAAI;QACnC,IAAI,CAACtQ,cAAe,CAACuQ,QAAQ,CAACD,KAAK,EAAEH,MAAM,CAAC;MAC9C,CAAC,CAAC;MAEF;MACA,IAAI,CAACnQ,cAAc,CAACwQ,cAAc,GAAIC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACC,SAAS,EAAE;UACnB,IAAI,CAACC,cAAc,CACjB/Q,YAAY,CAAC8C,EAAE,EACf,eAAe,EACfL,IAAI,CAACkM,SAAS,CAACkC,KAAK,CAACC,SAAS,CAAC,CAChC;;MAEL,CAAC;MAED;MACA,IAAI,CAAC1Q,cAAc,CAAC6Q,OAAO,GAAIJ,KAAK,IAAI;QACtC,IAAI,CAAC,IAAI,CAAC1Q,YAAY,EAAE;UACtB,IAAI,CAACA,YAAY,GAAG,IAAI+Q,WAAW,EAAE;UACrC,IAAI,CAACxQ,aAAa,CAACsC,IAAI,CAAC,IAAI,CAAC7C,YAAY,CAAC;;QAE5C0Q,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACV,SAAS,EAAE,CAAC7N,OAAO,CAAE8N,KAAK,IAAI;UAC7C,IAAI,CAACvQ,YAAa,CAACwQ,QAAQ,CAACD,KAAK,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC;MAED;MACA,MAAMW,KAAK,GAAG5O,IAAI,CAACC,KAAK,CAAC1C,YAAY,CAACqR,KAAK,CAAC;MAC5C,OAAO1V,IAAI,CACT,IAAI,CAACyE,cAAc,CAACwR,oBAAoB,CACtC,IAAIC,qBAAqB,CAACR,KAAK,CAAC,CACjC,CACF,CAACzN,IAAI,CACJlI,SAAS,CAAC,MAAMC,IAAI,CAAC,IAAI,CAACyE,cAAe,CAAC0R,YAAY,EAAE,CAAC,CAAC,EAC1DpW,SAAS,CAAEqW,MAAM,IAAI;QACnB,OAAOpW,IAAI,CAAC,IAAI,CAACyE,cAAe,CAACkR,mBAAmB,CAACS,MAAM,CAAC,CAAC,CAACnO,IAAI,CAChEtI,GAAG,CAAC,MAAMyW,MAAM,CAAC,CAClB;MACH,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACFrW,SAAS,CAAEqW,MAAM,IAAI;MACnB;MACA,OAAO,IAAI,CAAC5S,MAAM,CACf4J,MAAM,CAAuB;QAC5BC,QAAQ,EAAEtK,oBAAoB;QAC9B8J,SAAS,EAAE;UACT+I,MAAM,EAAEvR,YAAY,CAAC8C,EAAE;UACvBiP,MAAM,EAAEtP,IAAI,CAACkM,SAAS,CAACoD,MAAM;;OAEhC,CAAC,CACDnO,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;QACb,MAAMhE,IAAI,GAAGgE,MAAM,CAAClE,IAAI,EAAE8N,UAAU;QACpC,IAAI,CAAC5N,IAAI,EAAE;UACT,MAAM,IAAImE,KAAK,CAAC,uBAAuB,CAAC;;QAG1C;QACA,IAAI,CAAClE,IAAI,CAAC,gBAAgB,CAAC;QAE3B;QACA,IAAI,CAACjE,UAAU,CAACiD,IAAI,CAAC;UACnB,GAAGe,IAAI;UACPiO,MAAM,EAAEhS,YAAY,CAACgS,MAAM;UAC3BrL,IAAI,EAAE3G,YAAY,CAAC2G,IAAI;UACvB2B,cAAc,EAAEtI,YAAY,CAACsI;SAC9B,CAAC;QAEF;QACA,MAAMkJ,SAAS,GAAG,IAAI,CAACC,sBAAsB,CAC3CzR,YAAY,CAAC8C,EAAE,CAChB,CAACS,SAAS,EAAE;QACb,IAAI,CAAC3D,aAAa,CAACwP,IAAI,CAACoC,SAAS,CAAC;QAElC;QACA,IAAI,CAACxR,YAAY,CAACgD,IAAI,CAAC,IAAI,CAAC;QAE5B,OAAOe,IAAI;MACb,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACFxI,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD,IAAI,CAACsO,WAAW,EAAE;MAClB,OAAOvW,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACH;EAEA;;;;;;EAMA+J,UAAUA,CAACV,MAAc,EAAEW,MAAe;IACxC,IAAI,CAACtN,IAAI,CAAC,UAAU,CAAC;IAErB,OAAO,IAAI,CAACzF,MAAM,CACf4J,MAAM,CAAuB;MAC5BC,QAAQ,EAAErK,oBAAoB;MAC9B6J,SAAS,EAAE;QACT+I,MAAM;QACNW;;KAEH,CAAC,CACDtO,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMhE,IAAI,GAAGgE,MAAM,CAAClE,IAAI,EAAEoO,UAAU;MACpC,IAAI,CAAClO,IAAI,EAAE;QACT,MAAM,IAAImE,KAAK,CAAC,uBAAuB,CAAC;;MAG1C;MACA,IAAI,CAAClI,YAAY,CAACgD,IAAI,CAAC,IAAI,CAAC;MAE5B,OAAOe,IAAI;IACb,CAAC,CAAC,EACFxI,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EAEA;;;;;;EAMAiK,OAAOA,CAACZ,MAAc,EAAEa,QAAuB;IAC7C,IAAI,CAACxN,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACZ,IAAI,CAAC,UAAU,CAAC;IAErB,OAAO,IAAI,CAAC7E,MAAM,CACf4J,MAAM,CAAoB;MACzBC,QAAQ,EAAEpK,iBAAiB;MAC3B4J,SAAS,EAAE;QACT+I,MAAM;QACNa;;KAEH,CAAC,CACDxO,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMhE,IAAI,GAAGgE,MAAM,CAAClE,IAAI,EAAEsO,OAAO;MACjC,IAAI,CAACpO,IAAI,EAAE;QACT,MAAM,IAAImE,KAAK,CAAC,oBAAoB,CAAC;;MAGvC;MACA,IAAI,CAACwJ,WAAW,EAAE;MAElB;MACA,IAAI,CAAC3R,UAAU,CAACiD,IAAI,CAAC,IAAI,CAAC;MAE1B,OAAOe,IAAI;IACb,CAAC,CAAC,EACFxI,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAACsO,WAAW,EAAE;MAClB,OAAOvW,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAC1D,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOAmK,WAAWA,CACTd,MAAc,EACde,KAAe,EACflO,KAAe;IAEf,IAAI,IAAI,CAAClE,WAAW,EAAE;MACpB;MACA,IAAIoS,KAAK,KAAKtQ,SAAS,EAAE;QACvB,IAAI,CAAC9B,WAAW,CAACqS,cAAc,EAAE,CAAC3P,OAAO,CAAE8N,KAAK,IAAI;UAClDA,KAAK,CAAC8B,OAAO,GAAGF,KAAK;QACvB,CAAC,CAAC;;MAGJ,IAAIlO,KAAK,KAAKpC,SAAS,EAAE;QACvB,IAAI,CAAC9B,WAAW,CAACuS,cAAc,EAAE,CAAC7P,OAAO,CAAE8N,KAAK,IAAI;UAClDA,KAAK,CAAC8B,OAAO,GAAGpO,KAAK;QACvB,CAAC,CAAC;;;IAIN,OAAO,IAAI,CAACjF,MAAM,CACf4J,MAAM,CAAmC;MACxCC,QAAQ,EAAEnK,0BAA0B;MACpC2J,SAAS,EAAE;QACT+I,MAAM;QACNe,KAAK;QACLlO;;KAEH,CAAC,CACDR,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAM0G,OAAO,GAAG1G,MAAM,CAAClE,IAAI,EAAE6O,eAAe;MAC5C,IAAI,CAACjE,OAAO,EAAE;QACZ,MAAM,IAAIvG,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAOuG,OAAO;IAChB,CAAC,CAAC,EACFlT,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAuJ,sBAAsBA,CAACF,MAAc;IACnC,OAAO,IAAI,CAACpS,MAAM,CACfoE,SAAS,CAA6B;MACrCI,KAAK,EAAE7E,wBAAwB;MAC/B0J,SAAS,EAAE;QAAE+I;MAAM;KACpB,CAAC,CACD3N,IAAI,CACHtI,GAAG,CAAC,CAAC;MAAEuI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAE8O,UAAU,EAAE;QACrB,MAAM,IAAIzK,KAAK,CAAC,yBAAyB,CAAC;;MAE5C,OAAOrE,IAAI,CAAC8O,UAAU;IACxB,CAAC,CAAC,EACFnX,GAAG,CAAEoX,MAAM,IAAI;MACb,IAAI,CAAC3S,WAAW,CAAC+C,IAAI,CAAC4P,MAAM,CAAC;MAC7B,IAAI,CAACC,gBAAgB,CAACD,MAAM,CAAC;IAC/B,CAAC,CAAC,EACFrX,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC7D,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOA6I,cAAcA,CACZQ,MAAc,EACduB,UAAkB,EAClBC,UAAkB;IAElB,OAAO,IAAI,CAAC5T,MAAM,CACf4J,MAAM,CAAkC;MACvCC,QAAQ,EAAEvK,yBAAyB;MACnC+J,SAAS,EAAE;QACT+I,MAAM;QACNuB,UAAU;QACVC;;KAEH,CAAC,CACDnP,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAM0G,OAAO,GAAG1G,MAAM,CAAClE,IAAI,EAAEkN,cAAc;MAC3C,IAAI,CAACtC,OAAO,EAAE;QACZ,MAAM,IAAIvG,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAOuG,OAAO;IAChB,CAAC,CAAC,EACFlT,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACrD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;;;;EAUA8K,cAAcA,CACZpR,KAAA,GAAgB,EAAE,EAClBiJ,MAAA,GAAiB,CAAC,EAClBoI,MAAiB,EACjBtM,IAAe,EACfuM,SAAyB,EACzBC,OAAuB;IAEvB,OAAO,IAAI,CAAChU,MAAM,CACfyI,UAAU,CAA0B;MACnCjE,KAAK,EAAEtF,kBAAkB;MACzBmK,SAAS,EAAE;QACT5G,KAAK;QACLiJ,MAAM;QACNoI,MAAM;QACNtM,IAAI;QACJuM,SAAS;QACTC;OACD;MACDtL,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMqL,OAAO,GAAGrL,MAAM,CAAClE,IAAI,EAAEwP,WAAW,IAAI,EAAE;MAC9C,IAAI,CAACjU,MAAM,CAACuI,KAAK,CAAC,aAAayL,OAAO,CAACnL,MAAM,qBAAqB,CAAC;MACnE,OAAOmL,OAAO;IAChB,CAAC,CAAC,EACF7X,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACxD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAoL,cAAcA,CAAC/B,MAAc;IAC3B,OAAO,IAAI,CAACpS,MAAM,CACfyI,UAAU,CAAwB;MACjCjE,KAAK,EAAErF,kBAAkB;MACzBkK,SAAS,EAAE;QAAE+I;MAAM,CAAE;MACrB1J,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMwL,OAAO,GAAGxL,MAAM,CAAClE,IAAI,EAAE2P,WAAW;MACxC,IAAI,CAACD,OAAO,EAAE;QACZ,MAAM,IAAIrL,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,IAAI,CAAC9I,MAAM,CAACuI,KAAK,CAAC,+BAA+B4J,MAAM,EAAE,CAAC;MAC1D,OAAOgC,OAAO;IAChB,CAAC,CAAC,EACFhY,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACxD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIAuL,YAAYA,CAAA;IACV,OAAO,IAAI,CAACtU,MAAM,CACfyI,UAAU,CAAqB;MAC9BjE,KAAK,EAAEpF,gBAAgB;MACvBsJ,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAM2L,KAAK,GAAG3L,MAAM,CAAClE,IAAI,EAAE8P,SAAS;MACpC,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAIxL,KAAK,CAAC,sBAAsB,CAAC;;MAEzC,IAAI,CAAC9I,MAAM,CAACuI,KAAK,CAAC,uBAAuB,EAAE+L,KAAK,CAAC;MACjD,OAAOA,KAAK;IACd,CAAC,CAAC,EACFnY,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACtD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIQ2K,gBAAgBA,CAACD,MAAkB;IACzC,QAAQA,MAAM,CAACjM,IAAI;MACjB,KAAK,eAAe;QAClB,IAAI,CAACiN,kBAAkB,CAAChB,MAAM,CAAC;QAC/B;MACF,KAAK,QAAQ;QACX,IAAI,CAACiB,YAAY,CAACjB,MAAM,CAAC;QACzB;MACF,KAAK,UAAU;QACb,IAAI,CAACkB,aAAa,CAAClB,MAAM,CAAC;QAC1B;MACF,KAAK,QAAQ;QACX,IAAI,CAACmB,gBAAgB,CAACnB,MAAM,CAAC;QAC7B;MACF;QACE,IAAI,CAACxT,MAAM,CAACuI,KAAK,CAAC,0BAA0BiL,MAAM,CAACjM,IAAI,EAAE,EAAEiM,MAAM,CAAC;;EAExE;EAEA;;;;EAIQgB,kBAAkBA,CAAChB,MAAkB;IAC3C,IAAI,CAAC,IAAI,CAACxS,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACgE,KAAK,CAAC,gDAAgD,CAAC;MACnE;;IAGF,IAAI;MACF,MAAM0N,SAAS,GAAGrO,IAAI,CAACC,KAAK,CAACkQ,MAAM,CAAC/O,IAAI,CAAC;MACzC,IAAI,CAACzD,cAAc,CAChB4T,eAAe,CAAC,IAAIC,eAAe,CAACnD,SAAS,CAAC,CAAC,CAC/CnM,KAAK,CAAEvB,KAAK,IAAI;QACf,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MACjE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,6BAA6B,EAAEA,KAAc,CAAC;;EAEpE;EAEA;;;;EAIQyQ,YAAYA,CAACjB,MAAkB;IACrC,IAAI,CAAC,IAAI,CAACxS,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACgE,KAAK,CAAC,yCAAyC,CAAC;MAC5D;;IAGF,IAAI;MACF,MAAM2O,MAAM,GAAGtP,IAAI,CAACC,KAAK,CAACkQ,MAAM,CAAC/O,IAAI,CAAC;MACtC,IAAI,CAACzD,cAAc,CAChBwR,oBAAoB,CAAC,IAAIC,qBAAqB,CAACE,MAAM,CAAC,CAAC,CACvDpN,KAAK,CAAEvB,KAAK,IAAI;QACf,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,kCAAkC,EAAEA,KAAc,CAAC;MACvE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,sBAAsB,EAAEA,KAAc,CAAC;;EAE7D;EAEA;;;;EAIQ0Q,aAAaA,CAAClB,MAAkB;IACtC,IAAI,CAAChO,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAAC8M,WAAW,EAAE;IAElB;IACA,MAAMwC,WAAW,GAAG,IAAI,CAACnU,UAAU,CAACgO,KAAK;IACzC,IAAImG,WAAW,IAAIA,WAAW,CAACpR,EAAE,KAAK8P,MAAM,CAACrB,MAAM,EAAE;MACnD,IAAI,CAACxR,UAAU,CAACiD,IAAI,CAAC;QACnB,GAAGkR,WAAW;QACdjB,MAAM,EAAEnX,UAAU,CAACqY,KAAK;QACxBC,OAAO,EAAE,IAAIhL,IAAI,EAAE,CAACnH,WAAW;OAChC,CAAC;;EAEN;EAEA;;;;EAIQ8R,gBAAgBA,CAACnB,MAAkB;IACzC,IAAI,CAAChO,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAAC8M,WAAW,EAAE;IAElB;IACA,MAAMwC,WAAW,GAAG,IAAI,CAACnU,UAAU,CAACgO,KAAK;IACzC,IAAImG,WAAW,IAAIA,WAAW,CAACpR,EAAE,KAAK8P,MAAM,CAACrB,MAAM,EAAE;MACnD,IAAI,CAACxR,UAAU,CAACiD,IAAI,CAAC;QACnB,GAAGkR,WAAW;QACdjB,MAAM,EAAEnX,UAAU,CAACuY,QAAQ;QAC3BD,OAAO,EAAE,IAAIhL,IAAI,EAAE,CAACnH,WAAW;OAChC,CAAC;;EAEN;EAEA;;;EAGQyP,WAAWA,CAAA;IACjB,IAAI,IAAI,CAACxR,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACuQ,SAAS,EAAE,CAAC7N,OAAO,CAAE8N,KAAK,IAAKA,KAAK,CAAC9L,IAAI,EAAE,CAAC;MAC7D,IAAI,CAAC1E,WAAW,GAAG,IAAI;MACvB,IAAI,CAACO,YAAY,CAACuC,IAAI,CAAC,IAAI,CAAC;;IAG9B,IAAI,IAAI,CAAC5C,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACkU,KAAK,EAAE;MAC3B,IAAI,CAAClU,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACO,aAAa,CAACsC,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEA;;;;;EAKQsN,iBAAiBA,CAACF,QAAkB;IAC1C,MAAMmE,WAAW,GAA2B;MAC1CnQ,KAAK,EAAE,IAAI;MACXkO,KAAK,EACHlC,QAAQ,KAAKvU,QAAQ,CAAC2Y,KAAK,GACvB;QACEC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QACtBC,MAAM,EAAE;UAAED,KAAK,EAAE;QAAG;OACrB,GACD;KACP;IAED,OAAO,IAAIzZ,UAAU,CAAe2Z,QAAQ,IAAI;MAC9CC,SAAS,CAACC,YAAY,CACnBC,YAAY,CAACR,WAAW,CAAC,CACzBS,IAAI,CAAEzE,MAAM,IAAI;QACfqE,QAAQ,CAAC5R,IAAI,CAACuN,MAAM,CAAC;QACrBqE,QAAQ,CAACK,QAAQ,EAAE;MACrB,CAAC,CAAC,CACDtQ,KAAK,CAAEvB,KAAK,IAAI;QACf,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACzDwR,QAAQ,CAACxR,KAAK,CAAC,IAAI8E,KAAK,CAAC,gCAAgC,CAAC,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEA;;;;EAIQ8I,cAAcA,CAAA;IACpB,OAAO5H,IAAI,CAAC8L,GAAG,EAAE,CAACC,QAAQ,EAAE,GAAGpF,IAAI,CAACqF,MAAM,EAAE,CAACD,QAAQ,CAAC,EAAE,CAAC,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E;EAEA;EACA;EACA;EACA;EACAC,WAAWA,CACTC,YAAY,GAAG,KAAK,EACpBC,MAAe,EACfjN,IAAA,GAAe,CAAC,EAChB3G,KAAA,GAAgB,EAAE,EAClB6T,MAAA,GAAiB,UAAU,EAC3BC,SAAA,GAAoB,KAAK,EACzBC,QAAkB;IAElB,IAAI,CAACvW,MAAM,CAACwL,IAAI,CACd,gBAAgB,EAChB,2CAA2C2K,YAAY,YACrDC,MAAM,IAAI,SACZ,UAAUjN,IAAI,WAAW3G,KAAK,YAAY6T,MAAM,eAAeC,SAAS,cAAcC,QAAQ,EAAE,CACjG;IAED,MAAMT,GAAG,GAAG9L,IAAI,CAAC8L,GAAG,EAAE;IACtB,MAAMU,UAAU,GACd,CAACL,YAAY,IACb,IAAI,CAACzU,UAAU,CAACmH,MAAM,GAAG,CAAC,IAC1BiN,GAAG,GAAG,IAAI,CAACpV,aAAa,IAAI,IAAI,CAACD,cAAc,IAC/C,CAAC2V,MAAM,IACPjN,IAAI,KAAK,CAAC,IACV3G,KAAK,IAAI,IAAI,CAACd,UAAU,CAACmH,MAAM;IAEjC;IACA,IAAI2N,UAAU,EAAE;MACd,IAAI,CAACxW,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,uBAAuB,IAAI,CAAC7G,UAAU,CAACmH,MAAM,SAAS,CACvD;MACD,OAAO/M,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC4F,UAAU,CAAC,CAAC;;IAGjC,IAAI,CAAC1B,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,2DACE4N,YAAY,GAAG,cAAc,GAAG,aAClC,EAAE,CACH;IAED,OAAO,IAAI,CAACpW,MAAM,CACfyI,UAAU,CAAM;MACfjE,KAAK,EAAEnH,kBAAkB;MACzBgM,SAAS,EAAE;QACTgN,MAAM;QACNjN,IAAI;QACJ3G,KAAK;QACL6T,MAAM;QACNC,SAAS;QACTC,QAAQ,EAAEA,QAAQ,KAAK3T,SAAS,GAAG2T,QAAQ,GAAG;OAC/C;MACD9N,WAAW,EAAE0N,YAAY,GAAG,cAAc,GAAG;KAC9C,CAAC,CACDzN,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAAC3I,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,yBAAyB,EACzBI,MAAM,CACP;MAED,IAAIA,MAAM,CAACmE,MAAM,EAAE;QACjB,IAAI,CAAC9M,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChC2E,MAAM,CAACmE,MAAM,CACd;QACD,MAAM,IAAIhE,KAAK,CAACH,MAAM,CAACmE,MAAM,CAAC5Q,GAAG,CAAE6Q,CAAC,IAAKA,CAAC,CAACjB,OAAO,CAAC,CAACkB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,IAAI,CAACrE,MAAM,CAAClE,IAAI,EAAEyR,WAAW,EAAE;QAC7B,IAAI,CAAClW,MAAM,CAACgP,IAAI,CACd,gBAAgB,EAChB,oCAAoC,CACrC;QACD,OAAO,EAAE;;MAGX,MAAMyH,iBAAiB,GAAG9N,MAAM,CAAClE,IAAI,CAACyR,WAAW;MAEjD;MACA,IAAI,CAAClW,MAAM,CAACuI,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAE;QAC1D3G,UAAU,EAAE6U,iBAAiB,CAAC7U,UAAU;QACxCC,UAAU,EAAE4U,iBAAiB,CAAC5U,UAAU;QACxCC,WAAW,EAAE2U,iBAAiB,CAAC3U,WAAW;QAC1CC,WAAW,EAAE0U,iBAAiB,CAAC1U,WAAW;QAC1CC,eAAe,EAAEyU,iBAAiB,CAACzU;OACpC,CAAC;MAEF;MACA,MAAM0U,KAAK,GAAW,EAAE;MACxB,KAAK,MAAMC,IAAI,IAAIF,iBAAiB,CAACC,KAAK,EAAE;QAC1C,IAAI;UACF,IAAIC,IAAI,EAAE;YACRD,KAAK,CAAC1G,IAAI,CAAC,IAAI,CAACjF,aAAa,CAAC4L,IAAI,CAAC,CAAC;;SAEvC,CAAC,OAAO3S,KAAK,EAAE;UACd,IAAI,CAAChE,MAAM,CAACgP,IAAI,CACd,gBAAgB,EAChB,mCAAmC,EACnChL,KAAK,CACN;;;MAIL,IAAI,CAAChE,MAAM,CAACwL,IAAI,CACd,gBAAgB,EAChB,YAAYkL,KAAK,CAAC7N,MAAM,4BAA4B4N,iBAAiB,CAAC3U,WAAW,OAAO2U,iBAAiB,CAAC5U,UAAU,GAAG,CACxH;MAED;MACA,IAAI,CAACuU,MAAM,IAAIjN,IAAI,KAAK,CAAC,IAAI,CAACoN,QAAQ,EAAE;QACtC,IAAI,CAAC7U,UAAU,GAAG,CAAC,GAAGgV,KAAK,CAAC;QAC5B,IAAI,CAAChW,aAAa,GAAGsJ,IAAI,CAAC8L,GAAG,EAAE;QAC/B,IAAI,CAAC9V,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,2BAA2BmO,KAAK,CAAC7N,MAAM,QAAQ,CAChD;;MAGH;MACA,IAAI,CAAClH,qBAAqB,GAAG;QAC3BC,UAAU,EAAE6U,iBAAiB,CAAC7U,UAAU;QACxCC,UAAU,EAAE4U,iBAAiB,CAAC5U,UAAU;QACxCC,WAAW,EAAE2U,iBAAiB,CAAC3U,WAAW;QAC1CC,WAAW,EAAE0U,iBAAiB,CAAC1U,WAAW;QAC1CC,eAAe,EAAEyU,iBAAiB,CAACzU;OACpC;MAED,OAAO0U,KAAK;IACd,CAAC,CAAC,EACFva,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAEnE,IAAIA,KAAK,CAAC6J,aAAa,EAAE;QACvB,IAAI,CAAC7N,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAAC6J,aAAa,CACpB;;MAGH,IAAI7J,KAAK,CAAC8J,YAAY,EAAE;QACtB,IAAI,CAAC9N,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAAC8J,YAAY,CACnB;;MAGH;MACA,IACE,IAAI,CAACpM,UAAU,CAACmH,MAAM,GAAG,CAAC,IAC1BM,IAAI,KAAK,CAAC,IACV,CAACiN,MAAM,IACP,CAACG,QAAQ,EACT;QACA,IAAI,CAACvW,MAAM,CAACgP,IAAI,CACd,gBAAgB,EAChB,aAAa,IAAI,CAACtN,UAAU,CAACmH,MAAM,kCAAkC,CACtE;QACD,OAAO/M,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC4F,UAAU,CAAC,CAAC;;MAGjC,OAAO3F,UAAU,CACf,MACE,IAAI+M,KAAK,CACP,0BAA0B9E,KAAK,CAAC8H,OAAO,IAAI,eAAe,EAAE,CAC7D,CACJ;IACH,CAAC,CAAC,CACH;EACL;EACA8K,UAAUA,CAAC3L,MAAc;IACvB,OAAO,IAAI,CAAClL,MAAM,CACfyI,UAAU,CAAqB;MAC9BjE,KAAK,EAAEpH,cAAc;MACrBiM,SAAS,EAAE;QAAE1F,EAAE,EAAEuH;MAAM,CAAE;MACzBxC,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CAAEyM,MAAM,IAAK,IAAI,CAACoC,aAAa,CAACpC,MAAM,CAAClE,IAAI,EAAEmS,UAAU,CAAC,CAAC,EAC5Dza,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC5D,CAAC,CAAC,CACH;EACL;EACA+N,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC9W,MAAM,CACfyI,UAAU,CAAyB;MAClCjE,KAAK,EAAE1G,sBAAsB;MAC7B4K,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBtI,GAAG,CAAEyM,MAAM,IAAK,IAAI,CAACoC,aAAa,CAACpC,MAAM,CAAClE,IAAI,EAAEoS,cAAc,CAAC,CAAC,EAChE1a,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EACAgO,aAAaA,CAAC7L,MAAc;IAC1B,OAAO,IAAI,CAAClL,MAAM,CACf4J,MAAM,CAAwB;MAC7BC,QAAQ,EAAEpM,wBAAwB;MAClC4L,SAAS,EAAE;QAAE6B;MAAM;KACpB,CAAC,CACDzG,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEqS,aAAa,EAC7B,MAAM,IAAIhO,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAO,IAAI,CAACiC,aAAa,CAACpC,MAAM,CAAClE,IAAI,CAACqS,aAAa,CAAC;IACtD,CAAC,CAAC,EACF3a,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EACAiO,cAAcA,CAAC9L,MAAc;IAC3B,OAAO,IAAI,CAAClL,MAAM,CACf4J,MAAM,CAAyB;MAC9BC,QAAQ,EAAEnM,yBAAyB;MACnC2L,SAAS,EAAE;QAAE6B;MAAM;KACpB,CAAC,CACDzG,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEsS,cAAc,EAC9B,MAAM,IAAIjO,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAO,IAAI,CAACiC,aAAa,CAACpC,MAAM,CAAClE,IAAI,CAACsS,cAAc,CAAC;IACvD,CAAC,CAAC,EACF5a,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;EAGAkO,WAAWA,CACTlS,IAAY,EACZuH,cAAwB,EACxB4K,KAAY,EACZC,WAAoB;IAEpB,IAAI,CAAClX,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,mBAAmBzD,IAAI,SAASuH,cAAc,CAACxD,MAAM,eAAe,CACrE;IAED,IAAI,CAAC/D,IAAI,IAAI,CAACuH,cAAc,IAAIA,cAAc,CAACxD,MAAM,KAAK,CAAC,EAAE;MAC3D,OAAO9M,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,sCAAsC,CAAC,CACxD;;IAGH,OAAO,IAAI,CAAC/I,MAAM,CACf4J,MAAM,CAAC;MACNC,QAAQ,EAAE3L,qBAAqB;MAC/BmL,SAAS,EAAE;QAAEtE,IAAI;QAAEuH,cAAc;QAAE4K,KAAK;QAAEC;MAAW;KACtD,CAAC,CACD1S,IAAI,CACHtI,GAAG,CAAEyM,MAAW,IAAI;MAClB,MAAMwO,KAAK,GAAGxO,MAAM,CAAClE,IAAI,EAAEuS,WAAW;MACtC,IAAI,CAACG,KAAK,EAAE;QACV,MAAM,IAAIrO,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,IAAI,CAAC9I,MAAM,CAACwL,IAAI,CACd,gBAAgB,EAChB,+BAA+B2L,KAAK,CAACzT,EAAE,EAAE,CAC1C;MACD,OAAOyT,KAAK;IACd,CAAC,CAAC,EACFhb,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAsO,WAAWA,CAACC,OAAe,EAAEC,KAAU;IACrC,IAAI,CAACtX,MAAM,CAACuI,KAAK,CAAC,gBAAgB,EAAE,mBAAmB8O,OAAO,EAAE,CAAC;IAEjE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOtb,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAC/I,MAAM,CACf4J,MAAM,CAAC;MACNC,QAAQ,EAAE1L,qBAAqB;MAC/BkL,SAAS,EAAE;QAAE1F,EAAE,EAAE2T,OAAO;QAAEC;MAAK;KAChC,CAAC,CACD9S,IAAI,CACHtI,GAAG,CAAEyM,MAAW,IAAI;MAClB,MAAMwO,KAAK,GAAGxO,MAAM,CAAClE,IAAI,EAAE2S,WAAW;MACtC,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAIrO,KAAK,CAAC,mCAAmC,CAAC;;MAEtD,IAAI,CAAC9I,MAAM,CAACwL,IAAI,CACd,gBAAgB,EAChB,+BAA+B2L,KAAK,CAACzT,EAAE,EAAE,CAC1C;MACD,OAAOyT,KAAK;IACd,CAAC,CAAC,EACFhb,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,mCAAmC,CAAC,CACrD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAyO,WAAWA,CACTF,OAAe;IAEf,IAAI,CAACrX,MAAM,CAACuI,KAAK,CAAC,gBAAgB,EAAE,mBAAmB8O,OAAO,EAAE,CAAC;IAEjE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOtb,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAC/I,MAAM,CACf4J,MAAM,CAAC;MACNC,QAAQ,EAAEzL,qBAAqB;MAC/BiL,SAAS,EAAE;QAAE1F,EAAE,EAAE2T;MAAO;KACzB,CAAC,CACD7S,IAAI,CACHtI,GAAG,CAAEyM,MAAW,IAAI;MAClB,MAAMwG,QAAQ,GAAGxG,MAAM,CAAClE,IAAI,EAAE8S,WAAW;MACzC,IAAI,CAACpI,QAAQ,EAAE;QACb,MAAM,IAAIrG,KAAK,CAAC,mCAAmC,CAAC;;MAEtD,IAAI,CAAC9I,MAAM,CAACwL,IAAI,CACd,gBAAgB,EAChB,+BAA+B6L,OAAO,EAAE,CACzC;MACD,OAAOlI,QAAQ;IACjB,CAAC,CAAC,EACFhT,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,mCAAmC,CAAC,CACrD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA0O,UAAUA,CACRH,OAAe;IAEf,IAAI,CAACrX,MAAM,CAACuI,KAAK,CAAC,gBAAgB,EAAE,kBAAkB8O,OAAO,EAAE,CAAC;IAEhE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOtb,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAC/I,MAAM,CACf4J,MAAM,CAAC;MACNC,QAAQ,EAAExL,oBAAoB;MAC9BgL,SAAS,EAAE;QAAEiO;MAAO;KACrB,CAAC,CACD7S,IAAI,CACHtI,GAAG,CAAEyM,MAAW,IAAI;MAClB,MAAMwG,QAAQ,GAAGxG,MAAM,CAAClE,IAAI,EAAE+S,UAAU;MACxC,IAAI,CAACrI,QAAQ,EAAE;QACb,MAAM,IAAIrG,KAAK,CAAC,8BAA8B,CAAC;;MAEjD,IAAI,CAAC9I,MAAM,CAACwL,IAAI,CACd,gBAAgB,EAChB,4BAA4B6L,OAAO,EAAE,CACtC;MACD,OAAOlI,QAAQ;IACjB,CAAC,CAAC,EACFhT,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA2O,QAAQA,CAACJ,OAAe;IACtB,IAAI,CAACrX,MAAM,CAACuI,KAAK,CAAC,gBAAgB,EAAE,kBAAkB8O,OAAO,EAAE,CAAC;IAEhE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOtb,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAC/I,MAAM,CACfwE,KAAK,CAAC;MACLA,KAAK,EAAElG,eAAe;MACtB+K,SAAS,EAAE;QAAE1F,EAAE,EAAE2T;MAAO,CAAE;MAC1B5O,WAAW,EAAE;KACd,CAAC,CACDjE,IAAI,CACHtI,GAAG,CAAEyM,MAAW,IAAI;MAClB,MAAMwO,KAAK,GAAGxO,MAAM,CAAClE,IAAI,EAAEgT,QAAQ;MACnC,IAAI,CAACN,KAAK,EAAE;QACV,MAAM,IAAIrO,KAAK,CAAC,mBAAmB,CAAC;;MAEtC,IAAI,CAAC9I,MAAM,CAACwL,IAAI,CACd,gBAAgB,EAChB,iCAAiC6L,OAAO,EAAE,CAC3C;MACD,OAAOF,KAAK;IACd,CAAC,CAAC,EACFhb,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,oCAAoC,CAAC,CACtD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA4O,aAAaA,CAACzM,MAAc;IAC1B,IAAI,CAACjL,MAAM,CAACuI,KAAK,CAAC,gBAAgB,EAAE,4BAA4B0C,MAAM,EAAE,CAAC;IAEzE,IAAI,CAACA,MAAM,EAAE;MACX,OAAOlP,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,4BAA4B,CAAC,CAAC;;IAGlE,OAAO,IAAI,CAAC/I,MAAM,CACfwE,KAAK,CAAC;MACLA,KAAK,EAAEjG,qBAAqB;MAC5B8K,SAAS,EAAE;QAAE6B;MAAM,CAAE;MACrBxC,WAAW,EAAE;KACd,CAAC,CACDjE,IAAI,CACHtI,GAAG,CAAEyM,MAAW,IAAI;MAClB,MAAMmH,MAAM,GAAGnH,MAAM,CAAClE,IAAI,EAAEiT,aAAa,IAAI,EAAE;MAC/C,IAAI,CAAC1X,MAAM,CAACwL,IAAI,CACd,gBAAgB,EAChB,aAAasE,MAAM,CAACjH,MAAM,qBAAqBoC,MAAM,EAAE,CACxD;MACD,OAAO6E,MAAM;IACf,CAAC,CAAC,EACF3T,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EACA6O,sBAAsBA,CAACzO,cAAsB;IAC3ClD,OAAO,CAACC,GAAG,CACT,gEAAgEiD,cAAc,EAAE,CACjF;IAED;IACA,MAAM0O,UAAU,GAAG,IAAI,CAACC,YAAY,EAAE;IACtC7R,OAAO,CAACC,GAAG,CAAC,sCAAsC2R,UAAU,EAAE,CAAC;IAE/D,IAAI,CAACA,UAAU,EAAE;MACf5R,OAAO,CAACgJ,IAAI,CACV,+DAA+D,CAChE;MACD,IAAI,CAAChP,MAAM,CAACgP,IAAI,CACd,sEAAsE,CACvE;MACD,OAAOlT,EAAE,CAAC,IAA0B,CAAC;;IAGvCkK,OAAO,CAACC,GAAG,CACT,+EAA+EiD,cAAc,EAAE,CAChG;IACD,IAAI,CAAClJ,MAAM,CAACuI,KAAK,CACf,2EAA2EW,cAAc,EAAE,CAC5F;IAEDlD,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE;MACpEiD;KACD,CAAC;IACFlD,OAAO,CAACC,GAAG,CACT,4CAA4C,EAC5ChJ,yBAAyB,CAC1B;IACD+I,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAAEiD;IAAc,CAAE,CAAC;IAEpE,MAAM4O,IAAI,GAAG,IAAI,CAAC/X,MAAM,CACrBoE,SAAS,CAA2B;MACnCI,KAAK,EAAEtH,yBAAyB;MAChCmM,SAAS,EAAE;QAAEF;MAAc;KAC5B,CAAC,CACD1E,IAAI,CACHpI,GAAG,CAAEuM,MAAM,IAAI;MACb3C,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE0C,MAAM,CAAC;MAClE3C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0C,MAAM,CAAClE,IAAI,CAAC;MAClDuB,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC0C,MAAM,CAAClE,IAAI,EAAEsT,WAAW,CACzB;IACH,CAAC,CAAC,EACF7b,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMW,GAAG,GAAGX,MAAM,CAAClE,IAAI,EAAEsT,WAAW;MACpC,IAAI,CAACzO,GAAG,EAAE;QACRtD,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjD0C,MAAM,CACP;QACD,IAAI,CAAC3I,MAAM,CAACgP,IAAI,CAAC,gCAAgC,CAAC;QAClD,MAAM,IAAIlG,KAAK,CAAC,6BAA6B,CAAC;;MAGhD,IAAI,CAAC9I,MAAM,CAACuI,KAAK,CACf,+CAA+C,EAC/Ce,GAAG,CACJ;MAED;MACA,IAAI,CAACA,GAAG,CAAC5F,EAAE,IAAI,CAAC4F,GAAG,CAACiD,GAAG,EAAE;QACvB,IAAI,CAACvM,MAAM,CAACgP,IAAI,CACd,oDAAoD,CACrD;QACD1F,GAAG,CAAC5F,EAAE,GAAG,QAAQsG,IAAI,CAAC8L,GAAG,EAAE,EAAE;;MAG/B,IAAI;QACF;QACA,MAAMkC,iBAAiB,GAAG,IAAI,CAACzO,gBAAgB,CAACD,GAAG,CAAC;QAEpD,IAAI,CAACtJ,MAAM,CAACuI,KAAK,CACf,4CAA4C,EAC5CyP,iBAAiB,CAClB;QAED;QACA,IACEA,iBAAiB,CAACzQ,IAAI,KAAK/K,WAAW,CAAC4Y,KAAK,IAC5C4C,iBAAiB,CAACzQ,IAAI,KAAK/K,WAAW,CAACyb,aAAa,IACnDD,iBAAiB,CAACE,WAAW,IAC5BF,iBAAiB,CAACE,WAAW,CAACC,IAAI,CAC/BC,GAAG,IAAKA,GAAG,CAAC7Q,IAAI,KAAK,OAAO,CAC7B,EACJ;UACA,IAAI,CAACvH,MAAM,CAACuI,KAAK,CACf,iDAAiD,CAClD;;QAGH;QACA,IAAI,CAACtI,IAAI,CAACoY,GAAG,CAAC,MAAK;UACjB,IAAI,CAACrY,MAAM,CAACuI,KAAK,CACf,kDAAkD,CACnD;UACD,IAAI,CAAC+P,gCAAgC,CACnCpP,cAAc,EACd8O,iBAAiB,CAClB;QACH,CAAC,CAAC;QAEF,OAAOA,iBAAiB;OACzB,CAAC,OAAOxR,GAAG,EAAE;QACZ,IAAI,CAACxG,MAAM,CAACgE,KAAK,CAAC,8BAA8B,EAAEwC,GAAG,CAAC;QAEtD;QACA,MAAM+R,cAAc,GAAY;UAC9B7U,EAAE,EAAE4F,GAAG,CAAC5F,EAAE,IAAI4F,GAAG,CAACiD,GAAG,IAAI,QAAQvC,IAAI,CAAC8L,GAAG,EAAE,EAAE;UAC7CxI,OAAO,EAAEhE,GAAG,CAACgE,OAAO,IAAI,EAAE;UAC1B/F,IAAI,EAAE+B,GAAG,CAAC/B,IAAI,IAAI/K,WAAW,CAACgc,IAAI;UAClCnO,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACvB,GAAG,CAACe,SAAS,CAAC;UACvCkD,MAAM,EAAE,KAAK;UACbzC,MAAM,EAAExB,GAAG,CAACwB,MAAM,GACd,IAAI,CAACC,aAAa,CAACzB,GAAG,CAACwB,MAAM,CAAC,GAC9B;YACEpH,EAAE,EAAE,IAAI,CAACuI,gBAAgB,EAAE;YAC3BwM,QAAQ,EAAE;;SAEjB;QAED,IAAI,CAACzY,MAAM,CAACuI,KAAK,CACf,sCAAsC,EACtCgQ,cAAc,CACf;QACD,OAAOA,cAAc;;IAEzB,CAAC,CAAC,EACFpc,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD;MACA,OAAO/H,KAAK;IACd,CAAC,CAAC;IACF;IACAI,MAAM,CAAEyP,OAAO,IAAK,CAAC,CAACA,OAAO,CAAC;IAC9B;IACA9P,KAAK,CAAC,CAAC,CAAC,CACT;IAEHgK,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAE5D,MAAMyS,GAAG,GAAGZ,IAAI,CAAC3T,SAAS,CAAC;MACzBP,IAAI,EAAGkI,OAAO,IAAI;QAChB9F,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE6F,OAAO,CAAC;QACnE;QACA,IAAI,CAAC9L,MAAM,CAACuI,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEuD,OAAO,CAAC;QAErE;QACA,IAAI,CAACwM,gCAAgC,CAACpP,cAAc,EAAE4C,OAAO,CAAC;MAChE,CAAC;MACD9H,KAAK,EAAGwC,GAAG,IAAI;QACbR,OAAO,CAAChC,KAAK,CAAC,8BAA8B,EAAEwC,GAAG,CAAC;QAClD,IAAI,CAACxG,MAAM,CAACgE,KAAK,CAAC,gCAAgC,EAAEwC,GAAG,CAAC;MAC1D,CAAC;MACDqP,QAAQ,EAAEA,CAAA,KAAK;QACb7P,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MACjD;KACD,CAAC;IAEF;IACAD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEyS,GAAG,CAAC;IAC1D1S,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAClG,MAAM,CAAC;IAE1D,IAAI,CAACS,aAAa,CAACwP,IAAI,CAAC0I,GAAG,CAAC;IAC5B1S,OAAO,CAACC,GAAG,CACT,2FAA2F,IAAI,CAACzF,aAAa,CAACqI,MAAM,EAAE,CACvH;IACD,OAAOiP,IAAI;EACb;EAEA;;;;;EAKQQ,gCAAgCA,CACtCpP,cAAsB,EACtB4C,OAAgB;IAEhB,IAAI,CAAC9L,MAAM,CAACuI,KAAK,CACf,oCAAoCW,cAAc,qBAAqB4C,OAAO,CAACpI,EAAE,EAAE,CACpF;IAED;IACA,IAAI,CAACzD,IAAI,CAACoY,GAAG,CAAC,MAAK;MACjB;MACA,IAAI,CAACnY,kBAAkB,CAAC0D,IAAI,CAACsF,cAAc,CAAC;MAE5C,IAAI,CAAClJ,MAAM,CAACuI,KAAK,CAAC,oDAAoD,CAAC;IACzE,CAAC,CAAC;IAEF;IACAoQ,UAAU,CAAC,MAAK;MACd,IAAI,CAACpN,eAAe,CAACrC,cAAc,CAAC,CAAC/E,SAAS,CAAC;QAC7CP,IAAI,EAAGiI,YAAY,IAAI;UACrB,IAAI,CAAC7L,MAAM,CAACuI,KAAK,CACf,8BAA8BW,cAAc,mBAC1C2C,YAAY,EAAExC,QAAQ,EAAER,MAAM,IAAI,CACpC,WAAW,CACZ;QACH,CAAC;QACD7E,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gDAAgDkF,cAAc,GAAG,EACjElF,KAAK,CACN;QACH;OACD,CAAC;IACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACT;EAEA;;;EAGQ4U,0BAA0BA,CAAA;IAChC5S,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IAErE;IACA,IAAI,CAACwG,gBAAgB,CAAC,IAAI,CAAC,CAACtI,SAAS,CAAC;MACpCP,IAAI,EAAGzD,aAAa,IAAI;QACtB6F,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjD9F,aAAa,CAAC0I,MAAM,CACrB;MACH,CAAC;MACD7E,KAAK,EAAGA,KAAK,IAAI;QACfgC,OAAO,CAAChC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MACpE;KACD,CAAC;EACJ;EAEAM,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAAC,IAAI,CAACuT,YAAY,EAAE,EAAE;MACxB,IAAI,CAAC7X,MAAM,CAACgP,IAAI,CACd,+EAA+E,CAChF;MACD,OAAOjT,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE,IAAI,CAAC9I,MAAM,CAACuI,KAAK,CAAC,iDAAiD,CAAC;IAEpE,MAAMuP,IAAI,GAAG,IAAI,CAAC/X,MAAM,CACrBoE,SAAS,CAA8B;MACtCI,KAAK,EAAErH;KACR,CAAC,CACDsH,IAAI,CACHpI,GAAG,CAAEuM,MAAM,IACT,IAAI,CAAC3I,MAAM,CAACuI,KAAK,CACf,uDAAuD,EACvDI,MAAM,CACP,CACF,EACDzM,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMgO,IAAI,GAAGhO,MAAM,CAAClE,IAAI,EAAEoU,iBAAiB;MAC3C,IAAI,CAAClC,IAAI,EAAE;QACT,IAAI,CAAC3W,MAAM,CAACgE,KAAK,CAAC,4BAA4B,CAAC;QAC/C,MAAM,IAAI8E,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAO,IAAI,CAACiC,aAAa,CAAC4L,IAAI,CAAC;IACjC,CAAC,CAAC,EACFxa,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MAC/D,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,EACF9M,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAM0c,GAAG,GAAGZ,IAAI,CAAC3T,SAAS,EAAE;IAC5B,IAAI,CAAC3D,aAAa,CAACwP,IAAI,CAAC0I,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EACAgB,8BAA8BA,CAC5B5P,cAAsB;IAEtB,MAAM4O,IAAI,GAAG,IAAI,CAAC/X,MAAM,CACrBoE,SAAS,CAAwC;MAChDI,KAAK,EAAElH,iCAAiC;MACxC+L,SAAS,EAAE;QAAEF;MAAc;KAC5B,CAAC,CACD1E,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAM0C,IAAI,GAAG1C,MAAM,CAAClE,IAAI,EAAEsU,mBAAmB;MAC7C,IAAI,CAAC1N,IAAI,EAAE,MAAM,IAAIvC,KAAK,CAAC,kCAAkC,CAAC;MAE9D,MAAM4C,sBAAsB,GAAiB;QAC3C,GAAGL,IAAI;QACPM,YAAY,EACVN,IAAI,CAACM,YAAY,EAAEzP,GAAG,CAAEoQ,CAAC,IAAK,IAAI,CAACvB,aAAa,CAACuB,CAAC,CAAC,CAAC,IAAI,EAAE;QAC5D0M,WAAW,EAAE3N,IAAI,CAAC2N,WAAW,GACzB;UACE,GAAG3N,IAAI,CAAC2N,WAAW;UACnBlO,MAAM,EAAE,IAAI,CAACC,aAAa,CAACM,IAAI,CAAC2N,WAAW,CAAClO,MAAM,CAAC;UACnDT,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACQ,IAAI,CAAC2N,WAAW,CAAC3O,SAAS,CAAC;UACpDN,MAAM,EAAEsB,IAAI,CAAC2N,WAAW,CAACjP,MAAM,GAC3B,IAAI,CAACc,QAAQ,CAACQ,IAAI,CAAC2N,WAAW,CAACjP,MAAM,CAAC,GACtCnH,SAAS;UACb;UACAc,EAAE,EAAE2H,IAAI,CAAC2N,WAAW,CAACtV,EAAE;UACvB4J,OAAO,EAAEjC,IAAI,CAAC2N,WAAW,CAAC1L,OAAO;UACjC/F,IAAI,EAAE8D,IAAI,CAAC2N,WAAW,CAACzR,IAAI;UAC3BgG,MAAM,EAAElC,IAAI,CAAC2N,WAAW,CAACzL;UACzB;SACD,GACD,IAAI,CAAE;OACX;;MAED,OAAO7B,sBAAsC,CAAC,CAAC;IACjD,CAAC,CAAC,EACFvP,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,kCAAkC,EAClCA,KAAK,CACN;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;IAEH,MAAM4P,GAAG,GAAGZ,IAAI,CAAC3T,SAAS,EAAE;IAC5B,IAAI,CAAC3D,aAAa,CAACwP,IAAI,CAAC0I,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EACAmB,0BAA0BA,CACxB/P,cAAsB;IAEtB,MAAM4O,IAAI,GAAG,IAAI,CAAC/X,MAAM,CACrBoE,SAAS,CAAwB;MAChCI,KAAK,EAAE3G,6BAA6B;MACpCwL,SAAS,EAAE;QAAEF;MAAc;KAC5B,CAAC,CACD1E,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAKA,MAAM,CAAClE,IAAI,EAAEyU,eAAe,CAAC,EAC7C7c,MAAM,CAAC8c,OAAO,CAAC,EACfhd,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;IAEH,MAAM4P,GAAG,GAAGZ,IAAI,CAAC3T,SAAS,EAAE;IAC5B,IAAI,CAAC3D,aAAa,CAACwP,IAAI,CAAC0I,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EACQD,YAAYA,CAAA;IAClB,MAAMuB,KAAK,GAAGjW,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACgW,KAAK,EAAE;MACV,IAAI,CAACpZ,MAAM,CAACgP,IAAI,CAAC,oBAAoB,CAAC;MACtC,OAAO,KAAK;;IAGd,IAAI;MACF;MACA,MAAMqK,KAAK,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC;MAC9B,IAAID,KAAK,CAACxQ,MAAM,KAAK,CAAC,EAAE;QACtB,IAAI,CAAC7I,MAAM,CAACgP,IAAI,CAAC,0BAA0B,CAAC;QAC5C,OAAO,KAAK;;MAGd;MACA,MAAMuK,OAAO,GAAGlW,IAAI,CAACC,KAAK,CAACkW,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAE1C;MACA,IAAI,CAACE,OAAO,CAACE,GAAG,EAAE;QAChB,IAAI,CAACzZ,MAAM,CAACgP,IAAI,CAAC,8BAA8B,CAAC;QAChD,OAAO,KAAK;;MAGd,MAAM0K,cAAc,GAAG,IAAI1P,IAAI,CAACuP,OAAO,CAACE,GAAG,GAAG,IAAI,CAAC;MACnD,MAAM3D,GAAG,GAAG,IAAI9L,IAAI,EAAE;MAEtB,IAAI0P,cAAc,GAAG5D,GAAG,EAAE;QACxB,IAAI,CAAC9V,MAAM,CAACgP,IAAI,CAAC,cAAc,EAAE;UAC/B2K,UAAU,EAAED,cAAc,CAAC7W,WAAW,EAAE;UACxCiT,GAAG,EAAEA,GAAG,CAACjT,WAAW;SACrB,CAAC;QACF,OAAO,KAAK;;MAGd,OAAO,IAAI;KACZ,CAAC,OAAOmB,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,0CAA0C,EAC1CA,KAAc,CACf;MACD,OAAO,KAAK;;EAEhB;EAEAI,4BAA4BA,CAAA;IAC1B;IACA,IAAI,CAAC,IAAI,CAACyT,YAAY,EAAE,EAAE;MACxB,IAAI,CAAC7X,MAAM,CAACgP,IAAI,CACd,2EAA2E,CAC5E;MACD,OAAOlT,EAAE,CAAC,EAAE,CAAC;;IAGf,IAAI,CAACkE,MAAM,CAACuI,KAAK,CAAC,kDAAkD,CAAC;IAErE,MAAMuP,IAAI,GAAG,IAAI,CAAC/X,MAAM,CACrBoE,SAAS,CAAyB;MACjCI,KAAK,EAAE3F;KACR,CAAC,CACD4F,IAAI,CACHpI,GAAG,CAAEuM,MAAM,IACT,IAAI,CAAC3I,MAAM,CAACuI,KAAK,CACf,wDAAwD,EACxDI,MAAM,CACP,CACF,EACDzM,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMiH,eAAe,GAAGjH,MAAM,CAAClE,IAAI,EAAEmV,iBAAiB,IAAI,EAAE;MAC5D,IAAI,CAAC5Z,MAAM,CAACuI,KAAK,CACf,oCAAoC,EACpCqH,eAAe,CAChB;MACD,IAAI,CAACY,wBAAwB,CAACZ,eAAe,EAAE,IAAI,CAAC;MACpD,OAAOA,eAAe;IACxB,CAAC,CAAC,EACFzT,UAAU,CAAEqK,GAAG,IAAI;MACjB,IAAI,CAACxG,MAAM,CAACgE,KAAK,CACf,wCAAwC,EACxCwC,GAAY,CACb;MACD;MACA,OAAO1K,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC;IACF;IACAE,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAM0c,GAAG,GAAGZ,IAAI,CAAC3T,SAAS,EAAE;IAC5B,IAAI,CAAC3D,aAAa,CAACwP,IAAI,CAAC0I,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EACA5T,2BAA2BA,CAAA;IACzB;IACA,MAAMkV,KAAK,GAAGjW,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACgW,KAAK,EAAE;MACV,IAAI,CAACpZ,MAAM,CAACgP,IAAI,CACd,6DAA6D,CAC9D;MACD,OAAO/S,KAAK;;IAGd,IAAI,CAAC+D,MAAM,CAACuI,KAAK,CACf,4DAA4D,CAC7D;IAED,MAAMsR,OAAO,GAAG,IAAI,CAAC9Z,MAAM,CAACoE,SAAS,CAA4B;MAC/DI,KAAK,EAAE1H;KACR,CAAC;IAEF,MAAMid,UAAU,GAAGD,OAAO,CAACrV,IAAI,CAC7BtI,GAAG,CAAEyM,MAAM,IAAI;MACb,MAAMlF,YAAY,GAAGkF,MAAM,CAAClE,IAAI,EAAEsV,oBAAoB;MACtD,IAAI,CAACtW,YAAY,EAAE;QACjB,MAAM,IAAIqF,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,IAAI,CAAC9I,MAAM,CAACuI,KAAK,CACf,sCAAsC,EACtC9E,YAAY,CACb;MAED,MAAMuW,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAACxW,YAAY,CAAC;MAE3D;MACA,IAAI,IAAI,CAACrD,iBAAiB,CAACgN,GAAG,CAAC4M,UAAU,CAACtW,EAAE,CAAC,EAAE;QAC7C,IAAI,CAAC1D,MAAM,CAACuI,KAAK,CACf,mBAAmByR,UAAU,CAACtW,EAAE,6BAA6B,CAC9D;QACD,MAAM,IAAIoF,KAAK,CAAC,sCAAsC,CAAC;;MAGzD;MACA,IAAI,CAAC9I,MAAM,CAACuI,KAAK,CAAC,iDAAiD,CAAC;MAEpE;MACA,MAAM2R,oBAAoB,GAAG,IAAI,CAAC/Z,aAAa,CAACwO,KAAK;MACrD,MAAMwL,oBAAoB,GAAGD,oBAAoB,CAAC/N,IAAI,CACnD+B,CAAC,IAAKA,CAAC,CAACxK,EAAE,KAAKsW,UAAU,CAACtW,EAAE,CAC9B;MAED,IAAIyW,oBAAoB,EAAE;QACxB,IAAI,CAACna,MAAM,CAACuI,KAAK,CACf,sDAAsD,EACtDyR,UAAU,CAACtW,EAAE,CACd;QACD,OAAOsW,UAAU;;MAGnB;MACA,IAAI,CAACjU,qBAAqB,EAAE;MAE5B;MACA,IAAI,CAACqU,uBAAuB,CAACJ,UAAU,CAAC;MAExC;MACA,IAAI,CAAC/Z,IAAI,CAACoY,GAAG,CAAC,MAAK;QACjB;QACA,MAAMgC,oBAAoB,GAAG,CAACL,UAAU,EAAE,GAAGE,oBAAoB,CAAC;QAElE,IAAI,CAACla,MAAM,CAACuI,KAAK,CACf,wDAAwD8R,oBAAoB,CAACxR,MAAM,SAAS,CAC7F;QAED,IAAI,CAAC1I,aAAa,CAACyD,IAAI,CAACyW,oBAAoB,CAAC;QAC7C,IAAI,CAAC/Z,iBAAiB,CAACsD,IAAI,CAAC,IAAI,CAACtD,iBAAiB,CAACqO,KAAK,GAAG,CAAC,CAAC;MAC/D,CAAC,CAAC;MAEF,IAAI,CAAC3O,MAAM,CAACuI,KAAK,CACf,+CAA+C,EAC/CyR,UAAU,CACX;MAED,OAAOA,UAAU;IACnB,CAAC,CAAC;IACF;IACA7d,UAAU,CAAEqK,GAAG,IAAI;MACjB,IACEA,GAAG,YAAYsC,KAAK,IACpBtC,GAAG,CAACsF,OAAO,KAAK,sCAAsC,EACtD;QACA,OAAO7P,KAAK;;MAGd,IAAI,CAAC+D,MAAM,CAACgE,KAAK,CAAC,oCAAoC,EAAEwC,GAAY,CAAC;MACrE,OAAOvK,KAAK;IACd,CAAC,CAAC;IACF;IACAG,GAAG,CAAEqH,YAAY,IAAI;MACnB,IAAI,CAACzD,MAAM,CAACuI,KAAK,CACf,6CAA6C,EAC7C9E,YAAY,CACb;IACH,CAAC,CAAC,CACH;IAED,MAAMiV,GAAG,GAAGoB,UAAU,CAAC3V,SAAS,CAAC;MAC/BP,IAAI,EAAGH,YAAY,IAAI;QACrB,IAAI,CAACzD,MAAM,CAACuI,KAAK,CACf,yCAAyC,EACzC9E,YAAY,CACb;MACH,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,6CAA6C,EAC7CA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAACxD,aAAa,CAACwP,IAAI,CAAC0I,GAAG,CAAC;IAC5B,IAAI,CAAC1Y,MAAM,CAACuI,KAAK,CAAC,mDAAmD,CAAC;IACtE,OAAOuR,UAAU;EACnB;EACA;EACA;EACA;EAEQ9W,oBAAoBA,CAAA;IAC1B,IAAI,CAACsX,eAAe,GAAGC,WAAW,CAAC,MAAK;MACtC,IAAI,CAACC,2BAA2B,EAAE;IACpC,CAAC,EAAE,OAAO,CAAC;EACb;EACQA,2BAA2BA,CAAA;IACjC,MAAM1E,GAAG,GAAG,IAAI9L,IAAI,EAAE;IACtB,MAAMyQ,aAAa,GAAG,IAAIzQ,IAAI,CAAC8L,GAAG,CAAC4E,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAExE,IAAIC,YAAY,GAAG,CAAC;IAEpB,IAAI,CAACva,iBAAiB,CAACoD,OAAO,CAAC,CAACC,YAAY,EAAEC,EAAE,KAAI;MAClD,MAAMkX,gBAAgB,GAAG,IAAI5Q,IAAI,CAACvG,YAAY,CAAC4G,SAAS,CAAC;MACzD,IAAIuQ,gBAAgB,GAAGH,aAAa,EAAE;QACpC,IAAI,CAACra,iBAAiB,CAACya,MAAM,CAACnX,EAAE,CAAC;QACjCiX,YAAY,EAAE;;IAElB,CAAC,CAAC;IAEF,IAAIA,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAAC3a,MAAM,CAACuI,KAAK,CAAC,cAAcoS,YAAY,wBAAwB,CAAC;MAErE;MACA,MAAMG,sBAAsB,GAAGjX,KAAK,CAACtH,IAAI,CACvC,IAAI,CAAC6D,iBAAiB,CAAC0D,MAAM,EAAE,CAChC;MACD,MAAM4J,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CACtDmN,sBAAsB,CACvB;MAED,IAAI,CAAC3a,aAAa,CAACyD,IAAI,CAAC8J,mBAAmB,CAAC;MAC5C,IAAI,CAAC3J,iBAAiB,EAAE;;EAE5B;EACA;;;;;EAKQ4J,uBAAuBA,CAC7BxN,aAA6B;IAE7B,OAAOA,aAAa,CAAC4a,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACjC;MACA,MAAMC,KAAK,GAAG,IAAIlR,IAAI,CAACgR,CAAC,CAAC3Q,SAAS,IAAI,CAAC,CAAC;MACxC,MAAM8Q,KAAK,GAAG,IAAInR,IAAI,CAACiR,CAAC,CAAC5Q,SAAS,IAAI,CAAC,CAAC;MACxC,OAAO8Q,KAAK,CAACT,OAAO,EAAE,GAAGQ,KAAK,CAACR,OAAO,EAAE,CAAC,CAAC;IAC5C,CAAC,CAAC;EACJ;;EAEQzO,gBAAgBA,CAAA;IACtB,OAAO9I,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;EAC7C;EACQmG,gBAAgBA,CAACuC,OAAgB;IACvC,IAAI,CAACA,OAAO,EAAE;MACZ,IAAI,CAAC9L,MAAM,CAACgE,KAAK,CACf,6DAA6D,CAC9D;MACD,MAAM,IAAI8E,KAAK,CAAC,4BAA4B,CAAC;;IAG/C,IAAI;MACF;MACA,IAAI,CAACgD,OAAO,CAACpI,EAAE,IAAI,CAACoI,OAAO,CAACS,GAAG,EAAE;QAC/B,IAAI,CAACvM,MAAM,CAACgE,KAAK,CACf,wCAAwC,EACxCpB,SAAS,EACTkJ,OAAO,CACR;QACD,MAAM,IAAIhD,KAAK,CAAC,wBAAwB,CAAC;;MAG3C;MACA,IAAIsS,gBAAgB;MACpB,IAAI;QACFA,gBAAgB,GAAGtP,OAAO,CAAChB,MAAM,GAC7B,IAAI,CAACC,aAAa,CAACe,OAAO,CAAChB,MAAM,CAAC,GAClClI,SAAS;OACd,CAAC,OAAOoB,KAAK,EAAE;QACd,IAAI,CAAChE,MAAM,CAACgP,IAAI,CACd,yEAAyE,EACzEhL,KAAK,CACN;QACDoX,gBAAgB,GAAG;UACjB7O,GAAG,EAAET,OAAO,CAAC9C,QAAQ,IAAI,SAAS;UAClCtF,EAAE,EAAEoI,OAAO,CAAC9C,QAAQ,IAAI,SAAS;UACjCyP,QAAQ,EAAE,cAAc;UACxB4C,KAAK,EAAE,qBAAqB;UAC5BC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE;SACX;;MAGH;MACA,IAAIC,kBAAkB;MACtB,IAAI1P,OAAO,CAAC2P,QAAQ,EAAE;QACpB,IAAI;UACFD,kBAAkB,GAAG,IAAI,CAACzQ,aAAa,CAACe,OAAO,CAAC2P,QAAQ,CAAC;SAC1D,CAAC,OAAOzX,KAAK,EAAE;UACd,IAAI,CAAChE,MAAM,CAACgP,IAAI,CACd,2EAA2E,EAC3EhL,KAAK,CACN;UACDwX,kBAAkB,GAAG;YACnBjP,GAAG,EAAET,OAAO,CAAC7C,UAAU,IAAI,SAAS;YACpCvF,EAAE,EAAEoI,OAAO,CAAC7C,UAAU,IAAI,SAAS;YACnCwP,QAAQ,EAAE,cAAc;YACxB4C,KAAK,EAAE,qBAAqB;YAC5BC,IAAI,EAAE,MAAM;YACZC,QAAQ,EAAE;WACX;;;MAIL;MACA,MAAMG,qBAAqB,GACzB5P,OAAO,CAACoM,WAAW,EAAEhc,GAAG,CAAEkc,GAAG,KAAM;QACjC1U,EAAE,EAAE0U,GAAG,CAAC1U,EAAE,IAAI0U,GAAG,CAAC7L,GAAG,IAAI,cAAcvC,IAAI,CAAC8L,GAAG,EAAE,EAAE;QACnD6F,GAAG,EAAEvD,GAAG,CAACuD,GAAG,IAAI,EAAE;QAClBpU,IAAI,EAAE6Q,GAAG,CAAC7Q,IAAI,IAAI,SAAS;QAC3BzC,IAAI,EAAEsT,GAAG,CAACtT,IAAI,IAAI,YAAY;QAC9B+H,IAAI,EAAEuL,GAAG,CAACvL,IAAI,IAAI,CAAC;QACnB3F,QAAQ,EAAEkR,GAAG,CAAClR,QAAQ,IAAI;OAC3B,CAAC,CAAC,IAAI,EAAE;MAEX;MACA,MAAM8Q,iBAAiB,GAAG;QACxB,GAAGlM,OAAO;QACVS,GAAG,EAAET,OAAO,CAACpI,EAAE,IAAIoI,OAAO,CAACS,GAAG;QAC9B7I,EAAE,EAAEoI,OAAO,CAACpI,EAAE,IAAIoI,OAAO,CAACS,GAAG;QAC7Be,OAAO,EAAExB,OAAO,CAACwB,OAAO,IAAI,EAAE;QAC9BxC,MAAM,EAAEsQ,gBAAgB;QACxB/Q,SAAS,EAAE,IAAI,CAACC,aAAa,CAACwB,OAAO,CAACzB,SAAS,CAAC;QAChDN,MAAM,EAAE+B,OAAO,CAAC/B,MAAM,GAAG,IAAI,CAACO,aAAa,CAACwB,OAAO,CAAC/B,MAAM,CAAC,GAAGnH,SAAS;QACvEsV,WAAW,EAAEwD,qBAAqB;QAClCE,QAAQ,EAAE9P,OAAO,CAAC8P,QAAQ,IAAI;OAC/B;MAED;MACA,IAAIJ,kBAAkB,EAAE;QACtBxD,iBAAiB,CAACyD,QAAQ,GAAGD,kBAAkB;;MAGjD,IAAI,CAACxb,MAAM,CAACuI,KAAK,CAAC,kDAAkD,EAAE;QACpEkB,SAAS,EAAEuO,iBAAiB,CAACtU,EAAE;QAC/BsF,QAAQ,EAAEgP,iBAAiB,CAAClN,MAAM,EAAEpH;OACrC,CAAC;MAEF,OAAOsU,iBAAiB;KACzB,CAAC,OAAOhU,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,6CAA6C,EAC7CA,KAAK,YAAY8E,KAAK,GAAG9E,KAAK,GAAG,IAAI8E,KAAK,CAAC+S,MAAM,CAAC7X,KAAK,CAAC,CAAC,EACzD8H,OAAO,CACR;MACD,MAAM,IAAIhD,KAAK,CACb,gCACE9E,KAAK,YAAY8E,KAAK,GAAG9E,KAAK,CAAC8H,OAAO,GAAG+P,MAAM,CAAC7X,KAAK,CACvD,EAAE,CACH;;EAEL;EAEO+G,aAAaA,CAAC4L,IAAS;IAC5B,IAAI,CAACA,IAAI,EAAE;MACT,MAAM,IAAI7N,KAAK,CAAC,yBAAyB,CAAC;;IAG5C;IACA,MAAMmC,MAAM,GAAG0L,IAAI,CAACjT,EAAE,IAAIiT,IAAI,CAACpK,GAAG;IAClC,IAAI,CAACtB,MAAM,EAAE;MACX,MAAM,IAAInC,KAAK,CAAC,qBAAqB,CAAC;;IAGxC;IACA,MAAM2P,QAAQ,GAAG9B,IAAI,CAAC8B,QAAQ,IAAI,cAAc;IAChD,MAAM4C,KAAK,GAAG1E,IAAI,CAAC0E,KAAK,IAAI,QAAQpQ,MAAM,cAAc;IACxD,MAAMsQ,QAAQ,GACZ5E,IAAI,CAAC4E,QAAQ,KAAK3Y,SAAS,IAAI+T,IAAI,CAAC4E,QAAQ,KAAK,IAAI,GACjD5E,IAAI,CAAC4E,QAAQ,GACb,IAAI;IACV,MAAMD,IAAI,GAAG3E,IAAI,CAAC2E,IAAI,IAAI,MAAM;IAEhC;IACA,OAAO;MACL/O,GAAG,EAAEtB,MAAM;MACXvH,EAAE,EAAEuH,MAAM;MACVwN,QAAQ,EAAEA,QAAQ;MAClB4C,KAAK,EAAEA,KAAK;MACZC,IAAI,EAAEA,IAAI;MACVC,QAAQ,EAAEA,QAAQ;MAClB;MACAO,KAAK,EAAEnF,IAAI,CAACmF,KAAK,IAAI,IAAI;MACzBC,GAAG,EAAEpF,IAAI,CAACoF,GAAG;MACbxF,QAAQ,EAAEI,IAAI,CAACJ,QAAQ,IAAI,KAAK;MAChCyF,UAAU,EAAErF,IAAI,CAACqF,UAAU,GAAG,IAAIhS,IAAI,CAAC2M,IAAI,CAACqF,UAAU,CAAC,GAAGpZ,SAAS;MACnEqZ,SAAS,EAAEtF,IAAI,CAACsF,SAAS,GAAG,IAAIjS,IAAI,CAAC2M,IAAI,CAACsF,SAAS,CAAC,GAAGrZ,SAAS;MAChEsZ,SAAS,EAAEvF,IAAI,CAACuF,SAAS,GAAG,IAAIlS,IAAI,CAAC2M,IAAI,CAACuF,SAAS,CAAC,GAAGtZ,SAAS;MAChEuZ,cAAc,EAAExF,IAAI,CAACwF,cAAc;MACnCC,cAAc,EAAEzF,IAAI,CAACyF,cAAc;MACnCC,SAAS,EAAE1F,IAAI,CAAC0F;KACjB;EACH;EACQ/Q,qBAAqBA,CAACD,IAAkB;IAC9C,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAACrL,MAAM,CAACgE,KAAK,CACf,kEAAkE,CACnE;MACD,MAAM,IAAI8E,KAAK,CAAC,iCAAiC,CAAC;;IAGpD,IAAI;MACF;MACA,IAAI,CAACuC,IAAI,CAAC3H,EAAE,IAAI,CAAC2H,IAAI,CAACkB,GAAG,EAAE;QACzB,IAAI,CAACvM,MAAM,CAACgE,KAAK,CACf,6CAA6C,EAC7CpB,SAAS,EACTyI,IAAI,CACL;QACD,MAAM,IAAIvC,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA,MAAMwT,sBAAsB,GAAG,EAAE;MACjC,IAAIjR,IAAI,CAACM,YAAY,IAAI9H,KAAK,CAAC0Y,OAAO,CAAClR,IAAI,CAACM,YAAY,CAAC,EAAE;QACzD,KAAK,MAAM6Q,WAAW,IAAInR,IAAI,CAACM,YAAY,EAAE;UAC3C,IAAI;YACF,IAAI6Q,WAAW,EAAE;cACfF,sBAAsB,CAACtM,IAAI,CAAC,IAAI,CAACjF,aAAa,CAACyR,WAAW,CAAC,CAAC;;WAE/D,CAAC,OAAOxY,KAAK,EAAE;YACd,IAAI,CAAChE,MAAM,CAACgP,IAAI,CACd,0DAA0D,EAC1DhL,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAAChE,MAAM,CAACgP,IAAI,CACd,iFAAiF,EACjF3D,IAAI,CACL;;MAGH;MACA,MAAMoR,kBAAkB,GAAG,EAAE;MAC7B,IAAIpR,IAAI,CAAChC,QAAQ,IAAIxF,KAAK,CAAC0Y,OAAO,CAAClR,IAAI,CAAChC,QAAQ,CAAC,EAAE;QACjD,IAAI,CAACrJ,MAAM,CAACuI,KAAK,CAAC,mDAAmD,EAAE;UACrEkH,KAAK,EAAEpE,IAAI,CAAChC,QAAQ,CAACR;SACtB,CAAC;QAEF,KAAK,MAAMiD,OAAO,IAAIT,IAAI,CAAChC,QAAQ,EAAE;UACnC,IAAI;YACF,IAAIyC,OAAO,EAAE;cACX,MAAMkM,iBAAiB,GAAG,IAAI,CAACzO,gBAAgB,CAACuC,OAAO,CAAC;cACxD,IAAI,CAAC9L,MAAM,CAACuI,KAAK,CACf,kDAAkD,EAClD;gBACEkB,SAAS,EAAEuO,iBAAiB,CAACtU,EAAE;gBAC/B4J,OAAO,EAAE0K,iBAAiB,CAAC1K,OAAO,EAAE2I,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;gBACpDnL,MAAM,EAAEkN,iBAAiB,CAAClN,MAAM,EAAE2N;eACnC,CACF;cACDgE,kBAAkB,CAACzM,IAAI,CAACgI,iBAAiB,CAAC;;WAE7C,CAAC,OAAOhU,KAAK,EAAE;YACd,IAAI,CAAChE,MAAM,CAACgP,IAAI,CACd,sEAAsE,EACtEhL,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAAChE,MAAM,CAACuI,KAAK,CACf,8EAA8E,CAC/E;;MAGH;MACA,IAAImU,qBAAqB,GAAG,IAAI;MAChC,IAAIrR,IAAI,CAAC2N,WAAW,EAAE;QACpB,IAAI;UACF0D,qBAAqB,GAAG,IAAI,CAACnT,gBAAgB,CAAC8B,IAAI,CAAC2N,WAAW,CAAC;SAChE,CAAC,OAAOhV,KAAK,EAAE;UACd,IAAI,CAAChE,MAAM,CAACgP,IAAI,CACd,6DAA6D,EAC7DhL,KAAK,CACN;;;MAIL;MACA,MAAM0H,sBAAsB,GAAG;QAC7B,GAAGL,IAAI;QACPkB,GAAG,EAAElB,IAAI,CAAC3H,EAAE,IAAI2H,IAAI,CAACkB,GAAG;QACxB7I,EAAE,EAAE2H,IAAI,CAAC3H,EAAE,IAAI2H,IAAI,CAACkB,GAAG;QACvBZ,YAAY,EAAE2Q,sBAAsB;QACpCjT,QAAQ,EAAEoT,kBAAkB;QAC5BzD,WAAW,EAAE0D,qBAAqB;QAClCC,WAAW,EAAEtR,IAAI,CAACsR,WAAW,IAAI,CAAC;QAClCvQ,OAAO,EAAE,CAAC,CAACf,IAAI,CAACe,OAAO;QACvB6P,SAAS,EAAE,IAAI,CAAC3R,aAAa,CAACe,IAAI,CAAC4Q,SAAS,CAAC;QAC7CC,SAAS,EAAE,IAAI,CAAC5R,aAAa,CAACe,IAAI,CAAC6Q,SAAS;OAC7C;MAED,IAAI,CAAClc,MAAM,CAACuI,KAAK,CACf,uDAAuD,EACvD;QACEW,cAAc,EAAEwC,sBAAsB,CAAChI,EAAE;QACzCkZ,gBAAgB,EAAEN,sBAAsB,CAACzT,MAAM;QAC/CgU,YAAY,EAAEJ,kBAAkB,CAAC5T;OAClC,CACF;MAED,OAAO6C,sBAAsB;KAC9B,CAAC,OAAO1H,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,kDAAkD,EAClDA,KAAK,YAAY8E,KAAK,GAAG9E,KAAK,GAAG,IAAI8E,KAAK,CAAC+S,MAAM,CAAC7X,KAAK,CAAC,CAAC,EACzDqH,IAAI,CACL;MACD,MAAM,IAAIvC,KAAK,CACb,qCACE9E,KAAK,YAAY8E,KAAK,GAAG9E,KAAK,CAAC8H,OAAO,GAAG+P,MAAM,CAAC7X,KAAK,CACvD,EAAE,CACH;;EAEL;EACQsG,aAAaA,CAAC3H,IAA+B;IACnD,IAAI,CAACA,IAAI,EAAE,OAAO,IAAIqH,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAOrH,IAAI,KAAK,QAAQ,GAAG,IAAIqH,IAAI,CAACrH,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOqB,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgP,IAAI,CAAC,yBAAyBrM,IAAI,EAAE,EAAEqB,KAAK,CAAC;MACxD,OAAO,IAAIgG,IAAI,EAAE;;EAErB;EAEA;EACQa,QAAQA,CAAClI,IAA+B;IAC9C,IAAI,CAACA,IAAI,EAAE,OAAO,IAAIqH,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAOrH,IAAI,KAAK,QAAQ,GAAG,IAAIqH,IAAI,CAACrH,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOqB,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgP,IAAI,CAAC,+BAA+BrM,IAAI,EAAE,EAAEqB,KAAK,CAAC;MAC9D,OAAO,IAAIgG,IAAI,EAAE;;EAErB;EAOQiQ,qBAAqBA,CAACxW,YAA0B;IACtD,IAAI,CAACzD,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1B9E,YAAY,CACb;IAED,IAAI,CAACA,YAAY,EAAE;MACjB,IAAI,CAACzD,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,CAAC;MACxE,MAAM,IAAI8E,KAAK,CAAC,0BAA0B,CAAC;;IAG7C;IACA,MAAM+F,cAAc,GAAGpL,YAAY,CAACC,EAAE,IAAKD,YAAoB,CAAC8I,GAAG;IACnE,IAAI,CAACsC,cAAc,EAAE;MACnB,IAAI,CAAC7O,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BP,YAAY,CACb;MACD,MAAM,IAAIqF,KAAK,CAAC,6BAA6B,CAAC;;IAGhD,IAAI,CAACrF,YAAY,CAAC4G,SAAS,EAAE;MAC3B,IAAI,CAACrK,MAAM,CAACgP,IAAI,CACd,gBAAgB,EAChB,uDAAuD,EACvDvL,YAAY,CACb;MACDA,YAAY,CAAC4G,SAAS,GAAG,IAAIL,IAAI,EAAE;;IAGrC,IAAI;MACF,MAAMgQ,UAAU,GAAG;QACjB,GAAGvW,YAAY;QACf8I,GAAG,EAAEsC,cAAc;QACnBnL,EAAE,EAAEmL,cAAc;QAClBxE,SAAS,EAAE,IAAIL,IAAI,CAACvG,YAAY,CAAC4G,SAAS,CAAC;QAC3C,IAAI5G,YAAY,CAACuF,QAAQ,IAAI;UAC3BA,QAAQ,EAAE,IAAI,CAAC8T,eAAe,CAACrZ,YAAY,CAACuF,QAAQ;SACrD,CAAC;QACF,IAAIvF,YAAY,CAACqI,OAAO,IAAI;UAC1BA,OAAO,EAAE,IAAI,CAACiR,mBAAmB,CAACtZ,YAAY,CAACqI,OAAO;SACvD;OACF;MAED,IAAI,CAAC9L,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCyR,UAAU,CACX;MACD,OAAOA,UAAU;KAClB,CAAC,OAAOhW,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCA,KAAK,CACN;MACD,MAAMA,KAAK;;EAEf;EACQ8Y,eAAeA,CAAChS,MAAW;IACjC,OAAO;MACLpH,EAAE,EAAEoH,MAAM,CAACpH,EAAE;MACb+U,QAAQ,EAAE3N,MAAM,CAAC2N,QAAQ;MACzB,IAAI3N,MAAM,CAACgR,KAAK,IAAI;QAAEA,KAAK,EAAEhR,MAAM,CAACgR;MAAK,CAAE;KAC5C;EACH;EAEA;;;;;EAKQiB,mBAAmBA,CAACjR,OAAY;IACtC,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IAEzB,OAAO;MACLpI,EAAE,EAAEoI,OAAO,CAACpI,EAAE,IAAIoI,OAAO,CAACS,GAAG;MAC7Be,OAAO,EAAExB,OAAO,CAACwB,OAAO,IAAI,EAAE;MAC9B/F,IAAI,EAAEuE,OAAO,CAACvE,IAAI,IAAI,MAAM;MAC5B8C,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACiB,OAAO,CAACzB,SAAS,CAAC;MAC3C6N,WAAW,EAAEpM,OAAO,CAACoM,WAAW,IAAI,EAAE;MACtC,IAAIpM,OAAO,CAAChB,MAAM,IAAI;QAAEA,MAAM,EAAE,IAAI,CAACgS,eAAe,CAAChR,OAAO,CAAChB,MAAM;MAAC,CAAE;KACvE;EACH;EACA;;;;;EAKQ0C,WAAWA,CACjBrN,aAA4C,EAC5C6c,cAAA,GAA0B,IAAI;IAE9B,MAAMC,iBAAiB,GAAGpZ,KAAK,CAAC0Y,OAAO,CAACpc,aAAa,CAAC,GAClDA,aAAa,GACb,CAACA,aAAa,CAAC;IAEnB,IAAI,CAACH,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,oCAAoC0U,iBAAiB,CAACpU,MAAM,gBAAgB,CAC7E;IAED,IAAIoU,iBAAiB,CAACpU,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAAC7I,MAAM,CAACgP,IAAI,CAAC,gBAAgB,EAAE,qCAAqC,CAAC;MACzE;;IAGF;IACA,MAAMkO,kBAAkB,GAAGD,iBAAiB,CAAC5gB,MAAM,CAChD8Q,KAAK,IAAKA,KAAK,KAAKA,KAAK,CAACzJ,EAAE,IAAKyJ,KAAa,CAACZ,GAAG,CAAC,CACrD;IAED,IAAI2Q,kBAAkB,CAACrU,MAAM,KAAKoU,iBAAiB,CAACpU,MAAM,EAAE;MAC1D,IAAI,CAAC7I,MAAM,CAACgP,IAAI,CACd,gBAAgB,EAChB,SACEiO,iBAAiB,CAACpU,MAAM,GAAGqU,kBAAkB,CAACrU,MAChD,kCAAkC,CACnC;;IAGH,IAAIsU,UAAU,GAAG,CAAC;IAClB,IAAIC,YAAY,GAAG,CAAC;IAEpB;IACAF,kBAAkB,CAAC1Z,OAAO,CAAC,CAAC2J,KAAK,EAAEE,KAAK,KAAI;MAC1C,IAAI;QACF;QACA,MAAMgQ,OAAO,GAAGlQ,KAAK,CAACzJ,EAAE,IAAKyJ,KAAa,CAACZ,GAAG;QAC9C,IAAI,CAAC8Q,OAAO,EAAE;UACZ,IAAI,CAACrd,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1BmJ,KAAK,CACN;UACD;;QAGF;QACA,MAAM6M,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAAC9M,KAAK,CAAC;QAEpD;QACA,IAAI6P,cAAc,IAAI,IAAI,CAAC5c,iBAAiB,CAACgN,GAAG,CAAC4M,UAAU,CAACtW,EAAE,CAAC,EAAE;UAC/D,IAAI,CAAC1D,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,gBAAgByR,UAAU,CAACtW,EAAE,oCAAoC,CAClE;UACD0Z,YAAY,EAAE;UACd;;QAGF;QACA,IAAI,CAAChd,iBAAiB,CAACuD,GAAG,CAACqW,UAAU,CAACtW,EAAE,EAAEsW,UAAU,CAAC;QACrDmD,UAAU,EAAE;QAEZ,IAAI,CAACnd,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,sBAAsByR,UAAU,CAACtW,EAAE,WAAW,CAC/C;OACF,CAAC,OAAOM,KAAK,EAAE;QACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iCAAiCqJ,KAAK,GAAG,CAAC,GAAG,EAC7CrJ,KAAK,CACN;;IAEL,CAAC,CAAC;IAEF,IAAI,CAAChE,MAAM,CAACuI,KAAK,CACf,gBAAgB,EAChB,0BAA0B4U,UAAU,WAAWC,YAAY,oBAAoB,IAAI,CAAChd,iBAAiB,CAACyM,IAAI,EAAE,CAC7G;IAED;IACA,IAAI,CAACyQ,8BAA8B,EAAE;EACvC;EACA;;;;EAIQA,8BAA8BA,CAAA;IACpC,MAAMC,gBAAgB,GAAG1Z,KAAK,CAACtH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC0D,MAAM,EAAE,CAAC;IAEpE;IACA,MAAM4J,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CAAC4P,gBAAgB,CAAC;IAE1E,IAAI,CAACvd,MAAM,CAACuI,KAAK,CACf,cAAcmF,mBAAmB,CAAC7E,MAAM,2DAA2D,CACpG;IAED,IAAI,CAAC1I,aAAa,CAACyD,IAAI,CAAC8J,mBAAmB,CAAC;IAC5C,IAAI,CAAC3J,iBAAiB,EAAE;IACxB,IAAI,CAAC6J,+BAA+B,EAAE;EACxC;EAEA;;;EAGQ7J,iBAAiBA,CAAA;IACvB,MAAMwZ,gBAAgB,GAAG1Z,KAAK,CAACtH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC0D,MAAM,EAAE,CAAC;IACpE,MAAM0Z,mBAAmB,GAAGD,gBAAgB,CAAClhB,MAAM,CAAE6R,CAAC,IAAK,CAACA,CAAC,CAACX,MAAM,CAAC;IACrE,MAAMkC,KAAK,GAAG+N,mBAAmB,CAAC3U,MAAM;IAExC;IACA,IAAI,CAAC5I,IAAI,CAACoY,GAAG,CAAC,MAAK;MACjB,IAAI,CAAC/X,iBAAiB,CAACsD,IAAI,CAAC6L,KAAK,CAAC;MAElC;MACAtJ,MAAM,CAACsX,aAAa,CAClB,IAAIC,WAAW,CAAC,0BAA0B,EAAE;QAC1CC,MAAM,EAAE;UAAElO;QAAK;OAChB,CAAC,CACH;IACH,CAAC,CAAC;EACJ;EAEA;;;;EAIQ2K,uBAAuBA,CAAC3W,YAA0B;IACxD,IAAI,CAAC+J,WAAW,CAAC/J,YAAY,EAAE,IAAI,CAAC;EACtC;EACA;;;;;EAKQ+M,wBAAwBA,CAACoN,GAAa,EAAErQ,MAAe;IAC7DqQ,GAAG,CAACpa,OAAO,CAAEE,EAAE,IAAI;MACjB,MAAMyJ,KAAK,GAAG,IAAI,CAAC/M,iBAAiB,CAAC2P,GAAG,CAACrM,EAAE,CAAC;MAC5C,IAAIyJ,KAAK,EAAE;QACT,IAAI,CAAC/M,iBAAiB,CAACuD,GAAG,CAACD,EAAE,EAAE;UAC7B,GAAGyJ,KAAK;UACRI,MAAM;UACNxD,MAAM,EAAEwD,MAAM,GAAG,IAAIvD,IAAI,EAAE,CAACnH,WAAW,EAAE,GAAGD;SAC7C,CAAC;;IAEN,CAAC,CAAC;IACF,IAAI,CAAC0a,8BAA8B,EAAE;EACvC;EAEA;;;;;EAKQpO,4BAA4BA,CAACU,eAAyB;IAC5D5J,OAAO,CAACC,GAAG,CACT,4CAA4C,EAC5C2J,eAAe,CAAC/G,MAAM,EACtB,eAAe,CAChB;IACD7C,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3C,IAAI,CAAC7F,iBAAiB,CAACyM,IAAI,CAC5B;IAED,IAAIoC,YAAY,GAAG,CAAC;IACpBW,eAAe,CAACpM,OAAO,CAAEE,EAAE,IAAI;MAC7B,IAAI,IAAI,CAACtD,iBAAiB,CAACgN,GAAG,CAAC1J,EAAE,CAAC,EAAE;QAClCsC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEvC,EAAE,CAAC;QAChE,IAAI,CAACtD,iBAAiB,CAACya,MAAM,CAACnX,EAAE,CAAC;QACjCuL,YAAY,EAAE;OACf,MAAM;QACLjJ,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzDvC,EAAE,CACH;;IAEL,CAAC,CAAC;IAEFsC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEgJ,YAAY,EAAE,eAAe,CAAC;IAC5EjJ,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAAC7F,iBAAiB,CAACyM,IAAI,CAC5B;IAED,IAAIoC,YAAY,GAAG,CAAC,EAAE;MACpBjJ,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D,IAAI,CAACqX,8BAA8B,EAAE;;IAGvC,OAAOrO,YAAY;EACrB;EAEA;;;;;;EAMQG,mBAAmBA,CACzBpL,KAAU,EACV6Z,SAAiB,EACjBC,gBAAqB;IAErB,IAAI,CAAC9d,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,kBAAkB6Z,SAAS,GAAG,EAAE7Z,KAAK,CAAC;IAC1E,OAAOlI,EAAE,CAACgiB,gBAAgB,CAAC;EAC7B;EACA;EACAC,WAAWA,CAAC7U,cAAsB;IAChC,MAAM+B,MAAM,GAAG,IAAI,CAACgB,gBAAgB,EAAE;IACtC,IAAI,CAAChB,MAAM,EAAE;MACX,IAAI,CAACjL,MAAM,CAACgP,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAOlT,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAACiE,MAAM,CACf4J,MAAM,CAAsB;MAC3BC,QAAQ,EAAElM,qBAAqB;MAC/B0L,SAAS,EAAE;QACTkO,KAAK,EAAE;UACLpO,cAAc;UACd+B;;;KAGL,CAAC,CACDzG,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAKA,MAAM,CAAClE,IAAI,EAAEsZ,WAAW,IAAI,KAAK,CAAC,EAClD5hB,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEAkV,UAAUA,CAAC9U,cAAsB;IAC/B,MAAM+B,MAAM,GAAG,IAAI,CAACgB,gBAAgB,EAAE;IACtC,IAAI,CAAChB,MAAM,EAAE;MACX,IAAI,CAACjL,MAAM,CAACgP,IAAI,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;MACpE,OAAOlT,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAACiE,MAAM,CACf4J,MAAM,CAAqB;MAC1BC,QAAQ,EAAEjM,oBAAoB;MAC9ByL,SAAS,EAAE;QACTkO,KAAK,EAAE;UACLpO,cAAc;UACd+B;;;KAGL,CAAC,CACDzG,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAKA,MAAM,CAAClE,IAAI,EAAEuZ,UAAU,IAAI,KAAK,CAAC,EACjD7hB,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;;;;;;;EASAmV,WAAWA,CACThV,UAAkB,EAClBqE,OAAe,EACf4Q,IAAW,EACXC,WAAA,GAAmB,MAAM,EACzBjV,cAAuB;IAEvBlD,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;MAC1DgD,UAAU;MACVqE,OAAO,EAAEA,OAAO,EAAE2I,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MAClCmI,OAAO,EAAE,CAAC,CAACF,IAAI;MACfG,QAAQ,EAAEH,IAAI,EAAEpZ,IAAI;MACpBwZ,QAAQ,EAAEJ,IAAI,EAAE3W,IAAI;MACpBgX,QAAQ,EAAEL,IAAI,EAAErR,IAAI;MACpBsR,WAAW;MACXjV;KACD,CAAC;IAEF,IAAI,CAACD,UAAU,EAAE;MACf,MAAMjF,KAAK,GAAG,IAAI8E,KAAK,CAAC,yBAAyB,CAAC;MAClD9C,OAAO,CAAChC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,OAAOjI,UAAU,CAAC,MAAMiI,KAAK,CAAC;;IAGhC;IACA,MAAMoF,SAAS,GAAQ;MACrBH,UAAU;MACVqE,OAAO,EAAEA,OAAO,IAAI,EAAE;MACtB/F,IAAI,EAAE4W;KACP;IAED;IACA,IAAIjV,cAAc,EAAE;MAClBE,SAAS,CAACF,cAAc,GAAGA,cAAc;;IAG3C;IACA,IAAIgV,IAAI,EAAE;MACR9U,SAAS,CAAC8U,IAAI,GAAGA,IAAI;MACrBlY,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;QAC1DnB,IAAI,EAAEoZ,IAAI,CAACpZ,IAAI;QACfyC,IAAI,EAAE2W,IAAI,CAAC3W,IAAI;QACfsF,IAAI,EAAEqR,IAAI,CAACrR;OACZ,CAAC;;IAGJ7G,OAAO,CAACC,GAAG,CACT,sDAAsD,EACtDmD,SAAS,CACV;IAED,OAAO,IAAI,CAACrJ,MAAM,CACf4J,MAAM,CAAsB;MAC3BC,QAAQ,EAAE7M,qBAAqB;MAC/BqM,SAAS;MACToV,OAAO,EAAE;QACPC,YAAY,EAAE,CAAC,CAACP,IAAI,CAAE;;KAEzB,CAAC,CACD1Z,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb3C,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjD0C,MAAM,CACP;MAED,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEwZ,WAAW,EAAE;QAC7B,MAAM,IAAInV,KAAK,CAAC,sCAAsC,CAAC;;MAGzD,MAAMgD,OAAO,GAAGnD,MAAM,CAAClE,IAAI,CAACwZ,WAAW;MACvCjY,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;QAC5DvC,EAAE,EAAEoI,OAAO,CAACpI,EAAE;QACd6D,IAAI,EAAEuE,OAAO,CAACvE,IAAI;QAClB+F,OAAO,EAAExB,OAAO,CAACwB,OAAO,EAAE2I,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1CyI,cAAc,EAAE,CAAC,CAAC5S,OAAO,CAACoM,WAAW,EAAErP;OACxC,CAAC;MAEF;MACA,MAAMmP,iBAAiB,GAAG,IAAI,CAACzO,gBAAgB,CAACuC,OAAO,CAAC;MACxD9F,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzC+R,iBAAiB,CAClB;MAED,OAAOA,iBAAiB;IAC1B,CAAC,CAAC,EACF7b,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAElD;MACA,IAAI2a,YAAY,GAAG,mCAAmC;MACtD,IAAI3a,KAAK,CAAC8J,YAAY,EAAE;QACtB6Q,YAAY,GAAG,4BAA4B;OAC5C,MAAM,IAAI3a,KAAK,CAAC6J,aAAa,EAAEhF,MAAM,GAAG,CAAC,EAAE;QAC1C8V,YAAY,GAAG3a,KAAK,CAAC6J,aAAa,CAAC,CAAC,CAAC,CAAC/B,OAAO,IAAI6S,YAAY;;MAG/D,OAAO5iB,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC6V,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;EAGAC,iBAAiBA,CAACvU,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IACrC,IAAI;MACF,MAAM1H,IAAI,GAAG0H,SAAS,YAAYL,IAAI,GAAGK,SAAS,GAAG,IAAIL,IAAI,CAACK,SAAS,CAAC;MACxE,OAAO1H,IAAI,CAACkc,kBAAkB,CAAC,EAAE,EAAE;QACjCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;OACT,CAAC;KACH,CAAC,OAAOhb,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGAib,gBAAgBA,CAACjD,UAAqC;IACpD,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IACjC,MAAMkD,cAAc,GAClBlD,UAAU,YAAYhS,IAAI,GAAGgS,UAAU,GAAG,IAAIhS,IAAI,CAACgS,UAAU,CAAC;IAChE,MAAMlG,GAAG,GAAG,IAAI9L,IAAI,EAAE;IACtB,MAAMmV,SAAS,GACbxO,IAAI,CAACyO,GAAG,CAACtJ,GAAG,CAAC4E,OAAO,EAAE,GAAGwE,cAAc,CAACxE,OAAO,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAIyE,SAAS,GAAG,EAAE,EAAE;MAClB,OAAO,UAAUD,cAAc,CAACL,kBAAkB,CAAC,EAAE,EAAE;QACrDC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;;IAEN,OAAO,UAAUG,cAAc,CAACG,kBAAkB,EAAE,EAAE;EACxD;EAEA;;;EAGAC,iBAAiBA,CAACjV,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IAErC,IAAI;MACF,MAAM1H,IAAI,GAAG0H,SAAS,YAAYL,IAAI,GAAGK,SAAS,GAAG,IAAIL,IAAI,CAACK,SAAS,CAAC;MACxE,MAAMkV,KAAK,GAAG,IAAIvV,IAAI,EAAE;MAExB,IAAIrH,IAAI,CAAC6c,YAAY,EAAE,KAAKD,KAAK,CAACC,YAAY,EAAE,EAAE;QAChD,OAAO7c,IAAI,CAACkc,kBAAkB,CAAC,EAAE,EAAE;UACjCC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC;;MAGJ,MAAMU,SAAS,GAAG,IAAIzV,IAAI,CAACuV,KAAK,CAAC;MACjCE,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;MAE1C,IAAIhd,IAAI,CAAC6c,YAAY,EAAE,KAAKC,SAAS,CAACD,YAAY,EAAE,EAAE;QACpD,OAAO,SAAS7c,IAAI,CAACkc,kBAAkB,CAAC,EAAE,EAAE;UAC1CC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC,EAAE;;MAGN,MAAMa,GAAG,GAAGjd,IAAI,CACb0c,kBAAkB,CAAC,OAAO,EAAE;QAAEQ,OAAO,EAAE;MAAO,CAAE,CAAC,CACjDC,WAAW,EAAE;MAChB,OAAO,GAAGF,GAAG,MAAMjd,IAAI,CAACkc,kBAAkB,CAAC,EAAE,EAAE;QAC7CC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;KACL,CAAC,OAAO/a,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGA+b,oBAAoBA,CAAC1W,QAAe,EAAEgE,KAAa;IACjD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,IAAI;MACF,MAAM2S,UAAU,GAAG3W,QAAQ,CAACgE,KAAK,CAAC;MAClC,MAAM4S,OAAO,GAAG5W,QAAQ,CAACgE,KAAK,GAAG,CAAC,CAAC;MAEnC,IAAI,CAAC2S,UAAU,EAAE3V,SAAS,IAAI,CAAC4V,OAAO,EAAE5V,SAAS,EAAE,OAAO,IAAI;MAE9D,MAAM6V,WAAW,GAAG,IAAI,CAACC,oBAAoB,CAACH,UAAU,CAAC3V,SAAS,CAAC;MACnE,MAAM+V,QAAQ,GAAG,IAAI,CAACD,oBAAoB,CAACF,OAAO,CAAC5V,SAAS,CAAC;MAE7D,OAAO6V,WAAW,KAAKE,QAAQ;KAChC,CAAC,OAAOpc,KAAK,EAAE;MACd,OAAO,KAAK;;EAEhB;EAEQmc,oBAAoBA,CAAC9V,SAAoC;IAC/D,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IACrC,IAAI;MACF,OAAO,CACLA,SAAS,YAAYL,IAAI,GAAGK,SAAS,GAAG,IAAIL,IAAI,CAACK,SAAS,CAAC,EAC3DmV,YAAY,EAAE;KACjB,CAAC,OAAOxb,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGAqc,WAAWA,CAACC,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAC/B,IAAIA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,UAAU;IACpD,IAAID,QAAQ,CAAC9T,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,aAAa;IAClD,IAAI8T,QAAQ,CAAC9T,QAAQ,CAAC,MAAM,CAAC,IAAI8T,QAAQ,CAAC9T,QAAQ,CAAC,QAAQ,CAAC,EAC1D,OAAO,cAAc;IACvB,IAAI8T,QAAQ,CAAC9T,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAI8T,QAAQ,CAAC9T,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,oBAAoB;IAChE,IAAI8T,QAAQ,CAAC9T,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAI8T,QAAQ,CAAC9T,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAI8T,QAAQ,CAAC9T,QAAQ,CAAC,KAAK,CAAC,IAAI8T,QAAQ,CAAC9T,QAAQ,CAAC,YAAY,CAAC,EAC7D,OAAO,iBAAiB;IAC1B,OAAO,SAAS;EAClB;EAEA;;;EAGAgU,WAAWA,CAACF,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,MAAM;IAE5B,MAAMG,OAAO,GAA2B;MACtC,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,KAAK;MACxB,oBAAoB,EAAE,UAAU;MAChC,yEAAyE,EACvE,UAAU;MACZ,0BAA0B,EAAE,OAAO;MACnC,mEAAmE,EACjE,OAAO;MACT,+BAA+B,EAAE,YAAY;MAC7C,2EAA2E,EACzE,YAAY;MACd,QAAQ,EAAE,OAAO;MACjB,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,aAAa;MAChC,8BAA8B,EAAE;KACjC;IAED,KAAK,MAAM,CAACC,GAAG,EAAE/R,KAAK,CAAC,IAAIhJ,MAAM,CAACgb,OAAO,CAACF,OAAO,CAAC,EAAE;MAClD,IAAIH,QAAQ,CAAC9T,QAAQ,CAACkU,GAAG,CAAC,EAAE,OAAO/R,KAAK;;IAE1C,OAAO,MAAM;EACf;EAEA;;;EAGAiS,QAAQA,CAAC9U,OAAY;IACnB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACoM,WAAW,IAAIpM,OAAO,CAACoM,WAAW,CAACrP,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,KAAK;;IAGd,MAAMgY,UAAU,GAAG/U,OAAO,CAACoM,WAAW,CAAC,CAAC,CAAC;IACzC,IAAI,CAAC2I,UAAU,IAAI,CAACA,UAAU,CAACtZ,IAAI,EAAE;MACnC,OAAO,KAAK;;IAGd,MAAMA,IAAI,GAAGsZ,UAAU,CAACtZ,IAAI,CAACwO,QAAQ,EAAE;IACvC,OAAOxO,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO;EAC7C;EAEA;;;EAGAuZ,cAAcA,CAAChV,OAAY;IACzB,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAE1B;IACA,IACEA,OAAO,CAACvE,IAAI,KAAK/K,WAAW,CAACyb,aAAa,IAC1CnM,OAAO,CAACvE,IAAI,KAAK/K,WAAW,CAACyb,aAAa,EAC1C;MACA,OAAO,IAAI;;IAGb;IACA,IAAInM,OAAO,CAACoM,WAAW,IAAIpM,OAAO,CAACoM,WAAW,CAACrP,MAAM,GAAG,CAAC,EAAE;MACzD,OAAOiD,OAAO,CAACoM,WAAW,CAACC,IAAI,CAAEC,GAAQ,IAAI;QAC3C,MAAM7Q,IAAI,GAAG6Q,GAAG,CAAC7Q,IAAI,EAAEwO,QAAQ,EAAE;QACjC,OACExO,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACvBuE,OAAO,CAAC8P,QAAQ,EAAEkF,cAAc,KAC9BvZ,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO,CAAE;MAE7C,CAAC,CAAC;;IAGJ;IACA,OAAO,CAAC,CAACuE,OAAO,CAAC8P,QAAQ,EAAEkF,cAAc;EAC3C;EAEA;;;EAGAC,kBAAkBA,CAACjV,OAAY;IAC7B,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACoM,WAAW,IAAIpM,OAAO,CAACoM,WAAW,CAACrP,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX,MAAMmY,eAAe,GAAGlV,OAAO,CAACoM,WAAW,CAAC/L,IAAI,CAAEiM,GAAQ,IAAI;MAC5D,MAAM7Q,IAAI,GAAG6Q,GAAG,CAAC7Q,IAAI,EAAEwO,QAAQ,EAAE;MACjC,OACExO,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;IAEpB,CAAC,CAAC;IAEF,OAAOyZ,eAAe,EAAErF,GAAG,IAAI,EAAE;EACnC;EAEA;;;EAGAsF,uBAAuBA,CAACnV,OAAY;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,CAAC;IAEtB;IACA,IAAIA,OAAO,CAAC8P,QAAQ,EAAE1U,QAAQ,EAAE;MAC9B,OAAO4E,OAAO,CAAC8P,QAAQ,CAAC1U,QAAQ;;IAGlC;IACA,IAAI4E,OAAO,CAACoM,WAAW,IAAIpM,OAAO,CAACoM,WAAW,CAACrP,MAAM,GAAG,CAAC,EAAE;MACzD,MAAMmY,eAAe,GAAGlV,OAAO,CAACoM,WAAW,CAAC/L,IAAI,CAAEiM,GAAQ,IAAI;QAC5D,MAAM7Q,IAAI,GAAG6Q,GAAG,CAAC7Q,IAAI,EAAEwO,QAAQ,EAAE;QACjC,OACExO,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;MAEpB,CAAC,CAAC;MAEF,IAAIyZ,eAAe,IAAIA,eAAe,CAAC9Z,QAAQ,EAAE;QAC/C,OAAO8Z,eAAe,CAAC9Z,QAAQ;;;IAInC,OAAO,CAAC;EACV;EAEA;;;EAGAga,iBAAiBA,CAAC7T,KAAa;IAC7B,MAAM8T,OAAO,GAAG,CACd,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CACxE;IACD,OAAOA,OAAO,CAAC9T,KAAK,GAAG8T,OAAO,CAACtY,MAAM,CAAC;EACxC;EAEA;;;EAGAuY,mBAAmBA,CAACC,OAAe;IACjC,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,CAAC,EAAE;MAC7B,OAAO,MAAM;;IAGf,MAAMC,OAAO,GAAG3Q,IAAI,CAAC4Q,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMG,gBAAgB,GAAG7Q,IAAI,CAAC4Q,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGC,OAAO,IAAIE,gBAAgB,CAACzL,QAAQ,EAAE,CAAC0L,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA;;;EAGAC,WAAWA,CAAC5V,OAAY;IACtB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACoM,WAAW,IAAIpM,OAAO,CAACoM,WAAW,CAACrP,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX,MAAMgY,UAAU,GAAG/U,OAAO,CAACoM,WAAW,CAAC,CAAC,CAAC;IACzC,OAAO2I,UAAU,EAAElF,GAAG,IAAI,EAAE;EAC9B;EAEA;;;EAGAgG,cAAcA,CAAC7V,OAAY;IACzB,IAAI,CAACA,OAAO,EAAE,OAAOtP,WAAW,CAACgc,IAAI;IAErC,IAAI;MACF,IAAI1M,OAAO,CAACvE,IAAI,EAAE;QAChB,MAAMqa,OAAO,GAAG9V,OAAO,CAACvE,IAAI,CAACwO,QAAQ,EAAE;QACvC,IAAI6L,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UAC5C,OAAOplB,WAAW,CAACgc,IAAI;SACxB,MAAM,IAAIoJ,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAOplB,WAAW,CAACqlB,KAAK;SACzB,MAAM,IAAID,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UACnD,OAAOplB,WAAW,CAACslB,IAAI;SACxB,MAAM,IAAIF,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAOplB,WAAW,CAAC4Y,KAAK;SACzB,MAAM,IAAIwM,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAOplB,WAAW,CAACulB,KAAK;SACzB,MAAM,IAAIH,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,EAAE;UACvD,OAAOplB,WAAW,CAACwlB,MAAM;;;MAI7B,IAAIlW,OAAO,CAACoM,WAAW,EAAErP,MAAM,EAAE;QAC/B,MAAMgY,UAAU,GAAG/U,OAAO,CAACoM,WAAW,CAAC,CAAC,CAAC;QACzC,IAAI2I,UAAU,IAAIA,UAAU,CAACtZ,IAAI,EAAE;UACjC,MAAM0a,iBAAiB,GAAGpB,UAAU,CAACtZ,IAAI,CAACwO,QAAQ,EAAE;UAEpD,IAAIkM,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE,OAAOzlB,WAAW,CAACqlB,KAAK;WACzB,MAAM,IACLI,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAOzlB,WAAW,CAACslB,IAAI;WACxB,MAAM,IACLG,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAOzlB,WAAW,CAAC4Y,KAAK;WACzB,MAAM,IACL6M,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAOzlB,WAAW,CAACulB,KAAK;;;QAI5B,OAAOvlB,WAAW,CAACslB,IAAI;;MAGzB,OAAOtlB,WAAW,CAACgc,IAAI;KACxB,CAAC,OAAOxU,KAAK,EAAE;MACd,OAAOxH,WAAW,CAACgc,IAAI;;EAE3B;EAEA;;;EAGA0J,eAAeA,CAAA;IACb,OAAO,CACL,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL;EACH;EAEA;;;EAGAC,mBAAmBA,CAACrW,OAAY,EAAEE,aAA4B;IAC5D,IAAI,CAACF,OAAO,EAAE;MACZ,OAAO,kCAAkC;;IAG3C,IAAI;MACF,MAAMsW,aAAa,GACjBtW,OAAO,CAAChB,MAAM,EAAEpH,EAAE,KAAKsI,aAAa,IACpCF,OAAO,CAAChB,MAAM,EAAEyB,GAAG,KAAKP,aAAa,IACrCF,OAAO,CAAC9C,QAAQ,KAAKgD,aAAa;MAEpC,MAAMqW,SAAS,GAAGD,aAAa,GAC3B,kDAAkD,GAClD,qDAAqD;MAEzD,MAAMjE,WAAW,GAAG,IAAI,CAACwD,cAAc,CAAC7V,OAAO,CAAC;MAEhD,IAAIA,OAAO,CAACoM,WAAW,IAAIpM,OAAO,CAACoM,WAAW,CAACrP,MAAM,GAAG,CAAC,EAAE;QACzD,MAAMgY,UAAU,GAAG/U,OAAO,CAACoM,WAAW,CAAC,CAAC,CAAC;QACzC,IAAI2I,UAAU,IAAIA,UAAU,CAACtZ,IAAI,EAAE;UACjC,MAAM0a,iBAAiB,GAAGpB,UAAU,CAACtZ,IAAI,CAACwO,QAAQ,EAAE;UACpD,IAAIkM,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE,OAAO,cAAc;WACtB,MAAM,IACLA,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAO,GAAGI,SAAS,MAAM;;;;MAK/B;MAEA,OAAO,GAAGA,SAAS,wDAAwD;KAC5E,CAAC,OAAOre,KAAK,EAAE;MACd,OAAO,gEAAgE;;EAE3E;EAEA;EACA;EACA;EAEA;;;;;;;EAOA8M,YAAYA,CACVC,WAAmB,EACnBC,QAAkB,EAClB9H,cAAuB;IAEvBlD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;MAClD8K,WAAW;MACXC,QAAQ;MACR9H;KACD,CAAC;IAEF,IAAI,CAAC6H,WAAW,EAAE;MAChB,MAAM/M,KAAK,GAAG,IAAI8E,KAAK,CAAC,0BAA0B,CAAC;MACnD9C,OAAO,CAAChC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,OAAOjI,UAAU,CAAC,MAAMiI,KAAK,CAAC;;IAGhC,MAAMoF,SAAS,GAAG;MAChB2H,WAAW;MACXxJ,IAAI,EAAEyJ,QAAQ;MACd9H;KACD;IAEDlD,OAAO,CAACC,GAAG,CACT,qDAAqD,EACrDmD,SAAS,CACV;IAED,OAAO,IAAI,CAACrJ,MAAM,CACf4J,MAAM,CAAyB;MAC9BC,QAAQ,EAAExK,sBAAsB;MAChCgK;KACD,CAAC,CACD5E,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb3C,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjD0C,MAAM,CACP;MAED,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEqM,YAAY,EAAE;QAC9B,MAAM,IAAIhI,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMnE,IAAI,GAAGgE,MAAM,CAAClE,IAAI,CAACqM,YAAY;MACrC9K,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;QAC/CvC,EAAE,EAAEiB,IAAI,CAACjB,EAAE;QACX6D,IAAI,EAAE5C,IAAI,CAAC4C,IAAI;QACfsM,MAAM,EAAElP,IAAI,CAACkP,MAAM;QACnBjB,MAAM,EAAEjO,IAAI,CAACiO,MAAM,EAAE6F,QAAQ;QAC7B6J,SAAS,EAAE3d,IAAI,CAAC2d,SAAS,EAAE7J;OAC5B,CAAC;MAEF;MACA,IAAI,CAAC9X,UAAU,CAACiD,IAAI,CAACe,IAAI,CAAC;MAE1B,OAAOA,IAAI;IACb,CAAC,CAAC,EACFxI,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAElD,IAAI2a,YAAY,GAAG,wCAAwC;MAC3D,IAAI3a,KAAK,CAAC8J,YAAY,EAAE;QACtB6Q,YAAY,GAAG,4BAA4B;OAC5C,MAAM,IAAI3a,KAAK,CAAC6J,aAAa,EAAEhF,MAAM,GAAG,CAAC,EAAE;QAC1C8V,YAAY,GAAG3a,KAAK,CAAC6J,aAAa,CAAC,CAAC,CAAC,CAAC/B,OAAO,IAAI6S,YAAY;;MAG/D,OAAO5iB,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC6V,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKApM,UAAUA,CAAC3R,YAA0B;IACnCoF,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAErF,YAAY,CAAC8C,EAAE,CAAC;IAEnE,OAAO,IAAI,CAAC3D,MAAM,CACf4J,MAAM,CAAuB;MAC5BC,QAAQ,EAAEtK,oBAAoB;MAC9B8J,SAAS,EAAE;QACT+I,MAAM,EAAEvR,YAAY,CAAC8C;;KAExB,CAAC,CACDc,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb3C,OAAO,CAACC,GAAG,CACT,gDAAgD,EAChD0C,MAAM,CACP;MAED,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE8N,UAAU,EAAE;QAC5B,MAAM,IAAIzJ,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMnE,IAAI,GAAGgE,MAAM,CAAClE,IAAI,CAAC8N,UAAU;MAEnC;MACA,IAAI,CAAC5R,UAAU,CAACiD,IAAI,CAACe,IAAI,CAAC;MAC1B,IAAI,CAAC/D,YAAY,CAACgD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MAE9B;MACA,IAAI,CAAC4B,IAAI,CAAC,UAAU,CAAC;MACrB,IAAI,CAACZ,IAAI,CAAC,gBAAgB,CAAC;MAE3B,OAAOD,IAAI;IACb,CAAC,CAAC,EACFxI,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,yCAAyC,CAAC,CAC3D;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;;;;EAMA+J,UAAUA,CAACV,MAAc,EAAEW,MAAe;IACxC9M,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEkM,MAAM,EAAEW,MAAM,CAAC;IAElE,OAAO,IAAI,CAAC/S,MAAM,CACf4J,MAAM,CAA8B;MACnCC,QAAQ,EAAErK,oBAAoB;MAC9B6J,SAAS,EAAE;QACT+I,MAAM;QACNW,MAAM,EAAEA,MAAM,IAAI;;KAErB,CAAC,CACDtO,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb3C,OAAO,CAACC,GAAG,CACT,gDAAgD,EAChD0C,MAAM,CACP;MAED,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEoO,UAAU,EAAE;QAC5B,MAAM,IAAI/J,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAAClI,YAAY,CAACgD,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACjD,UAAU,CAACiD,IAAI,CAAC,IAAI,CAAC;MAE1B;MACA,IAAI,CAAC4B,IAAI,CAAC,UAAU,CAAC;MAErB,OAAOmD,MAAM,CAAClE,IAAI,CAACoO,UAAU;IAC/B,CAAC,CAAC,EACF1W,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOjI,UAAU,CAAC,MAAM,IAAI+M,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAiK,OAAOA,CAACZ,MAAc;IACpBnM,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEkM,MAAM,CAAC;IAEvD,OAAO,IAAI,CAACpS,MAAM,CACf4J,MAAM,CAA2B;MAChCC,QAAQ,EAAEpK,iBAAiB;MAC3B4J,SAAS,EAAE;QACT+I;;KAEH,CAAC,CACD3N,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb3C,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE0C,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEsO,OAAO,EAAE;QACzB,MAAM,IAAIjK,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAACnI,UAAU,CAACiD,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAAChD,YAAY,CAACgD,IAAI,CAAC,IAAI,CAAC;MAE5B;MACA,IAAI,CAAC8B,aAAa,EAAE;MACpB,IAAI,CAACd,IAAI,CAAC,UAAU,CAAC;MAErB,OAAO+D,MAAM,CAAClE,IAAI,CAACsO,OAAO;IAC5B,CAAC,CAAC,EACF5W,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC9C,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOAmK,WAAWA,CACTd,MAAc,EACdoQ,WAAqB,EACrBC,WAAqB;IAErBxc,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;MACjDkM,MAAM;MACNoQ,WAAW;MACXC;KACD,CAAC;IAEF,OAAO,IAAI,CAACziB,MAAM,CACf4J,MAAM,CAAmC;MACxCC,QAAQ,EAAEnK,0BAA0B;MACpC2J,SAAS,EAAE;QACT+I,MAAM;QACNoQ,WAAW;QACXC;;KAEH,CAAC,CACDhe,IAAI,CACHtI,GAAG,CAAEyM,MAAM,IAAI;MACb3C,OAAO,CAACC,GAAG,CACT,gDAAgD,EAChD0C,MAAM,CACP;MAED,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE6O,eAAe,EAAE;QACjC,MAAM,IAAIxK,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,OAAOH,MAAM,CAAClE,IAAI,CAAC6O,eAAe;IACpC,CAAC,CAAC,EACFnX,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOjI,UAAU,CACf,MAAM,IAAI+M,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;EACA2Z,oBAAoBA,CAAA;IAClB,IAAI,CAACjiB,aAAa,CAACgD,OAAO,CAAEkV,GAAG,IAAKA,GAAG,CAACgK,WAAW,EAAE,CAAC;IACtD,IAAI,CAACliB,aAAa,GAAG,EAAE;IACvB,IAAI,IAAI,CAAC8Z,eAAe,EAAE;MACxBqI,aAAa,CAAC,IAAI,CAACrI,eAAe,CAAC;;IAErC,IAAI,CAACla,iBAAiB,CAACmD,KAAK,EAAE;IAC9B,IAAI,CAACvD,MAAM,CAACuI,KAAK,CAAC,+BAA+B,CAAC;EACpD;EAEAqa,WAAWA,CAAA;IACT,IAAI,CAACH,oBAAoB,EAAE;EAC7B;;;uBAluJW5iB,cAAc,EAAAgjB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAM,MAAA;IAAA;EAAA;;;aAAdtjB,cAAc;MAAAujB,OAAA,EAAdvjB,cAAc,CAAAwjB,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}