{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/rendus.service\";\nimport * as i4 from \"@angular/common\";\nfunction EditEvaluationComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"div\", 12);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EditEvaluationComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"i\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EditEvaluationComponent_div_17_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"label\");\n    i0.ɵɵtext(2, \"Fichier soumis:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 61);\n    i0.ɵɵelement(4, \"i\", 62);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r3.getFileUrl(ctx_r3.rendu.fichierRendu), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getFileName(ctx_r3.rendu.fichierRendu), \" \");\n  }\n}\nfunction EditEvaluationComponent_div_17_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"small\", 64);\n    i0.ɵɵtext(2, \"Les commentaires sont obligatoires\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EditEvaluationComponent_div_17_i_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 65);\n  }\n}\nfunction EditEvaluationComponent_div_17_i_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 66);\n  }\n}\nfunction EditEvaluationComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"h2\", 17);\n    i0.ɵɵelement(3, \"i\", 18);\n    i0.ɵɵtext(4, \" Informations sur le rendu \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 19)(6, \"div\", 20)(7, \"label\");\n    i0.ɵɵtext(8, \"Projet:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 20)(12, \"label\");\n    i0.ɵɵtext(13, \"\\u00C9tudiant:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 20)(17, \"label\");\n    i0.ɵɵtext(18, \"Groupe:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 20)(22, \"label\");\n    i0.ɵɵtext(23, \"Date de soumission:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 21)(28, \"label\");\n    i0.ɵɵtext(29, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, EditEvaluationComponent_div_17_div_32_Template, 6, 2, \"div\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"form\", 23);\n    i0.ɵɵlistener(\"ngSubmit\", function EditEvaluationComponent_div_17_Template_form_ngSubmit_33_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onSubmit());\n    });\n    i0.ɵɵelementStart(34, \"div\", 24)(35, \"h3\", 17);\n    i0.ɵɵelement(36, \"i\", 25);\n    i0.ɵɵtext(37, \" Crit\\u00E8res d'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 26)(39, \"div\", 27)(40, \"div\", 28)(41, \"label\", 29);\n    i0.ɵɵelement(42, \"i\", 30);\n    i0.ɵɵtext(43, \" Structure et organisation du code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 31);\n    i0.ɵɵelement(45, \"input\", 32);\n    i0.ɵɵelementStart(46, \"span\", 33);\n    i0.ɵɵtext(47, \"/ 5\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"small\", 34);\n    i0.ɵɵtext(49, \"Qualit\\u00E9 de l'organisation et de la structure du code\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 28)(51, \"label\", 35);\n    i0.ɵɵelement(52, \"i\", 36);\n    i0.ɵɵtext(53, \" Bonnes pratiques \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 31);\n    i0.ɵɵelement(55, \"input\", 37);\n    i0.ɵɵelementStart(56, \"span\", 33);\n    i0.ɵɵtext(57, \"/ 5\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"small\", 34);\n    i0.ɵɵtext(59, \"Respect des conventions et bonnes pratiques\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 28)(61, \"label\", 38);\n    i0.ɵɵelement(62, \"i\", 39);\n    i0.ɵɵtext(63, \" Fonctionnalit\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"div\", 31);\n    i0.ɵɵelement(65, \"input\", 40);\n    i0.ɵɵelementStart(66, \"span\", 33);\n    i0.ɵɵtext(67, \"/ 5\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"small\", 34);\n    i0.ɵɵtext(69, \"Fonctionnement correct et complet\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"div\", 28)(71, \"label\", 41);\n    i0.ɵɵelement(72, \"i\", 42);\n    i0.ɵɵtext(73, \" Originalit\\u00E9 et cr\\u00E9ativit\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"div\", 31);\n    i0.ɵɵelement(75, \"input\", 43);\n    i0.ɵɵelementStart(76, \"span\", 33);\n    i0.ɵɵtext(77, \"/ 5\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"small\", 34);\n    i0.ɵɵtext(79, \"Innovation et cr\\u00E9ativit\\u00E9 dans la solution\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(80, \"div\", 44)(81, \"div\", 45)(82, \"span\", 46);\n    i0.ɵɵtext(83, \"Score total:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"span\", 47);\n    i0.ɵɵtext(85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"span\", 48);\n    i0.ɵɵtext(87);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"div\", 49);\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(90, \"div\", 50)(91, \"label\", 51);\n    i0.ɵɵelement(92, \"i\", 52);\n    i0.ɵɵtext(93, \" Commentaires et observations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(94, \"textarea\", 53);\n    i0.ɵɵtemplate(95, EditEvaluationComponent_div_17_div_95_Template, 3, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"div\", 55)(97, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function EditEvaluationComponent_div_17_Template_button_click_97_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.annuler());\n    });\n    i0.ɵɵelement(98, \"i\", 57);\n    i0.ɵɵtext(99, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"button\", 58);\n    i0.ɵɵtemplate(101, EditEvaluationComponent_div_17_i_101_Template, 1, 0, \"i\", 59);\n    i0.ɵɵtemplate(102, EditEvaluationComponent_div_17_i_102_Template, 1, 0, \"i\", 60);\n    i0.ɵɵtext(103);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_10_0;\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate((ctx_r2.rendu.projet == null ? null : ctx_r2.rendu.projet.titre) || \"Non sp\\u00E9cifi\\u00E9\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.nom, \" \", ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.groupe) || \"Non sp\\u00E9cifi\\u00E9\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(26, 17, ctx_r2.rendu.dateSoumission, \"dd/MM/yyyy \\u00E0 HH:mm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.rendu.description || \"Aucune description\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.fichierRendu);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.evaluationForm);\n    i0.ɵɵadvance(52);\n    i0.ɵɵtextInterpolate(ctx_r2.getScoreTotal());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"/ \", ctx_r2.getScoreMaximum(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.getScoreTotal() / ctx_r2.getScoreMaximum() * 100).toFixed(1), \"% \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r2.evaluationForm.get(\"commentaires\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r2.evaluationForm.get(\"commentaires\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isSubmitting);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.evaluationForm.invalid || ctx_r2.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isSubmitting ? \"Enregistrement...\" : \"Enregistrer l'\\u00E9valuation\", \" \");\n  }\n}\nexport class EditEvaluationComponent {\n  constructor(fb, route, router, rendusService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.rendusService = rendusService;\n    this.renduId = '';\n    this.rendu = null;\n    this.isLoading = true;\n    this.isSubmitting = false;\n    this.error = '';\n    this.evaluationForm = this.fb.group({\n      scores: this.fb.group({\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n      }),\n      commentaires: ['', Validators.required]\n    });\n  }\n  ngOnInit() {\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n    if (this.renduId) {\n      this.loadRendu();\n    } else {\n      this.error = 'ID de rendu manquant';\n      this.isLoading = false;\n    }\n  }\n  loadRendu() {\n    this.isLoading = true;\n    this.rendusService.getRenduById(this.renduId).subscribe({\n      next: data => {\n        this.rendu = data;\n        // Remplir le formulaire avec les données existantes\n        if (this.rendu.evaluation && this.rendu.evaluation.scores) {\n          this.evaluationForm.patchValue({\n            scores: {\n              structure: this.rendu.evaluation.scores.structure || 0,\n              pratiques: this.rendu.evaluation.scores.pratiques || 0,\n              fonctionnalite: this.rendu.evaluation.scores.fonctionnalite || 0,\n              originalite: this.rendu.evaluation.scores.originalite || 0\n            },\n            commentaires: this.rendu.evaluation.commentaires || ''\n          });\n        }\n        this.isLoading = false;\n      },\n      error: err => {\n        this.error = 'Erreur lors du chargement du rendu';\n        this.isLoading = false;\n        console.error(err);\n      }\n    });\n  }\n  onSubmit() {\n    if (this.evaluationForm.invalid) {\n      return;\n    }\n    this.isSubmitting = true;\n    const evaluationData = this.evaluationForm.value;\n    // Assurez-vous que renduId est disponible\n    if (!this.renduId) {\n      this.error = \"ID du rendu manquant\";\n      this.isSubmitting = false;\n      return;\n    }\n    this.rendusService.updateEvaluation(this.renduId, evaluationData).subscribe({\n      next: response => {\n        this.isSubmitting = false;\n        // Redirection vers la page de liste des rendus après succès\n        this.router.navigate(['/admin/projects/list-rendus']);\n      },\n      error: err => {\n        this.error = `Erreur lors de la mise à jour de l'évaluation: ${err.message || 'Erreur inconnue'}`;\n        this.isSubmitting = false;\n        console.error(err);\n      }\n    });\n  }\n  getScoreTotal() {\n    const scores = this.evaluationForm.get('scores')?.value;\n    if (!scores) return 0;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreMaximum() {\n    return 20; // 4 critères x 5 points maximum\n  }\n\n  annuler() {\n    this.router.navigate(['/admin/projects/list-rendus']);\n  }\n  // Méthodes pour gérer les fichiers\n  getFileUrl(filePath) {\n    if (!filePath) return '';\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    // Utiliser la route spécifique pour le téléchargement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n  getFileName(filePath) {\n    if (!filePath) return 'Fichier';\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    return filePath;\n  }\n  static {\n    this.ɵfac = function EditEvaluationComponent_Factory(t) {\n      return new (t || EditEvaluationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RendusService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EditEvaluationComponent,\n      selectors: [[\"app-edit-evaluation\"]],\n      decls: 18,\n      vars: 3,\n      consts: [[1, \"evaluation-container\"], [1, \"header-section\"], [1, \"page-title\"], [1, \"breadcrumb\"], [\"routerLink\", \"/admin/projects\", 1, \"breadcrumb-link\"], [1, \"breadcrumb-separator\"], [\"routerLink\", \"/admin/projects/list-rendus\", 1, \"breadcrumb-link\"], [1, \"breadcrumb-current\"], [\"class\", \"loading-spinner\", 4, \"ngIf\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"class\", \"main-content\", 4, \"ngIf\"], [1, \"loading-spinner\"], [1, \"spinner\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"main-content\"], [1, \"rendu-info-card\"], [1, \"card-title\"], [1, \"fas\", \"fa-file-alt\"], [1, \"info-grid\"], [1, \"info-item\"], [1, \"info-item\", \"full-width\"], [\"class\", \"info-item full-width\", 4, \"ngIf\"], [1, \"evaluation-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-card\"], [1, \"fas\", \"fa-star\"], [\"formGroupName\", \"scores\", 1, \"scores-section\"], [1, \"score-grid\"], [1, \"score-item\"], [\"for\", \"structure\", 1, \"score-label\"], [1, \"fas\", \"fa-code\"], [1, \"score-input-container\"], [\"id\", \"structure\", \"type\", \"number\", \"formControlName\", \"structure\", \"min\", \"0\", \"max\", \"5\", \"step\", \"0.5\", \"placeholder\", \"0\", 1, \"score-input\"], [1, \"score-max\"], [1, \"score-description\"], [\"for\", \"pratiques\", 1, \"score-label\"], [1, \"fas\", \"fa-check-circle\"], [\"id\", \"pratiques\", \"type\", \"number\", \"formControlName\", \"pratiques\", \"min\", \"0\", \"max\", \"5\", \"step\", \"0.5\", \"placeholder\", \"0\", 1, \"score-input\"], [\"for\", \"fonctionnalite\", 1, \"score-label\"], [1, \"fas\", \"fa-cogs\"], [\"id\", \"fonctionnalite\", \"type\", \"number\", \"formControlName\", \"fonctionnalite\", \"min\", \"0\", \"max\", \"5\", \"step\", \"0.5\", \"placeholder\", \"0\", 1, \"score-input\"], [\"for\", \"originalite\", 1, \"score-label\"], [1, \"fas\", \"fa-lightbulb\"], [\"id\", \"originalite\", \"type\", \"number\", \"formControlName\", \"originalite\", \"min\", \"0\", \"max\", \"5\", \"step\", \"0.5\", \"placeholder\", \"0\", 1, \"score-input\"], [1, \"score-total\"], [1, \"total-display\"], [1, \"total-label\"], [1, \"total-value\"], [1, \"total-max\"], [1, \"total-percentage\"], [1, \"comments-section\"], [\"for\", \"commentaires\", 1, \"comments-label\"], [1, \"fas\", \"fa-comment-alt\"], [\"id\", \"commentaires\", \"formControlName\", \"commentaires\", \"rows\", \"6\", \"placeholder\", \"Ajoutez vos commentaires d\\u00E9taill\\u00E9s sur le travail de l'\\u00E9tudiant...\", 1, \"comments-textarea\"], [\"class\", \"form-validation\", 4, \"ngIf\"], [1, \"actions\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-times\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [\"class\", \"fas fa-save\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"file-link\", 3, \"href\"], [1, \"fas\", \"fa-download\"], [1, \"form-validation\"], [1, \"error-text\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"fas\", \"fa-save\"]],\n      template: function EditEvaluationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"Modifier l'\\u00E9valuation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"nav\", 3)(5, \"a\", 4);\n          i0.ɵɵtext(6, \"Projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 5);\n          i0.ɵɵtext(8, \">\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"a\", 6);\n          i0.ɵɵtext(10, \"Rendus\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"span\", 5);\n          i0.ɵɵtext(12, \">\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"span\", 7);\n          i0.ɵɵtext(14, \"Modifier \\u00E9valuation\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(15, EditEvaluationComponent_div_15_Template, 4, 0, \"div\", 8);\n          i0.ɵɵtemplate(16, EditEvaluationComponent_div_16_Template, 3, 1, \"div\", 9);\n          i0.ɵɵtemplate(17, EditEvaluationComponent_div_17_Template, 104, 20, \"div\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rendu && !ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgIf, i2.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i4.DatePipe],\n      styles: [\"\\n\\n.evaluation-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  background-color: #f8fafc;\\n  min-height: 100vh;\\n}\\n\\n\\n\\n.header-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #1e293b;\\n  margin-bottom: 10px;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.page-title[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCDD\\\";\\n  font-size: 1.5rem;\\n}\\n\\n\\n\\n.breadcrumb[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 0.875rem;\\n  color: #64748b;\\n}\\n\\n.breadcrumb-link[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n  text-decoration: none;\\n  transition: color 0.2s;\\n}\\n\\n.breadcrumb-link[_ngcontent-%COMP%]:hover {\\n  color: #1d4ed8;\\n  text-decoration: underline;\\n}\\n\\n.breadcrumb-separator[_ngcontent-%COMP%] {\\n  color: #94a3b8;\\n}\\n\\n.breadcrumb-current[_ngcontent-%COMP%] {\\n  color: #1e293b;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n  text-align: center;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border: 4px solid #e2e8f0;\\n  border-top: 4px solid #3b82f6;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: 15px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #64748b;\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%] {\\n  background-color: #fef2f2;\\n  border: 1px solid #fecaca;\\n  color: #dc2626;\\n  padding: 16px;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  font-weight: 500;\\n}\\n\\n.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n\\n\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 25px;\\n}\\n\\n\\n\\n.rendu-info-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 25px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.card-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1e293b;\\n  margin-bottom: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding-bottom: 10px;\\n  border-bottom: 2px solid #f1f5f9;\\n}\\n\\n.card-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n  font-size: 1.1rem;\\n}\\n\\n\\n\\n.info-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 20px;\\n}\\n\\n.info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 5px;\\n}\\n\\n.info-item.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1 / -1;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #374151;\\n  font-size: 0.875rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #1e293b;\\n  font-size: 1rem;\\n  padding: 8px 12px;\\n  background-color: #f8fafc;\\n  border-radius: 6px;\\n  border: 1px solid #e2e8f0;\\n}\\n\\n\\n\\n.file-link[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: #3b82f6;\\n  text-decoration: none;\\n  padding: 8px 12px;\\n  background-color: #eff6ff;\\n  border: 1px solid #bfdbfe;\\n  border-radius: 6px;\\n  transition: all 0.2s;\\n  font-weight: 500;\\n}\\n\\n.file-link[_ngcontent-%COMP%]:hover {\\n  background-color: #dbeafe;\\n  border-color: #93c5fd;\\n  transform: translateY(-1px);\\n}\\n\\n.file-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n\\n\\n.evaluation-form[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e2e8f0;\\n  overflow: hidden;\\n}\\n\\n.form-card[_ngcontent-%COMP%] {\\n  padding: 25px;\\n}\\n\\n\\n\\n.scores-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.score-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 25px;\\n  margin-bottom: 25px;\\n}\\n\\n.score-item[_ngcontent-%COMP%] {\\n  background-color: #f8fafc;\\n  padding: 20px;\\n  border-radius: 10px;\\n  border: 1px solid #e2e8f0;\\n  transition: all 0.2s;\\n}\\n\\n.score-item[_ngcontent-%COMP%]:hover {\\n  border-color: #cbd5e1;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n\\n.score-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 600;\\n  color: #374151;\\n  margin-bottom: 10px;\\n  font-size: 0.95rem;\\n}\\n\\n.score-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n  font-size: 1rem;\\n}\\n\\n.score-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin-bottom: 8px;\\n}\\n\\n.score-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px 15px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  text-align: center;\\n  transition: all 0.2s;\\n  background-color: white;\\n}\\n\\n.score-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3b82f6;\\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n}\\n\\n.score-input[_ngcontent-%COMP%]:invalid {\\n  border-color: #ef4444;\\n}\\n\\n.score-max[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #64748b;\\n  font-size: 1rem;\\n}\\n\\n.score-description[_ngcontent-%COMP%] {\\n  color: #64748b;\\n  font-size: 0.8rem;\\n  line-height: 1.4;\\n  font-style: italic;\\n}\\n\\n\\n\\n.score-total[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 20px;\\n  border-radius: 12px;\\n  text-align: center;\\n  margin-top: 20px;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n.total-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  margin-bottom: 8px;\\n}\\n\\n.total-label[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n}\\n\\n.total-value[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n}\\n\\n.total-max[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  opacity: 0.9;\\n}\\n\\n.total-percentage[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  opacity: 0.9;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.comments-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.comments-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 600;\\n  color: #374151;\\n  margin-bottom: 12px;\\n  font-size: 1rem;\\n}\\n\\n.comments-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n  font-size: 1rem;\\n}\\n\\n.comments-textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  font-size: 0.95rem;\\n  line-height: 1.6;\\n  resize: vertical;\\n  min-height: 120px;\\n  transition: all 0.2s;\\n  font-family: inherit;\\n}\\n\\n.comments-textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3b82f6;\\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n}\\n\\n.comments-textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n  font-style: italic;\\n}\\n\\n\\n\\n.form-validation[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n}\\n\\n.error-text[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 15px;\\n  padding-top: 25px;\\n  border-top: 1px solid #e2e8f0;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 24px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 0.95rem;\\n  font-weight: 600;\\n  text-decoration: none;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  min-width: 140px;\\n  justify-content: center;\\n}\\n\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none !important;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\\n  color: white;\\n  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(0);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #f8fafc;\\n  color: #64748b;\\n  border: 2px solid #e2e8f0;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #f1f5f9;\\n  border-color: #cbd5e1;\\n  color: #475569;\\n  transform: translateY(-1px);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(0);\\n}\\n\\n.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .evaluation-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .score-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 20px;\\n  }\\n\\n  .info-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .actions[_ngcontent-%COMP%] {\\n    flex-direction: column-reverse;\\n    gap: 10px;\\n  }\\n\\n  .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n\\n  .total-display[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 5px;\\n  }\\n\\n  .total-value[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .evaluation-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n\\n  .rendu-info-card[_ngcontent-%COMP%], .form-card[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n\\n  .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n\\n  .score-item[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-out;\\n}\\n\\n.score-item[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-out;\\n}\\n\\n.score-item[_ngcontent-%COMP%]:nth-child(1) { animation-delay: 0.1s; }\\n.score-item[_ngcontent-%COMP%]:nth-child(2) { animation-delay: 0.2s; }\\n.score-item[_ngcontent-%COMP%]:nth-child(3) { animation-delay: 0.3s; }\\n.score-item[_ngcontent-%COMP%]:nth-child(4) { animation-delay: 0.4s; }\\n\\n\\n\\n.score-input[_ngcontent-%COMP%]:focus, .comments-textarea[_ngcontent-%COMP%]:focus {\\n  transform: translateY(-1px);\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%]:focus, .score-input[_ngcontent-%COMP%]:focus, .comments-textarea[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #3b82f6;\\n  outline-offset: 2px;\\n}\\n\\n\\n\\n.score-input[value=\\\"5\\\"][_ngcontent-%COMP%] {\\n  background-color: #dcfce7;\\n  border-color: #16a34a;\\n  color: #15803d;\\n}\\n\\n.score-input[value=\\\"4\\\"][_ngcontent-%COMP%] {\\n  background-color: #fef3c7;\\n  border-color: #d97706;\\n  color: #92400e;\\n}\\n\\n.score-input[value=\\\"3\\\"][_ngcontent-%COMP%] {\\n  background-color: #fef3c7;\\n  border-color: #f59e0b;\\n  color: #d97706;\\n}\\n\\n.score-input[value=\\\"2\\\"][_ngcontent-%COMP%] {\\n  background-color: #fed7aa;\\n  border-color: #ea580c;\\n  color: #c2410c;\\n}\\n\\n.score-input[value=\\\"1\\\"][_ngcontent-%COMP%] {\\n  background-color: #fecaca;\\n  border-color: #dc2626;\\n  color: #b91c1c;\\n}\\n\\n.score-input[value=\\\"0\\\"][_ngcontent-%COMP%] {\\n  background-color: #f3f4f6;\\n  border-color: #9ca3af;\\n  color: #6b7280;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "environment", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵproperty", "ctx_r3", "getFileUrl", "rendu", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵsanitizeUrl", "getFileName", "ɵɵtemplate", "EditEvaluationComponent_div_17_div_32_Template", "ɵɵlistener", "EditEvaluationComponent_div_17_Template_form_ngSubmit_33_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "EditEvaluationComponent_div_17_div_95_Template", "EditEvaluationComponent_div_17_Template_button_click_97_listener", "ctx_r9", "annuler", "EditEvaluationComponent_div_17_i_101_Template", "EditEvaluationComponent_div_17_i_102_Template", "ɵɵtextInterpolate", "ctx_r2", "projet", "titre", "ɵɵtextInterpolate2", "etudiant", "nom", "prenom", "groupe", "ɵɵpipeBind2", "dateSoumission", "description", "evaluationForm", "getScoreTotal", "getScoreMaximum", "toFixed", "tmp_10_0", "get", "invalid", "touched", "isSubmitting", "EditEvaluationComponent", "constructor", "fb", "route", "router", "rendusService", "renduId", "isLoading", "group", "scores", "structure", "required", "min", "max", "pratiques", "fonctionnalite", "originalite", "commentaires", "ngOnInit", "snapshot", "paramMap", "loadRendu", "getRenduById", "subscribe", "next", "data", "evaluation", "patchValue", "err", "console", "evaluationData", "value", "updateEvaluation", "response", "navigate", "message", "filePath", "fileName", "includes", "parts", "split", "length", "urlBackend", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "RendusService", "selectors", "decls", "vars", "consts", "template", "EditEvaluationComponent_Template", "rf", "ctx", "EditEvaluationComponent_div_15_Template", "EditEvaluationComponent_div_16_Template", "EditEvaluationComponent_div_17_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\edit-evaluation\\edit-evaluation.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\edit-evaluation\\edit-evaluation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { RendusService } from 'src/app/services/rendus.service';\nimport { environment } from 'src/environments/environment';\n\n@Component({\n  selector: 'app-edit-evaluation',\n  templateUrl: './edit-evaluation.component.html',\n  styleUrls: ['./edit-evaluation.component.css']\n})\nexport class EditEvaluationComponent implements OnInit {\n  renduId: string = '';\n  rendu: any = null;\n  evaluationForm: FormGroup;\n  isLoading: boolean = true;\n  isSubmitting: boolean = false;\n  error: string = '';\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private rendusService: RendusService\n  ) {\n    this.evaluationForm = this.fb.group({\n      scores: this.fb.group({\n\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n      }),\n\n      commentaires: ['', Validators.required]\n    });\n  }\n\n  ngOnInit(): void {\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n\n    if (this.renduId) {\n      this.loadRendu();\n    } else {\n      this.error = 'ID de rendu manquant';\n      this.isLoading = false;\n    }\n  }\n\n  loadRendu(): void {\n    this.isLoading = true;\n    this.rendusService.getRenduById(this.renduId).subscribe({\n      next: (data: any) => {\n        this.rendu = data;\n\n        // Remplir le formulaire avec les données existantes\n        if (this.rendu.evaluation && this.rendu.evaluation.scores) {\n          this.evaluationForm.patchValue({\n            scores: {\n              structure: this.rendu.evaluation.scores.structure || 0,\n              pratiques: this.rendu.evaluation.scores.pratiques || 0,\n              fonctionnalite: this.rendu.evaluation.scores.fonctionnalite || 0,\n              originalite: this.rendu.evaluation.scores.originalite || 0\n            },\n            commentaires: this.rendu.evaluation.commentaires || ''\n          });\n        }\n\n        this.isLoading = false;\n      },\n      error: (err: any) => {\n        this.error = 'Erreur lors du chargement du rendu';\n        this.isLoading = false;\n        console.error(err);\n      }\n    });\n  }\n\n  onSubmit(): void {\n    if (this.evaluationForm.invalid) {\n      return;\n    }\n\n    this.isSubmitting = true;\n    const evaluationData = this.evaluationForm.value;\n\n    // Assurez-vous que renduId est disponible\n    if (!this.renduId) {\n      this.error = \"ID du rendu manquant\";\n      this.isSubmitting = false;\n      return;\n    }\n\n    this.rendusService.updateEvaluation(this.renduId, evaluationData).subscribe({\n      next: (response: any) => {\n        this.isSubmitting = false;\n        // Redirection vers la page de liste des rendus après succès\n        this.router.navigate(['/admin/projects/list-rendus']);\n      },\n      error: (err: any) => {\n        this.error = `Erreur lors de la mise à jour de l'évaluation: ${err.message || 'Erreur inconnue'}`;\n        this.isSubmitting = false;\n        console.error(err);\n      }\n    });\n  }\n\n  getScoreTotal(): number {\n    const scores = this.evaluationForm.get('scores')?.value;\n    if (!scores) return 0;\n\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n\n  getScoreMaximum(): number {\n    return 20; // 4 critères x 5 points maximum\n  }\n\n  annuler(): void {\n    \n    this.router.navigate(['/admin/projects/list-rendus']);\n\n  }\n\n  // Méthodes pour gérer les fichiers\n  getFileUrl(filePath: string): string {\n    if (!filePath) return '';\n\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n\n    // Utiliser la route spécifique pour le téléchargement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n\n  getFileName(filePath: string): string {\n    if (!filePath) return 'Fichier';\n\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n\n    return filePath;\n  }\n}\n\n", "<div class=\"evaluation-container\">\n  <!-- En-tête -->\n  <div class=\"header-section\">\n    <h1 class=\"page-title\">Modifier l'évaluation</h1>\n    <nav class=\"breadcrumb\">\n      <a routerLink=\"/admin/projects\" class=\"breadcrumb-link\">Projets</a>\n      <span class=\"breadcrumb-separator\">></span>\n      <a routerLink=\"/admin/projects/list-rendus\" class=\"breadcrumb-link\">Rendus</a>\n      <span class=\"breadcrumb-separator\">></span>\n      <span class=\"breadcrumb-current\">Modifier évaluation</span>\n    </nav>\n  </div>\n\n  <!-- Loader -->\n  <div *ngIf=\"isLoading\" class=\"loading-spinner\">\n    <div class=\"spinner\"></div>\n    <p>Chargement des données...</p>\n  </div>\n\n  <!-- Message d'erreur -->\n  <div *ngIf=\"error\" class=\"error-message\">\n    <i class=\"fas fa-exclamation-triangle\"></i>\n    {{ error }}\n  </div>\n\n  <!-- Contenu principal -->\n  <div *ngIf=\"rendu && !isLoading\" class=\"main-content\">\n    <!-- Informations sur le rendu -->\n    <div class=\"rendu-info-card\">\n      <h2 class=\"card-title\">\n        <i class=\"fas fa-file-alt\"></i>\n        Informations sur le rendu\n      </h2>\n      <div class=\"info-grid\">\n        <div class=\"info-item\">\n          <label>Projet:</label>\n          <span>{{ rendu.projet?.titre || 'Non spécifié' }}</span>\n        </div>\n        <div class=\"info-item\">\n          <label>Étudiant:</label>\n          <span>{{ rendu.etudiant?.nom }} {{ rendu.etudiant?.prenom }}</span>\n        </div>\n        <div class=\"info-item\">\n          <label>Groupe:</label>\n          <span>{{ rendu.etudiant?.groupe || 'Non spécifié' }}</span>\n        </div>\n        <div class=\"info-item\">\n          <label>Date de soumission:</label>\n          <span>{{ rendu.dateSoumission | date:'dd/MM/yyyy à HH:mm' }}</span>\n        </div>\n        <div class=\"info-item full-width\">\n          <label>Description:</label>\n          <span>{{ rendu.description || 'Aucune description' }}</span>\n        </div>\n        <div class=\"info-item full-width\" *ngIf=\"rendu.fichierRendu\">\n          <label>Fichier soumis:</label>\n          <a [href]=\"getFileUrl(rendu.fichierRendu)\" target=\"_blank\" class=\"file-link\">\n            <i class=\"fas fa-download\"></i>\n            {{ getFileName(rendu.fichierRendu) }}\n          </a>\n        </div>\n      </div>\n    </div>\n\n    <!-- Formulaire d'évaluation -->\n    <form [formGroup]=\"evaluationForm\" (ngSubmit)=\"onSubmit()\" class=\"evaluation-form\">\n      <div class=\"form-card\">\n        <h3 class=\"card-title\">\n          <i class=\"fas fa-star\"></i>\n          Critères d'évaluation\n        </h3>\n\n        <div formGroupName=\"scores\" class=\"scores-section\">\n          <div class=\"score-grid\">\n            <!-- Structure et organisation -->\n            <div class=\"score-item\">\n              <label for=\"structure\" class=\"score-label\">\n                <i class=\"fas fa-code\"></i>\n                Structure et organisation du code\n              </label>\n              <div class=\"score-input-container\">\n                <input\n                  id=\"structure\"\n                  type=\"number\"\n                  formControlName=\"structure\"\n                  min=\"0\"\n                  max=\"5\"\n                  step=\"0.5\"\n                  class=\"score-input\"\n                  placeholder=\"0\"\n                />\n                <span class=\"score-max\">/ 5</span>\n              </div>\n              <small class=\"score-description\">Qualité de l'organisation et de la structure du code</small>\n            </div>\n\n            <!-- Bonnes pratiques -->\n            <div class=\"score-item\">\n              <label for=\"pratiques\" class=\"score-label\">\n                <i class=\"fas fa-check-circle\"></i>\n                Bonnes pratiques\n              </label>\n              <div class=\"score-input-container\">\n                <input\n                  id=\"pratiques\"\n                  type=\"number\"\n                  formControlName=\"pratiques\"\n                  min=\"0\"\n                  max=\"5\"\n                  step=\"0.5\"\n                  class=\"score-input\"\n                  placeholder=\"0\"\n                />\n                <span class=\"score-max\">/ 5</span>\n              </div>\n              <small class=\"score-description\">Respect des conventions et bonnes pratiques</small>\n            </div>\n\n            <!-- Fonctionnalité -->\n            <div class=\"score-item\">\n              <label for=\"fonctionnalite\" class=\"score-label\">\n                <i class=\"fas fa-cogs\"></i>\n                Fonctionnalité\n              </label>\n              <div class=\"score-input-container\">\n                <input\n                  id=\"fonctionnalite\"\n                  type=\"number\"\n                  formControlName=\"fonctionnalite\"\n                  min=\"0\"\n                  max=\"5\"\n                  step=\"0.5\"\n                  class=\"score-input\"\n                  placeholder=\"0\"\n                />\n                <span class=\"score-max\">/ 5</span>\n              </div>\n              <small class=\"score-description\">Fonctionnement correct et complet</small>\n            </div>\n\n            <!-- Originalité -->\n            <div class=\"score-item\">\n              <label for=\"originalite\" class=\"score-label\">\n                <i class=\"fas fa-lightbulb\"></i>\n                Originalité et créativité\n              </label>\n              <div class=\"score-input-container\">\n                <input\n                  id=\"originalite\"\n                  type=\"number\"\n                  formControlName=\"originalite\"\n                  min=\"0\"\n                  max=\"5\"\n                  step=\"0.5\"\n                  class=\"score-input\"\n                  placeholder=\"0\"\n                />\n                <span class=\"score-max\">/ 5</span>\n              </div>\n              <small class=\"score-description\">Innovation et créativité dans la solution</small>\n            </div>\n          </div>\n\n          <!-- Score total -->\n          <div class=\"score-total\">\n            <div class=\"total-display\">\n              <span class=\"total-label\">Score total:</span>\n              <span class=\"total-value\">{{ getScoreTotal() }}</span>\n              <span class=\"total-max\">/ {{ getScoreMaximum() }}</span>\n            </div>\n            <div class=\"total-percentage\">\n              {{ ((getScoreTotal() / getScoreMaximum()) * 100).toFixed(1) }}%\n            </div>\n          </div>\n        </div>\n\n        <!-- Commentaires -->\n        <div class=\"comments-section\">\n          <label for=\"commentaires\" class=\"comments-label\">\n            <i class=\"fas fa-comment-alt\"></i>\n            Commentaires et observations\n          </label>\n          <textarea\n            id=\"commentaires\"\n            formControlName=\"commentaires\"\n            rows=\"6\"\n            class=\"comments-textarea\"\n            placeholder=\"Ajoutez vos commentaires détaillés sur le travail de l'étudiant...\"\n          ></textarea>\n          <div class=\"form-validation\" *ngIf=\"evaluationForm.get('commentaires')?.invalid && evaluationForm.get('commentaires')?.touched\">\n            <small class=\"error-text\">Les commentaires sont obligatoires</small>\n          </div>\n        </div>\n\n        <!-- Actions -->\n        <div class=\"actions\">\n          <button\n            type=\"button\"\n            (click)=\"annuler()\"\n            class=\"btn btn-secondary\"\n            [disabled]=\"isSubmitting\"\n          >\n            <i class=\"fas fa-times\"></i>\n            Annuler\n          </button>\n\n          <button\n            type=\"submit\"\n            class=\"btn btn-primary\"\n            [disabled]=\"evaluationForm.invalid || isSubmitting\"\n          >\n            <i class=\"fas fa-spinner fa-spin\" *ngIf=\"isSubmitting\"></i>\n            <i class=\"fas fa-save\" *ngIf=\"!isSubmitting\"></i>\n            {{ isSubmitting ? 'Enregistrement...' : 'Enregistrer l\\'évaluation' }}\n          </button>\n        </div>\n      </div>\n    </form>\n  </div>\n</div>\n\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;ICUxDC,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAE,SAAA,cAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,qCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAIlCJ,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAAE,SAAA,YAA2C;IAC3CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IA+BMR,EAAA,CAAAC,cAAA,cAA6D;IACpDD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC9BJ,EAAA,CAAAC,cAAA,YAA6E;IAC3ED,EAAA,CAAAE,SAAA,YAA+B;IAC/BF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAHDJ,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAC,UAAA,CAAAD,MAAA,CAAAE,KAAA,CAAAC,YAAA,GAAAb,EAAA,CAAAc,aAAA,CAAuC;IAExCd,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAI,MAAA,CAAAK,WAAA,CAAAL,MAAA,CAAAE,KAAA,CAAAC,YAAA,OACF;;;;;IAkIAb,EAAA,CAAAC,cAAA,cAAgI;IACpGD,EAAA,CAAAG,MAAA,yCAAkC;IAAAH,EAAA,CAAAI,YAAA,EAAQ;;;;;IAqBpEJ,EAAA,CAAAE,SAAA,YAA2D;;;;;IAC3DF,EAAA,CAAAE,SAAA,YAAiD;;;;;;IA1L3DF,EAAA,CAAAC,cAAA,cAAsD;IAIhDD,EAAA,CAAAE,SAAA,YAA+B;IAC/BF,EAAA,CAAAG,MAAA,kCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,cAAuB;IAEZD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACtBJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,IAA2C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE1DJ,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAG,MAAA,sBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAsD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAErEJ,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACtBJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA8C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE7DJ,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAClCJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAsD;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAErEJ,EAAA,CAAAC,cAAA,eAAkC;IACzBD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC3BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA+C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE9DJ,EAAA,CAAAgB,UAAA,KAAAC,8CAAA,kBAMM;IACRjB,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAC,cAAA,gBAAmF;IAAhDD,EAAA,CAAAkB,UAAA,sBAAAC,kEAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAYvB,EAAA,CAAAwB,WAAA,CAAAF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IACxDzB,EAAA,CAAAC,cAAA,eAAuB;IAEnBD,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAG,MAAA,yCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAELJ,EAAA,CAAAC,cAAA,eAAmD;IAK3CD,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAG,MAAA,2CACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAE,SAAA,iBASE;IACFF,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAG,MAAA,WAAG;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpCJ,EAAA,CAAAC,cAAA,iBAAiC;IAAAD,EAAA,CAAAG,MAAA,iEAAoD;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAI/FJ,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAE,SAAA,aAAmC;IACnCF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAE,SAAA,iBASE;IACFF,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAG,MAAA,WAAG;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpCJ,EAAA,CAAAC,cAAA,iBAAiC;IAAAD,EAAA,CAAAG,MAAA,mDAA2C;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAItFJ,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAG,MAAA,6BACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAE,SAAA,iBASE;IACFF,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAG,MAAA,WAAG;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpCJ,EAAA,CAAAC,cAAA,iBAAiC;IAAAD,EAAA,CAAAG,MAAA,yCAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAI5EJ,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAG,MAAA,kDACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAE,SAAA,iBASE;IACFF,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAG,MAAA,WAAG;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpCJ,EAAA,CAAAC,cAAA,iBAAiC;IAAAD,EAAA,CAAAG,MAAA,2DAAyC;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKtFJ,EAAA,CAAAC,cAAA,eAAyB;IAEKD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC7CJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,IAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtDJ,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAG,MAAA,IAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE1DJ,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAKVJ,EAAA,CAAAC,cAAA,eAA8B;IAE1BD,EAAA,CAAAE,SAAA,aAAkC;IAClCF,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAE,SAAA,oBAMY;IACZF,EAAA,CAAAgB,UAAA,KAAAU,8CAAA,kBAEM;IACR1B,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,eAAqB;IAGjBD,EAAA,CAAAkB,UAAA,mBAAAS,iEAAA;MAAA3B,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAA5B,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAI,MAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAInB7B,EAAA,CAAAE,SAAA,aAA4B;IAC5BF,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAC,cAAA,mBAIC;IACCD,EAAA,CAAAgB,UAAA,MAAAc,6CAAA,gBAA2D;IAC3D9B,EAAA,CAAAgB,UAAA,MAAAe,6CAAA,gBAAiD;IACjD/B,EAAA,CAAAG,MAAA,KACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAlLHJ,EAAA,CAAAK,SAAA,IAA2C;IAA3CL,EAAA,CAAAgC,iBAAA,EAAAC,MAAA,CAAArB,KAAA,CAAAsB,MAAA,kBAAAD,MAAA,CAAArB,KAAA,CAAAsB,MAAA,CAAAC,KAAA,8BAA2C;IAI3CnC,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAoC,kBAAA,KAAAH,MAAA,CAAArB,KAAA,CAAAyB,QAAA,kBAAAJ,MAAA,CAAArB,KAAA,CAAAyB,QAAA,CAAAC,GAAA,OAAAL,MAAA,CAAArB,KAAA,CAAAyB,QAAA,kBAAAJ,MAAA,CAAArB,KAAA,CAAAyB,QAAA,CAAAE,MAAA,KAAsD;IAItDvC,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAgC,iBAAA,EAAAC,MAAA,CAAArB,KAAA,CAAAyB,QAAA,kBAAAJ,MAAA,CAAArB,KAAA,CAAAyB,QAAA,CAAAG,MAAA,8BAA8C;IAI9CxC,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAgC,iBAAA,CAAAhC,EAAA,CAAAyC,WAAA,SAAAR,MAAA,CAAArB,KAAA,CAAA8B,cAAA,6BAAsD;IAItD1C,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAgC,iBAAA,CAAAC,MAAA,CAAArB,KAAA,CAAA+B,WAAA,yBAA+C;IAEpB3C,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAS,UAAA,SAAAwB,MAAA,CAAArB,KAAA,CAAAC,YAAA,CAAwB;IAWzDb,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAS,UAAA,cAAAwB,MAAA,CAAAW,cAAA,CAA4B;IAsGE5C,EAAA,CAAAK,SAAA,IAAqB;IAArBL,EAAA,CAAAgC,iBAAA,CAAAC,MAAA,CAAAY,aAAA,GAAqB;IACvB7C,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAM,kBAAA,OAAA2B,MAAA,CAAAa,eAAA,OAAyB;IAGjD9C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,OAAA2B,MAAA,CAAAY,aAAA,KAAAZ,MAAA,CAAAa,eAAA,UAAAC,OAAA,UACF;IAiB4B/C,EAAA,CAAAK,SAAA,GAAgG;IAAhGL,EAAA,CAAAS,UAAA,WAAAuC,QAAA,GAAAf,MAAA,CAAAW,cAAA,CAAAK,GAAA,mCAAAD,QAAA,CAAAE,OAAA,OAAAF,QAAA,GAAAf,MAAA,CAAAW,cAAA,CAAAK,GAAA,mCAAAD,QAAA,CAAAG,OAAA,EAAgG;IAW5HnD,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAS,UAAA,aAAAwB,MAAA,CAAAmB,YAAA,CAAyB;IASzBpD,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAS,UAAA,aAAAwB,MAAA,CAAAW,cAAA,CAAAM,OAAA,IAAAjB,MAAA,CAAAmB,YAAA,CAAmD;IAEhBpD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,UAAA,SAAAwB,MAAA,CAAAmB,YAAA,CAAkB;IAC7BpD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,UAAA,UAAAwB,MAAA,CAAAmB,YAAA,CAAmB;IAC3CpD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA2B,MAAA,CAAAmB,YAAA,8DACF;;;AD3MV,OAAM,MAAOC,uBAAuB;EAQlCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAXvB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAA/C,KAAK,GAAQ,IAAI;IAEjB,KAAAgD,SAAS,GAAY,IAAI;IACzB,KAAAR,YAAY,GAAY,KAAK;IAC7B,KAAA5C,KAAK,GAAW,EAAE;IAQhB,IAAI,CAACoC,cAAc,GAAG,IAAI,CAACW,EAAE,CAACM,KAAK,CAAC;MAClCC,MAAM,EAAE,IAAI,CAACP,EAAE,CAACM,KAAK,CAAC;QAEpBE,SAAS,EAAE,CAAC,CAAC,EAAE,CAACjE,UAAU,CAACkE,QAAQ,EAAElE,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,EAAEnE,UAAU,CAACoE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EC,SAAS,EAAE,CAAC,CAAC,EAAE,CAACrE,UAAU,CAACkE,QAAQ,EAAElE,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,EAAEnE,UAAU,CAACoE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EE,cAAc,EAAE,CAAC,CAAC,EAAE,CAACtE,UAAU,CAACkE,QAAQ,EAAElE,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,EAAEnE,UAAU,CAACoE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChFG,WAAW,EAAE,CAAC,CAAC,EAAE,CAACvE,UAAU,CAACkE,QAAQ,EAAElE,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,EAAEnE,UAAU,CAACoE,GAAG,CAAC,CAAC,CAAC,CAAC;OAC7E,CAAC;MAEFI,YAAY,EAAE,CAAC,EAAE,EAAExE,UAAU,CAACkE,QAAQ;KACvC,CAAC;EACJ;EAEAO,QAAQA,CAAA;IACN,IAAI,CAACZ,OAAO,GAAG,IAAI,CAACH,KAAK,CAACgB,QAAQ,CAACC,QAAQ,CAACxB,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAEhE,IAAI,IAAI,CAACU,OAAO,EAAE;MAChB,IAAI,CAACe,SAAS,EAAE;KACjB,MAAM;MACL,IAAI,CAAClE,KAAK,GAAG,sBAAsB;MACnC,IAAI,CAACoD,SAAS,GAAG,KAAK;;EAE1B;EAEAc,SAASA,CAAA;IACP,IAAI,CAACd,SAAS,GAAG,IAAI;IACrB,IAAI,CAACF,aAAa,CAACiB,YAAY,CAAC,IAAI,CAAChB,OAAO,CAAC,CAACiB,SAAS,CAAC;MACtDC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAAClE,KAAK,GAAGkE,IAAI;QAEjB;QACA,IAAI,IAAI,CAAClE,KAAK,CAACmE,UAAU,IAAI,IAAI,CAACnE,KAAK,CAACmE,UAAU,CAACjB,MAAM,EAAE;UACzD,IAAI,CAAClB,cAAc,CAACoC,UAAU,CAAC;YAC7BlB,MAAM,EAAE;cACNC,SAAS,EAAE,IAAI,CAACnD,KAAK,CAACmE,UAAU,CAACjB,MAAM,CAACC,SAAS,IAAI,CAAC;cACtDI,SAAS,EAAE,IAAI,CAACvD,KAAK,CAACmE,UAAU,CAACjB,MAAM,CAACK,SAAS,IAAI,CAAC;cACtDC,cAAc,EAAE,IAAI,CAACxD,KAAK,CAACmE,UAAU,CAACjB,MAAM,CAACM,cAAc,IAAI,CAAC;cAChEC,WAAW,EAAE,IAAI,CAACzD,KAAK,CAACmE,UAAU,CAACjB,MAAM,CAACO,WAAW,IAAI;aAC1D;YACDC,YAAY,EAAE,IAAI,CAAC1D,KAAK,CAACmE,UAAU,CAACT,YAAY,IAAI;WACrD,CAAC;;QAGJ,IAAI,CAACV,SAAS,GAAG,KAAK;MACxB,CAAC;MACDpD,KAAK,EAAGyE,GAAQ,IAAI;QAClB,IAAI,CAACzE,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAACoD,SAAS,GAAG,KAAK;QACtBsB,OAAO,CAAC1E,KAAK,CAACyE,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAxD,QAAQA,CAAA;IACN,IAAI,IAAI,CAACmB,cAAc,CAACM,OAAO,EAAE;MAC/B;;IAGF,IAAI,CAACE,YAAY,GAAG,IAAI;IACxB,MAAM+B,cAAc,GAAG,IAAI,CAACvC,cAAc,CAACwC,KAAK;IAEhD;IACA,IAAI,CAAC,IAAI,CAACzB,OAAO,EAAE;MACjB,IAAI,CAACnD,KAAK,GAAG,sBAAsB;MACnC,IAAI,CAAC4C,YAAY,GAAG,KAAK;MACzB;;IAGF,IAAI,CAACM,aAAa,CAAC2B,gBAAgB,CAAC,IAAI,CAAC1B,OAAO,EAAEwB,cAAc,CAAC,CAACP,SAAS,CAAC;MAC1EC,IAAI,EAAGS,QAAa,IAAI;QACtB,IAAI,CAAClC,YAAY,GAAG,KAAK;QACzB;QACA,IAAI,CAACK,MAAM,CAAC8B,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;MACvD,CAAC;MACD/E,KAAK,EAAGyE,GAAQ,IAAI;QAClB,IAAI,CAACzE,KAAK,GAAG,kDAAkDyE,GAAG,CAACO,OAAO,IAAI,iBAAiB,EAAE;QACjG,IAAI,CAACpC,YAAY,GAAG,KAAK;QACzB8B,OAAO,CAAC1E,KAAK,CAACyE,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEApC,aAAaA,CAAA;IACX,MAAMiB,MAAM,GAAG,IAAI,CAAClB,cAAc,CAACK,GAAG,CAAC,QAAQ,CAAC,EAAEmC,KAAK;IACvD,IAAI,CAACtB,MAAM,EAAE,OAAO,CAAC;IAErB,OAAOA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACM,cAAc,GAAGN,MAAM,CAACO,WAAW;EACzF;EAEAvB,eAAeA,CAAA;IACb,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAjB,OAAOA,CAAA;IAEL,IAAI,CAAC4B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;EAEvD;EAEA;EACA5E,UAAUA,CAAC8E,QAAgB;IACzB,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,GAAG/F,WAAW,CAACgG,UAAU,uBAAuBL,QAAQ,EAAE;EACnE;EAEA3E,WAAWA,CAAC0E,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOL,QAAQ;EACjB;;;uBA5IWpC,uBAAuB,EAAArD,EAAA,CAAAgG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlG,EAAA,CAAAgG,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAApG,EAAA,CAAAgG,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAArG,EAAA,CAAAgG,iBAAA,CAAAM,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAvBlD,uBAAuB;MAAAmD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXpC9G,EAAA,CAAAC,cAAA,aAAkC;UAGPD,EAAA,CAAAG,MAAA,iCAAqB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjDJ,EAAA,CAAAC,cAAA,aAAwB;UACkCD,EAAA,CAAAG,MAAA,cAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACnEJ,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAG,MAAA,QAAC;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAC3CJ,EAAA,CAAAC,cAAA,WAAoE;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAC9EJ,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAG,MAAA,SAAC;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAC3CJ,EAAA,CAAAC,cAAA,eAAiC;UAAAD,EAAA,CAAAG,MAAA,gCAAmB;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAK/DJ,EAAA,CAAAgB,UAAA,KAAAgG,uCAAA,iBAGM;UAGNhH,EAAA,CAAAgB,UAAA,KAAAiG,uCAAA,iBAGM;UAGNjH,EAAA,CAAAgB,UAAA,KAAAkG,uCAAA,qBAgMM;UACRlH,EAAA,CAAAI,YAAA,EAAM;;;UA7MEJ,EAAA,CAAAK,SAAA,IAAe;UAAfL,EAAA,CAAAS,UAAA,SAAAsG,GAAA,CAAAnD,SAAA,CAAe;UAMf5D,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAS,UAAA,SAAAsG,GAAA,CAAAvG,KAAA,CAAW;UAMXR,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAS,UAAA,SAAAsG,GAAA,CAAAnG,KAAA,KAAAmG,GAAA,CAAAnD,SAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}