{"ast": null, "code": "import { catchError, finalize, takeUntil } from 'rxjs/operators';\nimport { Subject, of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/rendus.service\";\nimport * as i2 from \"../../../../services/evaluation.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction EvaluationsListComponent_div_25_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_25_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.clearSearch());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 31);\n    i0.ɵɵelement(3, \"path\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EvaluationsListComponent_div_25_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r7.filteredEvaluations.length, \" r\\u00E9sultat(s) trouv\\u00E9(s) pour \\\"\", ctx_r7.searchTerm, \"\\\" \");\n  }\n}\nfunction EvaluationsListComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 24);\n    i0.ɵɵelement(4, \"path\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"input\", 26);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_25_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.searchTerm = $event);\n    })(\"input\", function EvaluationsListComponent_div_25_Template_input_input_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onSearchChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, EvaluationsListComponent_div_25_div_6_Template, 4, 0, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, EvaluationsListComponent_div_25_div_7_Template, 2, 2, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.searchTerm);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.searchTerm);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.searchTerm);\n  }\n}\nfunction EvaluationsListComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"div\", 35)(3, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 37);\n    i0.ɵɵtext(5, \"Chargement des \\u00E9valuations...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EvaluationsListComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 3)(2, \"div\", 39);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 40);\n    i0.ɵɵelement(4, \"path\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 42);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_27_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.loadEvaluations());\n    });\n    i0.ɵɵtext(8, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.error);\n  }\n}\nfunction EvaluationsListComponent_div_28_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupe_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", groupe_r17);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(groupe_r17);\n  }\n}\nfunction EvaluationsListComponent_div_28_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const projet_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", projet_r18._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(projet_r18.titre);\n  }\n}\nfunction EvaluationsListComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 47);\n    i0.ɵɵelement(4, \"path\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h2\", 49);\n    i0.ɵɵtext(6, \"Filtres et recherche\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 50)(8, \"div\", 51)(9, \"label\", 52)(10, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 54);\n    i0.ɵɵelement(12, \"path\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Filtrer par groupe\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"select\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_28_Template_select_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.filterGroupe = $event);\n    })(\"change\", function EvaluationsListComponent_div_28_Template_select_change_15_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.applyFilters());\n    });\n    i0.ɵɵelementStart(16, \"option\", 57);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, EvaluationsListComponent_div_28_option_18_Template, 2, 2, \"option\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 51)(20, \"label\", 52)(21, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(22, \"svg\", 54);\n    i0.ɵɵelement(23, \"path\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25, \"Filtrer par projet\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"select\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_28_Template_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.filterProjet = $event);\n    })(\"change\", function EvaluationsListComponent_div_28_Template_select_change_26_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.applyFilters());\n    });\n    i0.ɵɵelementStart(27, \"option\", 57);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, EvaluationsListComponent_div_28_option_29_Template, 2, 2, \"option\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 51)(31, \"label\", 52)(32, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(33, \"svg\", 54);\n    i0.ɵɵelement(34, \"path\", 60)(35, \"path\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(36, \"span\");\n    i0.ɵɵtext(37, \"Actions rapides\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"div\", 62)(39, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_28_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.refreshList());\n    });\n    i0.ɵɵelementStart(40, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(41, \"svg\", 65);\n    i0.ɵɵelement(42, \"path\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(43, \"span\");\n    i0.ɵɵtext(44, \"Actualiser\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.filterGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Tous les groupes (\", ctx_r3.groupes.length, \")\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.groupes);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.filterProjet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Tous les projets (\", ctx_r3.projets.length, \")\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.projets);\n  }\n}\nfunction EvaluationsListComponent_div_29_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71)(2, \"div\", 4)(3, \"div\", 22)(4, \"div\", 72);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 74);\n    i0.ɵɵelement(8, \"path\", 75);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"div\", 76)(10, \"h3\", 77);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 78);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 79)(15, \"div\", 80);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(16, \"svg\", 54);\n    i0.ɵɵelement(17, \"path\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(18, \"span\", 81);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 80);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(21, \"svg\", 54);\n    i0.ɵɵelement(22, \"path\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(23, \"span\", 81);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(25, \"div\", 82)(26, \"div\", 53)(27, \"div\", 83);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 84);\n    i0.ɵɵelement(29, \"path\", 85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(30, \"div\")(31, \"p\", 86);\n    i0.ɵɵtext(32, \"\\u00C9valu\\u00E9e le\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 87);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 53)(36, \"div\", 88);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(37, \"svg\", 65);\n    i0.ɵɵelement(38, \"path\", 89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(39, \"div\")(40, \"p\", 86);\n    i0.ɵɵtext(41, \"Score total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"span\", 90);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"div\", 91)(45, \"p\", 92);\n    i0.ɵɵtext(46, \"D\\u00E9tail des scores\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 93)(48, \"div\", 94)(49, \"span\");\n    i0.ɵɵtext(50, \"Structure:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 42);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 94)(54, \"span\");\n    i0.ɵɵtext(55, \"Pratiques:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"span\", 42);\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 94)(59, \"span\");\n    i0.ɵɵtext(60, \"Fonctionnalit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"span\", 42);\n    i0.ɵɵtext(62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 94)(64, \"span\");\n    i0.ɵɵtext(65, \"Originalit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 42);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(68, \"div\", 95)(69, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_29_div_1_Template_button_click_69_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const evaluation_r26 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.viewEvaluationDetails(evaluation_r26.rendu));\n    });\n    i0.ɵɵelementStart(70, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(71, \"svg\", 97);\n    i0.ɵɵelement(72, \"path\", 61)(73, \"path\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(74, \"span\");\n    i0.ɵɵtext(75, \"Voir d\\u00E9tails\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_29_div_1_Template_button_click_76_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const evaluation_r26 = restoredCtx.$implicit;\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.editEvaluation(evaluation_r26.rendu));\n    });\n    i0.ɵɵelementStart(77, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(78, \"svg\", 97);\n    i0.ɵɵelement(79, \"path\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(80, \"span\");\n    i0.ɵɵtext(81, \"Modifier\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(82, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_29_div_1_Template_button_click_82_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const evaluation_r26 = restoredCtx.$implicit;\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.deleteEvaluation(evaluation_r26._id));\n    });\n    i0.ɵɵelementStart(83, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(84, \"svg\", 97);\n    i0.ɵɵelement(85, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(86, \"span\");\n    i0.ɵɵtext(87, \"Supprimer\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const evaluation_r26 = ctx.$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getStudentInitials(evaluation_r26.etudiant), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getStudentName(evaluation_r26.etudiant), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((evaluation_r26.etudiant == null ? null : evaluation_r26.etudiant.email) || \"Email non disponible\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getStudentGroup(evaluation_r26.etudiant), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getProjectTitle(evaluation_r26), \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r25.formatDate(evaluation_r26.dateEvaluation));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r25.getScoreIconClass(ctx_r25.getScoreTotal(evaluation_r26)));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r25.getScoreColorClass(ctx_r25.getScoreTotal(evaluation_r26)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getScoreTotal(evaluation_r26), \"/20 \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(evaluation_r26.scores.structure || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(evaluation_r26.scores.pratiques || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(evaluation_r26.scores.fonctionnalite || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(evaluation_r26.scores.originalite || 0);\n  }\n}\nfunction EvaluationsListComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtemplate(1, EvaluationsListComponent_div_29_div_1_Template, 88, 13, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.filteredEvaluations);\n  }\n}\nfunction EvaluationsListComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"div\", 104)(2, \"div\", 105);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 106);\n    i0.ɵɵelement(4, \"path\", 107);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h3\", 108);\n    i0.ɵɵtext(6, \"Aucune \\u00E9valuation trouv\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 109);\n    i0.ɵɵtext(8, \"Aucune \\u00E9valuation ne correspond \\u00E0 vos crit\\u00E8res de filtrage actuels.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_30_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.resetFilters());\n    });\n    i0.ɵɵelementStart(10, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 65);\n    i0.ɵɵelement(12, \"path\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"R\\u00E9initialiser les filtres\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nexport class EvaluationsListComponent {\n  constructor(rendusService, evaluationService, router) {\n    this.rendusService = rendusService;\n    this.evaluationService = evaluationService;\n    this.router = router;\n    this.evaluations = [];\n    this.filteredEvaluations = [];\n    this.isLoading = true;\n    this.error = '';\n    this.searchTerm = '';\n    this.filterGroupe = '';\n    this.filterProjet = '';\n    this.groupes = [];\n    this.projets = [];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.loadEvaluations();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadEvaluations() {\n    this.isLoading = true;\n    this.error = '';\n    console.log('Début du chargement des évaluations...');\n    this.evaluationService.getAllEvaluations().pipe(takeUntil(this.destroy$), catchError(err => {\n      this.error = 'Impossible de charger les évaluations. Veuillez réessayer plus tard.';\n      this.isLoading = false;\n      return of([]);\n    }), finalize(() => {\n      this.isLoading = false;\n    })).subscribe({\n      next: evaluations => {\n        console.log('Évaluations reçues:', evaluations);\n        if (!Array.isArray(evaluations)) {\n          this.error = 'Format de données incorrect. Veuillez réessayer plus tard.';\n          return;\n        }\n        // Vérifier et compléter les données manquantes\n        this.evaluations = evaluations.map(evaluation => {\n          const evalWithDetails = evaluation;\n          // Vérifier si les détails du projet sont disponibles\n          if (!evalWithDetails.projetDetails || !evalWithDetails.projetDetails.titre) {\n            console.warn('Détails du projet manquants pour l\\'évaluation:', evalWithDetails._id);\n            // Si le rendu contient des détails de projet, les utiliser\n            if (evalWithDetails.renduDetails && evalWithDetails.renduDetails.projet) {\n              evalWithDetails.projetDetails = evalWithDetails.renduDetails.projet;\n            }\n          }\n          return evalWithDetails;\n        });\n        this.extractGroupesAndProjets();\n        this.applyFilters();\n      }\n    });\n  }\n  extractGroupesAndProjets() {\n    const groupesSet = new Set();\n    this.evaluations.forEach(evaluation => {\n      if (evaluation.etudiant) {\n        const groupeName = this.getStudentGroup(evaluation.etudiant);\n        if (groupeName && groupeName !== 'Non spécifié') {\n          groupesSet.add(groupeName);\n        }\n      }\n    });\n    this.groupes = Array.from(groupesSet).sort();\n    // Extraire les projets uniques\n    const projetsMap = new Map();\n    this.evaluations.forEach(evaluation => {\n      if (evaluation.projetDetails && evaluation.projetDetails._id) {\n        projetsMap.set(evaluation.projetDetails._id, evaluation.projetDetails);\n      }\n    });\n    this.projets = Array.from(projetsMap.values());\n  }\n  applyFilters() {\n    let results = this.evaluations;\n    // Filtre par terme de recherche\n    if (this.searchTerm.trim() !== '') {\n      const term = this.searchTerm.toLowerCase().trim();\n      results = results.filter(evaluation => evaluation.etudiant?.nom?.toLowerCase().includes(term) || evaluation.etudiant?.prenom?.toLowerCase().includes(term) || evaluation.projetDetails?.titre?.toLowerCase().includes(term));\n    }\n    // Filtre par groupe\n    if (this.filterGroupe) {\n      results = results.filter(evaluation => evaluation.etudiant?.groupe === this.filterGroupe);\n    }\n    // Filtre par projet\n    if (this.filterProjet) {\n      results = results.filter(evaluation => evaluation.projetDetails?._id === this.filterProjet);\n    }\n    this.filteredEvaluations = results;\n  }\n  onSearchChange() {\n    this.applyFilters();\n  }\n  clearSearch() {\n    this.searchTerm = '';\n    this.applyFilters();\n  }\n  refreshList() {\n    console.log('Actualisation de la liste des évaluations...');\n    // Réinitialiser tous les filtres\n    this.searchTerm = '';\n    this.filterGroupe = '';\n    this.filterProjet = '';\n    // Recharger complètement les données depuis le serveur\n    this.loadEvaluations();\n    console.log('Liste actualisée et filtres réinitialisés');\n  }\n  resetFilters() {\n    console.log('Réinitialisation des filtres...');\n    // Réinitialiser tous les filtres\n    this.searchTerm = '';\n    this.filterGroupe = '';\n    this.filterProjet = '';\n    // Appliquer les filtres (qui vont maintenant montrer toutes les évaluations)\n    this.applyFilters();\n    console.log('Filtres réinitialisés');\n  }\n  editEvaluation(renduId) {\n    this.router.navigate(['/admin/projects/edit-evaluation', renduId]);\n  }\n  viewEvaluationDetails(renduId) {\n    this.router.navigate(['/admin/projects/evaluation-details', renduId]);\n  }\n  getScoreTotal(evaluation) {\n    if (!evaluation.scores) return 0;\n    const scores = evaluation.scores;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreClass(score) {\n    if (score >= 16) return 'text-green-600 bg-green-100';\n    if (score >= 12) return 'text-blue-600 bg-blue-100';\n    if (score >= 8) return 'text-yellow-600 bg-yellow-100';\n    return 'text-red-600 bg-red-100';\n  }\n  formatDate(date) {\n    if (!date) return 'Non disponible';\n    return new Date(date).toLocaleDateString();\n  }\n  updateMissingGroups() {\n    if (!confirm('Voulez-vous mettre à jour les groupes manquants des étudiants?')) {\n      return;\n    }\n    this.isLoading = true;\n    this.evaluationService.updateMissingGroups().subscribe({\n      next: response => {\n        alert(`${response.updatedCount} étudiants mis à jour avec leur groupe.`);\n        this.loadEvaluations();\n      },\n      error: err => {\n        alert('Erreur lors de la mise à jour des groupes.');\n        this.isLoading = false;\n      }\n    });\n  }\n  static {\n    this.ɵfac = function EvaluationsListComponent_Factory(t) {\n      return new (t || EvaluationsListComponent)(i0.ɵɵdirectiveInject(i1.RendusService), i0.ɵɵdirectiveInject(i2.EvaluationService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EvaluationsListComponent,\n      selectors: [[\"app-evaluations-list\"]],\n      decls: 31,\n      vars: 8,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-dark-bg-primary\", \"transition-colors\", \"duration-300\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-2xl\", \"p-8\", \"mb-8\", \"shadow-xl\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"bg-white/20\", \"dark:bg-black/20\", \"p-3\", \"rounded-xl\", \"backdrop-blur-sm\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-white/80\"], [1, \"hidden\", \"md:flex\", \"items-center\", \"space-x-4\", \"text-white/80\"], [1, \"text-center\"], [1, \"text-2xl\", \"font-bold\"], [1, \"text-sm\"], [1, \"w-px\", \"h-12\", \"bg-white/20\"], [\"class\", \"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-4 mb-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-6 mb-6 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 mb-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\", 4, \"ngIf\"], [\"class\", \"space-y-6\", 4, \"ngIf\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-4\", \"mb-6\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"relative\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-400\", \"dark:text-dark-text-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"], [\"type\", \"text\", \"placeholder\", \"Rechercher par nom, email, projet ou groupe...\", 1, \"block\", \"w-full\", \"pl-10\", \"pr-3\", \"py-3\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"leading-5\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-gray-500\", \"dark:placeholder-dark-text-secondary\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"class\", \"absolute inset-y-0 right-0 pr-3 flex items-center\", 4, \"ngIf\"], [\"class\", \"mt-2 text-sm text-text dark:text-dark-text-secondary\", 4, \"ngIf\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"pr-3\", \"flex\", \"items-center\"], [1, \"text-gray-400\", \"hover:text-gray-600\", \"dark:text-dark-text-secondary\", \"dark:hover:text-dark-text-primary\", \"transition-colors\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"mt-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-primary/30\", \"dark:border-dark-accent-primary/30\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-transparent\", \"border-t-primary\", \"dark:border-t-dark-accent-primary\", \"absolute\", \"top-0\", \"left-0\"], [1, \"mt-4\", \"text-text\", \"dark:text-dark-text-secondary\", \"animate-pulse\"], [1, \"bg-danger/10\", \"dark:bg-danger-dark/20\", \"border\", \"border-danger/30\", \"dark:border-danger-dark/40\", \"text-danger\", \"dark:text-danger-dark\", \"rounded-xl\", \"p-6\", \"mb-6\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-danger\", \"dark:text-danger-dark\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"font-medium\"], [1, \"px-4\", \"py-2\", \"bg-danger/20\", \"dark:bg-danger-dark/20\", \"text-danger\", \"dark:text-danger-dark\", \"rounded-lg\", \"hover:bg-danger/30\", \"dark:hover:bg-danger-dark/30\", \"transition-colors\", \"font-medium\", 3, \"click\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"mb-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [1, \"space-y-2\"], [1, \"block\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"flex\", \"flex-col\", \"space-y-2\"], [1, \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"], [3, \"value\"], [1, \"space-y-6\"], [\"class\", \"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"hover:shadow-xl\", \"transition-all\", \"duration-300\", \"group\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\", \"space-y-4\", \"lg:space-y-0\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-lg\", \"font-bold\", \"shadow-lg\"], [1, \"absolute\", \"-bottom-1\", \"-right-1\", \"w-6\", \"h-6\", \"bg-gradient-to-r\", \"from-success\", \"to-success-dark\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"flex-1\"], [1, \"text-lg\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"sm:items-center\", \"space-y-3\", \"sm:space-y-0\", \"sm:space-x-6\"], [1, \"bg-info/10\", \"dark:bg-dark-accent-primary/10\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-info\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L16 7\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"p-2\", \"rounded-lg\", 3, \"ngClass\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [1, \"text-lg\", \"font-bold\", 3, \"ngClass\"], [1, \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-lg\", \"p-3\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-1\"], [1, \"grid\", \"grid-cols-2\", \"gap-1\", \"text-xs\"], [1, \"flex\", \"justify-between\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-2\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-info\", \"to-primary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"group-hover/btn:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary-dark\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-danger\", \"to-danger-dark\", \"dark:from-danger-dark\", \"dark:to-danger\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"text-center\", \"py-16\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-12\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"max-w-md\", \"mx-auto\"], [1, \"bg-gradient-to-br\", \"from-primary/10\", \"to-secondary/10\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-secondary/20\", \"rounded-2xl\", \"p-6\", \"mb-6\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-16\", \"w-16\", \"mx-auto\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-2\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [1, \"px-6\", \"py-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"]],\n      template: function EvaluationsListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 6);\n          i0.ɵɵelement(7, \"path\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(8, \"div\")(9, \"h1\", 8);\n          i0.ɵɵtext(10, \"Liste des \\u00C9valuations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 9);\n          i0.ɵɵtext(12, \"Gestion et suivi des \\u00E9valuations de projets\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\", 11)(15, \"div\", 12);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 13);\n          i0.ɵɵtext(18, \"Total\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(19, \"div\", 14);\n          i0.ɵɵelementStart(20, \"div\", 11)(21, \"div\", 12);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 13);\n          i0.ɵɵtext(24, \"Moyenne\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(25, EvaluationsListComponent_div_25_Template, 8, 3, \"div\", 15);\n          i0.ɵɵtemplate(26, EvaluationsListComponent_div_26_Template, 6, 0, \"div\", 16);\n          i0.ɵɵtemplate(27, EvaluationsListComponent_div_27_Template, 9, 1, \"div\", 17);\n          i0.ɵɵtemplate(28, EvaluationsListComponent_div_28_Template, 45, 6, \"div\", 18);\n          i0.ɵɵtemplate(29, EvaluationsListComponent_div_29_Template, 2, 1, \"div\", 19);\n          i0.ɵɵtemplate(30, EvaluationsListComponent_div_30_Template, 15, 0, \"div\", 20);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵtextInterpolate(ctx.evaluations.length);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.getAverageScore());\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.filteredEvaluations.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.filteredEvaluations.length === 0);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\"\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 2rem 0;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  margin-top: 0.25rem;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_scaleIn {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n\\n\\n.glass-card[_ngcontent-%COMP%] {\\n  backdrop-filter: blur(10px);\\n  -webkit-backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .glass-card[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n\\n\\n.evaluation-card[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.evaluation-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .evaluation-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n.btn-modern[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.btn-modern[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n\\n.btn-modern[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n\\n\\n.avatar-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f5fad 0%, #7826b5 100%);\\n  transition: all 0.3s ease;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .avatar-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00f7ff 0%, #9d4edd 100%);\\n}\\n\\n.avatar-gradient[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 8px 25px rgba(79, 95, 173, 0.3);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .avatar-gradient[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 8px 25px rgba(0, 247, 255, 0.3);\\n}\\n\\n\\n\\n.score-badge[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_scaleIn 0.4s ease-out;\\n  transition: all 0.2s ease;\\n}\\n\\n.score-badge[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n\\n\\n.filter-select[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.filter-select[_ngcontent-%COMP%]:focus {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(79, 95, 173, 0.15);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 8px 25px rgba(0, 247, 255, 0.15);\\n}\\n\\n\\n\\n.header-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f5fad 0%, #7826b5 100%);\\n  animation: _ngcontent-%COMP%_slideInRight 0.8s ease-out;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .header-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00f7ff 0%, #9d4edd 100%);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .evaluation-card[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n\\n  .btn-modern[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n\\n  .filter-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 1rem;\\n  }\\n}\\n\\n\\n\\n.icon-hover[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n\\n.icon-hover[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(5deg);\\n}\\n\\n\\n\\n.tooltip[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.tooltip[_ngcontent-%COMP%]::after {\\n  content: attr(data-tooltip);\\n  position: absolute;\\n  bottom: 100%;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.8);\\n  color: white;\\n  padding: 0.5rem;\\n  border-radius: 0.375rem;\\n  font-size: 0.75rem;\\n  white-space: nowrap;\\n  opacity: 0;\\n  pointer-events: none;\\n  transition: opacity 0.3s;\\n  z-index: 1000;\\n}\\n\\n.tooltip[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n}\\n\\n\\n\\n.loading-pulse[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.8s ease-out;\\n}\\n\\n\\n\\n.focus-visible[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #4f5fad;\\n  outline-offset: 2px;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .focus-visible[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #00f7ff;\\n}\\n\\n\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);\\n  transition: all 0.3s ease;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);\\n  transform: scale(1.05);\\n  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);\\n  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["catchError", "finalize", "takeUntil", "Subject", "of", "i0", "ɵɵelementStart", "ɵɵlistener", "EvaluationsListComponent_div_25_div_6_Template_button_click_1_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r7", "filteredEvaluations", "length", "searchTerm", "ɵɵnamespaceHTML", "EvaluationsListComponent_div_25_Template_input_ngModelChange_5_listener", "$event", "_r11", "ctx_r10", "EvaluationsListComponent_div_25_Template_input_input_5_listener", "ctx_r12", "onSearchChange", "ɵɵtemplate", "EvaluationsListComponent_div_25_div_6_Template", "EvaluationsListComponent_div_25_div_7_Template", "ɵɵproperty", "ctx_r0", "EvaluationsListComponent_div_27_Template_button_click_7_listener", "_r14", "ctx_r13", "loadEvaluations", "ɵɵtextInterpolate", "ctx_r2", "error", "groupe_r17", "projet_r18", "_id", "titre", "EvaluationsListComponent_div_28_Template_select_ngModelChange_15_listener", "_r20", "ctx_r19", "filterGroupe", "EvaluationsListComponent_div_28_Template_select_change_15_listener", "ctx_r21", "applyFilters", "EvaluationsListComponent_div_28_option_18_Template", "EvaluationsListComponent_div_28_Template_select_ngModelChange_26_listener", "ctx_r22", "filterProjet", "EvaluationsListComponent_div_28_Template_select_change_26_listener", "ctx_r23", "EvaluationsListComponent_div_28_option_29_Template", "EvaluationsListComponent_div_28_Template_button_click_39_listener", "ctx_r24", "refreshList", "ctx_r3", "ɵɵtextInterpolate1", "groupes", "projets", "EvaluationsListComponent_div_29_div_1_Template_button_click_69_listener", "restoredCtx", "_r28", "evaluation_r26", "$implicit", "ctx_r27", "viewEvaluationDetails", "rendu", "EvaluationsListComponent_div_29_div_1_Template_button_click_76_listener", "ctx_r29", "editEvaluation", "EvaluationsListComponent_div_29_div_1_Template_button_click_82_listener", "ctx_r30", "deleteEvaluation", "ctx_r25", "getStudentInitials", "etudiant", "getStudentName", "email", "getStudentGroup", "getProjectTitle", "formatDate", "dateEvaluation", "getScoreIconClass", "getScoreTotal", "getScoreColorClass", "scores", "structure", "pratiques", "fonctionnalite", "originalite", "EvaluationsListComponent_div_29_div_1_Template", "ctx_r4", "EvaluationsListComponent_div_30_Template_button_click_9_listener", "_r32", "ctx_r31", "resetFilters", "EvaluationsListComponent", "constructor", "rendusService", "evaluationService", "router", "evaluations", "isLoading", "destroy$", "ngOnInit", "ngOnDestroy", "next", "complete", "console", "log", "getAllEvaluations", "pipe", "err", "subscribe", "Array", "isArray", "map", "evaluation", "evalWithDetails", "projetDetails", "warn", "renduDetails", "projet", "extractGroupesAndProjets", "groupesSet", "Set", "for<PERSON>ach", "groupeName", "add", "from", "sort", "projetsMap", "Map", "set", "values", "results", "trim", "term", "toLowerCase", "filter", "nom", "includes", "prenom", "groupe", "renduId", "navigate", "getScoreClass", "score", "date", "Date", "toLocaleDateString", "updateMissingGroups", "confirm", "response", "alert", "updatedCount", "ɵɵdirectiveInject", "i1", "RendusService", "i2", "EvaluationService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "EvaluationsListComponent_Template", "rf", "ctx", "EvaluationsListComponent_div_25_Template", "EvaluationsListComponent_div_26_Template", "EvaluationsListComponent_div_27_Template", "EvaluationsListComponent_div_28_Template", "EvaluationsListComponent_div_29_Template", "EvaluationsListComponent_div_30_Template", "getAverageScore"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\evaluations-list\\evaluations-list.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\evaluations-list\\evaluations-list.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { RendusService } from '../../../../services/rendus.service';\nimport { EvaluationService } from '../../../../services/evaluation.service';\nimport { catchError, finalize, takeUntil } from 'rxjs/operators';\nimport { Subject, of } from 'rxjs';\nimport { Evaluation } from '../../../../models/evaluation';\nimport { Rendu } from '../../../../models/rendu';\n\n// Interface pour les évaluations avec détails\ninterface EvaluationWithDetails extends Evaluation {\n  renduDetails?: Rendu;\n  etudiant?: any;\n  projetDetails?: any;\n}\n\n@Component({\n  selector: 'app-evaluations-list',\n  templateUrl: './evaluations-list.component.html',\n  styleUrls: ['./evaluations-list.component.css'],\n})\nexport class EvaluationsListComponent implements OnInit, OnDestroy {\n  evaluations: EvaluationWithDetails[] = [];\n  filteredEvaluations: EvaluationWithDetails[] = [];\n  isLoading: boolean = true;\n  error: string = '';\n  searchTerm: string = '';\n  filterGroupe: string = '';\n  filterProjet: string = '';\n  groupes: string[] = [];\n  projets: any[] = [];\n\n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private rendusService: RendusService,\n    private evaluationService: EvaluationService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadEvaluations();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadEvaluations(): void {\n    this.isLoading = true;\n    this.error = '';\n\n    console.log('Début du chargement des évaluations...');\n\n    this.evaluationService.getAllEvaluations()\n      .pipe(\n        takeUntil(this.destroy$),\n        catchError((err) => {\n          this.error =\n            'Impossible de charger les évaluations. Veuillez réessayer plus tard.';\n          this.isLoading = false;\n          return of([]);\n        }),\n        finalize(() => {\n          this.isLoading = false;\n        })\n      )\n      .subscribe({\n        next: (evaluations) => {\n          console.log('Évaluations reçues:', evaluations);\n\n          if (!Array.isArray(evaluations)) {\n            this.error =\n              'Format de données incorrect. Veuillez réessayer plus tard.';\n            return;\n          }\n\n          // Vérifier et compléter les données manquantes\n          this.evaluations = evaluations.map(evaluation => {\n            const evalWithDetails = evaluation as EvaluationWithDetails;\n\n            // Vérifier si les détails du projet sont disponibles\n            if (!evalWithDetails.projetDetails || !evalWithDetails.projetDetails.titre) {\n              console.warn('Détails du projet manquants pour l\\'évaluation:', evalWithDetails._id);\n\n              // Si le rendu contient des détails de projet, les utiliser\n              if (evalWithDetails.renduDetails && evalWithDetails.renduDetails.projet) {\n                evalWithDetails.projetDetails = evalWithDetails.renduDetails.projet;\n              }\n            }\n\n            return evalWithDetails;\n          });\n\n          this.extractGroupesAndProjets();\n          this.applyFilters();\n        }\n      });\n  }\n\n  extractGroupesAndProjets(): void {\n    const groupesSet = new Set<string>();\n\n    this.evaluations.forEach(evaluation => {\n      if (evaluation.etudiant) {\n        const groupeName = this.getStudentGroup(evaluation.etudiant);\n        if (groupeName && groupeName !== 'Non spécifié') {\n          groupesSet.add(groupeName);\n        }\n      }\n    });\n\n    this.groupes = Array.from(groupesSet).sort();\n\n    // Extraire les projets uniques\n    const projetsMap = new Map<string, any>();\n    this.evaluations.forEach((evaluation) => {\n      if (evaluation.projetDetails && evaluation.projetDetails._id) {\n        projetsMap.set(evaluation.projetDetails._id, evaluation.projetDetails);\n      }\n    });\n    this.projets = Array.from(projetsMap.values());\n  }\n\n  applyFilters(): void {\n    let results = this.evaluations;\n\n    // Filtre par terme de recherche\n    if (this.searchTerm.trim() !== '') {\n      const term = this.searchTerm.toLowerCase().trim();\n<<<<<<< HEAD\n      results = results.filter(\n        (evaluation) =>\n          evaluation.etudiant?.nom?.toLowerCase().includes(term) ||\n          evaluation.etudiant?.prenom?.toLowerCase().includes(term) ||\n          evaluation.projetDetails?.titre?.toLowerCase().includes(term)\n      );\n=======\n      results = results.filter(evaluation => {\n        if (!evaluation.etudiant) return false;\n\n        const studentName = this.getStudentName(evaluation.etudiant).toLowerCase();\n        const email = (evaluation.etudiant.email || '').toLowerCase();\n        const projectTitle = this.getProjectTitle(evaluation).toLowerCase();\n        const groupName = this.getStudentGroup(evaluation.etudiant).toLowerCase();\n\n        return studentName.includes(term) ||\n               email.includes(term) ||\n               projectTitle.includes(term) ||\n               groupName.includes(term);\n      });\n>>>>>>> 55e956fe8a3d446c30a69e1ddb353494fce2c82b\n    }\n\n    // Filtre par groupe\n    if (this.filterGroupe) {\n<<<<<<< HEAD\n      results = results.filter(\n        (evaluation) => evaluation.etudiant?.groupe === this.filterGroupe\n      );\n=======\n      results = results.filter(evaluation => {\n        const groupeName = this.getStudentGroup(evaluation.etudiant);\n        return groupeName === this.filterGroupe;\n      });\n>>>>>>> 55e956fe8a3d446c30a69e1ddb353494fce2c82b\n    }\n\n    // Filtre par projet\n    if (this.filterProjet) {\n      results = results.filter(\n        (evaluation) => evaluation.projetDetails?._id === this.filterProjet\n      );\n    }\n\n    this.filteredEvaluations = results;\n  }\n\n  onSearchChange(): void {\n    this.applyFilters();\n  }\n\n  clearSearch(): void {\n    this.searchTerm = '';\n    this.applyFilters();\n  }\n\n  refreshList(): void {\n    console.log('Actualisation de la liste des évaluations...');\n\n    // Réinitialiser tous les filtres\n    this.searchTerm = '';\n    this.filterGroupe = '';\n    this.filterProjet = '';\n\n    // Recharger complètement les données depuis le serveur\n    this.loadEvaluations();\n\n    console.log('Liste actualisée et filtres réinitialisés');\n  }\n\n  resetFilters(): void {\n    console.log('Réinitialisation des filtres...');\n\n    // Réinitialiser tous les filtres\n    this.searchTerm = '';\n    this.filterGroupe = '';\n    this.filterProjet = '';\n\n    // Appliquer les filtres (qui vont maintenant montrer toutes les évaluations)\n    this.applyFilters();\n\n    console.log('Filtres réinitialisés');\n  }\n\n  editEvaluation(renduId: string): void {\n    this.router.navigate(['/admin/projects/edit-evaluation', renduId]);\n  }\n\n  viewEvaluationDetails(renduId: string): void {\n    this.router.navigate(['/admin/projects/evaluation-details', renduId]);\n  }\n\n  getScoreTotal(evaluation: EvaluationWithDetails): number {\n    if (!evaluation.scores) return 0;\n\n    const scores = evaluation.scores;\n    return (\n      scores.structure +\n      scores.pratiques +\n      scores.fonctionnalite +\n      scores.originalite\n    );\n  }\n\n  getScoreClass(score: number): string {\n    if (score >= 16) return 'text-green-600 bg-green-100';\n    if (score >= 12) return 'text-blue-600 bg-blue-100';\n    if (score >= 8) return 'text-yellow-600 bg-yellow-100';\n    return 'text-red-600 bg-red-100';\n  }\n\n  formatDate(date: string | Date | undefined): string {\n    if (!date) return 'Non disponible';\n    return new Date(date).toLocaleDateString();\n  }\n\n<<<<<<< HEAD\n  // Ajouter cette méthode pour mettre à jour les groupes manquants\n  updateMissingGroups(): void {\n    if (\n      !confirm('Voulez-vous mettre à jour les groupes manquants des étudiants?')\n    ) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    this.evaluationService.updateMissingGroups().subscribe({\n      next: (response) => {\n        alert(\n          `${response.updatedCount} étudiants mis à jour avec leur groupe.`\n        );\n        this.loadEvaluations();\n      },\n      error: (err) => {\n        alert('Erreur lors de la mise à jour des groupes.');\n        this.isLoading = false;\n      },\n=======\n\n\n\n\n  // Nouvelles méthodes pour le design moderne\n  getStudentInitials(etudiant: any): string {\n    if (!etudiant) return '??';\n\n    // Priorité 1: firstName + lastName (si lastName existe et n'est pas vide)\n    const firstName = etudiant.firstName || '';\n    const lastName = etudiant.lastName || '';\n\n    if (firstName && lastName && lastName.trim()) {\n      return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();\n    }\n\n    // Priorité 2: fullName (diviser en mots)\n    const fullName = etudiant.fullName || etudiant.name || etudiant.username || '';\n    if (fullName && fullName.trim()) {\n      const parts = fullName.trim().split(' ');\n      if (parts.length >= 2) {\n        return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();\n      } else {\n        // Si un seul mot, prendre les 2 premières lettres\n        return fullName.substring(0, 2).toUpperCase();\n      }\n    }\n\n    // Priorité 3: firstName seul (prendre les 2 premières lettres)\n    if (firstName && firstName.trim()) {\n      return firstName.substring(0, 2).toUpperCase();\n    }\n\n    return '??';\n  }\n\n  getStudentName(etudiant: any): string {\n    if (!etudiant) return 'Utilisateur inconnu';\n\n    // Priorité 1: firstName + lastName (si lastName existe et n'est pas vide)\n    const firstName = etudiant.firstName || '';\n    const lastName = etudiant.lastName || '';\n\n    if (firstName && lastName && lastName.trim()) {\n      return `${firstName} ${lastName}`.trim();\n    }\n\n    // Priorité 2: fullName\n    const fullName = etudiant.fullName || etudiant.name || etudiant.username || '';\n    if (fullName && fullName.trim()) {\n      return fullName.trim();\n    }\n\n    // Priorité 3: firstName seul\n    if (firstName && firstName.trim()) {\n      return firstName.trim();\n    }\n\n    // Priorité 4: email comme fallback\n    if (etudiant.email) {\n      return etudiant.email;\n    }\n\n    return 'Utilisateur inconnu';\n  }\n\n  getStudentGroup(etudiant: any): string {\n    if (!etudiant) return 'Non spécifié';\n\n    // Debug: afficher les données de l'étudiant\n    console.log('Données étudiant pour groupe:', {\n      email: etudiant.email,\n      group: etudiant.group,\n      groupe: etudiant.groupe,\n      groupName: etudiant.groupName,\n      department: etudiant.department,\n      allData: etudiant\n    });\n\n    // Si group est un objet (référence populée avec le modèle Group)\n    if (etudiant.group && typeof etudiant.group === 'object' && etudiant.group.name) {\n      console.log(`Groupe objet trouvé pour ${etudiant.email}: ${etudiant.group.name}`);\n      return etudiant.group.name;\n    }\n\n    // Si group est une chaîne directe (valeur ajoutée manuellement)\n    if (etudiant.group && typeof etudiant.group === 'string' && etudiant.group.trim()) {\n      console.log(`Groupe string trouvé pour ${etudiant.email}: ${etudiant.group}`);\n      return etudiant.group.trim();\n    }\n\n    // Fallback vers d'autres champs possibles\n    if (etudiant.groupe && typeof etudiant.groupe === 'string' && etudiant.groupe.trim()) {\n      console.log(`Groupe (ancien champ) trouvé pour ${etudiant.email}: ${etudiant.groupe}`);\n      return etudiant.groupe.trim();\n    }\n\n    if (etudiant.groupName && typeof etudiant.groupName === 'string' && etudiant.groupName.trim()) {\n      console.log(`GroupName trouvé pour ${etudiant.email}: ${etudiant.groupName}`);\n      return etudiant.groupName.trim();\n    }\n\n    if (etudiant.department && typeof etudiant.department === 'string' && etudiant.department.trim()) {\n      console.log(`Department trouvé pour ${etudiant.email}: ${etudiant.department}`);\n      return etudiant.department.trim();\n    }\n\n    console.log(`Aucun groupe trouvé pour ${etudiant.email}`);\n    return 'Non spécifié';\n  }\n\n  getProjectTitle(evaluation: EvaluationWithDetails): string {\n    return evaluation.projetDetails?.titre ||\n           evaluation.renduDetails?.projet?.titre ||\n           'Projet inconnu';\n  }\n\n  getAverageScore(): string {\n    if (this.evaluations.length === 0) return '0';\n\n    const totalScore = this.evaluations.reduce((sum, evaluation) => {\n      return sum + this.getScoreTotal(evaluation);\n    }, 0);\n\n    const average = totalScore / this.evaluations.length;\n    return average.toFixed(1);\n  }\n\n  getScoreIconClass(score: number): string {\n    if (score >= 16) return 'bg-success/10 dark:bg-dark-accent-secondary/10 text-success dark:text-dark-accent-secondary';\n    if (score >= 12) return 'bg-info/10 dark:bg-dark-accent-primary/10 text-info dark:text-dark-accent-primary';\n    if (score >= 8) return 'bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning';\n    return 'bg-danger/10 dark:bg-danger-dark/20 text-danger dark:text-danger-dark';\n  }\n\n  getScoreColorClass(score: number): string {\n    if (score >= 16) return 'text-success dark:text-dark-accent-secondary';\n    if (score >= 12) return 'text-info dark:text-dark-accent-primary';\n    if (score >= 8) return 'text-warning dark:text-warning';\n    return 'text-danger dark:text-danger-dark';\n  }\n\n  // Méthode pour supprimer une évaluation\n  deleteEvaluation(evaluationId: string): void {\n    if (!confirm('Êtes-vous sûr de vouloir supprimer cette évaluation ? Cette action est irréversible.')) {\n      return;\n    }\n\n    this.evaluationService.deleteEvaluation(evaluationId).subscribe({\n      next: () => {\n        alert('Évaluation supprimée avec succès !');\n        this.loadEvaluations(); // Recharger la liste\n      },\n      error: (err: any) => {\n        console.error('Erreur lors de la suppression:', err);\n        alert('Erreur lors de la suppression de l\\'évaluation.');\n      }\n>>>>>>> 55e956fe8a3d446c30a69e1ddb353494fce2c82b\n    });\n  }\n}\n", "<div class=\"min-h-screen bg-[#edf1f4] dark:bg-dark-bg-primary transition-colors duration-300\">\n  <div class=\"container mx-auto px-4 py-8\">\n    <!-- Header avec gradient -->\n    <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-2xl p-8 mb-8 shadow-xl\">\n      <div class=\"flex items-center justify-between\">\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"bg-white/20 dark:bg-black/20 p-3 rounded-xl backdrop-blur-sm\">\n            <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"></path>\n            </svg>\n          </div>\n          <div>\n            <h1 class=\"text-3xl font-bold text-white mb-2\">Liste des Évaluations</h1>\n            <p class=\"text-white/80\">Gestion et suivi des évaluations de projets</p>\n          </div>\n        </div>\n        <div class=\"hidden md:flex items-center space-x-4 text-white/80\">\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold\">{{ evaluations.length }}</div>\n            <div class=\"text-sm\">Total</div>\n          </div>\n          <div class=\"w-px h-12 bg-white/20\"></div>\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold\">{{ getAverageScore() }}</div>\n            <div class=\"text-sm\">Moyenne</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Barre de recherche globale -->\n    <div *ngIf=\"!isLoading && !error\" class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-4 mb-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\n      <div class=\"relative\">\n        <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <svg class=\"w-5 h-5 text-gray-400 dark:text-dark-text-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n          </svg>\n        </div>\n        <input type=\"text\"\n               [(ngModel)]=\"searchTerm\"\n               (input)=\"onSearchChange()\"\n               class=\"block w-full pl-10 pr-3 py-3 border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl leading-5 bg-white dark:bg-dark-bg-secondary text-text-dark dark:text-dark-text-primary placeholder-gray-500 dark:placeholder-dark-text-secondary focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200\"\n               placeholder=\"Rechercher par nom, email, projet ou groupe...\">\n        <div *ngIf=\"searchTerm\" class=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n          <button (click)=\"clearSearch()\" class=\"text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text-primary transition-colors\">\n            <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n            </svg>\n          </button>\n        </div>\n      </div>\n      <div *ngIf=\"searchTerm\" class=\"mt-2 text-sm text-text dark:text-dark-text-secondary\">\n        {{ filteredEvaluations.length }} résultat(s) trouvé(s) pour \"{{ searchTerm }}\"\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div *ngIf=\"isLoading\" class=\"flex flex-col items-center justify-center py-16\">\n      <div class=\"relative\">\n        <div class=\"animate-spin rounded-full h-16 w-16 border-4 border-primary/30 dark:border-dark-accent-primary/30\"></div>\n        <div class=\"animate-spin rounded-full h-16 w-16 border-4 border-transparent border-t-primary dark:border-t-dark-accent-primary absolute top-0 left-0\"></div>\n      </div>\n      <p class=\"mt-4 text-text dark:text-dark-text-secondary animate-pulse\">Chargement des évaluations...</p>\n    </div>\n\n    <!-- Error State -->\n    <div *ngIf=\"error\" class=\"bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-6 mb-6 backdrop-blur-sm\">\n      <div class=\"flex items-center justify-between\">\n        <div class=\"flex items-center space-x-3\">\n          <svg class=\"w-5 h-5 text-danger dark:text-danger-dark flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n          </svg>\n          <p class=\"font-medium\">{{ error }}</p>\n        </div>\n        <button (click)=\"loadEvaluations()\"\n          class=\"px-4 py-2 bg-danger/20 dark:bg-danger-dark/20 text-danger dark:text-danger-dark rounded-lg hover:bg-danger/30 dark:hover:bg-danger-dark/30 transition-colors font-medium\">\n          Réessayer\n        </button>\n      </div>\n    </div>\n\n    <!-- Filtres modernes -->\n    <div *ngIf=\"!isLoading && !error\" class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 mb-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\n      <div class=\"flex items-center space-x-3 mb-6\">\n        <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg\">\n          <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"></path>\n          </svg>\n        </div>\n        <h2 class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary\">Filtres et recherche</h2>\n      </div>\n\n      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <!-- Filtre par groupe -->\n        <div class=\"space-y-2\">\n          <label class=\"block text-sm font-semibold text-text-dark dark:text-dark-text-primary\">\n            <div class=\"flex items-center space-x-2\">\n              <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n              </svg>\n              <span>Filtrer par groupe</span>\n            </div>\n          </label>\n          <select [(ngModel)]=\"filterGroupe\" (change)=\"applyFilters()\"\n            class=\"w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary\">\n            <option value=\"\">Tous les groupes ({{ groupes.length }})</option>\n            <option *ngFor=\"let groupe of groupes\" [value]=\"groupe\">{{ groupe }}</option>\n          </select>\n        </div>\n\n        <!-- Filtre par projet -->\n        <div class=\"space-y-2\">\n          <label class=\"block text-sm font-semibold text-text-dark dark:text-dark-text-primary\">\n            <div class=\"flex items-center space-x-2\">\n              <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\n              </svg>\n              <span>Filtrer par projet</span>\n            </div>\n          </label>\n          <select [(ngModel)]=\"filterProjet\" (change)=\"applyFilters()\"\n            class=\"w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary\">\n            <option value=\"\">Tous les projets ({{ projets.length }})</option>\n            <option *ngFor=\"let projet of projets\" [value]=\"projet._id\">{{ projet.titre }}</option>\n          </select>\n        </div>\n\n        <!-- Actions -->\n        <div class=\"space-y-2\">\n          <label class=\"block text-sm font-semibold text-text-dark dark:text-dark-text-primary\">\n            <div class=\"flex items-center space-x-2\">\n              <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n              </svg>\n              <span>Actions rapides</span>\n            </div>\n          </label>\n          <div class=\"flex flex-col space-y-2\">\n            <button (click)=\"refreshList()\"\n              class=\"px-4 py-3 bg-gradient-to-r from-secondary to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\n              <div class=\"flex items-center justify-center space-x-2\">\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n                </svg>\n                <span>Actualiser</span>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Liste des évaluations en cartes modernes -->\n    <div *ngIf=\"!isLoading && !error && filteredEvaluations.length > 0\" class=\"space-y-6\">\n      <div *ngFor=\"let evaluation of filteredEvaluations\" class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group\">\n        <div class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\">\n\n          <!-- Informations étudiant -->\n          <div class=\"flex items-center space-x-4\">\n            <div class=\"relative\">\n              <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center text-white text-lg font-bold shadow-lg\">\n                {{ getStudentInitials(evaluation.etudiant) }}\n              </div>\n              <div class=\"absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-r from-success to-success-dark dark:from-dark-accent-secondary dark:to-dark-accent-primary rounded-full flex items-center justify-center\">\n                <svg class=\"w-3 h-3 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                </svg>\n              </div>\n            </div>\n            <div class=\"flex-1\">\n              <h3 class=\"text-lg font-bold text-text-dark dark:text-dark-text-primary\">\n                {{ getStudentName(evaluation.etudiant) }}\n              </h3>\n              <p class=\"text-sm text-text dark:text-dark-text-secondary\">{{ evaluation.etudiant?.email || 'Email non disponible' }}</p>\n              <div class=\"flex items-center space-x-4 mt-2\">\n                <div class=\"flex items-center space-x-1\">\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                  </svg>\n                  <span class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary\">\n                    {{ getStudentGroup(evaluation.etudiant) }}\n                  </span>\n                </div>\n                <div class=\"flex items-center space-x-1\">\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\n                  </svg>\n                  <span class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary\">\n                    {{ getProjectTitle(evaluation) }}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Informations de l'évaluation -->\n          <div class=\"flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-6\">\n            <!-- Date d'évaluation -->\n            <div class=\"flex items-center space-x-2\">\n              <div class=\"bg-info/10 dark:bg-dark-accent-primary/10 p-2 rounded-lg\">\n                <svg class=\"w-4 h-4 text-info dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L16 7\"></path>\n                </svg>\n              </div>\n              <div>\n                <p class=\"text-xs text-text dark:text-dark-text-secondary\">Évaluée le</p>\n                <p class=\"text-sm font-semibold text-text-dark dark:text-dark-text-primary\">{{ formatDate(evaluation.dateEvaluation) }}</p>\n              </div>\n            </div>\n\n            <!-- Score -->\n            <div class=\"flex items-center space-x-2\">\n              <div [ngClass]=\"getScoreIconClass(getScoreTotal(evaluation))\" class=\"p-2 rounded-lg\">\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n                </svg>\n              </div>\n              <div>\n                <p class=\"text-xs text-text dark:text-dark-text-secondary\">Score total</p>\n                <span [ngClass]=\"getScoreColorClass(getScoreTotal(evaluation))\" class=\"text-lg font-bold\">\n                  {{ getScoreTotal(evaluation) }}/20\n                </span>\n              </div>\n            </div>\n\n            <!-- Détails des scores -->\n            <div class=\"bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-lg p-3\">\n              <p class=\"text-xs text-text dark:text-dark-text-secondary mb-1\">Détail des scores</p>\n              <div class=\"grid grid-cols-2 gap-1 text-xs\">\n                <div class=\"flex justify-between\">\n                  <span>Structure:</span>\n                  <span class=\"font-medium\">{{ evaluation.scores.structure || 0 }}</span>\n                </div>\n                <div class=\"flex justify-between\">\n                  <span>Pratiques:</span>\n                  <span class=\"font-medium\">{{ evaluation.scores.pratiques || 0 }}</span>\n                </div>\n                <div class=\"flex justify-between\">\n                  <span>Fonctionnalité:</span>\n                  <span class=\"font-medium\">{{ evaluation.scores.fonctionnalite || 0 }}</span>\n                </div>\n                <div class=\"flex justify-between\">\n                  <span>Originalité:</span>\n                  <span class=\"font-medium\">{{ evaluation.scores.originalite || 0 }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Actions -->\n          <div class=\"flex flex-col sm:flex-row gap-2\">\n            <button (click)=\"viewEvaluationDetails(evaluation.rendu)\"\n                    class=\"group/btn px-4 py-2 bg-gradient-to-r from-info to-primary dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\n              <div class=\"flex items-center justify-center space-x-2\">\n                <svg class=\"w-4 h-4 group-hover/btn:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"></path>\n                </svg>\n                <span>Voir détails</span>\n              </div>\n            </button>\n            <button (click)=\"editEvaluation(evaluation.rendu)\"\n                    class=\"group/btn px-4 py-2 bg-gradient-to-r from-secondary to-primary-dark dark:from-dark-accent-secondary dark:to-dark-accent-primary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\n              <div class=\"flex items-center justify-center space-x-2\">\n                <svg class=\"w-4 h-4 group-hover/btn:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\n                </svg>\n                <span>Modifier</span>\n              </div>\n            </button>\n            <button (click)=\"deleteEvaluation(evaluation._id)\"\n                    class=\"group/btn px-4 py-2 bg-gradient-to-r from-danger to-danger-dark dark:from-danger-dark dark:to-danger text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\n              <div class=\"flex items-center justify-center space-x-2\">\n                <svg class=\"w-4 h-4 group-hover/btn:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\n                </svg>\n                <span>Supprimer</span>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State moderne -->\n    <div *ngIf=\"!isLoading && !error && filteredEvaluations.length === 0\" class=\"text-center py-16\">\n      <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-12 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 max-w-md mx-auto\">\n        <div class=\"bg-gradient-to-br from-primary/10 to-secondary/10 dark:from-dark-accent-primary/20 dark:to-dark-accent-secondary/20 rounded-2xl p-6 mb-6\">\n          <svg class=\"h-16 w-16 mx-auto text-primary dark:text-dark-accent-primary\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\n          </svg>\n        </div>\n        <h3 class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary mb-2\">Aucune évaluation trouvée</h3>\n        <p class=\"text-text dark:text-dark-text-secondary mb-4\">Aucune évaluation ne correspond à vos critères de filtrage actuels.</p>\n        <button (click)=\"resetFilters()\" class=\"px-6 py-2 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\n          <div class=\"flex items-center justify-center space-x-2\">\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n            </svg>\n            <span>Réinitialiser les filtres</span>\n          </div>\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n\n"], "mappings": "AAIA,SAASA,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AAChE,SAASC,OAAO,EAAEC,EAAE,QAAQ,MAAM;;;;;;;;;;ICsC1BC,EAAA,CAAAC,cAAA,cAAkF;IACxED,EAAA,CAAAE,UAAA,mBAAAC,uEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC7BT,EAAA,CAAAU,cAAA,EAA2E;IAA3EV,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAW,SAAA,eAAsG;IACxGX,EAAA,CAAAY,YAAA,EAAM;;;;;IAIZZ,EAAA,CAAAC,cAAA,cAAqF;IACnFD,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAY,YAAA,EAAM;;;;IADJZ,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAe,kBAAA,MAAAC,MAAA,CAAAC,mBAAA,CAAAC,MAAA,8CAAAF,MAAA,CAAAG,UAAA,QACF;;;;;;IAtBFnB,EAAA,CAAAC,cAAA,cAA6L;IAGvLD,EAAA,CAAAU,cAAA,EAAuH;IAAvHV,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAW,SAAA,eAA6H;IAC/HX,EAAA,CAAAY,YAAA,EAAM;IAERZ,EAAA,CAAAoB,eAAA,EAIoE;IAJpEpB,EAAA,CAAAC,cAAA,gBAIoE;IAH7DD,EAAA,CAAAE,UAAA,2BAAAmB,wEAAAC,MAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAmB,IAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAgB,OAAA,CAAAL,UAAA,GAAAG,MAAA;IAAA,EAAwB,mBAAAG,gEAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAmB,IAAA;MAAA,MAAAG,OAAA,GAAA1B,EAAA,CAAAO,aAAA;MAAA,OACfP,EAAA,CAAAQ,WAAA,CAAAkB,OAAA,CAAAC,cAAA,EAAgB;IAAA,EADD;IAD/B3B,EAAA,CAAAY,YAAA,EAIoE;IACpEZ,EAAA,CAAA4B,UAAA,IAAAC,8CAAA,kBAMM;IACR7B,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAA4B,UAAA,IAAAE,8CAAA,kBAEM;IACR9B,EAAA,CAAAY,YAAA,EAAM;;;;IAfKZ,EAAA,CAAAc,SAAA,GAAwB;IAAxBd,EAAA,CAAA+B,UAAA,YAAAC,MAAA,CAAAb,UAAA,CAAwB;IAIzBnB,EAAA,CAAAc,SAAA,GAAgB;IAAhBd,EAAA,CAAA+B,UAAA,SAAAC,MAAA,CAAAb,UAAA,CAAgB;IAQlBnB,EAAA,CAAAc,SAAA,GAAgB;IAAhBd,EAAA,CAAA+B,UAAA,SAAAC,MAAA,CAAAb,UAAA,CAAgB;;;;;IAMxBnB,EAAA,CAAAC,cAAA,cAA+E;IAE3ED,EAAA,CAAAW,SAAA,cAAqH;IAEvHX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAa,MAAA,yCAA6B;IAAAb,EAAA,CAAAY,YAAA,EAAI;;;;;;IAIzGZ,EAAA,CAAAC,cAAA,cAAyL;IAGnLD,EAAA,CAAAU,cAAA,EAA2H;IAA3HV,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAW,SAAA,eAAmI;IACrIX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAuB;IAAvBpB,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAa,MAAA,GAAW;IAAAb,EAAA,CAAAY,YAAA,EAAI;IAExCZ,EAAA,CAAAC,cAAA,iBACmL;IAD3KD,EAAA,CAAAE,UAAA,mBAAA+B,iEAAA;MAAAjC,EAAA,CAAAI,aAAA,CAAA8B,IAAA;MAAA,MAAAC,OAAA,GAAAnC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA2B,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAEjCpC,EAAA,CAAAa,MAAA,uBACF;IAAAb,EAAA,CAAAY,YAAA,EAAS;;;;IALgBZ,EAAA,CAAAc,SAAA,GAAW;IAAXd,EAAA,CAAAqC,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAkChCvC,EAAA,CAAAC,cAAA,iBAAwD;IAAAD,EAAA,CAAAa,MAAA,GAAY;IAAAb,EAAA,CAAAY,YAAA,EAAS;;;;IAAtCZ,EAAA,CAAA+B,UAAA,UAAAS,UAAA,CAAgB;IAACxC,EAAA,CAAAc,SAAA,GAAY;IAAZd,EAAA,CAAAqC,iBAAA,CAAAG,UAAA,CAAY;;;;;IAiBpExC,EAAA,CAAAC,cAAA,iBAA4D;IAAAD,EAAA,CAAAa,MAAA,GAAkB;IAAAb,EAAA,CAAAY,YAAA,EAAS;;;;IAAhDZ,EAAA,CAAA+B,UAAA,UAAAU,UAAA,CAAAC,GAAA,CAAoB;IAAC1C,EAAA,CAAAc,SAAA,GAAkB;IAAlBd,EAAA,CAAAqC,iBAAA,CAAAI,UAAA,CAAAE,KAAA,CAAkB;;;;;;IAzCtF3C,EAAA,CAAAC,cAAA,cAA6L;IAGvLD,EAAA,CAAAU,cAAA,EAAsF;IAAtFV,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAW,SAAA,eAAyO;IAC3OX,EAAA,CAAAY,YAAA,EAAM;IAERZ,EAAA,CAAAoB,eAAA,EAAyE;IAAzEpB,EAAA,CAAAC,cAAA,aAAyE;IAAAD,EAAA,CAAAa,MAAA,2BAAoB;IAAAb,EAAA,CAAAY,YAAA,EAAK;IAGpGZ,EAAA,CAAAC,cAAA,cAAmD;IAK3CD,EAAA,CAAAU,cAAA,EAAsH;IAAtHV,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAW,SAAA,gBAAwV;IAC1VX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAM;IAANpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,0BAAkB;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAGnCZ,EAAA,CAAAC,cAAA,kBAC8V;IADtVD,EAAA,CAAAE,UAAA,2BAAA0C,0EAAAtB,MAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAyC,IAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAsC,OAAA,CAAAC,YAAA,GAAAzB,MAAA;IAAA,EAA0B,oBAAA0B,mEAAA;MAAAhD,EAAA,CAAAI,aAAA,CAAAyC,IAAA;MAAA,MAAAI,OAAA,GAAAjD,EAAA,CAAAO,aAAA;MAAA,OAAWP,EAAA,CAAAQ,WAAA,CAAAyC,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAzB;IAEhClD,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAa,MAAA,IAAuC;IAAAb,EAAA,CAAAY,YAAA,EAAS;IACjEZ,EAAA,CAAA4B,UAAA,KAAAuB,kDAAA,qBAA6E;IAC/EnD,EAAA,CAAAY,YAAA,EAAS;IAIXZ,EAAA,CAAAC,cAAA,eAAuB;IAGjBD,EAAA,CAAAU,cAAA,EAAsH;IAAtHV,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAW,SAAA,gBAA4J;IAC9JX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAM;IAANpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,0BAAkB;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAGnCZ,EAAA,CAAAC,cAAA,kBAC8V;IADtVD,EAAA,CAAAE,UAAA,2BAAAkD,0EAAA9B,MAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAyC,IAAA;MAAA,MAAAQ,OAAA,GAAArD,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA6C,OAAA,CAAAC,YAAA,GAAAhC,MAAA;IAAA,EAA0B,oBAAAiC,mEAAA;MAAAvD,EAAA,CAAAI,aAAA,CAAAyC,IAAA;MAAA,MAAAW,OAAA,GAAAxD,EAAA,CAAAO,aAAA;MAAA,OAAWP,EAAA,CAAAQ,WAAA,CAAAgD,OAAA,CAAAN,YAAA,EAAc;IAAA,EAAzB;IAEhClD,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAa,MAAA,IAAuC;IAAAb,EAAA,CAAAY,YAAA,EAAS;IACjEZ,EAAA,CAAA4B,UAAA,KAAA6B,kDAAA,qBAAuF;IACzFzD,EAAA,CAAAY,YAAA,EAAS;IAIXZ,EAAA,CAAAC,cAAA,eAAuB;IAGjBD,EAAA,CAAAU,cAAA,EAAsH;IAAtHV,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAW,SAAA,gBAAqjB;IAEvjBX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAM;IAANpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,uBAAe;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAGhCZ,EAAA,CAAAC,cAAA,eAAqC;IAC3BD,EAAA,CAAAE,UAAA,mBAAAwD,kEAAA;MAAA1D,EAAA,CAAAI,aAAA,CAAAyC,IAAA;MAAA,MAAAc,OAAA,GAAA3D,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAmD,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAE7B5D,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAU,cAAA,EAA2E;IAA3EV,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAW,SAAA,gBAA6L;IAC/LX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAM;IAANpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,kBAAU;IAAAb,EAAA,CAAAY,YAAA,EAAO;;;;IA1CrBZ,EAAA,CAAAc,SAAA,IAA0B;IAA1Bd,EAAA,CAAA+B,UAAA,YAAA8B,MAAA,CAAAd,YAAA,CAA0B;IAEf/C,EAAA,CAAAc,SAAA,GAAuC;IAAvCd,EAAA,CAAA8D,kBAAA,uBAAAD,MAAA,CAAAE,OAAA,CAAA7C,MAAA,MAAuC;IAC7BlB,EAAA,CAAAc,SAAA,GAAU;IAAVd,EAAA,CAAA+B,UAAA,YAAA8B,MAAA,CAAAE,OAAA,CAAU;IAc/B/D,EAAA,CAAAc,SAAA,GAA0B;IAA1Bd,EAAA,CAAA+B,UAAA,YAAA8B,MAAA,CAAAP,YAAA,CAA0B;IAEftD,EAAA,CAAAc,SAAA,GAAuC;IAAvCd,EAAA,CAAA8D,kBAAA,uBAAAD,MAAA,CAAAG,OAAA,CAAA9C,MAAA,MAAuC;IAC7BlB,EAAA,CAAAc,SAAA,GAAU;IAAVd,EAAA,CAAA+B,UAAA,YAAA8B,MAAA,CAAAG,OAAA,CAAU;;;;;;IAgC3ChE,EAAA,CAAAC,cAAA,cAA4P;IAOlPD,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,cAAyM;IACvMD,EAAA,CAAAU,cAAA,EAAsF;IAAtFV,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAW,SAAA,eAA+H;IACjIX,EAAA,CAAAY,YAAA,EAAM;IAGVZ,EAAA,CAAAoB,eAAA,EAAoB;IAApBpB,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAa,MAAA,IACF;IAAAb,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aAA2D;IAAAD,EAAA,CAAAa,MAAA,IAA0D;IAAAb,EAAA,CAAAY,YAAA,EAAI;IACzHZ,EAAA,CAAAC,cAAA,eAA8C;IAE1CD,EAAA,CAAAU,cAAA,EAAsH;IAAtHV,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAW,SAAA,gBAAwV;IAC1VX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAA6E;IAA7EpB,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAa,MAAA,IACF;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAETZ,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAAU,cAAA,EAAsH;IAAtHV,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAW,SAAA,gBAA4J;IAC9JX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAA6E;IAA7EpB,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAa,MAAA,IACF;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAOfZ,EAAA,CAAAC,cAAA,eAA2F;IAIrFD,EAAA,CAAAU,cAAA,EAAmH;IAAnHV,EAAA,CAAAC,cAAA,eAAmH;IACjHD,EAAA,CAAAW,SAAA,gBAAqK;IACvKX,EAAA,CAAAY,YAAA,EAAM;IAERZ,EAAA,CAAAoB,eAAA,EAAK;IAALpB,EAAA,CAAAC,cAAA,WAAK;IACwDD,EAAA,CAAAa,MAAA,4BAAU;IAAAb,EAAA,CAAAY,YAAA,EAAI;IACzEZ,EAAA,CAAAC,cAAA,aAA4E;IAAAD,EAAA,CAAAa,MAAA,IAA2C;IAAAb,EAAA,CAAAY,YAAA,EAAI;IAK/HZ,EAAA,CAAAC,cAAA,eAAyC;IAErCD,EAAA,CAAAU,cAAA,EAA2E;IAA3EV,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAW,SAAA,gBAAsR;IACxRX,EAAA,CAAAY,YAAA,EAAM;IAERZ,EAAA,CAAAoB,eAAA,EAAK;IAALpB,EAAA,CAAAC,cAAA,WAAK;IACwDD,EAAA,CAAAa,MAAA,mBAAW;IAAAb,EAAA,CAAAY,YAAA,EAAI;IAC1EZ,EAAA,CAAAC,cAAA,gBAA0F;IACxFD,EAAA,CAAAa,MAAA,IACF;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAKXZ,EAAA,CAAAC,cAAA,eAAmE;IACDD,EAAA,CAAAa,MAAA,8BAAiB;IAAAb,EAAA,CAAAY,YAAA,EAAI;IACrFZ,EAAA,CAAAC,cAAA,eAA4C;IAElCD,EAAA,CAAAa,MAAA,kBAAU;IAAAb,EAAA,CAAAY,YAAA,EAAO;IACvBZ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAa,MAAA,IAAsC;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAEzEZ,EAAA,CAAAC,cAAA,eAAkC;IAC1BD,EAAA,CAAAa,MAAA,kBAAU;IAAAb,EAAA,CAAAY,YAAA,EAAO;IACvBZ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAa,MAAA,IAAsC;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAEzEZ,EAAA,CAAAC,cAAA,eAAkC;IAC1BD,EAAA,CAAAa,MAAA,4BAAe;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAC5BZ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAa,MAAA,IAA2C;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAE9EZ,EAAA,CAAAC,cAAA,eAAkC;IAC1BD,EAAA,CAAAa,MAAA,yBAAY;IAAAb,EAAA,CAAAY,YAAA,EAAO;IACzBZ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAa,MAAA,IAAwC;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAOjFZ,EAAA,CAAAC,cAAA,eAA6C;IACnCD,EAAA,CAAAE,UAAA,mBAAA+D,wEAAA;MAAA,MAAAC,WAAA,GAAAlE,EAAA,CAAAI,aAAA,CAAA+D,IAAA;MAAA,MAAAC,cAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAtE,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA8D,OAAA,CAAAC,qBAAA,CAAAH,cAAA,CAAAI,KAAA,CAAuC;IAAA,EAAC;IAEvDxE,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAU,cAAA,EAA0H;IAA1HV,EAAA,CAAAC,cAAA,eAA0H;IACxHD,EAAA,CAAAW,SAAA,gBAAkH;IAEpHX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAM;IAANpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,yBAAY;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAG7BZ,EAAA,CAAAC,cAAA,kBAC8O;IADtOD,EAAA,CAAAE,UAAA,mBAAAuE,wEAAA;MAAA,MAAAP,WAAA,GAAAlE,EAAA,CAAAI,aAAA,CAAA+D,IAAA;MAAA,MAAAC,cAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAK,OAAA,GAAA1E,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAkE,OAAA,CAAAC,cAAA,CAAAP,cAAA,CAAAI,KAAA,CAAgC;IAAA,EAAC;IAEhDxE,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAU,cAAA,EAA0H;IAA1HV,EAAA,CAAAC,cAAA,eAA0H;IACxHD,EAAA,CAAAW,SAAA,iBAAwM;IAC1MX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAM;IAANpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,gBAAQ;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAGzBZ,EAAA,CAAAC,cAAA,mBACmN;IAD3MD,EAAA,CAAAE,UAAA,mBAAA0E,wEAAA;MAAA,MAAAV,WAAA,GAAAlE,EAAA,CAAAI,aAAA,CAAA+D,IAAA;MAAA,MAAAC,cAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAQ,OAAA,GAAA7E,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAqE,OAAA,CAAAC,gBAAA,CAAAV,cAAA,CAAA1B,GAAA,CAAgC;IAAA,EAAC;IAEhD1C,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAU,cAAA,EAA0H;IAA1HV,EAAA,CAAAC,cAAA,eAA0H;IACxHD,EAAA,CAAAW,SAAA,iBAA8M;IAChNX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAM;IAANpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,iBAAS;IAAAb,EAAA,CAAAY,YAAA,EAAO;;;;;IAnHtBZ,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAA8D,kBAAA,MAAAiB,OAAA,CAAAC,kBAAA,CAAAZ,cAAA,CAAAa,QAAA,OACF;IASEjF,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAA8D,kBAAA,MAAAiB,OAAA,CAAAG,cAAA,CAAAd,cAAA,CAAAa,QAAA,OACF;IAC2DjF,EAAA,CAAAc,SAAA,GAA0D;IAA1Dd,EAAA,CAAAqC,iBAAA,EAAA+B,cAAA,CAAAa,QAAA,kBAAAb,cAAA,CAAAa,QAAA,CAAAE,KAAA,4BAA0D;IAO/GnF,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAA8D,kBAAA,MAAAiB,OAAA,CAAAK,eAAA,CAAAhB,cAAA,CAAAa,QAAA,OACF;IAOEjF,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAA8D,kBAAA,MAAAiB,OAAA,CAAAM,eAAA,CAAAjB,cAAA,OACF;IAiB0EpE,EAAA,CAAAc,SAAA,IAA2C;IAA3Cd,EAAA,CAAAqC,iBAAA,CAAA0C,OAAA,CAAAO,UAAA,CAAAlB,cAAA,CAAAmB,cAAA,EAA2C;IAMpHvF,EAAA,CAAAc,SAAA,GAAwD;IAAxDd,EAAA,CAAA+B,UAAA,YAAAgD,OAAA,CAAAS,iBAAA,CAAAT,OAAA,CAAAU,aAAA,CAAArB,cAAA,GAAwD;IAOrDpE,EAAA,CAAAc,SAAA,GAAyD;IAAzDd,EAAA,CAAA+B,UAAA,YAAAgD,OAAA,CAAAW,kBAAA,CAAAX,OAAA,CAAAU,aAAA,CAAArB,cAAA,GAAyD;IAC7DpE,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAA8D,kBAAA,MAAAiB,OAAA,CAAAU,aAAA,CAAArB,cAAA,UACF;IAU4BpE,EAAA,CAAAc,SAAA,GAAsC;IAAtCd,EAAA,CAAAqC,iBAAA,CAAA+B,cAAA,CAAAuB,MAAA,CAAAC,SAAA,MAAsC;IAItC5F,EAAA,CAAAc,SAAA,GAAsC;IAAtCd,EAAA,CAAAqC,iBAAA,CAAA+B,cAAA,CAAAuB,MAAA,CAAAE,SAAA,MAAsC;IAItC7F,EAAA,CAAAc,SAAA,GAA2C;IAA3Cd,EAAA,CAAAqC,iBAAA,CAAA+B,cAAA,CAAAuB,MAAA,CAAAG,cAAA,MAA2C;IAI3C9F,EAAA,CAAAc,SAAA,GAAwC;IAAxCd,EAAA,CAAAqC,iBAAA,CAAA+B,cAAA,CAAAuB,MAAA,CAAAI,WAAA,MAAwC;;;;;IA1FhF/F,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAA4B,UAAA,IAAAoE,8CAAA,oBA+HM;IACRhG,EAAA,CAAAY,YAAA,EAAM;;;;IAhIwBZ,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAA+B,UAAA,YAAAkE,MAAA,CAAAhF,mBAAA,CAAsB;;;;;;IAmIpDjB,EAAA,CAAAC,cAAA,eAAgG;IAG1FD,EAAA,CAAAU,cAAA,EAAgI;IAAhIV,EAAA,CAAAC,cAAA,eAAgI;IAC9HD,EAAA,CAAAW,SAAA,gBAA8O;IAChPX,EAAA,CAAAY,YAAA,EAAM;IAERZ,EAAA,CAAAoB,eAAA,EAA8E;IAA9EpB,EAAA,CAAAC,cAAA,cAA8E;IAAAD,EAAA,CAAAa,MAAA,0CAAyB;IAAAb,EAAA,CAAAY,YAAA,EAAK;IAC5GZ,EAAA,CAAAC,cAAA,aAAwD;IAAAD,EAAA,CAAAa,MAAA,yFAAmE;IAAAb,EAAA,CAAAY,YAAA,EAAI;IAC/HZ,EAAA,CAAAC,cAAA,kBAA2P;IAAnPD,EAAA,CAAAE,UAAA,mBAAAgG,iEAAA;MAAAlG,EAAA,CAAAI,aAAA,CAAA+F,IAAA;MAAA,MAAAC,OAAA,GAAApG,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA4F,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAC9BrG,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAU,cAAA,EAA2E;IAA3EV,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAW,SAAA,gBAA6L;IAC/LX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAM;IAANpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,sCAAyB;IAAAb,EAAA,CAAAY,YAAA,EAAO;;;ADvRlD,OAAM,MAAO0F,wBAAwB;EAanCC,YACUC,aAA4B,EAC5BC,iBAAoC,EACpCC,MAAc;IAFd,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IAfhB,KAAAC,WAAW,GAA4B,EAAE;IACzC,KAAA1F,mBAAmB,GAA4B,EAAE;IACjD,KAAA2F,SAAS,GAAY,IAAI;IACzB,KAAArE,KAAK,GAAW,EAAE;IAClB,KAAApB,UAAU,GAAW,EAAE;IACvB,KAAA4B,YAAY,GAAW,EAAE;IACzB,KAAAO,YAAY,GAAW,EAAE;IACzB,KAAAS,OAAO,GAAa,EAAE;IACtB,KAAAC,OAAO,GAAU,EAAE;IAEX,KAAA6C,QAAQ,GAAG,IAAI/G,OAAO,EAAQ;EAMnC;EAEHgH,QAAQA,CAAA;IACN,IAAI,CAAC1E,eAAe,EAAE;EACxB;EAEA2E,WAAWA,CAAA;IACT,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;IACpB,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;EAC1B;EAEA7E,eAAeA,CAAA;IACb,IAAI,CAACwE,SAAS,GAAG,IAAI;IACrB,IAAI,CAACrE,KAAK,GAAG,EAAE;IAEf2E,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IAErD,IAAI,CAACV,iBAAiB,CAACW,iBAAiB,EAAE,CACvCC,IAAI,CACHxH,SAAS,CAAC,IAAI,CAACgH,QAAQ,CAAC,EACxBlH,UAAU,CAAE2H,GAAG,IAAI;MACjB,IAAI,CAAC/E,KAAK,GACR,sEAAsE;MACxE,IAAI,CAACqE,SAAS,GAAG,KAAK;MACtB,OAAO7G,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,EACFH,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACgH,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CACH,CACAW,SAAS,CAAC;MACTP,IAAI,EAAGL,WAAW,IAAI;QACpBO,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAER,WAAW,CAAC;QAE/C,IAAI,CAACa,KAAK,CAACC,OAAO,CAACd,WAAW,CAAC,EAAE;UAC/B,IAAI,CAACpE,KAAK,GACR,4DAA4D;UAC9D;;QAGF;QACA,IAAI,CAACoE,WAAW,GAAGA,WAAW,CAACe,GAAG,CAACC,UAAU,IAAG;UAC9C,MAAMC,eAAe,GAAGD,UAAmC;UAE3D;UACA,IAAI,CAACC,eAAe,CAACC,aAAa,IAAI,CAACD,eAAe,CAACC,aAAa,CAAClF,KAAK,EAAE;YAC1EuE,OAAO,CAACY,IAAI,CAAC,iDAAiD,EAAEF,eAAe,CAAClF,GAAG,CAAC;YAEpF;YACA,IAAIkF,eAAe,CAACG,YAAY,IAAIH,eAAe,CAACG,YAAY,CAACC,MAAM,EAAE;cACvEJ,eAAe,CAACC,aAAa,GAAGD,eAAe,CAACG,YAAY,CAACC,MAAM;;;UAIvE,OAAOJ,eAAe;QACxB,CAAC,CAAC;QAEF,IAAI,CAACK,wBAAwB,EAAE;QAC/B,IAAI,CAAC/E,YAAY,EAAE;MACrB;KACD,CAAC;EACN;EAEA+E,wBAAwBA,CAAA;IACtB,MAAMC,UAAU,GAAG,IAAIC,GAAG,EAAU;IAEpC,IAAI,CAACxB,WAAW,CAACyB,OAAO,CAACT,UAAU,IAAG;MACpC,IAAIA,UAAU,CAAC1C,QAAQ,EAAE;QACvB,MAAMoD,UAAU,GAAG,IAAI,CAACjD,eAAe,CAACuC,UAAU,CAAC1C,QAAQ,CAAC;QAC5D,IAAIoD,UAAU,IAAIA,UAAU,KAAK,cAAc,EAAE;UAC/CH,UAAU,CAACI,GAAG,CAACD,UAAU,CAAC;;;IAGhC,CAAC,CAAC;IAEF,IAAI,CAACtE,OAAO,GAAGyD,KAAK,CAACe,IAAI,CAACL,UAAU,CAAC,CAACM,IAAI,EAAE;IAE5C;IACA,MAAMC,UAAU,GAAG,IAAIC,GAAG,EAAe;IACzC,IAAI,CAAC/B,WAAW,CAACyB,OAAO,CAAET,UAAU,IAAI;MACtC,IAAIA,UAAU,CAACE,aAAa,IAAIF,UAAU,CAACE,aAAa,CAACnF,GAAG,EAAE;QAC5D+F,UAAU,CAACE,GAAG,CAAChB,UAAU,CAACE,aAAa,CAACnF,GAAG,EAAEiF,UAAU,CAACE,aAAa,CAAC;;IAE1E,CAAC,CAAC;IACF,IAAI,CAAC7D,OAAO,GAAGwD,KAAK,CAACe,IAAI,CAACE,UAAU,CAACG,MAAM,EAAE,CAAC;EAChD;EAEA1F,YAAYA,CAAA;IACV,IAAI2F,OAAO,GAAG,IAAI,CAAClC,WAAW;IAE9B;IACA,IAAI,IAAI,CAACxF,UAAU,CAAC2H,IAAI,EAAE,KAAK,EAAE,EAAE;MACjC,MAAMC,IAAI,GAAG,IAAI,CAAC5H,UAAU,CAAC6H,WAAW,EAAE,CAACF,IAAI,EAAE;MAEjDD,OAAO,GAAGA,OAAO,CAACI,MAAM,CACrBtB,UAAU,IACTA,UAAU,CAAC1C,QAAQ,EAAEiE,GAAG,EAAEF,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,IACtDpB,UAAU,CAAC1C,QAAQ,EAAEmE,MAAM,EAAEJ,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,IACzDpB,UAAU,CAACE,aAAa,EAAElF,KAAK,EAAEqG,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,CAChE;;IAkBH;IACA,IAAI,IAAI,CAAChG,YAAY,EAAE;MAErB8F,OAAO,GAAGA,OAAO,CAACI,MAAM,CACrBtB,UAAU,IAAKA,UAAU,CAAC1C,QAAQ,EAAEoE,MAAM,KAAK,IAAI,CAACtG,YAAY,CAClE;;IASH;IACA,IAAI,IAAI,CAACO,YAAY,EAAE;MACrBuF,OAAO,GAAGA,OAAO,CAACI,MAAM,CACrBtB,UAAU,IAAKA,UAAU,CAACE,aAAa,EAAEnF,GAAG,KAAK,IAAI,CAACY,YAAY,CACpE;;IAGH,IAAI,CAACrC,mBAAmB,GAAG4H,OAAO;EACpC;EAEAlH,cAAcA,CAAA;IACZ,IAAI,CAACuB,YAAY,EAAE;EACrB;EAEAzC,WAAWA,CAAA;IACT,IAAI,CAACU,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC+B,YAAY,EAAE;EACrB;EAEAU,WAAWA,CAAA;IACTsD,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAE3D;IACA,IAAI,CAAChG,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC4B,YAAY,GAAG,EAAE;IACtB,IAAI,CAACO,YAAY,GAAG,EAAE;IAEtB;IACA,IAAI,CAAClB,eAAe,EAAE;IAEtB8E,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EAC1D;EAEAd,YAAYA,CAAA;IACVa,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C;IACA,IAAI,CAAChG,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC4B,YAAY,GAAG,EAAE;IACtB,IAAI,CAACO,YAAY,GAAG,EAAE;IAEtB;IACA,IAAI,CAACJ,YAAY,EAAE;IAEnBgE,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;EACtC;EAEAxC,cAAcA,CAAC2E,OAAe;IAC5B,IAAI,CAAC5C,MAAM,CAAC6C,QAAQ,CAAC,CAAC,iCAAiC,EAAED,OAAO,CAAC,CAAC;EACpE;EAEA/E,qBAAqBA,CAAC+E,OAAe;IACnC,IAAI,CAAC5C,MAAM,CAAC6C,QAAQ,CAAC,CAAC,oCAAoC,EAAED,OAAO,CAAC,CAAC;EACvE;EAEA7D,aAAaA,CAACkC,UAAiC;IAC7C,IAAI,CAACA,UAAU,CAAChC,MAAM,EAAE,OAAO,CAAC;IAEhC,MAAMA,MAAM,GAAGgC,UAAU,CAAChC,MAAM;IAChC,OACEA,MAAM,CAACC,SAAS,GAChBD,MAAM,CAACE,SAAS,GAChBF,MAAM,CAACG,cAAc,GACrBH,MAAM,CAACI,WAAW;EAEtB;EAEAyD,aAAaA,CAACC,KAAa;IACzB,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,6BAA6B;IACrD,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,2BAA2B;IACnD,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,+BAA+B;IACtD,OAAO,yBAAyB;EAClC;EAEAnE,UAAUA,CAACoE,IAA+B;IACxC,IAAI,CAACA,IAAI,EAAE,OAAO,gBAAgB;IAClC,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,EAAE;EAC5C;EAIAC,mBAAmBA,CAAA;IACjB,IACE,CAACC,OAAO,CAAC,gEAAgE,CAAC,EAC1E;MACA;;IAGF,IAAI,CAAClD,SAAS,GAAG,IAAI;IAErB,IAAI,CAACH,iBAAiB,CAACoD,mBAAmB,EAAE,CAACtC,SAAS,CAAC;MACrDP,IAAI,EAAG+C,QAAQ,IAAI;QACjBC,KAAK,CACH,GAAGD,QAAQ,CAACE,YAAY,yCAAyC,CAClE;QACD,IAAI,CAAC7H,eAAe,EAAE;MACxB,CAAC;MACDG,KAAK,EAAG+E,GAAG,IAAI;QACb0C,KAAK,CAAC,4CAA4C,CAAC;QACnD,IAAI,CAACpD,SAAS,GAAG,KAAK;MACxB;KAgKD,CAAC;EACJ;;;uBAzZWN,wBAAwB,EAAAtG,EAAA,CAAAkK,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAApK,EAAA,CAAAkK,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAtK,EAAA,CAAAkK,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxBlE,wBAAwB;MAAAmE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBrC/K,EAAA,CAAAC,cAAA,aAA8F;UAOlFD,EAAA,CAAAU,cAAA,EAAsF;UAAtFV,EAAA,CAAAC,cAAA,aAAsF;UACpFD,EAAA,CAAAW,SAAA,cAAiP;UACnPX,EAAA,CAAAY,YAAA,EAAM;UAERZ,EAAA,CAAAoB,eAAA,EAAK;UAALpB,EAAA,CAAAC,cAAA,UAAK;UAC4CD,EAAA,CAAAa,MAAA,kCAAqB;UAAAb,EAAA,CAAAY,YAAA,EAAK;UACzEZ,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAa,MAAA,wDAA2C;UAAAb,EAAA,CAAAY,YAAA,EAAI;UAG5EZ,EAAA,CAAAC,cAAA,eAAiE;UAE7BD,EAAA,CAAAa,MAAA,IAAwB;UAAAb,EAAA,CAAAY,YAAA,EAAM;UAC9DZ,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAa,MAAA,aAAK;UAAAb,EAAA,CAAAY,YAAA,EAAM;UAElCZ,EAAA,CAAAW,SAAA,eAAyC;UACzCX,EAAA,CAAAC,cAAA,eAAyB;UACSD,EAAA,CAAAa,MAAA,IAAuB;UAAAb,EAAA,CAAAY,YAAA,EAAM;UAC7DZ,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAY,YAAA,EAAM;UAO1CZ,EAAA,CAAA4B,UAAA,KAAAqJ,wCAAA,kBAuBM;UAGNjL,EAAA,CAAA4B,UAAA,KAAAsJ,wCAAA,kBAMM;UAGNlL,EAAA,CAAA4B,UAAA,KAAAuJ,wCAAA,kBAaM;UAGNnL,EAAA,CAAA4B,UAAA,KAAAwJ,wCAAA,mBAqEM;UAGNpL,EAAA,CAAA4B,UAAA,KAAAyJ,wCAAA,kBAiIM;UAGNrL,EAAA,CAAA4B,UAAA,KAAA0J,wCAAA,mBAkBM;UACRtL,EAAA,CAAAY,YAAA,EAAM;;;UA/RoCZ,EAAA,CAAAc,SAAA,IAAwB;UAAxBd,EAAA,CAAAqC,iBAAA,CAAA2I,GAAA,CAAArE,WAAA,CAAAzF,MAAA,CAAwB;UAKxBlB,EAAA,CAAAc,SAAA,GAAuB;UAAvBd,EAAA,CAAAqC,iBAAA,CAAA2I,GAAA,CAAAO,eAAA,GAAuB;UAQzDvL,EAAA,CAAAc,SAAA,GAA0B;UAA1Bd,EAAA,CAAA+B,UAAA,UAAAiJ,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAzI,KAAA,CAA0B;UA0B1BvC,EAAA,CAAAc,SAAA,GAAe;UAAfd,EAAA,CAAA+B,UAAA,SAAAiJ,GAAA,CAAApE,SAAA,CAAe;UASf5G,EAAA,CAAAc,SAAA,GAAW;UAAXd,EAAA,CAAA+B,UAAA,SAAAiJ,GAAA,CAAAzI,KAAA,CAAW;UAgBXvC,EAAA,CAAAc,SAAA,GAA0B;UAA1Bd,EAAA,CAAA+B,UAAA,UAAAiJ,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAzI,KAAA,CAA0B;UAwE1BvC,EAAA,CAAAc,SAAA,GAA4D;UAA5Dd,EAAA,CAAA+B,UAAA,UAAAiJ,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAzI,KAAA,IAAAyI,GAAA,CAAA/J,mBAAA,CAAAC,MAAA,KAA4D;UAoI5DlB,EAAA,CAAAc,SAAA,GAA8D;UAA9Dd,EAAA,CAAA+B,UAAA,UAAAiJ,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAzI,KAAA,IAAAyI,GAAA,CAAA/J,mBAAA,CAAAC,MAAA,OAA8D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}