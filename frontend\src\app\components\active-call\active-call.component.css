/* Styles pour le composant ActiveCall */

/* Animation pour les cercles de pulsation */
@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* Animation pour le visualiseur audio */
@keyframes audio-wave {
  0%, 100% {
    height: 15px;
  }
  50% {
    height: 40px;
  }
}

/* Effet de glow pour les boutons */
.glow-effect {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.7);
}

/* Animation pour les barres audio */
.audio-bar {
  animation: audio-wave 1.5s ease-in-out infinite;
}

.audio-bar:nth-child(2) { animation-delay: 0.1s; }
.audio-bar:nth-child(3) { animation-delay: 0.2s; }
.audio-bar:nth-child(4) { animation-delay: 0.3s; }
.audio-bar:nth-child(5) { animation-delay: 0.4s; }
.audio-bar:nth-child(6) { animation-delay: 0.5s; }
.audio-bar:nth-child(7) { animation-delay: 0.6s; }
.audio-bar:nth-child(8) { animation-delay: 0.7s; }
.audio-bar:nth-child(9) { animation-delay: 0.8s; }
.audio-bar:nth-child(10) { animation-delay: 0.9s; }
.audio-bar:nth-child(11) { animation-delay: 1.0s; }
.audio-bar:nth-child(12) { animation-delay: 1.1s; }
.audio-bar:nth-child(13) { animation-delay: 1.2s; }
.audio-bar:nth-child(14) { animation-delay: 1.3s; }
.audio-bar:nth-child(15) { animation-delay: 1.4s; }

/* Effet de backdrop blur pour les navigateurs qui le supportent */
.backdrop-blur-custom {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Transition fluide pour tous les éléments interactifs */
button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

button:active {
  transform: scale(0.95);
}

/* Effet de survol pour les boutons */
button:hover {
  filter: brightness(1.1);
}

/* Style pour les icônes */
.fas {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Animation de rotation pour l'icône de chargement */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Effet de pulsation personnalisé */
@keyframes custom-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.custom-pulse {
  animation: custom-pulse 2s ease-in-out infinite;
}

/* Responsive design */
@media (max-width: 768px) {
  .w-48 {
    width: 8rem;
  }
  
  .h-36 {
    height: 6rem;
  }
  
  .text-4xl {
    font-size: 2rem;
  }
  
  .w-16 {
    width: 3rem;
  }
  
  .h-16 {
    height: 3rem;
  }
}

/* Effet de gradient animé pour le fond */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animated-gradient {
  background: linear-gradient(-45deg, #1f2937, #1e3a8a, #7c3aed, #1f2937);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}
