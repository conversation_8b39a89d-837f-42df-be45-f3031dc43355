{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-31 01:42:15"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-31 01:42:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:43:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 9ms","timestamp":"2025-05-31 01:43:10"}
{"level":"http","message":"POST / 200 - 30ms","timestamp":"2025-05-31 01:43:10"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:43:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:43:10","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:43:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:43:10","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:43:11"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 328ms","timestamp":"2025-05-31 01:43:11"}
{"level":"http","message":"GraphQL anonymous completed in 291ms","timestamp":"2025-05-31 01:43:11"}
{"level":"http","message":"POST / 200 - 296ms","timestamp":"2025-05-31 01:43:11"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 91ms","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 45","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:43:11"}
{"level":"http","message":"GraphQL anonymous completed in 817ms","timestamp":"2025-05-31 01:43:11"}
{"level":"http","message":"POST / 200 - 823ms","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:43:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:43:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:43:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:43:41"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 01:43:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:44:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:44:11"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 01:44:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:44:34","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:44:34"}
{"level":"http","message":"POST / 200 - 30ms","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:44:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:44:34","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:44:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:44:34","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:44:34"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 228ms","timestamp":"2025-05-31 01:44:34"}
{"level":"http","message":"GraphQL anonymous completed in 237ms","timestamp":"2025-05-31 01:44:34"}
{"level":"http","message":"POST / 200 - 245ms","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 45","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 01:44:34"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 281ms","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:44:34"}
{"level":"http","message":"GraphQL anonymous completed in 585ms","timestamp":"2025-05-31 01:44:34"}
{"level":"http","message":"POST / 200 - 591ms","timestamp":"2025-05-31 01:44:34"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:44:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:44:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:44:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:44:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:44:41"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-31 01:44:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:44:42","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:44:42"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 01:44:42"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:44:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:44:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:44:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:44:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:44:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:44:42","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:44:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:44:42","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:44:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:44:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:44:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:44:42"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 268ms","timestamp":"2025-05-31 01:44:43"}
{"level":"http","message":"GraphQL anonymous completed in 295ms","timestamp":"2025-05-31 01:44:43"}
{"level":"http","message":"POST / 200 - 299ms","timestamp":"2025-05-31 01:44:43"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 45","timestamp":"2025-05-31 01:44:43"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 01:44:43"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 252ms","timestamp":"2025-05-31 01:44:43"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:44:43"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:44:43"}
{"level":"http","message":"GraphQL anonymous completed in 591ms","timestamp":"2025-05-31 01:44:43"}
{"level":"http","message":"POST / 200 - 597ms","timestamp":"2025-05-31 01:44:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:44:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:44:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:44:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-31 01:44:47","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-31 01:44:47"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:44:47"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:44:47"}
{"level":"http","message":"GraphQL anonymous completed in 691ms","timestamp":"2025-05-31 01:44:47"}
{"level":"http","message":"POST / 200 - 698ms","timestamp":"2025-05-31 01:44:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-31 01:44:49","variables":{"callId":"call_1748652289312_tgunkdn40","callType":"AUDIO","conversationId":"6839c3a5b3d00b25cf26afd1","offer":"{\"type\":\"offer\",\"sdp\":\"v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n\"}","recipientId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"http","message":"GraphQL anonymous completed in 349ms","timestamp":"2025-05-31 01:44:49"}
{"level":"http","message":"POST / 200 - 353ms","timestamp":"2025-05-31 01:44:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:45:04","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:45:04"}
{"level":"http","message":"POST / 200 - 34ms","timestamp":"2025-05-31 01:45:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:45:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:45:11"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 01:45:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:45:12","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:45:12"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 01:45:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:45:34","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:45:34"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-31 01:45:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:45:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:45:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:45:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:45:42","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:45:42"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 01:45:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:46:04","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:46:04"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 01:46:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:46:12","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:46:12"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 01:46:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:46:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:46:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 01:46:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:46:34","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:46:34"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:46:34"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 01:46:40"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 01:46:40"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 01:46:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:46:42","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:46:42"}
{"level":"http","message":"POST / 200 - 170ms","timestamp":"2025-05-31 01:46:42"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:46:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:42"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:46:42","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:46:42"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:46:42"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:46:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:42"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:46:46","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:46:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:46:46","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:46"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:46"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:46"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:46"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 01:46:46"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 408ms","timestamp":"2025-05-31 01:46:46"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 01:46:47"}
{"level":"http","message":"GraphQL anonymous completed in 625ms","timestamp":"2025-05-31 01:46:47"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 45","timestamp":"2025-05-31 01:46:47"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 01:46:47"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:46:47"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:46:47"}
{"level":"http","message":"GraphQL anonymous completed in 979ms","timestamp":"2025-05-31 01:46:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:46:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:46:48"}
{"level":"http","message":"POST / 200 - 37ms","timestamp":"2025-05-31 01:46:48"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:46:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:46:48","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:46:48"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 67ms","timestamp":"2025-05-31 01:46:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:46:48","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:48"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:48"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:48"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:48"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:48"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:48"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:48"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:46:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:46:49"}
{"level":"http","message":"POST / 200 - 15ms","timestamp":"2025-05-31 01:46:49"}
{"level":"http","message":"GraphQL anonymous completed in 428ms","timestamp":"2025-05-31 01:46:49"}
{"level":"http","message":"POST / 200 - 436ms","timestamp":"2025-05-31 01:46:49"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:46:49"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:49"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:49"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:49"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:49"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 74ms","timestamp":"2025-05-31 01:46:49"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 874ms","timestamp":"2025-05-31 01:46:49"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 95ms","timestamp":"2025-05-31 01:46:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:46:49","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:46:49"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 45","timestamp":"2025-05-31 01:46:49"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 01:46:49"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:49"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:49"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:49"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:46:49","variables":{"limit":10,"page":1}}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 45","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 01:46:50"}
{"level":"http","message":"GraphQL anonymous completed in 137ms","timestamp":"2025-05-31 01:46:50"}
{"level":"http","message":"POST / 200 - 153ms","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:46:50"}
{"level":"http","message":"GraphQL anonymous completed in 1376ms","timestamp":"2025-05-31 01:46:50"}
{"level":"http","message":"POST / 200 - 1427ms","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:46:50"}
{"level":"http","message":"GraphQL anonymous completed in 631ms","timestamp":"2025-05-31 01:46:50"}
{"level":"http","message":"POST / 200 - 641ms","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:46:50","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:46:50"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:50"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 68ms","timestamp":"2025-05-31 01:46:50"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 82ms","timestamp":"2025-05-31 01:46:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:46:51","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:46:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:46:51","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:51"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:51"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:51"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:51"}
{"level":"http","message":"GraphQL anonymous completed in 112ms","timestamp":"2025-05-31 01:46:51"}
{"level":"http","message":"POST / 200 - 116ms","timestamp":"2025-05-31 01:46:51"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 45","timestamp":"2025-05-31 01:46:51"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 01:46:51"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:46:51"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:46:51"}
{"level":"http","message":"GraphQL anonymous completed in 407ms","timestamp":"2025-05-31 01:46:51"}
{"level":"http","message":"POST / 200 - 412ms","timestamp":"2025-05-31 01:46:51"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:51"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:51"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:46:51"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 01:47:09"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 01:47:09"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 01:47:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:47:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:47:11"}
{"level":"http","message":"POST / 200 - 17ms","timestamp":"2025-05-31 01:47:11"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:47:11"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:11"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:11"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:11"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:47:11","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:47:11"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:12"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:47:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:47:12","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 856ms","timestamp":"2025-05-31 01:47:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:47:12","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:47:12"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 01:47:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:47:12","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:47:12","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:47:12"}
{"level":"http","message":"GraphQL anonymous completed in 662ms","timestamp":"2025-05-31 01:47:12"}
{"level":"http","message":"POST / 200 - 745ms","timestamp":"2025-05-31 01:47:13"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 583ms","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 45","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 01:47:13"}
{"level":"http","message":"GraphQL anonymous completed in 686ms","timestamp":"2025-05-31 01:47:13"}
{"level":"http","message":"POST / 200 - 726ms","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 45","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:47:13"}
{"level":"http","message":"GraphQL anonymous completed in 1727ms","timestamp":"2025-05-31 01:47:13"}
{"level":"http","message":"POST / 200 - 1785ms","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:47:13"}
{"level":"http","message":"GraphQL anonymous completed in 790ms","timestamp":"2025-05-31 01:47:13"}
{"level":"http","message":"POST / 200 - 877ms","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:13"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1066ms","timestamp":"2025-05-31 01:47:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:47:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:47:13"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-31 01:47:13"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 74ms","timestamp":"2025-05-31 01:47:13"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:47:14","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:47:14"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 76ms","timestamp":"2025-05-31 01:47:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:47:14","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:14"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 52ms","timestamp":"2025-05-31 01:47:14"}
{"level":"http","message":"GraphQL anonymous completed in 115ms","timestamp":"2025-05-31 01:47:14"}
{"level":"http","message":"POST / 200 - 119ms","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 45","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:47:14"}
{"level":"http","message":"GraphQL anonymous completed in 420ms","timestamp":"2025-05-31 01:47:14"}
{"level":"http","message":"POST / 200 - 422ms","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:14"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 01:47:33"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 01:47:33"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 01:47:34"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:47:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:47:34","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:47:34"}
{"level":"http","message":"POST / 200 - 113ms","timestamp":"2025-05-31 01:47:34"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:34"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:47:35","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:47:35","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:47:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:47:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:47:35"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 268ms","timestamp":"2025-05-31 01:47:35"}
{"level":"http","message":"GraphQL anonymous completed in 394ms","timestamp":"2025-05-31 01:47:35"}
{"level":"http","message":"POST / 200 - 402ms","timestamp":"2025-05-31 01:47:35"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 84ms","timestamp":"2025-05-31 01:47:35"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 62ms","timestamp":"2025-05-31 01:47:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:47:35","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:47:35"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 81ms","timestamp":"2025-05-31 01:47:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:47:35","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:35"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 45","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 01:47:36"}
{"level":"http","message":"GraphQL anonymous completed in 178ms","timestamp":"2025-05-31 01:47:36"}
{"level":"http","message":"POST / 200 - 183ms","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 45","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:47:36"}
{"level":"http","message":"GraphQL anonymous completed in 964ms","timestamp":"2025-05-31 01:47:36"}
{"level":"http","message":"POST / 200 - 969ms","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:47:36"}
{"level":"http","message":"GraphQL anonymous completed in 520ms","timestamp":"2025-05-31 01:47:36"}
{"level":"http","message":"POST / 200 - 526ms","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:47:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:47:36"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:36"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:36"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 58ms","timestamp":"2025-05-31 01:47:37"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 69ms","timestamp":"2025-05-31 01:47:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:47:37","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:47:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:47:37","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:37"}
{"level":"http","message":"GraphQL anonymous completed in 123ms","timestamp":"2025-05-31 01:47:37"}
{"level":"http","message":"POST / 200 - 126ms","timestamp":"2025-05-31 01:47:37"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 45","timestamp":"2025-05-31 01:47:37"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 01:47:37"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:47:37"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:47:37"}
{"level":"http","message":"GraphQL anonymous completed in 453ms","timestamp":"2025-05-31 01:47:37"}
{"level":"http","message":"POST / 200 - 457ms","timestamp":"2025-05-31 01:47:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:37"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:47:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:48:04","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:48:04"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:48:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:48:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:48:05"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-31 01:48:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:48:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:48:06"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 01:48:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:48:34","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:48:34"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:48:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:48:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:48:35"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 01:48:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:48:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:48:36"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 01:48:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-31 01:48:47","variables":{"content":"","conversationId":"6839c3a5b3d00b25cf26afd1","file":{"file":{"encoding":"7bit","filename":"voice_1748652527778.webm","mimetype":"audio/webm"},"promise":{}},"receiverId":"6839c1fcb3d00b25cf26afa7","type":"AUDIO"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, type=AUDIO, hasMetadata=false","timestamp":"2025-05-31 01:48:47"}
{"level":"info","message":"[MessageService] File upload completed: voice_1748652527778.webm -> https://res.cloudinary.com/dhefwodjk/video/upload/v1748652529/message_attachments/message_attachments/b5715cc1-8404-43ef-b1bb-7c1b94ff65b1.webm","timestamp":"2025-05-31 01:48:49"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a51f159453243ca87c71a","timestamp":"2025-05-31 01:48:50"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a51f159453243ca87c71a","timestamp":"2025-05-31 01:48:50"}
{"level":"http","message":"GraphQL anonymous completed in 2639ms","timestamp":"2025-05-31 01:48:50"}
{"level":"http","message":"POST / 200 - 2651ms","timestamp":"2025-05-31 01:48:50"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:48:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:48:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-31 01:48:50","variables":{"messageId":"683a51f159453243ca87c71a"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:48:50","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:48:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:48:50","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:48:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:48:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:48:50","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:48:50"}
{"level":"http","message":"GraphQL anonymous completed in 355ms","timestamp":"2025-05-31 01:48:51"}
{"level":"http","message":"POST / 200 - 361ms","timestamp":"2025-05-31 01:48:51"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 46","timestamp":"2025-05-31 01:48:51"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 01:48:51"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 46","timestamp":"2025-05-31 01:48:51"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 01:48:51"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:48:51"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:48:51"}
{"level":"http","message":"GraphQL anonymous completed in 702ms","timestamp":"2025-05-31 01:48:51"}
{"level":"http","message":"POST / 200 - 712ms","timestamp":"2025-05-31 01:48:51"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:48:51"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:48:51"}
{"level":"http","message":"GraphQL anonymous completed in 704ms","timestamp":"2025-05-31 01:48:51"}
{"level":"http","message":"POST / 200 - 709ms","timestamp":"2025-05-31 01:48:51"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 46","timestamp":"2025-05-31 01:48:51"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 01:48:51"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:48:52"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:48:52"}
{"level":"http","message":"GraphQL anonymous completed in 1308ms","timestamp":"2025-05-31 01:48:52"}
{"level":"http","message":"POST / 200 - 1311ms","timestamp":"2025-05-31 01:48:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:49:04","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:49:04"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 01:49:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:49:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:49:05"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:49:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:49:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:49:06"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:49:06"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 01:49:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:49:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:49:11"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 01:49:11"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:49:11"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:49:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 01:49:12","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:49:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 01:49:12","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:49:12"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 288ms","timestamp":"2025-05-31 01:49:12"}
{"level":"http","message":"GraphQL anonymous completed in 342ms","timestamp":"2025-05-31 01:49:12"}
{"level":"http","message":"POST / 200 - 346ms","timestamp":"2025-05-31 01:49:12"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 92ms","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 46","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 01:49:12"}
{"level":"http","message":"GraphQL anonymous completed in 833ms","timestamp":"2025-05-31 01:49:12"}
{"level":"http","message":"POST / 200 - 837ms","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:49:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:49:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:49:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:49:35"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 01:49:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:49:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:49:36"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:49:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:49:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:49:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:49:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:50:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:50:05"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:50:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:50:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:50:06"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-31 01:50:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:50:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:50:11"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 01:50:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:50:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:50:35"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 01:50:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-31 01:50:40","variables":{"callId":"call_1748652640698_wterhwgzl","callType":"AUDIO","conversationId":"6839c3a5b3d00b25cf26afd1","offer":"{\"type\":\"offer\",\"sdp\":\"v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n\"}","recipientId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"http","message":"GraphQL anonymous completed in 166ms","timestamp":"2025-05-31 01:50:40"}
{"level":"http","message":"POST / 200 - 171ms","timestamp":"2025-05-31 01:50:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:50:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:50:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 01:50:41"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:50:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:51:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:51:05"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 01:51:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:51:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:51:11"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 01:51:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:51:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:51:28"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 01:51:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-31 01:51:32","variables":{"callId":"call_1748652692450_o9sq2lxf3","callType":"AUDIO","conversationId":"6839c3a5b3d00b25cf26afd1","offer":"{\"type\":\"offer\",\"sdp\":\"v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n\"}","recipientId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"http","message":"GraphQL anonymous completed in 330ms","timestamp":"2025-05-31 01:51:32"}
{"level":"http","message":"POST / 200 - 335ms","timestamp":"2025-05-31 01:51:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:51:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 01:51:35"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 01:51:35"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:51:37"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-31 01:51:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:51:43","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 8ms","timestamp":"2025-05-31 01:51:43"}
{"level":"http","message":"POST / 200 - 27ms","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:43"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:51:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:51:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:52:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:52:05"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 01:52:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 01:52:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 01:52:11"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 01:52:11"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 01:52:15"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 01:52:16"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:16"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:16"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:16"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:16"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:16"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:16"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:16"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:16"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:16"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:16"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:16"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 01:52:16"}
