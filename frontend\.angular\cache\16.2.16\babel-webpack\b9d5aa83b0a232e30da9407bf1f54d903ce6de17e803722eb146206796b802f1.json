{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/reunion.service\";\nimport * as i3 from \"src/app/services/planning.service\";\nimport * as i4 from \"@app/services/data.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/services/authuser.service\";\nimport * as i7 from \"src/app/services/toast.service\";\nimport * as i8 from \"@angular/common\";\nfunction ReunionFormComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error.message || \"Une erreur est survenue\", \" \");\n  }\n}\nfunction ReunionFormComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.successMessage, \" \");\n  }\n}\nfunction ReunionFormComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" Le titre est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" La date est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" L'heure de d\\u00E9but est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" L'heure de fin est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_span_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 66);\n    i0.ɵɵelement(2, \"circle\", 67)(3, \"path\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" V\\u00E9rification... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.lienVisioError, \" \");\n  }\n}\nfunction ReunionFormComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"i\", 72);\n    i0.ɵɵtext(2, \" Lien disponible \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_63_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const planning_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", planning_r17._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(planning_r17.titre);\n  }\n}\nfunction ReunionFormComponent_div_63_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" Le planning est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"h3\", 74);\n    i0.ɵɵelement(2, \"i\", 75);\n    i0.ɵɵtext(3, \" Planning \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"label\", 76);\n    i0.ɵɵelement(5, \"i\", 77);\n    i0.ɵɵtext(6, \" S\\u00E9lectionnez un planning * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"select\", 78)(8, \"option\", 79);\n    i0.ɵɵtext(9, \"Choisissez un planning...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, ReunionFormComponent_div_63_option_10_Template, 2, 2, \"option\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ReunionFormComponent_div_63_div_11_Template, 3, 0, \"div\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.plannings);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r9.reunionForm.get(\"planning\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r9.reunionForm.get(\"planning\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction ReunionFormComponent_div_64_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.selectedPlanning.description, \" \");\n  }\n}\nfunction ReunionFormComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"h3\", 83);\n    i0.ɵɵelement(2, \"i\", 84);\n    i0.ɵɵtext(3, \" Planning s\\u00E9lectionn\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 85)(5, \"span\", 86);\n    i0.ɵɵelement(6, \"i\", 75);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 87);\n    i0.ɵɵelement(9, \"i\", 88);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, ReunionFormComponent_div_64_div_13_Template, 3, 1, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.selectedPlanning.titre, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(11, 4, ctx_r10.selectedPlanning.dateDebut, \"dd/MM/yyyy\"), \" - \", i0.ɵɵpipeBind2(12, 7, ctx_r10.selectedPlanning.dateFin, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.selectedPlanning.description);\n  }\n}\nfunction ReunionFormComponent_ng_container_73_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 92);\n    i0.ɵɵelement(1, \"i\", 93);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r21._id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", user_r21.username, \" \");\n  }\n}\nfunction ReunionFormComponent_ng_container_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ReunionFormComponent_ng_container_73_option_1_Template, 3, 2, \"option\", 91);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const users_r19 = ctx.ngIf;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", users_r19);\n  }\n}\nfunction ReunionFormComponent_i_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 94);\n  }\n}\nfunction ReunionFormComponent_i_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 95);\n  }\n}\nfunction ReunionFormComponent_i_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 96);\n  }\n}\nexport class ReunionFormComponent {\n  constructor(fb, reunionService, planningService, userService, route, router, authService, toastService) {\n    this.fb = fb;\n    this.reunionService = reunionService;\n    this.planningService = planningService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.authService = authService;\n    this.toastService = toastService;\n    this.plannings = [];\n    this.loading = true;\n    this.isSubmitting = false;\n    this.error = null;\n    this.successMessage = null;\n    this.isEditMode = false;\n    this.currentReunionId = null;\n    this.planningIdFromUrl = null;\n    this.selectedPlanning = null;\n    this.lienVisioError = null;\n    this.isCheckingLienVisio = false;\n    this.reunionForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      date: ['', Validators.required],\n      heureDebut: ['', Validators.required],\n      heureFin: ['', Validators.required],\n      lieu: [''],\n      lienVisio: [''],\n      planning: ['', Validators.required],\n      participants: [[]]\n    });\n    this.users$ = this.userService.getAllUsers();\n  }\n  ngOnInit() {\n    this.loadPlannings();\n    this.checkEditMode();\n    this.checkPlanningParam();\n    this.setupLienVisioValidation();\n  }\n  checkEditMode() {\n    const reunionId = this.route.snapshot.paramMap.get('id');\n    if (reunionId) {\n      this.isEditMode = true;\n      this.currentReunionId = reunionId;\n      this.loadReunion(reunionId);\n    }\n  }\n  loadPlannings() {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) return;\n    this.planningService.getPlanningsByUser(userId).subscribe({\n      next: response => {\n        this.plannings = response.plannings || [];\n        console.log('🔍 Plannings chargés:', this.plannings);\n        console.log('🔍 Premier planning:', this.plannings[0]);\n      },\n      error: err => {\n        this.error = err;\n        console.error('❌ Erreur chargement plannings:', err);\n      }\n    });\n  }\n  loadReunion(id) {\n    this.reunionService.getReunionById(id).subscribe({\n      next: reunion => {\n        this.reunionForm.patchValue({\n          titre: reunion.titre,\n          description: reunion.description,\n          dateDebut: this.formatDateForInput(reunion.dateDebut),\n          dateFin: this.formatDateForInput(reunion.dateFin),\n          lieu: reunion.lieu,\n          lienVisio: reunion.lienVisio,\n          planningId: reunion.planningId,\n          participants: reunion.participants\n        });\n        this.loading = false;\n      },\n      error: err => {\n        this.error = err;\n        this.loading = false;\n      }\n    });\n  }\n  formatDateForInput(date) {\n    return new Date(date).toISOString().slice(0, 16); // yyyy-MM-ddTHH:mm\n  }\n\n  checkPlanningParam() {\n    const planningId = this.route.snapshot.queryParamMap.get('planningId');\n    if (planningId) {\n      this.planningIdFromUrl = planningId;\n      // Si un ID de planning est fourni dans les paramètres de requête, le sélectionner automatiquement\n      this.reunionForm.patchValue({\n        planning: planningId\n      });\n      // Récupérer les détails du planning pour l'affichage\n      this.planningService.getPlanningById(planningId).subscribe({\n        next: response => {\n          this.selectedPlanning = response.planning;\n          // Ajouter le planning à la liste locale pour la validation\n          if (this.selectedPlanning && !this.plannings.find(p => p._id === planningId)) {\n            this.plannings.push(this.selectedPlanning);\n            console.log('✅ Planning ajouté à la liste locale pour validation:', this.selectedPlanning);\n          }\n        },\n        error: err => {\n          console.error('Erreur lors de la récupération du planning:', err);\n          this.toastService.error('Planning introuvable', 'Le planning sélectionné n\\'existe pas ou vous n\\'avez pas les permissions pour y accéder');\n        }\n      });\n    }\n  }\n  onSubmit() {\n    if (this.reunionForm.invalid || !this.canSubmit()) {\n      this.toastService.warning('Formulaire invalide', 'Veuillez corriger les erreurs avant de soumettre le formulaire');\n      return;\n    }\n    // Validation de la date par rapport au planning\n    if (!this.validateDateInPlanningRange()) {\n      return;\n    }\n    this.isSubmitting = true;\n    this.error = null;\n    this.successMessage = null;\n    const formValue = this.reunionForm.value;\n    const date = formValue.date; // already in yyyy-MM-dd format from input[type=date]\n    const heureDebut = formValue.heureDebut; // already in HH:mm format from input[type=time]\n    const heureFin = formValue.heureFin;\n    const reunionData = {\n      titre: formValue.titre,\n      description: formValue.description,\n      date: date,\n      heureDebut: heureDebut,\n      heureFin: heureFin,\n      lieu: formValue.lieu,\n      lienVisio: formValue.lienVisio,\n      planning: formValue.planning,\n      participants: formValue.participants || []\n    };\n    console.log('🔍 Données de la réunion à envoyer:', reunionData);\n    console.log('🔍 Planning ID sélectionné:', formValue.planning);\n    console.log('🔍 Type du planning ID:', typeof formValue.planning);\n    console.log('🔍 Plannings disponibles:', this.plannings);\n    this.reunionService.createReunion(reunionData).subscribe({\n      next: () => {\n        this.isSubmitting = false;\n        this.toastService.success('Réunion créée', 'La réunion a été créée avec succès');\n        // Réinitialiser le formulaire pour permettre la création d'une nouvelle réunion\n        this.resetForm();\n        // Redirection immédiate\n        this.router.navigate(['/reunions']);\n      },\n      error: err => {\n        this.isSubmitting = false;\n        console.error('Erreur lors de la création de la réunion:', err);\n        if (err.status === 403) {\n          this.toastService.accessDenied('créer une réunion', err.status);\n        } else if (err.status === 401) {\n          this.toastService.error('Non autorisé', 'Vous devez être connecté pour créer une réunion');\n        } else {\n          const errorMessage = err.error?.message || 'Erreur lors de la création de la réunion';\n          this.toastService.error('Erreur de création', errorMessage, 8000);\n        }\n      }\n    });\n  }\n  resetForm() {\n    // Reset the form to its initial state\n    this.reunionForm.reset({\n      titre: '',\n      description: '',\n      date: '',\n      heureDebut: '',\n      heureFin: '',\n      lieu: '',\n      lienVisio: '',\n      planning: '',\n      participants: []\n    });\n    // Mark the form as pristine and untouched to reset validation states\n    this.reunionForm.markAsPristine();\n    this.reunionForm.markAsUntouched();\n  }\n  goReunion() {\n    this.router.navigate(['/reunions']);\n  }\n  /**\n   * Configure la validation en temps réel du lien visio avec debounce\n   */\n  setupLienVisioValidation() {\n    this.reunionForm.get('lienVisio')?.valueChanges.pipe(debounceTime(500),\n    // Attendre 500ms après la dernière saisie\n    distinctUntilChanged() // Ne déclencher que si la valeur a changé\n    ).subscribe(value => {\n      if (value && value.trim() !== '') {\n        this.checkLienVisioUniqueness(value.trim());\n      } else {\n        this.lienVisioError = null;\n      }\n    });\n  }\n  /**\n   * Vérifie l'unicité du lien visio\n   * @param lienVisio Le lien à vérifier\n   */\n  checkLienVisioUniqueness(lienVisio) {\n    if (!lienVisio || lienVisio.trim() === '') {\n      this.lienVisioError = null;\n      return;\n    }\n    this.isCheckingLienVisio = true;\n    this.lienVisioError = null;\n    // Utiliser la nouvelle route dédiée pour vérifier l'unicité\n    this.reunionService.checkLienVisioUniqueness(lienVisio, this.currentReunionId || undefined).subscribe({\n      next: response => {\n        this.isCheckingLienVisio = false;\n        if (response.success && !response.isUnique) {\n          this.lienVisioError = `Ce lien est déjà utilisé par la réunion \"${response.conflictWith?.titre}\"`;\n        } else {\n          this.lienVisioError = null;\n        }\n      },\n      error: error => {\n        this.isCheckingLienVisio = false;\n        console.error('Erreur lors de la vérification du lien visio:', error);\n        this.lienVisioError = 'Erreur lors de la vérification du lien';\n      }\n    });\n  }\n  /**\n   * Vérifie si le formulaire peut être soumis\n   */\n  canSubmit() {\n    return this.reunionForm.valid && !this.lienVisioError && !this.isCheckingLienVisio;\n  }\n  /**\n   * Valide que la date de la réunion est dans l'intervalle du planning sélectionné\n   */\n  validateDateInPlanningRange() {\n    const formValue = this.reunionForm.value;\n    const reunionDate = formValue.date;\n    const planningId = formValue.planning;\n    if (!reunionDate || !planningId) {\n      return true; // Si pas de date ou planning, laisser la validation backend gérer\n    }\n    // Chercher d'abord dans la liste locale, puis dans selectedPlanning\n    let selectedPlanning = this.plannings.find(p => p._id === planningId);\n    if (!selectedPlanning && this.selectedPlanning && this.selectedPlanning._id === planningId) {\n      selectedPlanning = this.selectedPlanning;\n    }\n    if (!selectedPlanning) {\n      console.warn('⚠️ Planning non trouvé pour validation:', planningId);\n      console.log('📋 Plannings disponibles:', this.plannings.map(p => ({\n        id: p._id,\n        titre: p.titre\n      })));\n      console.log('🎯 Selected planning:', this.selectedPlanning);\n      // Ne pas bloquer si le planning n'est pas trouvé - laisser le backend valider\n      return true;\n    }\n    // Convertir les dates pour comparaison\n    const reunionDateObj = new Date(reunionDate);\n    const planningDateDebut = new Date(selectedPlanning.dateDebut);\n    const planningDateFin = new Date(selectedPlanning.dateFin);\n    // Comparer seulement les dates (sans les heures)\n    reunionDateObj.setHours(0, 0, 0, 0);\n    planningDateDebut.setHours(0, 0, 0, 0);\n    planningDateFin.setHours(0, 0, 0, 0);\n    if (reunionDateObj < planningDateDebut || reunionDateObj > planningDateFin) {\n      this.toastService.error('Date invalide', `La date de la réunion doit être comprise entre le ${planningDateDebut.toLocaleDateString('fr-FR')} et le ${planningDateFin.toLocaleDateString('fr-FR')} (période du planning \"${selectedPlanning.titre}\")`, 10000);\n      return false;\n    }\n    return true;\n  }\n  static {\n    this.ɵfac = function ReunionFormComponent_Factory(t) {\n      return new (t || ReunionFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ReunionService), i0.ɵɵdirectiveInject(i3.PlanningService), i0.ɵɵdirectiveInject(i4.DataService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.AuthuserService), i0.ɵɵdirectiveInject(i7.ToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReunionFormComponent,\n      selectors: [[\"app-reunion-form\"]],\n      decls: 87,\n      vars: 24,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"rounded-t-lg\", \"p-6\", \"text-white\", \"mb-0\"], [1, \"text-2xl\", \"font-bold\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-plus-circle\", \"mr-3\", \"text-purple-200\"], [1, \"text-purple-100\", \"mt-2\"], [1, \"bg-white\", \"rounded-b-lg\", \"shadow-lg\", \"p-6\", \"border-t-0\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [1, \"relative\"], [\"for\", \"titre\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-purple-700\", \"mb-2\"], [1, \"fas\", \"fa-tag\", \"mr-2\", \"text-purple-500\"], [\"id\", \"titre\", \"type\", \"text\", \"formControlName\", \"titre\", \"placeholder\", \"Nom de votre r\\u00E9union...\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-purple-200\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"class\", \"text-red-500 text-sm mt-2 flex items-center\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-indigo-700\", \"mb-2\"], [1, \"fas\", \"fa-align-left\", \"mr-2\", \"text-indigo-500\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"D\\u00E9crivez votre r\\u00E9union...\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-indigo-200\", \"shadow-sm\", \"focus:border-indigo-500\", \"focus:ring-indigo-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [1, \"bg-gradient-to-r\", \"from-blue-50\", \"to-cyan-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-blue-200\"], [1, \"text-lg\", \"font-semibold\", \"text-blue-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-calendar-clock\", \"mr-2\", \"text-blue-600\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [\"for\", \"date\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-blue-700\", \"mb-2\"], [1, \"fas\", \"fa-calendar\", \"mr-2\", \"text-blue-500\"], [\"id\", \"date\", \"type\", \"date\", \"formControlName\", \"date\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-blue-200\", \"shadow-sm\", \"focus:border-blue-500\", \"focus:ring-blue-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"heureDebut\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-green-700\", \"mb-2\"], [1, \"fas\", \"fa-play\", \"mr-2\", \"text-green-500\"], [\"id\", \"heureDebut\", \"type\", \"time\", \"formControlName\", \"heureDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-green-200\", \"shadow-sm\", \"focus:border-green-500\", \"focus:ring-green-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"heureFin\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-red-700\", \"mb-2\"], [1, \"fas\", \"fa-stop\", \"mr-2\", \"text-red-500\"], [\"id\", \"heureFin\", \"type\", \"time\", \"formControlName\", \"heureFin\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-red-200\", \"shadow-sm\", \"focus:border-red-500\", \"focus:ring-red-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [1, \"bg-gradient-to-r\", \"from-orange-50\", \"to-yellow-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-orange-200\"], [1, \"text-lg\", \"font-semibold\", \"text-orange-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-map-marker-alt\", \"mr-2\", \"text-orange-600\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"lieu\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-orange-700\", \"mb-2\"], [1, \"fas\", \"fa-map-marker-alt\", \"mr-2\", \"text-orange-500\"], [\"id\", \"lieu\", \"type\", \"text\", \"formControlName\", \"lieu\", \"placeholder\", \"Salle 101, Bureau A, Google Meet...\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-orange-200\", \"shadow-sm\", \"focus:border-orange-500\", \"focus:ring-orange-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"lienVisio\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-cyan-700\", \"mb-2\"], [1, \"fas\", \"fa-video\", \"mr-2\", \"text-cyan-500\"], [\"class\", \"ml-2 text-xs text-cyan-500\", 4, \"ngIf\"], [\"id\", \"lienVisio\", \"type\", \"url\", \"formControlName\", \"lienVisio\", \"placeholder\", \"https://meet.google.com/...\"], [\"class\", \"text-red-500 text-sm mt-2 flex items-center bg-red-50 p-2 rounded border border-red-200\", 4, \"ngIf\"], [\"class\", \"text-green-600 text-sm mt-2 flex items-center bg-green-50 p-2 rounded border border-green-200\", 4, \"ngIf\"], [\"class\", \"bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200\", 4, \"ngIf\"], [\"class\", \"bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-lg border-2 border-purple-200 shadow-sm\", 4, \"ngIf\"], [1, \"bg-gradient-to-r\", \"from-emerald-50\", \"to-teal-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-emerald-200\"], [1, \"text-lg\", \"font-semibold\", \"text-emerald-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-users\", \"mr-2\", \"text-emerald-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-emerald-700\", \"mb-2\"], [1, \"fas\", \"fa-user-friends\", \"mr-2\", \"text-emerald-500\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-emerald-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-emerald-500\", \"focus:border-emerald-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"text-sm\", \"min-h-[120px]\"], [4, \"ngIf\"], [1, \"text-xs\", \"text-emerald-600\", \"mt-2\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"mt-8\", \"flex\", \"justify-end\", \"space-x-4\", \"bg-gray-50\", \"p-4\", \"rounded-lg\", \"border-t\", \"border-gray-200\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"border-2\", \"border-gray-300\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-100\", \"hover:border-gray-400\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [\"type\", \"submit\", 1, \"px-6\", \"py-3\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"hover:from-purple-700\", \"hover:to-indigo-700\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"shadow-lg\", 3, \"disabled\"], [\"class\", \"fas fa-save mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-search mr-2\", 4, \"ngIf\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"bg-green-100\", \"border\", \"border-green-400\", \"text-green-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"text-red-500\", \"text-sm\", \"mt-2\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [1, \"ml-2\", \"text-xs\", \"text-cyan-500\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"inline\", \"h-3\", \"w-3\", \"animate-spin\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"text-red-500\", \"text-sm\", \"mt-2\", \"flex\", \"items-center\", \"bg-red-50\", \"p-2\", \"rounded\", \"border\", \"border-red-200\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-2\"], [1, \"text-green-600\", \"text-sm\", \"mt-2\", \"flex\", \"items-center\", \"bg-green-50\", \"p-2\", \"rounded\", \"border\", \"border-green-200\"], [1, \"fas\", \"fa-check-circle\", \"mr-2\"], [1, \"bg-gradient-to-r\", \"from-purple-50\", \"to-pink-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-purple-200\"], [1, \"text-lg\", \"font-semibold\", \"text-purple-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-calendar-alt\", \"mr-2\", \"text-purple-600\"], [\"for\", \"planning\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-purple-700\", \"mb-2\"], [1, \"fas\", \"fa-list\", \"mr-2\", \"text-purple-500\"], [\"id\", \"planning\", \"formControlName\", \"planning\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-purple-200\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"bg-gradient-to-r\", \"from-purple-50\", \"to-indigo-50\", \"p-4\", \"rounded-lg\", \"border-2\", \"border-purple-200\", \"shadow-sm\"], [1, \"text-lg\", \"font-semibold\", \"text-purple-800\", \"mb-3\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-calendar-check\", \"mr-2\", \"text-purple-600\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"font-semibold\", \"text-purple-800\", \"text-lg\"], [1, \"text-sm\", \"font-medium\", \"text-red-600\", \"bg-red-50\", \"px-2\", \"py-1\", \"rounded-full\", \"border\", \"border-red-200\"], [1, \"fas\", \"fa-clock\", \"mr-1\"], [\"class\", \"text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300\", 4, \"ngIf\"], [1, \"text-sm\", \"text-indigo-700\", \"mt-2\", \"bg-indigo-50\", \"p-2\", \"rounded\", \"border-l-4\", \"border-indigo-300\"], [\"class\", \"py-2\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"py-2\", 3, \"value\"], [1, \"fas\", \"fa-user\", \"mr-2\"], [1, \"fas\", \"fa-save\", \"mr-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"], [1, \"fas\", \"fa-search\", \"mr-2\"]],\n      template: function ReunionFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function ReunionFormComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(8, ReunionFormComponent_div_8_Template, 2, 1, \"div\", 6);\n          i0.ɵɵtemplate(9, ReunionFormComponent_div_9_Template, 2, 1, \"div\", 7);\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"label\", 10);\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵtext(14, \" Titre * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"input\", 12);\n          i0.ɵɵtemplate(16, ReunionFormComponent_div_16_Template, 3, 0, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 9)(18, \"label\", 14);\n          i0.ɵɵelement(19, \"i\", 15);\n          i0.ɵɵtext(20, \" Description \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"textarea\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 17)(23, \"h3\", 18);\n          i0.ɵɵelement(24, \"i\", 19);\n          i0.ɵɵtext(25, \" Planification \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\")(28, \"label\", 21);\n          i0.ɵɵelement(29, \"i\", 22);\n          i0.ɵɵtext(30, \" Date * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(31, \"input\", 23);\n          i0.ɵɵtemplate(32, ReunionFormComponent_div_32_Template, 3, 0, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\")(34, \"label\", 24);\n          i0.ɵɵelement(35, \"i\", 25);\n          i0.ɵɵtext(36, \" Heure de d\\u00E9but * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 26);\n          i0.ɵɵtemplate(38, ReunionFormComponent_div_38_Template, 3, 0, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\")(40, \"label\", 27);\n          i0.ɵɵelement(41, \"i\", 28);\n          i0.ɵɵtext(42, \" Heure de fin * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(43, \"input\", 29);\n          i0.ɵɵtemplate(44, ReunionFormComponent_div_44_Template, 3, 0, \"div\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 30)(46, \"h3\", 31);\n          i0.ɵɵelement(47, \"i\", 32);\n          i0.ɵɵtext(48, \" Localisation \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 33)(50, \"div\")(51, \"label\", 34);\n          i0.ɵɵelement(52, \"i\", 35);\n          i0.ɵɵtext(53, \" Lieu / Salle \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(54, \"input\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\")(56, \"label\", 37);\n          i0.ɵɵelement(57, \"i\", 38);\n          i0.ɵɵtext(58, \" Lien Visio \");\n          i0.ɵɵtemplate(59, ReunionFormComponent_span_59_Template, 5, 0, \"span\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(60, \"input\", 40);\n          i0.ɵɵtemplate(61, ReunionFormComponent_div_61_Template, 3, 1, \"div\", 41);\n          i0.ɵɵtemplate(62, ReunionFormComponent_div_62_Template, 3, 0, \"div\", 42);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(63, ReunionFormComponent_div_63_Template, 12, 2, \"div\", 43);\n          i0.ɵɵtemplate(64, ReunionFormComponent_div_64_Template, 14, 10, \"div\", 44);\n          i0.ɵɵelementStart(65, \"div\", 45)(66, \"h3\", 46);\n          i0.ɵɵelement(67, \"i\", 47);\n          i0.ɵɵtext(68, \" Participants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"label\", 48);\n          i0.ɵɵelement(70, \"i\", 49);\n          i0.ɵɵtext(71, \" S\\u00E9lectionnez les participants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"select\", 50);\n          i0.ɵɵtemplate(73, ReunionFormComponent_ng_container_73_Template, 2, 1, \"ng-container\", 51);\n          i0.ɵɵpipe(74, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"p\", 52);\n          i0.ɵɵelement(76, \"i\", 53);\n          i0.ɵɵtext(77, \" Maintenez Ctrl (ou Cmd) pour s\\u00E9lectionner plusieurs participants \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(78, \"div\", 54)(79, \"button\", 55);\n          i0.ɵɵlistener(\"click\", function ReunionFormComponent_Template_button_click_79_listener() {\n            return ctx.goReunion();\n          });\n          i0.ɵɵelement(80, \"i\", 56);\n          i0.ɵɵtext(81, \" Annuler \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"button\", 57);\n          i0.ɵɵtemplate(83, ReunionFormComponent_i_83_Template, 1, 0, \"i\", 58);\n          i0.ɵɵtemplate(84, ReunionFormComponent_i_84_Template, 1, 0, \"i\", 59);\n          i0.ɵɵtemplate(85, ReunionFormComponent_i_85_Template, 1, 0, \"i\", 60);\n          i0.ɵɵtext(86);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_5_0;\n          let tmp_6_0;\n          let tmp_7_0;\n          let tmp_8_0;\n          let tmp_12_0;\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifier la R\\u00E9union\" : \"Nouvelle R\\u00E9union\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifiez les d\\u00E9tails de votre r\\u00E9union\" : \"Cr\\u00E9ez une nouvelle r\\u00E9union pour votre \\u00E9quipe\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.reunionForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.successMessage);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCheckingLienVisio);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(\"mt-1 block w-full rounded-lg shadow-sm focus:ring-cyan-500 focus:ring-2 transition-all duration-200 px-4 py-3 \" + (ctx.lienVisioError ? \"border-2 border-red-300 focus:border-red-500\" : \"border-2 border-cyan-200 focus:border-cyan-500\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.lienVisioError);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.lienVisioError && !ctx.isCheckingLienVisio && ((tmp_12_0 = ctx.reunionForm.get(\"lienVisio\")) == null ? null : tmp_12_0.value) && ((tmp_12_0 = ctx.reunionForm.get(\"lienVisio\")) == null ? null : tmp_12_0.value.trim()) !== \"\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.planningIdFromUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.planningIdFromUrl && ctx.selectedPlanning);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(74, 22, ctx.users$));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"disabled\", !ctx.canSubmit() || ctx.isSubmitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitting && !ctx.isCheckingLienVisio);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCheckingLienVisio);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"Enregistrement...\" : ctx.isCheckingLienVisio ? \"V\\u00E9rification...\" : \"Cr\\u00E9er la r\\u00E9union\", \" \");\n        }\n      },\n      dependencies: [i8.NgForOf, i8.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i8.AsyncPipe, i8.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLWZvcm0uY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcmV1bmlvbnMvcmV1bmlvbi1mb3JtL3JldW5pb24tZm9ybS5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3S0FBd0siLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "message", "ctx_r1", "successMessage", "ɵɵelement", "ɵɵnamespaceSVG", "ctx_r7", "lienVisioError", "ɵɵproperty", "planning_r17", "_id", "ɵɵtextInterpolate", "titre", "ɵɵtemplate", "ReunionFormComponent_div_63_option_10_Template", "ReunionFormComponent_div_63_div_11_Template", "ctx_r9", "plannings", "tmp_1_0", "reunionForm", "get", "invalid", "touched", "ctx_r18", "selectedPlanning", "description", "ReunionFormComponent_div_64_div_13_Template", "ctx_r10", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "dateDebut", "dateFin", "user_r21", "username", "ɵɵelementContainerStart", "ReunionFormComponent_ng_container_73_option_1_Template", "ɵɵelementContainerEnd", "users_r19", "ReunionFormComponent", "constructor", "fb", "reunionService", "planningService", "userService", "route", "router", "authService", "toastService", "loading", "isSubmitting", "isEditMode", "currentReunionId", "planningIdFromUrl", "isCheckingLienVisio", "group", "required", "date", "heureDebut", "heure<PERSON>in", "lieu", "lienVisio", "planning", "participants", "users$", "getAllUsers", "ngOnInit", "loadPlannings", "checkEditMode", "checkPlanningParam", "setupLienVisioValidation", "reunionId", "snapshot", "paramMap", "loadReunion", "userId", "getCurrentUserId", "getPlanningsByUser", "subscribe", "next", "response", "console", "log", "err", "id", "getReunionById", "reunion", "patchValue", "formatDateForInput", "planningId", "Date", "toISOString", "slice", "queryParamMap", "getPlanningById", "find", "p", "push", "onSubmit", "canSubmit", "warning", "validateDateInPlanningRange", "formValue", "value", "reunionData", "createReunion", "success", "resetForm", "navigate", "status", "accessDenied", "errorMessage", "reset", "mark<PERSON><PERSON>ristine", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goReunion", "valueChanges", "pipe", "trim", "checkLienVisioUniqueness", "undefined", "isUnique", "conflictWith", "valid", "reunionDate", "warn", "map", "reunionDateObj", "planningDateDebut", "planningDateFin", "setHours", "toLocaleDateString", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ReunionService", "i3", "PlanningService", "i4", "DataService", "i5", "ActivatedRoute", "Router", "i6", "AuthuserService", "i7", "ToastService", "selectors", "decls", "vars", "consts", "template", "ReunionFormComponent_Template", "rf", "ctx", "ɵɵlistener", "ReunionFormComponent_Template_form_ngSubmit_7_listener", "ReunionFormComponent_div_8_Template", "ReunionFormComponent_div_9_Template", "ReunionFormComponent_div_16_Template", "ReunionFormComponent_div_32_Template", "ReunionFormComponent_div_38_Template", "ReunionFormComponent_div_44_Template", "ReunionFormComponent_span_59_Template", "ReunionFormComponent_div_61_Template", "ReunionFormComponent_div_62_Template", "ReunionFormComponent_div_63_Template", "ReunionFormComponent_div_64_Template", "ReunionFormComponent_ng_container_73_Template", "ReunionFormComponent_Template_button_click_79_listener", "ReunionFormComponent_i_83_Template", "ReunionFormComponent_i_84_Template", "ReunionFormComponent_i_85_Template", "tmp_5_0", "tmp_6_0", "tmp_7_0", "tmp_8_0", "ɵɵclassMap", "tmp_12_0", "ɵɵpipeBind1"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\reunions\\reunion-form\\reunion-form.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\reunions\\reunion-form\\reunion-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ReunionService } from 'src/app/services/reunion.service';\nimport { PlanningService } from 'src/app/services/planning.service';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Reunion } from 'src/app/models/reunion.model';\nimport { Planning } from 'src/app/models/planning.model';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport {Observable} from \"rxjs\";\nimport {User} from \"@app/models/user.model\";\nimport {DataService} from \"@app/services/data.service\";\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { ToastService } from 'src/app/services/toast.service';\n\n@Component({\n  selector: 'app-reunion-form',\n  templateUrl: './reunion-form.component.html',\n  styleUrls: ['./reunion-form.component.css']\n})\nexport class ReunionFormComponent implements OnInit {\n  reunionForm: FormGroup;\n  plannings: Planning[] = [];\n  users$: Observable<User[]>;\n  loading = true;\n  isSubmitting = false;\n  error: any = null;\n  successMessage: string | null = null;\n  isEditMode = false;\n  currentReunionId: string | null = null;\n  planningIdFromUrl: string | null = null;\n  selectedPlanning: Planning | null = null;\n  lienVisioError: string | null = null;\n  isCheckingLienVisio = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private reunionService: ReunionService,\n    private planningService: PlanningService,\n    private userService: DataService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private authService: AuthuserService,\n    private toastService: ToastService\n  ) {\n    this.reunionForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      date: ['', Validators.required],\n      heureDebut: ['', Validators.required],\n      heureFin: ['', Validators.required],\n      lieu: [''],\n      lienVisio: [''],\n      planning: ['', Validators.required],\n      participants: [[]]\n    });\n\n\n    this.users$ = this.userService.getAllUsers();\n  }\n\n  ngOnInit(): void {\n    this.loadPlannings();\n    this.checkEditMode();\n    this.checkPlanningParam();\n    this.setupLienVisioValidation();\n  }\n\n  checkEditMode(): void {\n    const reunionId = this.route.snapshot.paramMap.get('id');\n    if (reunionId) {\n      this.isEditMode = true;\n      this.currentReunionId = reunionId;\n      this.loadReunion(reunionId);\n    }\n  }\n\n  loadPlannings(): void {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) return;\n\n    this.planningService.getPlanningsByUser(userId).subscribe({\n      next: (response:any) => {\n        this.plannings = response.plannings || [];\n        console.log('🔍 Plannings chargés:', this.plannings);\n        console.log('🔍 Premier planning:', this.plannings[0]);\n      },\n      error: (err) => {\n        this.error = err;\n        console.error('❌ Erreur chargement plannings:', err);\n      }\n    });\n  }\n\n  loadReunion(id: string): void {\n    this.reunionService.getReunionById(id).subscribe({\n      next: (reunion) => {\n        this.reunionForm.patchValue({\n          titre: reunion.titre,\n          description: reunion.description,\n          dateDebut: this.formatDateForInput(reunion.dateDebut),\n          dateFin: this.formatDateForInput(reunion.dateFin),\n          lieu: reunion.lieu,\n          lienVisio: reunion.lienVisio,\n          planningId: reunion.planningId,\n          participants: reunion.participants\n        });\n        this.loading = false;\n      },\n      error: (err) => {\n        this.error = err;\n        this.loading = false;\n      }\n    });\n  }\n\n  formatDateForInput(date: Date | string): string {\n    return new Date(date).toISOString().slice(0, 16); // yyyy-MM-ddTHH:mm\n  }\n\n  checkPlanningParam(): void {\n    const planningId = this.route.snapshot.queryParamMap.get('planningId');\n    if (planningId) {\n      this.planningIdFromUrl = planningId;\n\n      // Si un ID de planning est fourni dans les paramètres de requête, le sélectionner automatiquement\n      this.reunionForm.patchValue({\n        planning: planningId\n      });\n\n      // Récupérer les détails du planning pour l'affichage\n      this.planningService.getPlanningById(planningId).subscribe({\n        next: (response: any) => {\n          this.selectedPlanning = response.planning;\n          // Ajouter le planning à la liste locale pour la validation\n          if (this.selectedPlanning && !this.plannings.find(p => p._id === planningId)) {\n            this.plannings.push(this.selectedPlanning);\n            console.log('✅ Planning ajouté à la liste locale pour validation:', this.selectedPlanning);\n          }\n        },\n        error: (err) => {\n          console.error('Erreur lors de la récupération du planning:', err);\n          this.toastService.error(\n            'Planning introuvable',\n            'Le planning sélectionné n\\'existe pas ou vous n\\'avez pas les permissions pour y accéder'\n          );\n        }\n      });\n    }\n  }\n\n  onSubmit(): void {\n    if (this.reunionForm.invalid || !this.canSubmit()) {\n      this.toastService.warning(\n        'Formulaire invalide',\n        'Veuillez corriger les erreurs avant de soumettre le formulaire'\n      );\n      return;\n    }\n\n    // Validation de la date par rapport au planning\n    if (!this.validateDateInPlanningRange()) {\n      return;\n    }\n\n    this.isSubmitting = true;\n    this.error = null;\n    this.successMessage = null;\n    const formValue = this.reunionForm.value;\n\n    const date = formValue.date; // already in yyyy-MM-dd format from input[type=date]\n    const heureDebut = formValue.heureDebut; // already in HH:mm format from input[type=time]\n    const heureFin = formValue.heureFin;\n\n    const reunionData: any = {\n      titre: formValue.titre,\n      description: formValue.description,\n      date: date,\n      heureDebut: heureDebut,\n      heureFin: heureFin,\n      lieu: formValue.lieu,\n      lienVisio: formValue.lienVisio,\n      planning: formValue.planning,\n      participants: formValue.participants || []\n    };\n\n    console.log('🔍 Données de la réunion à envoyer:', reunionData);\n    console.log('🔍 Planning ID sélectionné:', formValue.planning);\n    console.log('🔍 Type du planning ID:', typeof formValue.planning);\n    console.log('🔍 Plannings disponibles:', this.plannings);\n\n    this.reunionService.createReunion(reunionData).subscribe({\n      next: () => {\n        this.isSubmitting = false;\n\n        this.toastService.success(\n          'Réunion créée',\n          'La réunion a été créée avec succès'\n        );\n\n        // Réinitialiser le formulaire pour permettre la création d'une nouvelle réunion\n        this.resetForm();\n\n        // Redirection immédiate\n        this.router.navigate(['/reunions']);\n      },\n      error: (err) => {\n        this.isSubmitting = false;\n        console.error('Erreur lors de la création de la réunion:', err);\n\n        if (err.status === 403) {\n          this.toastService.accessDenied('créer une réunion', err.status);\n        } else if (err.status === 401) {\n          this.toastService.error(\n            'Non autorisé',\n            'Vous devez être connecté pour créer une réunion'\n          );\n        } else {\n          const errorMessage = err.error?.message || 'Erreur lors de la création de la réunion';\n          this.toastService.error(\n            'Erreur de création',\n            errorMessage,\n            8000\n          );\n        }\n      }\n    });\n  }\n\n  resetForm(): void {\n    // Reset the form to its initial state\n    this.reunionForm.reset({\n      titre: '',\n      description: '',\n      date: '',\n      heureDebut: '',\n      heureFin: '',\n      lieu: '',\n      lienVisio: '',\n      planning: '',\n      participants: []\n    });\n\n    // Mark the form as pristine and untouched to reset validation states\n    this.reunionForm.markAsPristine();\n    this.reunionForm.markAsUntouched();\n  }\n\n\n  goReunion(): void {\n    this.router.navigate(['/reunions']);\n  }\n\n  /**\n   * Configure la validation en temps réel du lien visio avec debounce\n   */\n  setupLienVisioValidation(): void {\n    this.reunionForm.get('lienVisio')?.valueChanges\n      .pipe(\n        debounceTime(500), // Attendre 500ms après la dernière saisie\n        distinctUntilChanged() // Ne déclencher que si la valeur a changé\n      )\n      .subscribe(value => {\n        if (value && value.trim() !== '') {\n          this.checkLienVisioUniqueness(value.trim());\n        } else {\n          this.lienVisioError = null;\n        }\n      });\n  }\n\n  /**\n   * Vérifie l'unicité du lien visio\n   * @param lienVisio Le lien à vérifier\n   */\n  checkLienVisioUniqueness(lienVisio: string): void {\n    if (!lienVisio || lienVisio.trim() === '') {\n      this.lienVisioError = null;\n      return;\n    }\n\n    this.isCheckingLienVisio = true;\n    this.lienVisioError = null;\n\n    // Utiliser la nouvelle route dédiée pour vérifier l'unicité\n    this.reunionService.checkLienVisioUniqueness(lienVisio, this.currentReunionId || undefined).subscribe({\n      next: (response) => {\n        this.isCheckingLienVisio = false;\n\n        if (response.success && !response.isUnique) {\n          this.lienVisioError = `Ce lien est déjà utilisé par la réunion \"${response.conflictWith?.titre}\"`;\n        } else {\n          this.lienVisioError = null;\n        }\n      },\n      error: (error) => {\n        this.isCheckingLienVisio = false;\n        console.error('Erreur lors de la vérification du lien visio:', error);\n        this.lienVisioError = 'Erreur lors de la vérification du lien';\n      }\n    });\n  }\n\n  /**\n   * Vérifie si le formulaire peut être soumis\n   */\n  canSubmit(): boolean {\n    return this.reunionForm.valid && !this.lienVisioError && !this.isCheckingLienVisio;\n  }\n\n  /**\n   * Valide que la date de la réunion est dans l'intervalle du planning sélectionné\n   */\n  validateDateInPlanningRange(): boolean {\n    const formValue = this.reunionForm.value;\n    const reunionDate = formValue.date;\n    const planningId = formValue.planning;\n\n    if (!reunionDate || !planningId) {\n      return true; // Si pas de date ou planning, laisser la validation backend gérer\n    }\n\n    // Chercher d'abord dans la liste locale, puis dans selectedPlanning\n    let selectedPlanning = this.plannings.find(p => p._id === planningId);\n\n    if (!selectedPlanning && this.selectedPlanning && this.selectedPlanning._id === planningId) {\n      selectedPlanning = this.selectedPlanning;\n    }\n\n    if (!selectedPlanning) {\n      console.warn('⚠️ Planning non trouvé pour validation:', planningId);\n      console.log('📋 Plannings disponibles:', this.plannings.map(p => ({ id: p._id, titre: p.titre })));\n      console.log('🎯 Selected planning:', this.selectedPlanning);\n\n      // Ne pas bloquer si le planning n'est pas trouvé - laisser le backend valider\n      return true;\n    }\n\n    // Convertir les dates pour comparaison\n    const reunionDateObj = new Date(reunionDate);\n    const planningDateDebut = new Date(selectedPlanning.dateDebut);\n    const planningDateFin = new Date(selectedPlanning.dateFin);\n\n    // Comparer seulement les dates (sans les heures)\n    reunionDateObj.setHours(0, 0, 0, 0);\n    planningDateDebut.setHours(0, 0, 0, 0);\n    planningDateFin.setHours(0, 0, 0, 0);\n\n    if (reunionDateObj < planningDateDebut || reunionDateObj > planningDateFin) {\n      this.toastService.error(\n        'Date invalide',\n        `La date de la réunion doit être comprise entre le ${planningDateDebut.toLocaleDateString('fr-FR')} et le ${planningDateFin.toLocaleDateString('fr-FR')} (période du planning \"${selectedPlanning.titre}\")`,\n        10000\n      );\n      return false;\n    }\n\n    return true;\n  }\n}", "<div class=\"container mx-auto px-4 py-6 max-w-3xl\">\n  <!-- En-tête avec gradient coloré -->\n  <div class=\"bg-gradient-to-r from-purple-600 to-indigo-600 rounded-t-lg p-6 text-white mb-0\">\n    <h1 class=\"text-2xl font-bold flex items-center\">\n      <i class=\"fas fa-plus-circle mr-3 text-purple-200\"></i>\n      {{ isEditMode ? 'Modifier la Réunion' : 'Nouvelle Réunion' }}\n    </h1>\n    <p class=\"text-purple-100 mt-2\">\n      {{ isEditMode ? 'Modifiez les détails de votre réunion' : 'Créez une nouvelle réunion pour votre équipe' }}\n    </p>\n  </div>\n\n  <form [formGroup]=\"reunionForm\" (ngSubmit)=\"onSubmit()\" class=\"bg-white rounded-b-lg shadow-lg p-6 border-t-0\">\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n      {{ error.message || 'Une erreur est survenue' }}\n    </div>\n    <div *ngIf=\"successMessage\" class=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\">\n      {{ successMessage }}\n    </div>\n\n    <div class=\"grid grid-cols-1 gap-6\">\n      <!-- Titre -->\n      <div class=\"relative\">\n        <label for=\"titre\" class=\"block text-sm font-medium text-purple-700 mb-2\">\n          <i class=\"fas fa-tag mr-2 text-purple-500\"></i>\n          Titre *\n        </label>\n        <input id=\"titre\" type=\"text\" formControlName=\"titre\"\n               class=\"mt-1 block w-full rounded-lg border-2 border-purple-200 shadow-sm focus:border-purple-500 focus:ring-purple-500 focus:ring-2 transition-all duration-200 px-4 py-3\"\n               placeholder=\"Nom de votre réunion...\">\n        <div *ngIf=\"reunionForm.get('titre')?.invalid && reunionForm.get('titre')?.touched\"\n             class=\"text-red-500 text-sm mt-2 flex items-center\">\n          <i class=\"fas fa-exclamation-circle mr-1\"></i>\n          Le titre est obligatoire\n        </div>\n      </div>\n\n      <!-- Description -->\n      <div class=\"relative\">\n        <label for=\"description\" class=\"block text-sm font-medium text-indigo-700 mb-2\">\n          <i class=\"fas fa-align-left mr-2 text-indigo-500\"></i>\n          Description\n        </label>\n        <textarea id=\"description\" formControlName=\"description\" rows=\"3\"\n                  class=\"mt-1 block w-full rounded-lg border-2 border-indigo-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 focus:ring-2 transition-all duration-200 px-4 py-3\"\n                  placeholder=\"Décrivez votre réunion...\"></textarea>\n      </div>\n\n      <!-- Date and Time -->\n      <div class=\"bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg border border-blue-200\">\n        <h3 class=\"text-lg font-semibold text-blue-800 mb-4 flex items-center\">\n          <i class=\"fas fa-calendar-clock mr-2 text-blue-600\"></i>\n          Planification\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <label for=\"date\" class=\"block text-sm font-medium text-blue-700 mb-2\">\n              <i class=\"fas fa-calendar mr-2 text-blue-500\"></i>\n              Date *\n            </label>\n            <input id=\"date\" type=\"date\" formControlName=\"date\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-blue-200 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n            <div *ngIf=\"reunionForm.get('date')?.invalid && reunionForm.get('date')?.touched\"\n                 class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              La date est obligatoire\n            </div>\n          </div>\n\n          <div>\n            <label for=\"heureDebut\" class=\"block text-sm font-medium text-green-700 mb-2\">\n              <i class=\"fas fa-play mr-2 text-green-500\"></i>\n              Heure de début *\n            </label>\n            <input id=\"heureDebut\" type=\"time\" formControlName=\"heureDebut\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-green-200 shadow-sm focus:border-green-500 focus:ring-green-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n            <div *ngIf=\"reunionForm.get('heureDebut')?.invalid && reunionForm.get('heureDebut')?.touched\"\n                 class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              L'heure de début est obligatoire\n            </div>\n          </div>\n\n          <div>\n            <label for=\"heureFin\" class=\"block text-sm font-medium text-red-700 mb-2\">\n              <i class=\"fas fa-stop mr-2 text-red-500\"></i>\n              Heure de fin *\n            </label>\n            <input id=\"heureFin\" type=\"time\" formControlName=\"heureFin\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-red-200 shadow-sm focus:border-red-500 focus:ring-red-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n            <div *ngIf=\"reunionForm.get('heureFin')?.invalid && reunionForm.get('heureFin')?.touched\"\n                 class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              L'heure de fin est obligatoire\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Lieu / Lien visio -->\n      <div class=\"bg-gradient-to-r from-orange-50 to-yellow-50 p-4 rounded-lg border border-orange-200\">\n        <h3 class=\"text-lg font-semibold text-orange-800 mb-4 flex items-center\">\n          <i class=\"fas fa-map-marker-alt mr-2 text-orange-600\"></i>\n          Localisation\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label for=\"lieu\" class=\"block text-sm font-medium text-orange-700 mb-2\">\n              <i class=\"fas fa-map-marker-alt mr-2 text-orange-500\"></i>\n              Lieu / Salle\n            </label>\n            <input id=\"lieu\" type=\"text\" formControlName=\"lieu\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-orange-200 shadow-sm focus:border-orange-500 focus:ring-orange-500 focus:ring-2 transition-all duration-200 px-4 py-3\"\n                   placeholder=\"Salle 101, Bureau A, Google Meet...\">\n          </div>\n\n          <div>\n            <label for=\"lienVisio\" class=\"block text-sm font-medium text-cyan-700 mb-2\">\n              <i class=\"fas fa-video mr-2 text-cyan-500\"></i>\n              Lien Visio\n              <span *ngIf=\"isCheckingLienVisio\" class=\"ml-2 text-xs text-cyan-500\">\n                <svg class=\"inline h-3 w-3 animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                  <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n                Vérification...\n              </span>\n            </label>\n            <input id=\"lienVisio\" type=\"url\" formControlName=\"lienVisio\"\n                   [class]=\"'mt-1 block w-full rounded-lg shadow-sm focus:ring-cyan-500 focus:ring-2 transition-all duration-200 px-4 py-3 ' +\n                           (lienVisioError ? 'border-2 border-red-300 focus:border-red-500' : 'border-2 border-cyan-200 focus:border-cyan-500')\"\n                   placeholder=\"https://meet.google.com/...\">\n\n            <!-- Message d'erreur pour l'unicité du lien -->\n            <div *ngIf=\"lienVisioError\" class=\"text-red-500 text-sm mt-2 flex items-center bg-red-50 p-2 rounded border border-red-200\">\n              <i class=\"fas fa-exclamation-triangle mr-2\"></i>\n              {{ lienVisioError }}\n            </div>\n\n            <!-- Message de succès -->\n            <div *ngIf=\"!lienVisioError && !isCheckingLienVisio && reunionForm.get('lienVisio')?.value && reunionForm.get('lienVisio')?.value.trim() !== ''\"\n                 class=\"text-green-600 text-sm mt-2 flex items-center bg-green-50 p-2 rounded border border-green-200\">\n              <i class=\"fas fa-check-circle mr-2\"></i>\n              Lien disponible\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Planning - affiché seulement si pas de planningId dans l'URL -->\n      <div *ngIf=\"!planningIdFromUrl\" class=\"bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200\">\n        <h3 class=\"text-lg font-semibold text-purple-800 mb-4 flex items-center\">\n          <i class=\"fas fa-calendar-alt mr-2 text-purple-600\"></i>\n          Planning\n        </h3>\n        <label for=\"planning\" class=\"block text-sm font-medium text-purple-700 mb-2\">\n          <i class=\"fas fa-list mr-2 text-purple-500\"></i>\n          Sélectionnez un planning *\n        </label>\n        <select id=\"planning\" formControlName=\"planning\"\n                class=\"mt-1 block w-full rounded-lg border-2 border-purple-200 shadow-sm focus:border-purple-500 focus:ring-purple-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n          <option value=\"\">Choisissez un planning...</option>\n          <option *ngFor=\"let planning of plannings\" [value]=\"planning._id\">{{ planning.titre }}</option>\n        </select>\n        <div *ngIf=\"reunionForm.get('planning')?.invalid && reunionForm.get('planning')?.touched\"\n             class=\"text-red-500 text-sm mt-2 flex items-center\">\n          <i class=\"fas fa-exclamation-circle mr-1\"></i>\n          Le planning est obligatoire\n        </div>\n      </div>\n\n      <!-- Planning info - affiché quand planningId est dans l'URL -->\n      <div *ngIf=\"planningIdFromUrl && selectedPlanning\" class=\"bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-lg border-2 border-purple-200 shadow-sm\">\n        <h3 class=\"text-lg font-semibold text-purple-800 mb-3 flex items-center\">\n          <i class=\"fas fa-calendar-check mr-2 text-purple-600\"></i>\n          Planning sélectionné\n        </h3>\n        <div class=\"flex items-center justify-between\">\n          <span class=\"font-semibold text-purple-800 text-lg\">\n            <i class=\"fas fa-calendar-alt mr-2 text-purple-600\"></i>\n            {{ selectedPlanning.titre }}\n          </span>\n          <span class=\"text-sm font-medium text-red-600 bg-red-50 px-2 py-1 rounded-full border border-red-200\">\n            <i class=\"fas fa-clock mr-1\"></i>\n            {{ selectedPlanning.dateDebut | date:'dd/MM/yyyy' }} -\n            {{ selectedPlanning.dateFin | date:'dd/MM/yyyy' }}\n          </span>\n        </div>\n        <div *ngIf=\"selectedPlanning.description\" class=\"text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300\">\n          <i class=\"fas fa-info-circle mr-1\"></i>\n          {{ selectedPlanning.description }}\n        </div>\n      </div>\n\n      <!-- Participants -->\n      <div class=\"bg-gradient-to-r from-emerald-50 to-teal-50 p-4 rounded-lg border border-emerald-200\">\n        <h3 class=\"text-lg font-semibold text-emerald-800 mb-4 flex items-center\">\n          <i class=\"fas fa-users mr-2 text-emerald-600\"></i>\n          Participants\n        </h3>\n        <label class=\"block text-sm font-medium text-emerald-700 mb-2\">\n          <i class=\"fas fa-user-friends mr-2 text-emerald-500\"></i>\n          Sélectionnez les participants\n        </label>\n        <select formControlName=\"participants\" multiple\n                class=\"mt-1 block w-full px-4 py-3 border-2 border-emerald-200 rounded-lg shadow-sm focus:ring-emerald-500 focus:border-emerald-500 focus:ring-2 transition-all duration-200 text-sm min-h-[120px]\">\n          <ng-container *ngIf=\"users$ | async as users\">\n            <option *ngFor=\"let user of users\" [value]=\"user._id\" class=\"py-2\">\n              <i class=\"fas fa-user mr-2\"></i>{{ user.username }}\n            </option>\n          </ng-container>\n        </select>\n        <p class=\"text-xs text-emerald-600 mt-2\">\n          <i class=\"fas fa-info-circle mr-1\"></i>\n          Maintenez Ctrl (ou Cmd) pour sélectionner plusieurs participants\n        </p>\n      </div>\n    </div>\n\n    <!-- Boutons d'action avec design amélioré -->\n    <div class=\"mt-8 flex justify-end space-x-4 bg-gray-50 p-4 rounded-lg border-t border-gray-200\">\n      <button type=\"button\" (click)=\"goReunion()\"\n              class=\"px-6 py-3 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-all duration-200 flex items-center\">\n        <i class=\"fas fa-times mr-2\"></i>\n        Annuler\n      </button>\n      <button type=\"submit\" [disabled]=\"!canSubmit() || isSubmitting\"\n              class=\"px-6 py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-lg\">\n        <i class=\"fas fa-save mr-2\" *ngIf=\"!isSubmitting && !isCheckingLienVisio\"></i>\n        <i class=\"fas fa-spinner fa-spin mr-2\" *ngIf=\"isSubmitting\"></i>\n        <i class=\"fas fa-search mr-2\" *ngIf=\"isCheckingLienVisio\"></i>\n        {{ isSubmitting ? 'Enregistrement...' : (isCheckingLienVisio ? 'Vérification...' : 'Créer la réunion') }}\n      </button>\n    </div>\n  </form>\n</div>"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAUnE,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,gBAAgB;;;;;;;;;;;;ICE/DC,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,KAAA,CAAAC,OAAA,mCACF;;;;;IACAR,EAAA,CAAAC,cAAA,cAA+G;IAC7GD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAI,MAAA,CAAAC,cAAA,MACF;;;;;IAYIV,EAAA,CAAAC,cAAA,cACyD;IACvDD,EAAA,CAAAW,SAAA,YAA8C;IAC9CX,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA4BFH,EAAA,CAAAC,cAAA,cACyD;IACvDD,EAAA,CAAAW,SAAA,YAA8C;IAC9CX,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAUNH,EAAA,CAAAC,cAAA,cACyD;IACvDD,EAAA,CAAAW,SAAA,YAA8C;IAC9CX,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAUNH,EAAA,CAAAC,cAAA,cACyD;IACvDD,EAAA,CAAAW,SAAA,YAA8C;IAC9CX,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA0BJH,EAAA,CAAAC,cAAA,eAAqE;IACnED,EAAA,CAAAY,cAAA,EAAyE;IAAzEZ,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAW,SAAA,iBAAkG;IAEpGX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAQTH,EAAA,CAAAC,cAAA,cAA4H;IAC1HD,EAAA,CAAAW,SAAA,YAAgD;IAChDX,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAQ,MAAA,CAAAC,cAAA,MACF;;;;;IAGAd,EAAA,CAAAC,cAAA,cAC2G;IACzGD,EAAA,CAAAW,SAAA,YAAwC;IACxCX,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAkBRH,EAAA,CAAAC,cAAA,iBAAkE;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAApDH,EAAA,CAAAe,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAsB;IAACjB,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAkB,iBAAA,CAAAF,YAAA,CAAAG,KAAA,CAAoB;;;;;IAExFnB,EAAA,CAAAC,cAAA,cACyD;IACvDD,EAAA,CAAAW,SAAA,YAA8C;IAC9CX,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAlBRH,EAAA,CAAAC,cAAA,cAA2H;IAEvHD,EAAA,CAAAW,SAAA,YAAwD;IACxDX,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAW,SAAA,YAAgD;IAChDX,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBACmL;IAChKD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnDH,EAAA,CAAAoB,UAAA,KAAAC,8CAAA,qBAA+F;IACjGrB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAoB,UAAA,KAAAE,2CAAA,kBAIM;IACRtB,EAAA,CAAAG,YAAA,EAAM;;;;;IAP2BH,EAAA,CAAAI,SAAA,IAAY;IAAZJ,EAAA,CAAAe,UAAA,YAAAQ,MAAA,CAAAC,SAAA,CAAY;IAErCxB,EAAA,CAAAI,SAAA,GAAkF;IAAlFJ,EAAA,CAAAe,UAAA,WAAAU,OAAA,GAAAF,MAAA,CAAAG,WAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,OAAA,OAAAH,OAAA,GAAAF,MAAA,CAAAG,WAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAI,OAAA,EAAkF;;;;;IAwBxF7B,EAAA,CAAAC,cAAA,cAAqI;IACnID,EAAA,CAAAW,SAAA,YAAuC;IACvCX,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAyB,OAAA,CAAAC,gBAAA,CAAAC,WAAA,MACF;;;;;IAnBFhC,EAAA,CAAAC,cAAA,cAA4J;IAExJD,EAAA,CAAAW,SAAA,YAA0D;IAC1DX,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA+C;IAE3CD,EAAA,CAAAW,SAAA,YAAwD;IACxDX,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAAsG;IACpGD,EAAA,CAAAW,SAAA,YAAiC;IACjCX,EAAA,CAAAE,MAAA,IAEF;;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAoB,UAAA,KAAAa,2CAAA,kBAGM;IACRjC,EAAA,CAAAG,YAAA,EAAM;;;;IAZAH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA6B,OAAA,CAAAH,gBAAA,CAAAZ,KAAA,MACF;IAGEnB,EAAA,CAAAI,SAAA,GAEF;IAFEJ,EAAA,CAAAmC,kBAAA,MAAAnC,EAAA,CAAAoC,WAAA,QAAAF,OAAA,CAAAH,gBAAA,CAAAM,SAAA,wBAAArC,EAAA,CAAAoC,WAAA,QAAAF,OAAA,CAAAH,gBAAA,CAAAO,OAAA,qBAEF;IAEItC,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAe,UAAA,SAAAmB,OAAA,CAAAH,gBAAA,CAAAC,WAAA,CAAkC;;;;;IAmBpChC,EAAA,CAAAC,cAAA,iBAAmE;IACjED,EAAA,CAAAW,SAAA,YAAgC;IAAAX,EAAA,CAAAE,MAAA,GAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF0BH,EAAA,CAAAe,UAAA,UAAAwB,QAAA,CAAAtB,GAAA,CAAkB;IACnBjB,EAAA,CAAAI,SAAA,GAClC;IADkCJ,EAAA,CAAAK,kBAAA,KAAAkC,QAAA,CAAAC,QAAA,MAClC;;;;;IAHFxC,EAAA,CAAAyC,uBAAA,GAA8C;IAC5CzC,EAAA,CAAAoB,UAAA,IAAAsB,sDAAA,qBAES;IACX1C,EAAA,CAAA2C,qBAAA,EAAe;;;;IAHY3C,EAAA,CAAAI,SAAA,GAAQ;IAARJ,EAAA,CAAAe,UAAA,YAAA6B,SAAA,CAAQ;;;;;IAqBrC5C,EAAA,CAAAW,SAAA,YAA8E;;;;;IAC9EX,EAAA,CAAAW,SAAA,YAAgE;;;;;IAChEX,EAAA,CAAAW,SAAA,YAA8D;;;ADnNtE,OAAM,MAAOkC,oBAAoB;EAe/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,eAAgC,EAChCC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,WAA4B,EAC5BC,YAA0B;IAP1B,KAAAP,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IArBtB,KAAA9B,SAAS,GAAe,EAAE;IAE1B,KAAA+B,OAAO,GAAG,IAAI;IACd,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAjD,KAAK,GAAQ,IAAI;IACjB,KAAAG,cAAc,GAAkB,IAAI;IACpC,KAAA+C,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,iBAAiB,GAAkB,IAAI;IACvC,KAAA5B,gBAAgB,GAAoB,IAAI;IACxC,KAAAjB,cAAc,GAAkB,IAAI;IACpC,KAAA8C,mBAAmB,GAAG,KAAK;IAYzB,IAAI,CAAClC,WAAW,GAAG,IAAI,CAACqB,EAAE,CAACc,KAAK,CAAC;MAC/B1C,KAAK,EAAE,CAAC,EAAE,EAAEtB,UAAU,CAACiE,QAAQ,CAAC;MAChC9B,WAAW,EAAE,CAAC,EAAE,CAAC;MACjB+B,IAAI,EAAE,CAAC,EAAE,EAAElE,UAAU,CAACiE,QAAQ,CAAC;MAC/BE,UAAU,EAAE,CAAC,EAAE,EAAEnE,UAAU,CAACiE,QAAQ,CAAC;MACrCG,QAAQ,EAAE,CAAC,EAAE,EAAEpE,UAAU,CAACiE,QAAQ,CAAC;MACnCI,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC,EAAE,EAAEvE,UAAU,CAACiE,QAAQ,CAAC;MACnCO,YAAY,EAAE,CAAC,EAAE;KAClB,CAAC;IAGF,IAAI,CAACC,MAAM,GAAG,IAAI,CAACpB,WAAW,CAACqB,WAAW,EAAE;EAC9C;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEAF,aAAaA,CAAA;IACX,MAAMG,SAAS,GAAG,IAAI,CAAC1B,KAAK,CAAC2B,QAAQ,CAACC,QAAQ,CAACpD,GAAG,CAAC,IAAI,CAAC;IACxD,IAAIkD,SAAS,EAAE;MACb,IAAI,CAACpB,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,gBAAgB,GAAGmB,SAAS;MACjC,IAAI,CAACG,WAAW,CAACH,SAAS,CAAC;;EAE/B;EAEAJ,aAAaA,CAAA;IACX,MAAMQ,MAAM,GAAG,IAAI,CAAC5B,WAAW,CAAC6B,gBAAgB,EAAE;IAClD,IAAI,CAACD,MAAM,EAAE;IAEb,IAAI,CAAChC,eAAe,CAACkC,kBAAkB,CAACF,MAAM,CAAC,CAACG,SAAS,CAAC;MACxDC,IAAI,EAAGC,QAAY,IAAI;QACrB,IAAI,CAAC9D,SAAS,GAAG8D,QAAQ,CAAC9D,SAAS,IAAI,EAAE;QACzC+D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAChE,SAAS,CAAC;QACpD+D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAChE,SAAS,CAAC,CAAC,CAAC,CAAC;MACxD,CAAC;MACDjB,KAAK,EAAGkF,GAAG,IAAI;QACb,IAAI,CAAClF,KAAK,GAAGkF,GAAG;QAChBF,OAAO,CAAChF,KAAK,CAAC,gCAAgC,EAAEkF,GAAG,CAAC;MACtD;KACD,CAAC;EACJ;EAEAT,WAAWA,CAACU,EAAU;IACpB,IAAI,CAAC1C,cAAc,CAAC2C,cAAc,CAACD,EAAE,CAAC,CAACN,SAAS,CAAC;MAC/CC,IAAI,EAAGO,OAAO,IAAI;QAChB,IAAI,CAAClE,WAAW,CAACmE,UAAU,CAAC;UAC1B1E,KAAK,EAAEyE,OAAO,CAACzE,KAAK;UACpBa,WAAW,EAAE4D,OAAO,CAAC5D,WAAW;UAChCK,SAAS,EAAE,IAAI,CAACyD,kBAAkB,CAACF,OAAO,CAACvD,SAAS,CAAC;UACrDC,OAAO,EAAE,IAAI,CAACwD,kBAAkB,CAACF,OAAO,CAACtD,OAAO,CAAC;UACjD4B,IAAI,EAAE0B,OAAO,CAAC1B,IAAI;UAClBC,SAAS,EAAEyB,OAAO,CAACzB,SAAS;UAC5B4B,UAAU,EAAEH,OAAO,CAACG,UAAU;UAC9B1B,YAAY,EAAEuB,OAAO,CAACvB;SACvB,CAAC;QACF,IAAI,CAACd,OAAO,GAAG,KAAK;MACtB,CAAC;MACDhD,KAAK,EAAGkF,GAAG,IAAI;QACb,IAAI,CAAClF,KAAK,GAAGkF,GAAG;QAChB,IAAI,CAAClC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAuC,kBAAkBA,CAAC/B,IAAmB;IACpC,OAAO,IAAIiC,IAAI,CAACjC,IAAI,CAAC,CAACkC,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACpD;;EAEAvB,kBAAkBA,CAAA;IAChB,MAAMoB,UAAU,GAAG,IAAI,CAAC5C,KAAK,CAAC2B,QAAQ,CAACqB,aAAa,CAACxE,GAAG,CAAC,YAAY,CAAC;IACtE,IAAIoE,UAAU,EAAE;MACd,IAAI,CAACpC,iBAAiB,GAAGoC,UAAU;MAEnC;MACA,IAAI,CAACrE,WAAW,CAACmE,UAAU,CAAC;QAC1BzB,QAAQ,EAAE2B;OACX,CAAC;MAEF;MACA,IAAI,CAAC9C,eAAe,CAACmD,eAAe,CAACL,UAAU,CAAC,CAACX,SAAS,CAAC;QACzDC,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAI,CAACvD,gBAAgB,GAAGuD,QAAQ,CAAClB,QAAQ;UACzC;UACA,IAAI,IAAI,CAACrC,gBAAgB,IAAI,CAAC,IAAI,CAACP,SAAS,CAAC6E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrF,GAAG,KAAK8E,UAAU,CAAC,EAAE;YAC5E,IAAI,CAACvE,SAAS,CAAC+E,IAAI,CAAC,IAAI,CAACxE,gBAAgB,CAAC;YAC1CwD,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE,IAAI,CAACzD,gBAAgB,CAAC;;QAE9F,CAAC;QACDxB,KAAK,EAAGkF,GAAG,IAAI;UACbF,OAAO,CAAChF,KAAK,CAAC,6CAA6C,EAAEkF,GAAG,CAAC;UACjE,IAAI,CAACnC,YAAY,CAAC/C,KAAK,CACrB,sBAAsB,EACtB,0FAA0F,CAC3F;QACH;OACD,CAAC;;EAEN;EAEAiG,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC9E,WAAW,CAACE,OAAO,IAAI,CAAC,IAAI,CAAC6E,SAAS,EAAE,EAAE;MACjD,IAAI,CAACnD,YAAY,CAACoD,OAAO,CACvB,qBAAqB,EACrB,gEAAgE,CACjE;MACD;;IAGF;IACA,IAAI,CAAC,IAAI,CAACC,2BAA2B,EAAE,EAAE;MACvC;;IAGF,IAAI,CAACnD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACjD,KAAK,GAAG,IAAI;IACjB,IAAI,CAACG,cAAc,GAAG,IAAI;IAC1B,MAAMkG,SAAS,GAAG,IAAI,CAAClF,WAAW,CAACmF,KAAK;IAExC,MAAM9C,IAAI,GAAG6C,SAAS,CAAC7C,IAAI,CAAC,CAAC;IAC7B,MAAMC,UAAU,GAAG4C,SAAS,CAAC5C,UAAU,CAAC,CAAC;IACzC,MAAMC,QAAQ,GAAG2C,SAAS,CAAC3C,QAAQ;IAEnC,MAAM6C,WAAW,GAAQ;MACvB3F,KAAK,EAAEyF,SAAS,CAACzF,KAAK;MACtBa,WAAW,EAAE4E,SAAS,CAAC5E,WAAW;MAClC+B,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA,UAAU;MACtBC,QAAQ,EAAEA,QAAQ;MAClBC,IAAI,EAAE0C,SAAS,CAAC1C,IAAI;MACpBC,SAAS,EAAEyC,SAAS,CAACzC,SAAS;MAC9BC,QAAQ,EAAEwC,SAAS,CAACxC,QAAQ;MAC5BC,YAAY,EAAEuC,SAAS,CAACvC,YAAY,IAAI;KACzC;IAEDkB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEsB,WAAW,CAAC;IAC/DvB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEoB,SAAS,CAACxC,QAAQ,CAAC;IAC9DmB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,OAAOoB,SAAS,CAACxC,QAAQ,CAAC;IACjEmB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAChE,SAAS,CAAC;IAExD,IAAI,CAACwB,cAAc,CAAC+D,aAAa,CAACD,WAAW,CAAC,CAAC1B,SAAS,CAAC;MACvDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC7B,YAAY,GAAG,KAAK;QAEzB,IAAI,CAACF,YAAY,CAAC0D,OAAO,CACvB,eAAe,EACf,oCAAoC,CACrC;QAED;QACA,IAAI,CAACC,SAAS,EAAE;QAEhB;QACA,IAAI,CAAC7D,MAAM,CAAC8D,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC,CAAC;MACD3G,KAAK,EAAGkF,GAAG,IAAI;QACb,IAAI,CAACjC,YAAY,GAAG,KAAK;QACzB+B,OAAO,CAAChF,KAAK,CAAC,2CAA2C,EAAEkF,GAAG,CAAC;QAE/D,IAAIA,GAAG,CAAC0B,MAAM,KAAK,GAAG,EAAE;UACtB,IAAI,CAAC7D,YAAY,CAAC8D,YAAY,CAAC,mBAAmB,EAAE3B,GAAG,CAAC0B,MAAM,CAAC;SAChE,MAAM,IAAI1B,GAAG,CAAC0B,MAAM,KAAK,GAAG,EAAE;UAC7B,IAAI,CAAC7D,YAAY,CAAC/C,KAAK,CACrB,cAAc,EACd,iDAAiD,CAClD;SACF,MAAM;UACL,MAAM8G,YAAY,GAAG5B,GAAG,CAAClF,KAAK,EAAEC,OAAO,IAAI,0CAA0C;UACrF,IAAI,CAAC8C,YAAY,CAAC/C,KAAK,CACrB,oBAAoB,EACpB8G,YAAY,EACZ,IAAI,CACL;;MAEL;KACD,CAAC;EACJ;EAEAJ,SAASA,CAAA;IACP;IACA,IAAI,CAACvF,WAAW,CAAC4F,KAAK,CAAC;MACrBnG,KAAK,EAAE,EAAE;MACTa,WAAW,EAAE,EAAE;MACf+B,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE;KACf,CAAC;IAEF;IACA,IAAI,CAAC3C,WAAW,CAAC6F,cAAc,EAAE;IACjC,IAAI,CAAC7F,WAAW,CAAC8F,eAAe,EAAE;EACpC;EAGAC,SAASA,CAAA;IACP,IAAI,CAACrE,MAAM,CAAC8D,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEA;;;EAGAtC,wBAAwBA,CAAA;IACtB,IAAI,CAAClD,WAAW,CAACC,GAAG,CAAC,WAAW,CAAC,EAAE+F,YAAY,CAC5CC,IAAI,CACH7H,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBC,oBAAoB,EAAE,CAAC;KACxB,CACAqF,SAAS,CAACyB,KAAK,IAAG;MACjB,IAAIA,KAAK,IAAIA,KAAK,CAACe,IAAI,EAAE,KAAK,EAAE,EAAE;QAChC,IAAI,CAACC,wBAAwB,CAAChB,KAAK,CAACe,IAAI,EAAE,CAAC;OAC5C,MAAM;QACL,IAAI,CAAC9G,cAAc,GAAG,IAAI;;IAE9B,CAAC,CAAC;EACN;EAEA;;;;EAIA+G,wBAAwBA,CAAC1D,SAAiB;IACxC,IAAI,CAACA,SAAS,IAAIA,SAAS,CAACyD,IAAI,EAAE,KAAK,EAAE,EAAE;MACzC,IAAI,CAAC9G,cAAc,GAAG,IAAI;MAC1B;;IAGF,IAAI,CAAC8C,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC9C,cAAc,GAAG,IAAI;IAE1B;IACA,IAAI,CAACkC,cAAc,CAAC6E,wBAAwB,CAAC1D,SAAS,EAAE,IAAI,CAACT,gBAAgB,IAAIoE,SAAS,CAAC,CAAC1C,SAAS,CAAC;MACpGC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC1B,mBAAmB,GAAG,KAAK;QAEhC,IAAI0B,QAAQ,CAAC0B,OAAO,IAAI,CAAC1B,QAAQ,CAACyC,QAAQ,EAAE;UAC1C,IAAI,CAACjH,cAAc,GAAG,4CAA4CwE,QAAQ,CAAC0C,YAAY,EAAE7G,KAAK,GAAG;SAClG,MAAM;UACL,IAAI,CAACL,cAAc,GAAG,IAAI;;MAE9B,CAAC;MACDP,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACqD,mBAAmB,GAAG,KAAK;QAChC2B,OAAO,CAAChF,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAACO,cAAc,GAAG,wCAAwC;MAChE;KACD,CAAC;EACJ;EAEA;;;EAGA2F,SAASA,CAAA;IACP,OAAO,IAAI,CAAC/E,WAAW,CAACuG,KAAK,IAAI,CAAC,IAAI,CAACnH,cAAc,IAAI,CAAC,IAAI,CAAC8C,mBAAmB;EACpF;EAEA;;;EAGA+C,2BAA2BA,CAAA;IACzB,MAAMC,SAAS,GAAG,IAAI,CAAClF,WAAW,CAACmF,KAAK;IACxC,MAAMqB,WAAW,GAAGtB,SAAS,CAAC7C,IAAI;IAClC,MAAMgC,UAAU,GAAGa,SAAS,CAACxC,QAAQ;IAErC,IAAI,CAAC8D,WAAW,IAAI,CAACnC,UAAU,EAAE;MAC/B,OAAO,IAAI,CAAC,CAAC;;IAGf;IACA,IAAIhE,gBAAgB,GAAG,IAAI,CAACP,SAAS,CAAC6E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrF,GAAG,KAAK8E,UAAU,CAAC;IAErE,IAAI,CAAChE,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACd,GAAG,KAAK8E,UAAU,EAAE;MAC1FhE,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;;IAG1C,IAAI,CAACA,gBAAgB,EAAE;MACrBwD,OAAO,CAAC4C,IAAI,CAAC,yCAAyC,EAAEpC,UAAU,CAAC;MACnER,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAChE,SAAS,CAAC4G,GAAG,CAAC9B,CAAC,KAAK;QAAEZ,EAAE,EAAEY,CAAC,CAACrF,GAAG;QAAEE,KAAK,EAAEmF,CAAC,CAACnF;MAAK,CAAE,CAAC,CAAC,CAAC;MAClGoE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACzD,gBAAgB,CAAC;MAE3D;MACA,OAAO,IAAI;;IAGb;IACA,MAAMsG,cAAc,GAAG,IAAIrC,IAAI,CAACkC,WAAW,CAAC;IAC5C,MAAMI,iBAAiB,GAAG,IAAItC,IAAI,CAACjE,gBAAgB,CAACM,SAAS,CAAC;IAC9D,MAAMkG,eAAe,GAAG,IAAIvC,IAAI,CAACjE,gBAAgB,CAACO,OAAO,CAAC;IAE1D;IACA+F,cAAc,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnCF,iBAAiB,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtCD,eAAe,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEpC,IAAIH,cAAc,GAAGC,iBAAiB,IAAID,cAAc,GAAGE,eAAe,EAAE;MAC1E,IAAI,CAACjF,YAAY,CAAC/C,KAAK,CACrB,eAAe,EACf,qDAAqD+H,iBAAiB,CAACG,kBAAkB,CAAC,OAAO,CAAC,UAAUF,eAAe,CAACE,kBAAkB,CAAC,OAAO,CAAC,0BAA0B1G,gBAAgB,CAACZ,KAAK,IAAI,EAC3M,KAAK,CACN;MACD,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;;;uBAlVW0B,oBAAoB,EAAA7C,EAAA,CAAA0I,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5I,EAAA,CAAA0I,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA9I,EAAA,CAAA0I,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAhJ,EAAA,CAAA0I,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAlJ,EAAA,CAAA0I,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAApJ,EAAA,CAAA0I,iBAAA,CAAAS,EAAA,CAAAE,MAAA,GAAArJ,EAAA,CAAA0I,iBAAA,CAAAY,EAAA,CAAAC,eAAA,GAAAvJ,EAAA,CAAA0I,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAApB5G,oBAAoB;MAAA6G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBjChK,EAAA,CAAAC,cAAA,aAAmD;UAI7CD,EAAA,CAAAW,SAAA,WAAuD;UACvDX,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAAgC;UAC9BD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,cAA+G;UAA/ED,EAAA,CAAAkK,UAAA,sBAAAC,uDAAA;YAAA,OAAYF,GAAA,CAAAzD,QAAA,EAAU;UAAA,EAAC;UACrDxG,EAAA,CAAAoB,UAAA,IAAAgJ,mCAAA,iBAEM;UACNpK,EAAA,CAAAoB,UAAA,IAAAiJ,mCAAA,iBAEM;UAENrK,EAAA,CAAAC,cAAA,cAAoC;UAI9BD,EAAA,CAAAW,SAAA,aAA+C;UAC/CX,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAW,SAAA,iBAE6C;UAC7CX,EAAA,CAAAoB,UAAA,KAAAkJ,oCAAA,kBAIM;UACRtK,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAAsB;UAElBD,EAAA,CAAAW,SAAA,aAAsD;UACtDX,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAW,SAAA,oBAE6D;UAC/DX,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA4F;UAExFD,EAAA,CAAAW,SAAA,aAAwD;UACxDX,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAAmD;UAG7CD,EAAA,CAAAW,SAAA,aAAkD;UAClDX,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAW,SAAA,iBAC4K;UAC5KX,EAAA,CAAAoB,UAAA,KAAAmJ,oCAAA,kBAIM;UACRvK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAW,SAAA,aAA+C;UAC/CX,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAW,SAAA,iBAC+K;UAC/KX,EAAA,CAAAoB,UAAA,KAAAoJ,oCAAA,kBAIM;UACRxK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAW,SAAA,aAA6C;UAC7CX,EAAA,CAAAE,MAAA,wBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAW,SAAA,iBACyK;UACzKX,EAAA,CAAAoB,UAAA,KAAAqJ,oCAAA,kBAIM;UACRzK,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAAkG;UAE9FD,EAAA,CAAAW,SAAA,aAA0D;UAC1DX,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAAmD;UAG7CD,EAAA,CAAAW,SAAA,aAA0D;UAC1DX,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAW,SAAA,iBAEyD;UAC3DX,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAW,SAAA,aAA+C;UAC/CX,EAAA,CAAAE,MAAA,oBACA;UAAAF,EAAA,CAAAoB,UAAA,KAAAsJ,qCAAA,mBAMO;UACT1K,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAW,SAAA,iBAGiD;UAGjDX,EAAA,CAAAoB,UAAA,KAAAuJ,oCAAA,kBAGM;UAGN3K,EAAA,CAAAoB,UAAA,KAAAwJ,oCAAA,kBAIM;UACR5K,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAoB,UAAA,KAAAyJ,oCAAA,mBAmBM;UAGN7K,EAAA,CAAAoB,UAAA,KAAA0J,oCAAA,oBAoBM;UAGN9K,EAAA,CAAAC,cAAA,eAAkG;UAE9FD,EAAA,CAAAW,SAAA,aAAkD;UAClDX,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,iBAA+D;UAC7DD,EAAA,CAAAW,SAAA,aAAyD;UACzDX,EAAA,CAAAE,MAAA,4CACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,kBAC4M;UAC1MD,EAAA,CAAAoB,UAAA,KAAA2J,6CAAA,2BAIe;;UACjB/K,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,aAAyC;UACvCD,EAAA,CAAAW,SAAA,aAAuC;UACvCX,EAAA,CAAAE,MAAA,+EACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKRH,EAAA,CAAAC,cAAA,eAAgG;UACxED,EAAA,CAAAkK,UAAA,mBAAAc,uDAAA;YAAA,OAASf,GAAA,CAAAxC,SAAA,EAAW;UAAA,EAAC;UAEzCzH,EAAA,CAAAW,SAAA,aAAiC;UACjCX,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBACqQ;UACnQD,EAAA,CAAAoB,UAAA,KAAA6J,kCAAA,gBAA8E;UAC9EjL,EAAA,CAAAoB,UAAA,KAAA8J,kCAAA,gBAAgE;UAChElL,EAAA,CAAAoB,UAAA,KAAA+J,kCAAA,gBAA8D;UAC9DnL,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;UAnOTH,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAA4J,GAAA,CAAAxG,UAAA,6DACF;UAEEzD,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAA4J,GAAA,CAAAxG,UAAA,0HACF;UAGIzD,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAe,UAAA,cAAAkJ,GAAA,CAAAvI,WAAA,CAAyB;UACvB1B,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAe,UAAA,SAAAkJ,GAAA,CAAA1J,KAAA,CAAW;UAGXP,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAe,UAAA,SAAAkJ,GAAA,CAAAvJ,cAAA,CAAoB;UAchBV,EAAA,CAAAI,SAAA,GAA4E;UAA5EJ,EAAA,CAAAe,UAAA,WAAAqK,OAAA,GAAAnB,GAAA,CAAAvI,WAAA,CAAAC,GAAA,4BAAAyJ,OAAA,CAAAxJ,OAAA,OAAAwJ,OAAA,GAAAnB,GAAA,CAAAvI,WAAA,CAAAC,GAAA,4BAAAyJ,OAAA,CAAAvJ,OAAA,EAA4E;UAgCxE7B,EAAA,CAAAI,SAAA,IAA0E;UAA1EJ,EAAA,CAAAe,UAAA,WAAAsK,OAAA,GAAApB,GAAA,CAAAvI,WAAA,CAAAC,GAAA,2BAAA0J,OAAA,CAAAzJ,OAAA,OAAAyJ,OAAA,GAAApB,GAAA,CAAAvI,WAAA,CAAAC,GAAA,2BAAA0J,OAAA,CAAAxJ,OAAA,EAA0E;UAc1E7B,EAAA,CAAAI,SAAA,GAAsF;UAAtFJ,EAAA,CAAAe,UAAA,WAAAuK,OAAA,GAAArB,GAAA,CAAAvI,WAAA,CAAAC,GAAA,iCAAA2J,OAAA,CAAA1J,OAAA,OAAA0J,OAAA,GAAArB,GAAA,CAAAvI,WAAA,CAAAC,GAAA,iCAAA2J,OAAA,CAAAzJ,OAAA,EAAsF;UActF7B,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAe,UAAA,WAAAwK,OAAA,GAAAtB,GAAA,CAAAvI,WAAA,CAAAC,GAAA,+BAAA4J,OAAA,CAAA3J,OAAA,OAAA2J,OAAA,GAAAtB,GAAA,CAAAvI,WAAA,CAAAC,GAAA,+BAAA4J,OAAA,CAAA1J,OAAA,EAAkF;UA8B/E7B,EAAA,CAAAI,SAAA,IAAyB;UAAzBJ,EAAA,CAAAe,UAAA,SAAAkJ,GAAA,CAAArG,mBAAA,CAAyB;UAS3B5D,EAAA,CAAAI,SAAA,GAC6H;UAD7HJ,EAAA,CAAAwL,UAAA,qHAAAvB,GAAA,CAAAnJ,cAAA,sGAC6H;UAI9Hd,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAe,UAAA,SAAAkJ,GAAA,CAAAnJ,cAAA,CAAoB;UAMpBd,EAAA,CAAAI,SAAA,GAAyI;UAAzIJ,EAAA,CAAAe,UAAA,UAAAkJ,GAAA,CAAAnJ,cAAA,KAAAmJ,GAAA,CAAArG,mBAAA,MAAA6H,QAAA,GAAAxB,GAAA,CAAAvI,WAAA,CAAAC,GAAA,gCAAA8J,QAAA,CAAA5E,KAAA,OAAA4E,QAAA,GAAAxB,GAAA,CAAAvI,WAAA,CAAAC,GAAA,gCAAA8J,QAAA,CAAA5E,KAAA,CAAAe,IAAA,WAAyI;UAU/I5H,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAe,UAAA,UAAAkJ,GAAA,CAAAtG,iBAAA,CAAwB;UAsBxB3D,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAe,UAAA,SAAAkJ,GAAA,CAAAtG,iBAAA,IAAAsG,GAAA,CAAAlI,gBAAA,CAA2C;UAkC9B/B,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAe,UAAA,SAAAf,EAAA,CAAA0L,WAAA,SAAAzB,GAAA,CAAA3F,MAAA,EAAqB;UAoBlBtE,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAe,UAAA,cAAAkJ,GAAA,CAAAxD,SAAA,MAAAwD,GAAA,CAAAzG,YAAA,CAAyC;UAEhCxD,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAe,UAAA,UAAAkJ,GAAA,CAAAzG,YAAA,KAAAyG,GAAA,CAAArG,mBAAA,CAA2C;UAChC5D,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAe,UAAA,SAAAkJ,GAAA,CAAAzG,YAAA,CAAkB;UAC3BxD,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAe,UAAA,SAAAkJ,GAAA,CAAArG,mBAAA,CAAyB;UACxD5D,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAA4J,GAAA,CAAAzG,YAAA,yBAAAyG,GAAA,CAAArG,mBAAA,8DACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}