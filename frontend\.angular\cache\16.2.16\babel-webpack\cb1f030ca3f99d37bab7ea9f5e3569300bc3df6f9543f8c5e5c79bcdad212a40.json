{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MessagesRoutingModule } from './messages-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ApolloModule } from 'apollo-angular';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport { VoiceMessageModule } from 'src/app/components/voice-message/voice-message.module';\nexport let MessagesModule = class MessagesModule {};\nMessagesModule = __decorate([NgModule({\n  declarations: [MessageChatComponent, MessagesListComponent, UserListComponent, MessageLayoutComponent, CallInterfaceComponent],\n  imports: [CommonModule, MessagesRoutingModule, FormsModule, ReactiveFormsModule, ApolloModule, RouterModule, VoiceMessageModule],\n  providers: [UserStatusService, MessageService]\n})], MessagesModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "MessagesRoutingModule", "FormsModule", "ReactiveFormsModule", "ApolloModule", "MessageChatComponent", "MessagesListComponent", "UserListComponent", "MessageLayoutComponent", "UserStatusService", "MessageService", "VoiceMessageModule", "MessagesModule", "__decorate", "declarations", "CallInterfaceComponent", "imports", "providers"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\messages.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\nimport { MessagesRoutingModule } from './messages-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ApolloModule } from 'apollo-angular';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\n\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport { VoiceMessageModule } from 'src/app/components/voice-message/voice-message.module';\n\n@NgModule({\n  declarations: [\n    MessageChatComponent,\n    MessagesListComponent,\n    UserListComponent,\n    MessageLayoutComponent,\n    CallInterfaceComponent,\n  ],\n  imports: [\n    CommonModule,\n    MessagesRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    ApolloModule,\n    RouterModule,\n    VoiceMessageModule,\n  ],\n  providers: [UserStatusService, MessageService],\n})\nexport class MessagesModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,sBAAsB,QAAQ,2CAA2C;AAElF,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,kBAAkB,QAAQ,uDAAuD;AAqBnF,WAAMC,cAAc,GAApB,MAAMA,cAAc,GAAG;AAAjBA,cAAc,GAAAC,UAAA,EAnB1Bf,QAAQ,CAAC;EACRgB,YAAY,EAAE,CACZT,oBAAoB,EACpBC,qBAAqB,EACrBC,iBAAiB,EACjBC,sBAAsB,EACtBO,sBAAsB,CACvB;EACDC,OAAO,EAAE,CACPjB,YAAY,EACZE,qBAAqB,EACrBC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZJ,YAAY,EACZW,kBAAkB,CACnB;EACDM,SAAS,EAAE,CAACR,iBAAiB,EAAEC,cAAc;CAC9C,CAAC,C,EACWE,cAAc,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}