<!-- Interface d'appel WebRTC avec Tailwind CSS -->
<div
  class="fixed inset-0 z-50 transition-all duration-300 bg-gray-900 dark:bg-gray-800"
  [class.opacity-100]="isVisible"
  [class.opacity-0]="!isVisible"
  [class.pointer-events-none]="!isVisible"
  style="
    background: linear-gradient(135deg, #111827 0%, #1f2937 50%, #374151 100%);
  "
>
  <!-- === APPEL VIDÉO === -->
  <div *ngIf="callType === 'VIDEO'" class="relative w-full h-full">
    <!-- Vidéo distante (plein écran) -->
    <video
      #remoteVideo
      class="w-full h-full object-cover bg-gray-800 dark:bg-gray-900"
      [class.cursor-pointer]="remoteStream"
      autoplay
      playsinline
      (click)="toggleFullscreen()"
    ></video>

    <!-- Placeholder si pas de vidéo distante -->
    <div
      *ngIf="!remoteStream"
      class="absolute inset-0 flex flex-col items-center justify-center"
      style="
        background: linear-gradient(
          135deg,
          #1e40af 0%,
          #3b82f6 50%,
          #06b6d4 100%
        );
      "
    >
      <div class="relative mb-8">
        <img
          [src]="otherParticipant?.image || '/assets/images/default-avatar.png'"
          [alt]="otherParticipant?.username"
          class="w-32 h-32 rounded-full border-4 border-white/30 shadow-2xl"
        />
        <div
          class="absolute inset-0 rounded-full border-4 border-cyan-400 animate-pulse opacity-70"
        ></div>
      </div>
      <h2 class="text-3xl font-bold text-white mb-2 drop-shadow-lg">
        {{ otherParticipant?.username }}
      </h2>
      <p class="text-cyan-200 text-lg font-medium">{{ callStatus }}</p>

      <!-- Indicateur de connexion -->
      <div *ngIf="!isConnected" class="mt-4 flex items-center space-x-2">
        <div class="w-3 h-3 bg-cyan-400 rounded-full animate-bounce"></div>
        <div
          class="w-3 h-3 bg-cyan-400 rounded-full animate-bounce"
          style="animation-delay: 0.1s"
        ></div>
        <div
          class="w-3 h-3 bg-cyan-400 rounded-full animate-bounce"
          style="animation-delay: 0.2s"
        ></div>
      </div>
    </div>

    <!-- Vidéo locale (picture-in-picture) -->
    <div
      class="absolute top-4 right-4 w-36 h-28 bg-gray-800 dark:bg-gray-900 rounded-xl overflow-hidden border-2 border-white/30 shadow-2xl backdrop-blur-sm"
    >
      <video
        #localVideo
        *ngIf="isVideoEnabled"
        class="w-full h-full object-cover"
        autoplay
        playsinline
        muted
      ></video>

      <!-- Placeholder vidéo locale désactivée -->
      <div
        *ngIf="!isVideoEnabled"
        class="w-full h-full bg-gray-700 dark:bg-gray-800 flex items-center justify-center"
      >
        <i class="fas fa-video-slash text-white text-xl opacity-80"></i>
      </div>

      <!-- Indicateur de statut -->
      <div class="absolute bottom-1 right-1">
        <div
          *ngIf="isVideoEnabled"
          class="w-2 h-2 bg-green-400 rounded-full shadow-lg"
          title="Caméra activée"
        ></div>
        <div
          *ngIf="!isVideoEnabled"
          class="w-2 h-2 bg-red-400 rounded-full shadow-lg"
          title="Caméra désactivée"
        ></div>
      </div>
    </div>

    <!-- En-tête d'appel vidéo -->
    <div
      class="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/70 to-transparent p-4 backdrop-blur-sm"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="relative">
            <img
              [src]="
                otherParticipant?.image || '/assets/images/default-avatar.png'
              "
              [alt]="otherParticipant?.username"
              class="w-12 h-12 rounded-full border-2 border-white/40 shadow-lg"
            />
            <div
              *ngIf="isConnected"
              class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white shadow-sm"
              title="Connecté"
            ></div>
          </div>
          <div>
            <p class="text-white font-semibold text-lg drop-shadow-md">
              {{ otherParticipant?.username }}
            </p>
            <p class="text-cyan-200 text-sm font-medium">{{ callStatus }}</p>
            <p
              *ngIf="isConnected && callDuration > 0"
              class="text-green-300 text-sm font-mono"
            >
              {{ formatDuration(callDuration) }}
            </p>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <!-- Bouton plein écran -->
          <button
            (click)="toggleFullscreen()"
            class="p-3 rounded-full bg-white/20 hover:bg-white/30 transition-all duration-200 backdrop-blur-sm border border-white/20"
            title="Plein écran"
          >
            <i class="fas fa-expand text-white text-lg"></i>
          </button>

          <!-- Indicateur de qualité -->
          <div
            class="flex items-center space-x-1 bg-black/30 px-2 py-1 rounded-full"
          >
            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
            <span class="text-white text-xs font-medium">HD</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- === APPEL AUDIO === -->
  <div
    *ngIf="callType === 'AUDIO'"
    class="flex flex-col items-center justify-center h-full p-8 relative"
  >
    <!-- Effet de fond animé -->
    <div class="absolute inset-0 opacity-20">
      <div
        class="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-500 rounded-full blur-3xl animate-pulse"
      ></div>
      <div
        class="absolute bottom-1/4 right-1/4 w-40 h-40 bg-cyan-500 rounded-full blur-3xl animate-pulse"
        style="animation-delay: 1s"
      ></div>
      <div
        class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-purple-500 rounded-full blur-3xl animate-pulse"
        style="animation-delay: 2s"
      ></div>
    </div>

    <!-- Avatar principal -->
    <div class="relative mb-8 z-10">
      <div class="relative">
        <img
          [src]="otherParticipant?.image || '/assets/images/default-avatar.png'"
          [alt]="otherParticipant?.username"
          class="w-56 h-56 rounded-full border-4 border-white/30 shadow-2xl"
        />
        <!-- Cercle d'animation -->
        <div
          class="absolute inset-0 rounded-full border-4 border-cyan-400 opacity-70"
          [class.animate-pulse]="!isConnected"
          [class.animate-spin]="isConnected"
          style="animation-duration: 3s"
        ></div>
        <!-- Indicateur de connexion -->
        <div
          *ngIf="isConnected"
          class="absolute -bottom-2 -right-2 w-8 h-8 bg-green-400 rounded-full border-4 border-white shadow-lg flex items-center justify-center"
        >
          <i class="fas fa-check text-white text-sm"></i>
        </div>
      </div>

      <!-- Ondes sonores animées -->
      <div
        *ngIf="isConnected"
        class="absolute -inset-8 flex items-center justify-center"
      >
        <div
          class="absolute w-72 h-72 border-2 border-cyan-400/30 rounded-full animate-ping"
        ></div>
        <div
          class="absolute w-80 h-80 border-2 border-blue-400/20 rounded-full animate-ping"
          style="animation-delay: 0.5s"
        ></div>
        <div
          class="absolute w-88 h-88 border-2 border-purple-400/10 rounded-full animate-ping"
          style="animation-delay: 1s"
        ></div>
      </div>
    </div>

    <!-- Informations utilisateur -->
    <div class="text-center z-10">
      <h2 class="text-5xl font-bold text-white mb-4 drop-shadow-lg">
        {{ otherParticipant?.username }}
      </h2>
      <p class="text-cyan-200 text-xl mb-4 font-medium">{{ callStatus }}</p>
      <p
        *ngIf="isConnected && callDuration > 0"
        class="text-green-300 text-3xl font-mono font-bold drop-shadow-md"
      >
        {{ formatDuration(callDuration) }}
      </p>

      <!-- Indicateur de qualité audio -->
      <div
        *ngIf="isConnected"
        class="mt-6 flex items-center justify-center space-x-2"
      >
        <div
          class="flex items-center space-x-1 bg-black/30 px-3 py-2 rounded-full backdrop-blur-sm"
        >
          <i class="fas fa-volume-up text-green-400"></i>
          <span class="text-white text-sm font-medium">Audio HD</span>
        </div>
      </div>
    </div>
  </div>

  <!-- === CONTRÔLES D'APPEL === -->
  <div
    class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-8 backdrop-blur-sm"
  >
    <div class="flex items-center justify-center space-x-8">
      <!-- Bouton Micro -->
      <div class="relative group">
        <button
          (click)="toggleMute()"
          class="w-20 h-20 rounded-full flex items-center justify-center transition-all duration-300 shadow-2xl border-2 transform hover:scale-110"
          [class]="
            isMuted
              ? 'bg-red-500 hover:bg-red-600 border-red-400 shadow-red-500/50'
              : 'bg-gray-700/80 hover:bg-gray-600/80 border-gray-500 shadow-gray-700/50'
          "
          [title]="isMuted ? 'Activer le micro' : 'Couper le micro'"
        >
          <i
            class="fas text-white text-2xl"
            [class]="isMuted ? 'fa-microphone-slash' : 'fa-microphone'"
          ></i>
        </button>
        <!-- Tooltip -->
        <div
          class="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity"
        >
          {{ isMuted ? "Activer micro" : "Couper micro" }}
        </div>
      </div>

      <!-- Bouton Vidéo (seulement pour appels vidéo) -->
      <div *ngIf="callType === 'VIDEO'" class="relative group">
        <button
          (click)="toggleVideo()"
          class="w-20 h-20 rounded-full flex items-center justify-center transition-all duration-300 shadow-2xl border-2 transform hover:scale-110"
          [class]="
            !isVideoEnabled
              ? 'bg-red-500 hover:bg-red-600 border-red-400 shadow-red-500/50'
              : 'bg-gray-700/80 hover:bg-gray-600/80 border-gray-500 shadow-gray-700/50'
          "
          [title]="
            isVideoEnabled ? 'Désactiver la caméra' : 'Activer la caméra'
          "
        >
          <i
            class="fas text-white text-2xl"
            [class]="isVideoEnabled ? 'fa-video' : 'fa-video-slash'"
          ></i>
        </button>
        <!-- Tooltip -->
        <div
          class="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity"
        >
          {{ isVideoEnabled ? "Désactiver caméra" : "Activer caméra" }}
        </div>
      </div>

      <!-- Bouton Haut-parleur -->
      <div class="relative group">
        <button
          (click)="toggleSpeaker()"
          class="w-20 h-20 rounded-full flex items-center justify-center transition-all duration-300 shadow-2xl border-2 transform hover:scale-110"
          [class]="
            isSpeakerOn
              ? 'bg-blue-500 hover:bg-blue-600 border-blue-400 shadow-blue-500/50'
              : 'bg-gray-700/80 hover:bg-gray-600/80 border-gray-500 shadow-gray-700/50'
          "
          [title]="
            isSpeakerOn ? 'Désactiver haut-parleur' : 'Activer haut-parleur'
          "
        >
          <i
            class="fas text-white text-2xl"
            [class]="isSpeakerOn ? 'fa-volume-up' : 'fa-volume-down'"
          ></i>
        </button>
        <!-- Tooltip -->
        <div
          class="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity"
        >
          {{ isSpeakerOn ? "Désactiver haut-parleur" : "Activer haut-parleur" }}
        </div>
      </div>

      <!-- Bouton Raccrocher -->
      <div class="relative group">
        <button
          (click)="endCall()"
          class="w-24 h-24 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all duration-300 shadow-2xl border-2 border-red-400 shadow-red-500/50 transform hover:scale-110"
          title="Raccrocher"
        >
          <i class="fas fa-phone-slash text-white text-3xl"></i>
        </button>
        <!-- Tooltip -->
        <div
          class="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity"
        >
          Raccrocher
        </div>
      </div>
    </div>

    <!-- Indicateurs de statut -->
    <div class="flex items-center justify-center mt-4 space-x-4">
      <div
        *ngIf="isMuted"
        class="flex items-center space-x-1 bg-red-500/20 px-2 py-1 rounded-full"
      >
        <i class="fas fa-microphone-slash text-red-400 text-xs"></i>
        <span class="text-red-300 text-xs">Micro coupé</span>
      </div>
      <div
        *ngIf="!isVideoEnabled && callType === 'VIDEO'"
        class="flex items-center space-x-1 bg-red-500/20 px-2 py-1 rounded-full"
      >
        <i class="fas fa-video-slash text-red-400 text-xs"></i>
        <span class="text-red-300 text-xs">Caméra désactivée</span>
      </div>
      <div
        *ngIf="isSpeakerOn"
        class="flex items-center space-x-1 bg-blue-500/20 px-2 py-1 rounded-full"
      >
        <i class="fas fa-volume-up text-blue-400 text-xs"></i>
        <span class="text-blue-300 text-xs">Haut-parleur</span>
      </div>
    </div>
  </div>

  <!-- === MODAL APPEL ENTRANT === -->
  <div
    *ngIf="isIncoming && !isConnected"
    class="absolute inset-0 bg-black/80 flex items-center justify-center p-8"
  >
    <div
      class="bg-gray-800 rounded-2xl p-8 max-w-sm w-full text-center shadow-2xl border border-gray-700"
    >
      <!-- Avatar et info -->
      <div class="mb-8">
        <div class="relative inline-block mb-4">
          <img
            [src]="
              otherParticipant?.image || '/assets/images/default-avatar.png'
            "
            [alt]="otherParticipant?.username"
            class="w-24 h-24 rounded-full border-4 border-white/20"
          />
          <div
            class="absolute inset-0 rounded-full border-4 border-cyan-400 animate-pulse"
          ></div>
        </div>

        <h2 class="text-2xl font-bold text-white mb-2">
          {{ otherParticipant?.username }}
        </h2>
        <p class="text-cyan-300 text-lg">{{ getCallTypeLabel() }} entrant...</p>
      </div>

      <!-- Boutons d'action -->
      <div class="flex items-center justify-center space-x-8">
        <button
          (click)="rejectCall()"
          class="w-16 h-16 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all duration-200 shadow-lg"
          title="Rejeter l'appel"
        >
          <i class="material-icons text-white text-2xl">call_end</i>
        </button>

        <button
          (click)="acceptCall()"
          class="w-16 h-16 rounded-full bg-green-500 hover:bg-green-600 flex items-center justify-center transition-all duration-200 shadow-lg"
          title="Accepter l'appel"
        >
          <i class="material-icons text-white text-2xl">{{
            getCallTypeIcon()
          }}</i>
        </button>
      </div>
    </div>
  </div>

  <!-- Audio éléments (cachés) -->
  <audio #remoteAudio autoplay *ngIf="callType === 'AUDIO'"></audio>
</div>

<!-- Overlay de chargement -->
<div
  *ngIf="isVisible && !isConnected && !isIncoming"
  class="fixed inset-0 z-60 bg-black/50 flex items-center justify-center"
>
  <div
    class="bg-gray-800 rounded-xl p-8 text-center shadow-2xl border border-gray-700"
  >
    <div
      class="w-12 h-12 border-4 border-cyan-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"
    ></div>
    <p class="text-white text-lg">{{ callStatus }}</p>
  </div>
</div>
