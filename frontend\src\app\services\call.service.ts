import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { BehaviorSubject, Observable, throwError, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import {
  Call,
  CallType,
  CallStatus,
  IncomingCall,
  CallSuccess,
} from '../models/message.model';
import {
  INITIATE_CALL_MUTATION,
  ACCEPT_CALL_MUTATION,
  REJECT_CALL_MUTATION,
  END_CALL_MUTATION,
  TOGGLE_CALL_MEDIA_MUTATION,
  INCOMING_CALL_SUBSCRIPTION,
} from '../graphql/message.graphql';
import { LoggerService } from './logger.service';

@Injectable({
  providedIn: 'root',
})
export class CallService implements OnDestroy {
  // État des appels
  private activeCall = new BehaviorSubject<Call | null>(null);
  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);

  // Observables publics
  public activeCall$ = this.activeCall.asObservable();
  public incomingCall$ = this.incomingCall.asObservable();

  // Propriétés pour la gestion des sons
  private sounds: { [key: string]: HTMLAudioElement } = {};
  private isPlaying: { [key: string]: boolean } = {};
  private muted = false;

  constructor(private apollo: Apollo, private logger: LoggerService) {
    this.preloadSounds();
    this.subscribeToIncomingCalls();
  }

  /**
   * Précharge les sons utilisés dans l'application
   */
  private preloadSounds(): void {
    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');
    this.loadSound('call-end', 'assets/sounds/call-end.mp3');
    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');
  }

  /**
   * Charge un fichier audio
   */
  private loadSound(name: string, path: string): void {
    try {
      const audio = new Audio(path);
      audio.load();
      this.sounds[name] = audio;
      this.isPlaying[name] = false;

      audio.addEventListener('ended', () => {
        this.isPlaying[name] = false;
      });
    } catch (error) {
      console.error(`Error loading sound ${name}:`, error);
    }
  }

  /**
   * Joue un son
   */
  private play(name: string, loop: boolean = false): void {
    if (this.muted) return;

    try {
      const sound = this.sounds[name];
      if (!sound) return;

      sound.loop = loop;
      if (!this.isPlaying[name]) {
        sound.currentTime = 0;
        sound.play().catch(console.error);
        this.isPlaying[name] = true;
      }
    } catch (error) {
      console.error(`Error playing sound ${name}:`, error);
    }
  }

  /**
   * Arrête un son
   */
  private stop(name: string): void {
    try {
      const sound = this.sounds[name];
      if (!sound) return;

      if (this.isPlaying[name]) {
        sound.pause();
        sound.currentTime = 0;
        this.isPlaying[name] = false;
      }
    } catch (error) {
      console.error(`Error stopping sound ${name}:`, error);
    }
  }

  /**
   * S'abonne aux appels entrants
   */
  private subscribeToIncomingCalls(): void {
    console.log('🔔 [CallService] Setting up incoming call subscription...');

    this.apollo
      .subscribe<{ incomingCall: IncomingCall }>({
        query: INCOMING_CALL_SUBSCRIPTION,
      })
      .subscribe({
        next: ({ data }) => {
          if (data?.incomingCall) {
            console.log('📞 [CallService] Incoming call received:', {
              callId: data.incomingCall.id,
              callType: data.incomingCall.type,
              caller: data.incomingCall.caller?.username,
              conversationId: data.incomingCall.conversationId,
            });
            this.handleIncomingCall(data.incomingCall);
          }
        },
        error: (error) => {
          console.error(
            '❌ [CallService] Error in incoming call subscription:',
            error
          );
        },
      });
  }

  /**
   * Gère un appel entrant
   */
  private handleIncomingCall(call: IncomingCall): void {
    console.log('🔔 [CallService] Handling incoming call:', {
      callId: call.id,
      callType: call.type,
      caller: call.caller?.username,
      conversationId: call.conversationId,
    });

    this.incomingCall.next(call);
    this.play('ringtone', true);

    console.log(
      '🔊 [CallService] Ringtone started, call notification sent to UI'
    );
  }

  /**
   * Initie un appel WebRTC
   */
  initiateCall(
    recipientId: string,
    callType: CallType,
    conversationId?: string
  ): Observable<Call> {
    console.log('🔄 [CallService] Initiating call:', {
      recipientId,
      callType,
      conversationId,
    });

    if (!recipientId) {
      const error = new Error('Recipient ID is required');
      console.error('❌ [CallService] initiateCall error:', error);
      return throwError(() => error);
    }

    // Générer un ID unique pour l'appel
    const callId = `call_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Pour l'instant, utiliser une offre WebRTC factice
    const offer = JSON.stringify({
      type: 'offer',
      sdp: 'v=0\r\no=- 0 0 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n',
    });

    const variables = {
      recipientId,
      callType: callType,
      callId,
      offer,
      conversationId,
    };

    console.log('📤 [CallService] Sending initiate call mutation:', variables);

    return this.apollo
      .mutate<{ initiateCall: Call }>({
        mutation: INITIATE_CALL_MUTATION,
        variables,
      })
      .pipe(
        map((result) => {
          console.log('✅ [CallService] Call initiated successfully:', result);

          if (!result.data?.initiateCall) {
            throw new Error('No call data received from server');
          }

          const call = result.data.initiateCall;
          console.log('📞 [CallService] Call details:', {
            id: call.id,
            type: call.type,
            status: call.status,
            caller: call.caller?.username,
            recipient: call.recipient?.username,
          });

          // Mettre à jour l'état local
          this.activeCall.next(call);

          return call;
        }),
        catchError((error) => {
          console.error('❌ [CallService] initiateCall error:', error);
          this.logger.error('Error initiating call:', error);

          let errorMessage = "Erreur lors de l'initiation de l'appel";
          if (error.networkError) {
            errorMessage = 'Erreur de connexion réseau';
          } else if (error.graphQLErrors?.length > 0) {
            errorMessage = error.graphQLErrors[0].message || errorMessage;
          }

          return throwError(() => new Error(errorMessage));
        })
      );
  }

  /**
   * Accepte un appel entrant
   */
  acceptCall(incomingCall: IncomingCall): Observable<Call> {
    console.log('🔄 [CallService] Accepting call:', incomingCall.id);

    // Générer une réponse WebRTC factice
    const answer = JSON.stringify({
      type: 'answer',
      sdp: 'v=0\r\no=- 0 0 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n',
    });

    return this.apollo
      .mutate<{ acceptCall: Call }>({
        mutation: ACCEPT_CALL_MUTATION,
        variables: {
          callId: incomingCall.id,
          answer,
        },
      })
      .pipe(
        map((result) => {
          console.log('✅ [CallService] Call accepted successfully:', result);

          if (!result.data?.acceptCall) {
            throw new Error('No call data received from server');
          }

          const call = result.data.acceptCall;

          // Mettre à jour l'état local
          this.activeCall.next(call);
          this.incomingCall.next(null); // Supprimer l'appel entrant

          // Arrêter la sonnerie
          this.stop('ringtone');
          this.play('call-connected');

          return call;
        }),
        catchError((error) => {
          console.error('❌ [CallService] acceptCall error:', error);
          this.logger.error('Error accepting call:', error);
          return throwError(
            () => new Error("Erreur lors de l'acceptation de l'appel")
          );
        })
      );
  }

  /**
   * Rejette un appel entrant
   */
  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {
    console.log('🔄 [CallService] Rejecting call:', callId, reason);

    return this.apollo
      .mutate<{ rejectCall: CallSuccess }>({
        mutation: REJECT_CALL_MUTATION,
        variables: {
          callId,
          reason: reason || 'User rejected',
        },
      })
      .pipe(
        map((result) => {
          console.log('✅ [CallService] Call rejected successfully:', result);

          if (!result.data?.rejectCall) {
            throw new Error('No response received from server');
          }

          // Mettre à jour l'état local
          this.incomingCall.next(null);
          this.activeCall.next(null);

          // Arrêter la sonnerie
          this.stop('ringtone');

          return result.data.rejectCall;
        }),
        catchError((error) => {
          console.error('❌ [CallService] rejectCall error:', error);
          this.logger.error('Error rejecting call:', error);
          return throwError(() => new Error("Erreur lors du rejet de l'appel"));
        })
      );
  }

  /**
   * Termine un appel en cours
   */
  endCall(callId: string): Observable<CallSuccess> {
    console.log('🔄 [CallService] Ending call:', callId);

    return this.apollo
      .mutate<{ endCall: CallSuccess }>({
        mutation: END_CALL_MUTATION,
        variables: {
          callId,
          feedback: null, // Pas de feedback pour l'instant
        },
      })
      .pipe(
        map((result) => {
          console.log('✅ [CallService] Call ended successfully:', result);

          if (!result.data?.endCall) {
            throw new Error('No response received from server');
          }

          // Mettre à jour l'état local
          this.activeCall.next(null);
          this.incomingCall.next(null);

          // Arrêter tous les sons
          this.stopAllSounds();
          this.play('call-end');

          return result.data.endCall;
        }),
        catchError((error) => {
          console.error('❌ [CallService] endCall error:', error);
          this.logger.error('Error ending call:', error);
          return throwError(
            () => new Error("Erreur lors de la fin de l'appel")
          );
        })
      );
  }

  /**
   * Bascule l'état des médias (audio/vidéo) pendant un appel
   */
  toggleMedia(
    callId: string,
    enableVideo?: boolean,
    enableAudio?: boolean
  ): Observable<CallSuccess> {
    console.log('🔄 [CallService] Toggling media:', {
      callId,
      enableVideo,
      enableAudio,
    });

    return this.apollo
      .mutate<{ toggleCallMedia: CallSuccess }>({
        mutation: TOGGLE_CALL_MEDIA_MUTATION,
        variables: {
          callId,
          video: enableVideo,
          audio: enableAudio,
        },
      })
      .pipe(
        map((result) => {
          console.log('✅ [CallService] Media toggled successfully:', result);

          if (!result.data?.toggleCallMedia) {
            throw new Error('No response received from server');
          }

          return result.data.toggleCallMedia;
        }),
        catchError((error) => {
          console.error('❌ [CallService] toggleMedia error:', error);
          this.logger.error('Error toggling media:', error);
          return throwError(
            () => new Error('Erreur lors du changement des médias')
          );
        })
      );
  }

  /**
   * Arrête tous les sons
   */
  private stopAllSounds(): void {
    Object.keys(this.sounds).forEach((name) => {
      this.stop(name);
    });
  }

  ngOnDestroy(): void {
    this.stopAllSounds();
  }
}
