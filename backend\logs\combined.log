{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 23:47:18"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 23:47:18"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 23:50:15"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 23:50:15"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 23:52:53"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 23:52:53"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 23:53:17"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 23:53:17"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 23:54:54"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 23:54:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetAllUsers($search: String, $page: Int, $limit: Int, $sortBy: String, $sortOrder: String, $isOnline: Boolean) {\n  getAllUsers(\n    search: $search\n    page: $page\n    limit: $limit\n    sortBy: $sortBy\n    sortOrder: $sortOrder\n    isOnline: $isOnline\n  ) {\n    users {\n      id\n      username\n      email\n      image\n      isOnline\n      lastActive\n    }\n    totalCount\n    totalPages\n    currentPage\n    hasNextPage\n    hasPreviousPage\n  }\n}","timestamp":"2025-05-30 23:55:24","variables":{"isOnline":null,"limit":10,"page":1,"search":"","sortBy":"username","sortOrder":"asc"}}
{"level":"http","message":"GraphQL anonymous completed in 127ms","timestamp":"2025-05-30 23:55:24"}
{"level":"http","message":"POST / 200 - 145ms","timestamp":"2025-05-30 23:55:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetAllUsers($search: String, $page: Int, $limit: Int, $sortBy: String, $sortOrder: String, $isOnline: Boolean) {\n  getAllUsers(\n    search: $search\n    page: $page\n    limit: $limit\n    sortBy: $sortBy\n    sortOrder: $sortOrder\n    isOnline: $isOnline\n  ) {\n    users {\n      id\n      username\n      email\n      image\n      isOnline\n      lastActive\n    }\n    totalCount\n    totalPages\n    currentPage\n    hasNextPage\n    hasPreviousPage\n  }\n}","timestamp":"2025-05-30 23:55:38","variables":{"isOnline":null,"limit":10,"page":1,"search":"","sortBy":"username","sortOrder":"asc"}}
{"level":"http","message":"GraphQL anonymous completed in 77ms","timestamp":"2025-05-30 23:55:38"}
{"level":"http","message":"POST / 200 - 83ms","timestamp":"2025-05-30 23:55:38"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 23:55:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:44"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:44"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 23:55:54"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:54"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:54"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:54"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:55:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetAllUsers($search: String, $page: Int, $limit: Int, $sortBy: String, $sortOrder: String, $isOnline: Boolean) {\n  getAllUsers(\n    search: $search\n    page: $page\n    limit: $limit\n    sortBy: $sortBy\n    sortOrder: $sortOrder\n    isOnline: $isOnline\n  ) {\n    users {\n      id\n      username\n      email\n      image\n      isOnline\n      lastActive\n    }\n    totalCount\n    totalPages\n    currentPage\n    hasNextPage\n    hasPreviousPage\n  }\n}","timestamp":"2025-05-30 23:56:08","variables":{"isOnline":null,"limit":10,"page":1,"search":"","sortBy":"username","sortOrder":"asc"}}
{"level":"http","message":"GraphQL anonymous completed in 75ms","timestamp":"2025-05-30 23:56:08"}
{"level":"http","message":"POST / 200 - 77ms","timestamp":"2025-05-30 23:56:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetAllUsers($search: String, $page: Int, $limit: Int, $sortBy: String, $sortOrder: String, $isOnline: Boolean) {\n  getAllUsers(\n    search: $search\n    page: $page\n    limit: $limit\n    sortBy: $sortBy\n    sortOrder: $sortOrder\n    isOnline: $isOnline\n  ) {\n    users {\n      id\n      username\n      email\n      image\n      isOnline\n      lastActive\n    }\n    totalCount\n    totalPages\n    currentPage\n    hasNextPage\n    hasPreviousPage\n  }\n}","timestamp":"2025-05-30 23:57:28","variables":{"isOnline":null,"limit":10,"page":1,"search":"","sortBy":"username","sortOrder":"asc"}}
{"level":"http","message":"GraphQL anonymous completed in 69ms","timestamp":"2025-05-30 23:57:28"}
{"level":"http","message":"POST / 200 - 72ms","timestamp":"2025-05-30 23:57:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetAllUsers($search: String, $page: Int, $limit: Int, $sortBy: String, $sortOrder: String, $isOnline: Boolean) {\n  getAllUsers(\n    search: $search\n    page: $page\n    limit: $limit\n    sortBy: $sortBy\n    sortOrder: $sortOrder\n    isOnline: $isOnline\n  ) {\n    users {\n      id\n      username\n      email\n      image\n      isOnline\n      lastActive\n    }\n    totalCount\n    totalPages\n    currentPage\n    hasNextPage\n    hasPreviousPage\n  }\n}","timestamp":"2025-05-30 23:58:28","variables":{"isOnline":null,"limit":10,"page":1,"search":"","sortBy":"username","sortOrder":"asc"}}
{"level":"http","message":"GraphQL anonymous completed in 74ms","timestamp":"2025-05-30 23:58:28"}
{"level":"http","message":"POST / 200 - 81ms","timestamp":"2025-05-30 23:58:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetAllUsers($search: String, $page: Int, $limit: Int, $sortBy: String, $sortOrder: String, $isOnline: Boolean) {\n  getAllUsers(\n    search: $search\n    page: $page\n    limit: $limit\n    sortBy: $sortBy\n    sortOrder: $sortOrder\n    isOnline: $isOnline\n  ) {\n    users {\n      id\n      username\n      email\n      image\n      isOnline\n      lastActive\n    }\n    totalCount\n    totalPages\n    currentPage\n    hasNextPage\n    hasPreviousPage\n  }\n}","timestamp":"2025-05-30 23:58:39","variables":{"isOnline":null,"limit":10,"page":1,"search":"","sortBy":"username","sortOrder":"asc"}}
{"level":"http","message":"GraphQL anonymous completed in 65ms","timestamp":"2025-05-30 23:58:40"}
{"level":"http","message":"POST / 200 - 69ms","timestamp":"2025-05-30 23:58:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 23:58:52","variables":{"userId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 336ms","timestamp":"2025-05-30 23:58:53"}
{"level":"http","message":"POST / 200 - 340ms","timestamp":"2025-05-30 23:58:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 23:58:53","variables":{"userId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 307ms","timestamp":"2025-05-30 23:58:53"}
{"level":"http","message":"POST / 200 - 312ms","timestamp":"2025-05-30 23:58:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 23:58:53","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 23:58:53"}
{"level":"http","message":"GET /getall 200 - 81ms","timestamp":"2025-05-30 23:58:53"}
{"level":"http","message":"GET /getall 304 - 46ms","timestamp":"2025-05-30 23:58:53"}
{"level":"http","message":"GET /getall 304 - 61ms","timestamp":"2025-05-30 23:58:53"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 37","timestamp":"2025-05-30 23:58:53"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 23:58:53"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 23:58:54"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 23:58:54"}
{"level":"http","message":"GraphQL anonymous completed in 624ms","timestamp":"2025-05-30 23:58:54"}
{"level":"http","message":"POST / 200 - 631ms","timestamp":"2025-05-30 23:58:54"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:58:54"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:58:54"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:58:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-30 23:58:56","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-30 23:58:56"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 23:58:57"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 23:58:57"}
{"level":"http","message":"GraphQL anonymous completed in 751ms","timestamp":"2025-05-30 23:58:57"}
{"level":"http","message":"POST / 200 - 756ms","timestamp":"2025-05-30 23:58:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-30 23:59:20","variables":{"content":"kol chay jawou bahi","conversationId":"6839c3a5b3d00b25cf26afd1","receiverId":"6839c1fcb3d00b25cf26afa7","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, type=TEXT, hasMetadata=false","timestamp":"2025-05-30 23:59:20"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a3848046ce129783db8df","timestamp":"2025-05-30 23:59:20"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a3848046ce129783db8df","timestamp":"2025-05-30 23:59:20"}
{"level":"http","message":"GraphQL anonymous completed in 571ms","timestamp":"2025-05-30 23:59:20"}
{"level":"http","message":"POST / 200 - 575ms","timestamp":"2025-05-30 23:59:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-30 23:59:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 23:59:20","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 23:59:20"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 38","timestamp":"2025-05-30 23:59:21"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 23:59:21"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 23:59:21"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 23:59:21"}
{"level":"http","message":"GraphQL anonymous completed in 619ms","timestamp":"2025-05-30 23:59:21"}
{"level":"http","message":"POST / 200 - 624ms","timestamp":"2025-05-30 23:59:21"}
{"level":"http","message":"GET / 200 - 47ms","timestamp":"2025-05-30 23:59:37"}
{"level":"http","message":"GET /getall 304 - 47ms","timestamp":"2025-05-30 23:59:37"}
{"level":"http","message":"GET / 304 - 50ms","timestamp":"2025-05-30 23:59:37"}
{"level":"http","message":"GET /getall 304 - 46ms","timestamp":"2025-05-30 23:59:37"}
{"level":"http","message":"GET / 304 - 43ms","timestamp":"2025-05-30 23:59:37"}
{"level":"http","message":"GET /getall 304 - 50ms","timestamp":"2025-05-30 23:59:37"}
{"level":"http","message":"GET /getall 304 - 56ms","timestamp":"2025-05-30 23:59:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 52ms","timestamp":"2025-05-30 23:59:41"}
{"level":"http","message":"GET /getall 304 - 44ms","timestamp":"2025-05-30 23:59:41"}
{"level":"http","message":"GET /getall 304 - 69ms","timestamp":"2025-05-30 23:59:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 23:59:46","variables":{}}
{"level":"http","message":"GET /getall 304 - 68ms","timestamp":"2025-05-30 23:59:46"}
{"level":"http","message":"GET /getall 304 - 58ms","timestamp":"2025-05-30 23:59:46"}
{"level":"http","message":"GET /getall 304 - 79ms","timestamp":"2025-05-30 23:59:46"}
{"level":"info","message":"[MessageService] Found 2 conversations","timestamp":"2025-05-30 23:59:46"}
{"level":"http","message":"GraphQL anonymous completed in 514ms","timestamp":"2025-05-30 23:59:46"}
{"level":"http","message":"POST / 200 - 516ms","timestamp":"2025-05-30 23:59:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 23:59:47","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-30 23:59:47"}
{"level":"http","message":"GET /getall 304 - 52ms","timestamp":"2025-05-30 23:59:47"}
{"level":"http","message":"GET /getall 304 - 60ms","timestamp":"2025-05-30 23:59:47"}
{"level":"http","message":"GET /getall 304 - 66ms","timestamp":"2025-05-30 23:59:48"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 38","timestamp":"2025-05-30 23:59:48"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-30 23:59:48"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-30 23:59:48"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 23:59:48"}
{"level":"http","message":"GraphQL anonymous completed in 639ms","timestamp":"2025-05-30 23:59:48"}
{"level":"http","message":"POST / 200 - 643ms","timestamp":"2025-05-30 23:59:48"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:02:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-31 00:03:14","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-31 00:03:14"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:03:15"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:03:15"}
{"level":"http","message":"GraphQL anonymous completed in 494ms","timestamp":"2025-05-31 00:03:15"}
{"level":"http","message":"POST / 200 - 511ms","timestamp":"2025-05-31 00:03:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:03:15","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /getall 304 - 57ms","timestamp":"2025-05-31 00:03:15"}
{"level":"http","message":"GraphQL anonymous completed in 120ms","timestamp":"2025-05-31 00:03:15"}
{"level":"http","message":"POST / 200 - 129ms","timestamp":"2025-05-31 00:03:15"}
{"level":"http","message":"GET /getall 304 - 81ms","timestamp":"2025-05-31 00:03:15"}
{"level":"http","message":"GET /getall 304 - 57ms","timestamp":"2025-05-31 00:03:16"}
{"level":"http","message":"GET / 304 - 853ms","timestamp":"2025-05-31 00:03:29"}
{"level":"http","message":"GET / 304 - 853ms","timestamp":"2025-05-31 00:03:29"}
{"level":"http","message":"GET /getall 304 - 860ms","timestamp":"2025-05-31 00:03:29"}
{"level":"http","message":"GET / 304 - 43ms","timestamp":"2025-05-31 00:03:29"}
{"level":"http","message":"GET /getall 304 - 51ms","timestamp":"2025-05-31 00:03:29"}
{"level":"http","message":"GET /getall 304 - 63ms","timestamp":"2025-05-31 00:03:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-31 00:03:34","variables":{}}
{"level":"http","message":"GET /getall 304 - 200ms","timestamp":"2025-05-31 00:03:34"}
{"level":"info","message":"[MessageService] Found 2 conversations","timestamp":"2025-05-31 00:03:34"}
{"level":"http","message":"GraphQL anonymous completed in 439ms","timestamp":"2025-05-31 00:03:34"}
{"level":"http","message":"POST / 200 - 443ms","timestamp":"2025-05-31 00:03:34"}
{"level":"http","message":"GET /getall 304 - 476ms","timestamp":"2025-05-31 00:03:35"}
{"level":"http","message":"GET /getall 304 - 55ms","timestamp":"2025-05-31 00:03:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:03:36","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:03:36"}
{"level":"http","message":"GET /getall 304 - 60ms","timestamp":"2025-05-31 00:03:36"}
{"level":"http","message":"GET /getall 304 - 68ms","timestamp":"2025-05-31 00:03:36"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 38","timestamp":"2025-05-31 00:03:36"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:03:36"}
{"level":"http","message":"GET /getall 304 - 55ms","timestamp":"2025-05-31 00:03:36"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:03:37"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:03:37"}
{"level":"http","message":"GraphQL anonymous completed in 437ms","timestamp":"2025-05-31 00:03:37"}
{"level":"http","message":"POST / 200 - 443ms","timestamp":"2025-05-31 00:03:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:04:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 3ms","timestamp":"2025-05-31 00:04:06"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 00:04:06"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-31 00:04:06"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-31 00:04:08"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-31 00:04:12"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-31 00:04:18"}
{"level":"http","message":"POST /login 200 - 349ms","timestamp":"2025-05-31 00:04:22"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:04:28"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:04:28"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:04:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:04:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:04:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:04:29"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:04:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-31 00:04:29","variables":{}}
{"level":"info","message":"[MessageService] Found 3 conversations","timestamp":"2025-05-31 00:04:29"}
{"level":"http","message":"GraphQL anonymous completed in 910ms","timestamp":"2025-05-31 00:04:30"}
{"level":"http","message":"POST / 200 - 916ms","timestamp":"2025-05-31 00:04:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:04:33","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:04:33"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 38","timestamp":"2025-05-31 00:04:33"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:04:33"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:04:34"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:04:34"}
{"level":"http","message":"GraphQL anonymous completed in 647ms","timestamp":"2025-05-31 00:04:34"}
{"level":"http","message":"POST / 200 - 653ms","timestamp":"2025-05-31 00:04:34"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:04:34"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:04:34"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:04:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:04:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:04:36"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:04:36"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:04:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:05:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:05:06"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:05:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:05:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:05:36"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:05:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:06:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:06:06"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:06:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:06:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:06:36"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:06:36"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:07:03"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:07:03","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:07:03"}
{"level":"http","message":"POST / 200 - 22ms","timestamp":"2025-05-31 00:07:03"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:07:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:07:04","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:07:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:07:04","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:07:04"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 349ms","timestamp":"2025-05-31 00:07:04"}
{"level":"http","message":"GraphQL anonymous completed in 392ms","timestamp":"2025-05-31 00:07:04"}
{"level":"http","message":"POST / 200 - 397ms","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 38","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:07:04"}
{"level":"http","message":"GraphQL anonymous completed in 720ms","timestamp":"2025-05-31 00:07:04"}
{"level":"http","message":"POST / 200 - 724ms","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:07:04"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:07:04"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 439ms","timestamp":"2025-05-31 00:07:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:07:34","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:07:34"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:07:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:08:04","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:08:04"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:08:04"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:08:15"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:08:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:08:17","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:08:17"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:08:17","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:08:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:08:17","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:17"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 185ms","timestamp":"2025-05-31 00:08:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:08:17","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:08:17"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:08:17"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:08:17"}
{"level":"http","message":"GraphQL anonymous completed in 338ms","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"POST / 200 - 345ms","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 273ms","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 85ms","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 38","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:08:18","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 61ms","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:08:18","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"GraphQL anonymous completed in 117ms","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"POST / 200 - 122ms","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"GraphQL anonymous completed in 634ms","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"POST / 200 - 640ms","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 38","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"GraphQL anonymous completed in 601ms","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"POST / 200 - 606ms","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:08:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:08:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:08:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:08:26"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:08:26"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:08:26"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:26"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:26"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:26"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:08:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:08:26","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 208ms","timestamp":"2025-05-31 00:08:26"}
{"level":"http","message":"GraphQL anonymous completed in 238ms","timestamp":"2025-05-31 00:08:26"}
{"level":"http","message":"POST / 200 - 243ms","timestamp":"2025-05-31 00:08:26"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 65ms","timestamp":"2025-05-31 00:08:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:08:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:08:47"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-31 00:08:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:08:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:08:48"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 00:08:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:08:56","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:08:56"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:08:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:09:17","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:09:17"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:09:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:09:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:09:18"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:09:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:09:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:09:26"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-31 00:09:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:09:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:09:47"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:09:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:09:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:09:48"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 00:09:48"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:09:54"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:09:54"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:09:54"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:09:54"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:09:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-31 00:09:54","variables":{}}
{"level":"info","message":"[MessageService] Found 3 conversations","timestamp":"2025-05-31 00:09:55"}
{"level":"http","message":"GraphQL anonymous completed in 551ms","timestamp":"2025-05-31 00:09:55"}
{"level":"http","message":"POST / 200 - 569ms","timestamp":"2025-05-31 00:09:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:09:56","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:09:56"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 00:09:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:09:56","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:09:56"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 38","timestamp":"2025-05-31 00:09:56"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:09:56"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:09:56"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:09:56"}
{"level":"http","message":"GraphQL anonymous completed in 426ms","timestamp":"2025-05-31 00:09:56"}
{"level":"http","message":"POST / 200 - 431ms","timestamp":"2025-05-31 00:09:56"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:09:56"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:09:56"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:09:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-31 00:10:10","variables":{"content":"salem","conversationId":"6839c3a5b3d00b25cf26afd1","receiverId":"6839bf2db3d00b25cf26af7e","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839c1fcb3d00b25cf26afa7, receiverId=6839bf2db3d00b25cf26af7e, type=TEXT, hasMetadata=false","timestamp":"2025-05-31 00:10:10"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a3ad2046ce129783db98c","timestamp":"2025-05-31 00:10:11"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a3ad2046ce129783db98c","timestamp":"2025-05-31 00:10:11"}
{"level":"http","message":"GraphQL anonymous completed in 609ms","timestamp":"2025-05-31 00:10:11"}
{"level":"http","message":"POST / 200 - 614ms","timestamp":"2025-05-31 00:10:11"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:10:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:10:11","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:10:11"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:10:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:10:11","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:10:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:10:11"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 39","timestamp":"2025-05-31 00:10:11"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:10:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-31 00:10:11","variables":{"messageId":"683a3ad2046ce129783db98c"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:10:11","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:10:11"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 39","timestamp":"2025-05-31 00:10:11"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:10:11"}
{"level":"http","message":"GraphQL anonymous completed in 250ms","timestamp":"2025-05-31 00:10:11"}
{"level":"http","message":"POST / 200 - 254ms","timestamp":"2025-05-31 00:10:11"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 39","timestamp":"2025-05-31 00:10:11"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:10:11"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:10:12"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:10:12"}
{"level":"http","message":"GraphQL anonymous completed in 426ms","timestamp":"2025-05-31 00:10:12"}
{"level":"http","message":"POST / 200 - 431ms","timestamp":"2025-05-31 00:10:12"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:10:12"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:10:12"}
{"level":"http","message":"GraphQL anonymous completed in 615ms","timestamp":"2025-05-31 00:10:12"}
{"level":"http","message":"POST / 200 - 623ms","timestamp":"2025-05-31 00:10:12"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:10:12"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:10:12"}
{"level":"http","message":"GraphQL anonymous completed in 447ms","timestamp":"2025-05-31 00:10:12"}
{"level":"http","message":"POST / 200 - 450ms","timestamp":"2025-05-31 00:10:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:10:17","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:10:17"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-31 00:10:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:10:17","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:10:17"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:10:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-31 00:10:18","variables":{"content":"","conversationId":"6839c3a5b3d00b25cf26afd1","file":{"file":{"encoding":"7bit","filename":"voice_1748646618636.webm","mimetype":"audio/webm"},"promise":{}},"receiverId":"6839bf2db3d00b25cf26af7e","type":"AUDIO"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839c1fcb3d00b25cf26afa7, receiverId=6839bf2db3d00b25cf26af7e, type=AUDIO, hasMetadata=false","timestamp":"2025-05-31 00:10:18"}
{"level":"info","message":"[MessageService] File upload completed: voice_1748646618636.webm -> https://res.cloudinary.com/dhefwodjk/video/upload/v1748646619/message_attachments/message_attachments/74de31da-57a3-403b-86a4-d63b0f310a70.webm","timestamp":"2025-05-31 00:10:20"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a3adc046ce129783db9bc","timestamp":"2025-05-31 00:10:20"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a3adc046ce129783db9bc","timestamp":"2025-05-31 00:10:20"}
{"level":"http","message":"GraphQL anonymous completed in 2317ms","timestamp":"2025-05-31 00:10:20"}
{"level":"http","message":"POST / 200 - 2328ms","timestamp":"2025-05-31 00:10:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:10:20","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:10:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-31 00:10:21","variables":{"messageId":"683a3adc046ce129783db9bc"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:10:21","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:10:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:10:21","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:10:21"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 40","timestamp":"2025-05-31 00:10:21"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:10:21"}
{"level":"http","message":"GraphQL anonymous completed in 724ms","timestamp":"2025-05-31 00:10:21"}
{"level":"http","message":"POST / 200 - 731ms","timestamp":"2025-05-31 00:10:21"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:10:21"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:10:21"}
{"level":"http","message":"GraphQL anonymous completed in 887ms","timestamp":"2025-05-31 00:10:21"}
{"level":"http","message":"POST / 200 - 893ms","timestamp":"2025-05-31 00:10:21"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:10:22"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 40","timestamp":"2025-05-31 00:10:22"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:10:22"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 40","timestamp":"2025-05-31 00:10:22"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:10:22"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:10:22"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:10:22"}
{"level":"http","message":"GraphQL anonymous completed in 1274ms","timestamp":"2025-05-31 00:10:22"}
{"level":"http","message":"POST / 200 - 1279ms","timestamp":"2025-05-31 00:10:22"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:10:22"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:10:22"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:10:22"}
{"level":"http","message":"GraphQL anonymous completed in 1275ms","timestamp":"2025-05-31 00:10:22"}
{"level":"http","message":"POST / 200 - 1280ms","timestamp":"2025-05-31 00:10:22"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:10:22"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:10:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:10:26"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:10:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:10:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:10:47"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:10:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:10:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:10:47"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:10:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:10:56","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:10:56"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:10:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:11:17","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:11:17"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:11:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-31 00:11:21","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-31 00:11:21"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:11:21"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:11:21"}
{"level":"http","message":"GraphQL anonymous completed in 339ms","timestamp":"2025-05-31 00:11:21"}
{"level":"http","message":"POST / 200 - 343ms","timestamp":"2025-05-31 00:11:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:11:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:11:26"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-31 00:11:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:11:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:11:28"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:11:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:11:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:11:47"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:11:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:11:56","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:11:56"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:11:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:12:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:12:18"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:12:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:12:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:12:26"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:12:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:12:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:12:28"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-31 00:12:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:12:56","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:12:56"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:12:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:13:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:13:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:13:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:13:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:13:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:13:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:13:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:13:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:13:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:14:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:14:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:14:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:14:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:14:28"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:14:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:14:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:14:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:14:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:15:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:15:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:15:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:15:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:15:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:15:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:15:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:15:28"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:15:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:16:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:16:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:16:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:16:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:16:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:16:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:16:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:16:28"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:16:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:17:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:17:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:17:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:17:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:17:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:17:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:17:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:17:28"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:17:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:17:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:17:47"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-31 00:17:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-31 00:17:57","variables":{"content":"","conversationId":"6839c3a5b3d00b25cf26afd1","file":{"file":{"encoding":"7bit","filename":"default-avatar.png","mimetype":"image/png"},"promise":{}},"receiverId":"6839c1fcb3d00b25cf26afa7","type":"IMAGE"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, type=IMAGE, hasMetadata=false","timestamp":"2025-05-31 00:17:57"}
{"level":"info","message":"[MessageService] File upload completed: default-avatar.png -> https://res.cloudinary.com/dhefwodjk/image/upload/v1748647078/message_attachments/message_attachments/74f75e5d-f069-4109-ae26-0b90247a151a.avif","timestamp":"2025-05-31 00:17:59"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a3ca7046ce129783db9f1","timestamp":"2025-05-31 00:17:59"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a3ca7046ce129783db9f1","timestamp":"2025-05-31 00:17:59"}
{"level":"http","message":"GraphQL anonymous completed in 2380ms","timestamp":"2025-05-31 00:17:59"}
{"level":"http","message":"POST / 200 - 2386ms","timestamp":"2025-05-31 00:17:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:17:59","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:17:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-31 00:17:59","variables":{"messageId":"683a3ca7046ce129783db9f1"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-31 00:17:59","variables":{"messageId":"683a3ca7046ce129783db9f1"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:18:00","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:18:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:18:00","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:18:00"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 41","timestamp":"2025-05-31 00:18:00"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:18:00"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 41","timestamp":"2025-05-31 00:18:00"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:18:00"}
{"level":"http","message":"GraphQL anonymous completed in 570ms","timestamp":"2025-05-31 00:18:00"}
{"level":"http","message":"POST / 200 - 579ms","timestamp":"2025-05-31 00:18:00"}
{"level":"http","message":"GraphQL anonymous completed in 563ms","timestamp":"2025-05-31 00:18:00"}
{"level":"http","message":"POST / 200 - 569ms","timestamp":"2025-05-31 00:18:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:18:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:18:00"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:18:00"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:18:00"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:18:00"}
{"level":"http","message":"GraphQL anonymous completed in 709ms","timestamp":"2025-05-31 00:18:00"}
{"level":"http","message":"POST / 200 - 714ms","timestamp":"2025-05-31 00:18:00"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:00"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:18:00"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:18:00"}
{"level":"http","message":"GraphQL anonymous completed in 747ms","timestamp":"2025-05-31 00:18:00"}
{"level":"http","message":"POST / 200 - 755ms","timestamp":"2025-05-31 00:18:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:18:00"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 41","timestamp":"2025-05-31 00:18:01"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:18:01"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:18:01"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:18:01"}
{"level":"http","message":"GraphQL anonymous completed in 1603ms","timestamp":"2025-05-31 00:18:01"}
{"level":"http","message":"POST / 200 - 1609ms","timestamp":"2025-05-31 00:18:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:18:01"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:18:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:18:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:18:05"}
{"level":"http","message":"POST / 200 - 15ms","timestamp":"2025-05-31 00:18:05"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:18:05"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:05"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:05"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:05"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:18:05","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:18:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:18:05","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:05"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:05"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:05"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:05"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 411ms","timestamp":"2025-05-31 00:18:06"}
{"level":"http","message":"GraphQL anonymous completed in 438ms","timestamp":"2025-05-31 00:18:06"}
{"level":"http","message":"POST / 200 - 443ms","timestamp":"2025-05-31 00:18:06"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 41","timestamp":"2025-05-31 00:18:06"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:18:06"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:18:06"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:18:06"}
{"level":"http","message":"GraphQL anonymous completed in 781ms","timestamp":"2025-05-31 00:18:06"}
{"level":"http","message":"POST / 200 - 786ms","timestamp":"2025-05-31 00:18:06"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:06"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:06"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:06"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 467ms","timestamp":"2025-05-31 00:18:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-31 00:18:11","variables":{"content":"cc","conversationId":"6839c3a5b3d00b25cf26afd1","receiverId":"6839c1fcb3d00b25cf26afa7","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, type=TEXT, hasMetadata=false","timestamp":"2025-05-31 00:18:11"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a3cb3046ce129783dba32","timestamp":"2025-05-31 00:18:11"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a3cb3046ce129783dba32","timestamp":"2025-05-31 00:18:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-31 00:18:11","variables":{"messageId":"683a3cb3046ce129783dba32"}}
{"level":"http","message":"GraphQL anonymous completed in 591ms","timestamp":"2025-05-31 00:18:11"}
{"level":"http","message":"POST / 200 - 595ms","timestamp":"2025-05-31 00:18:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-31 00:18:11","variables":{"messageId":"683a3cb3046ce129783dba32"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:18:11","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:18:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:18:11","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:18:11"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:18:11","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:18:11"}
{"level":"http","message":"GraphQL anonymous completed in 325ms","timestamp":"2025-05-31 00:18:12"}
{"level":"http","message":"POST / 200 - 330ms","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 42","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:18:12"}
{"level":"http","message":"GraphQL anonymous completed in 401ms","timestamp":"2025-05-31 00:18:12"}
{"level":"http","message":"POST / 200 - 408ms","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 42","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 42","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:18:12"}
{"level":"http","message":"GraphQL anonymous completed in 587ms","timestamp":"2025-05-31 00:18:12"}
{"level":"http","message":"POST / 200 - 593ms","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:18:12"}
{"level":"http","message":"GraphQL anonymous completed in 640ms","timestamp":"2025-05-31 00:18:12"}
{"level":"http","message":"POST / 200 - 645ms","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:18:12"}
{"level":"http","message":"GraphQL anonymous completed in 531ms","timestamp":"2025-05-31 00:18:12"}
{"level":"http","message":"POST / 200 - 539ms","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:18:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:18:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-31 00:18:17","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-31 00:18:17"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:18:18"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:18:18"}
{"level":"http","message":"GraphQL anonymous completed in 748ms","timestamp":"2025-05-31 00:18:18"}
{"level":"http","message":"POST / 200 - 755ms","timestamp":"2025-05-31 00:18:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:18:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:18:26"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:18:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:18:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:18:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:18:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:18:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:18:35"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:18:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-31 00:18:44","variables":{"content":"","conversationId":"6839c3a5b3d00b25cf26afd1","file":{"file":{"encoding":"7bit","filename":"voice_1748647124430.webm","mimetype":"audio/webm"},"promise":{}},"receiverId":"6839c1fcb3d00b25cf26afa7","type":"AUDIO"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, type=AUDIO, hasMetadata=false","timestamp":"2025-05-31 00:18:44"}
{"level":"info","message":"[MessageService] File upload completed: voice_1748647124430.webm -> https://res.cloudinary.com/dhefwodjk/video/upload/v1748647125/message_attachments/message_attachments/13c54871-f2c0-41de-9206-f93c1e29aae6.webm","timestamp":"2025-05-31 00:18:46"}
{"level":"info","message":"[MessageService] Message saved successfully: 683a3cd6046ce129783dba6b","timestamp":"2025-05-31 00:18:46"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=683a3cd6046ce129783dba6b","timestamp":"2025-05-31 00:18:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:18:46","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:18:46"}
{"level":"http","message":"GraphQL anonymous completed in 2481ms","timestamp":"2025-05-31 00:18:46"}
{"level":"http","message":"POST / 200 - 2486ms","timestamp":"2025-05-31 00:18:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-31 00:18:46","variables":{"messageId":"683a3cd6046ce129783dba6b"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:18:46","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:18:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-31 00:18:46","variables":{"messageId":"683a3cd6046ce129783dba6b"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:18:47","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 43","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 43","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:18:47"}
{"level":"http","message":"GraphQL anonymous completed in 301ms","timestamp":"2025-05-31 00:18:47"}
{"level":"http","message":"POST / 200 - 306ms","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 43","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:18:47"}
{"level":"http","message":"GraphQL anonymous completed in 331ms","timestamp":"2025-05-31 00:18:47"}
{"level":"http","message":"POST / 200 - 334ms","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:18:47"}
{"level":"http","message":"GraphQL anonymous completed in 550ms","timestamp":"2025-05-31 00:18:47"}
{"level":"http","message":"POST / 200 - 553ms","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:18:47"}
{"level":"http","message":"GraphQL anonymous completed in 561ms","timestamp":"2025-05-31 00:18:47"}
{"level":"http","message":"POST / 200 - 566ms","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:18:47"}
{"level":"http","message":"GraphQL anonymous completed in 436ms","timestamp":"2025-05-31 00:18:47"}
{"level":"http","message":"POST / 200 - 440ms","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:18:47"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:18:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:18:56","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:18:56"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:18:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:19:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:19:05"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:19:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:19:07","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 305ms","timestamp":"2025-05-31 00:19:07"}
{"level":"http","message":"POST / 200 - 314ms","timestamp":"2025-05-31 00:19:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation DeleteAllNotifications {\n  deleteAllNotifications {\n    success\n    count\n    message\n  }\n}","timestamp":"2025-05-31 00:19:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 127ms","timestamp":"2025-05-31 00:19:10"}
{"level":"http","message":"POST / 200 - 130ms","timestamp":"2025-05-31 00:19:10"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:19:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:19:12","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 67ms","timestamp":"2025-05-31 00:19:12"}
{"level":"http","message":"POST / 200 - 79ms","timestamp":"2025-05-31 00:19:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation DeleteAllNotifications {\n  deleteAllNotifications {\n    success\n    count\n    message\n  }\n}","timestamp":"2025-05-31 00:19:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 110ms","timestamp":"2025-05-31 00:19:15"}
{"level":"http","message":"POST / 200 - 112ms","timestamp":"2025-05-31 00:19:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-31 00:19:23","variables":{}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:19:23"}
{"level":"info","message":"[MessageService] Found 2 conversations","timestamp":"2025-05-31 00:19:24"}
{"level":"http","message":"GraphQL anonymous completed in 509ms","timestamp":"2025-05-31 00:19:24"}
{"level":"http","message":"POST / 200 - 518ms","timestamp":"2025-05-31 00:19:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:19:25","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:19:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:19:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:19:26"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:19:26"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 43","timestamp":"2025-05-31 00:19:26"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:19:26"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:19:26"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:19:26"}
{"level":"http","message":"GraphQL anonymous completed in 673ms","timestamp":"2025-05-31 00:19:26"}
{"level":"http","message":"POST / 200 - 678ms","timestamp":"2025-05-31 00:19:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:19:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:19:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:19:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:19:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:19:35"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:19:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:19:56","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:19:56"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:19:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:20:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:20:05"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 00:20:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:20:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:20:26"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:20:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:20:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:20:28"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:20:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:20:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:20:35"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:20:35"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:20:48"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:20:48"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:20:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:20:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:20:49"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-31 00:20:49"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:20:49"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:49"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:49"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:49"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:20:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:20:49"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:20:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:20:50","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:20:50","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:20:50","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"GraphQL anonymous completed in 69ms","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"POST / 200 - 74ms","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:20:50","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 60ms","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 68ms","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:20:50","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:20:50","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"GraphQL anonymous completed in 58ms","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"POST / 200 - 62ms","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:20:50","variables":{"limit":10,"page":2}}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 43","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"GraphQL anonymous completed in 61ms","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"POST / 200 - 65ms","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"GraphQL anonymous completed in 461ms","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"POST / 200 - 464ms","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:20:50"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 1150ms","timestamp":"2025-05-31 00:20:51"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 72ms","timestamp":"2025-05-31 00:20:51"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 43","timestamp":"2025-05-31 00:20:51"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:20:51"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 119ms","timestamp":"2025-05-31 00:20:51"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 68ms","timestamp":"2025-05-31 00:20:51"}
{"level":"http","message":"GraphQL anonymous completed in 1219ms","timestamp":"2025-05-31 00:20:51"}
{"level":"http","message":"POST / 200 - 1222ms","timestamp":"2025-05-31 00:20:51"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:20:51"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:20:51"}
{"level":"http","message":"GraphQL anonymous completed in 1499ms","timestamp":"2025-05-31 00:20:51"}
{"level":"http","message":"POST / 200 - 1504ms","timestamp":"2025-05-31 00:20:51"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:51"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:51"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:20:51"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:21:17"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:21:17"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:21:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:21:19","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:21:19"}
{"level":"http","message":"POST / 200 - 19ms","timestamp":"2025-05-31 00:21:19"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:21:19"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:19"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:19"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:19"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:21:19","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:21:19","variables":{"limit":10,"page":2}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:21:20","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:21:20","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:21:20","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:21:20","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"GraphQL anonymous completed in 1377ms","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"POST / 200 - 1398ms","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1422ms","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"GraphQL anonymous completed in 1112ms","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"POST / 200 - 1140ms","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 56ms","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:21:20","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:21:20","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 57ms","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"GraphQL anonymous completed in 79ms","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"POST / 200 - 83ms","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 73ms","timestamp":"2025-05-31 00:21:20"}
{"level":"http","message":"GraphQL anonymous completed in 380ms","timestamp":"2025-05-31 00:21:21"}
{"level":"http","message":"POST / 200 - 384ms","timestamp":"2025-05-31 00:21:21"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 367ms","timestamp":"2025-05-31 00:21:21"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 57ms","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 43","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 43","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:21:21"}
{"level":"http","message":"GraphQL anonymous completed in 503ms","timestamp":"2025-05-31 00:21:21"}
{"level":"http","message":"POST / 200 - 513ms","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:21:21"}
{"level":"http","message":"GraphQL anonymous completed in 803ms","timestamp":"2025-05-31 00:21:21"}
{"level":"http","message":"POST / 200 - 810ms","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:21"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:21:22"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:21:31"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:21:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:21:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:21:32"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-31 00:21:32"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:21:33"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:33"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:33"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:33"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:21:33","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 230ms","timestamp":"2025-05-31 00:21:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:21:33","variables":{"limit":10,"page":2}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 69ms","timestamp":"2025-05-31 00:21:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:21:33","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:21:33"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:21:33"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:21:33"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:33"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:33"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:33"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:21:33","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:21:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:21:33","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:34"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:34"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:34"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:34"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 189ms","timestamp":"2025-05-31 00:21:34"}
{"level":"http","message":"GraphQL anonymous completed in 1758ms","timestamp":"2025-05-31 00:21:34"}
{"level":"http","message":"POST / 200 - 1766ms","timestamp":"2025-05-31 00:21:34"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1013ms","timestamp":"2025-05-31 00:21:35"}
{"level":"http","message":"GraphQL anonymous completed in 1586ms","timestamp":"2025-05-31 00:21:35"}
{"level":"http","message":"POST / 200 - 1626ms","timestamp":"2025-05-31 00:21:35"}
{"level":"http","message":"GraphQL anonymous completed in 1070ms","timestamp":"2025-05-31 00:21:35"}
{"level":"http","message":"POST / 200 - 1073ms","timestamp":"2025-05-31 00:21:35"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 43","timestamp":"2025-05-31 00:21:35"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:21:35"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:21:35"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:21:35"}
{"level":"http","message":"GraphQL anonymous completed in 1446ms","timestamp":"2025-05-31 00:21:35"}
{"level":"http","message":"POST / 200 - 1451ms","timestamp":"2025-05-31 00:21:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:35"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:35"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:21:50"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:21:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:21:51","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:21:51"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-31 00:21:51"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:21:51"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:51"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:51"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:51"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:21:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:21:51","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:21:51","variables":{"limit":10,"page":2}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:21:52","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"POST / 200 - 54ms","timestamp":"2025-05-31 00:21:52"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:21:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:21:52","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:21:52","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:52"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"GraphQL anonymous completed in 57ms","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"POST / 200 - 61ms","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"GraphQL anonymous completed in 987ms","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"POST / 200 - 992ms","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 1216ms","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 555ms","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"GraphQL anonymous completed in 1205ms","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"POST / 200 - 1208ms","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 67ms","timestamp":"2025-05-31 00:21:52"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 62ms","timestamp":"2025-05-31 00:21:52"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 43","timestamp":"2025-05-31 00:21:53"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:21:53"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:21:53"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:21:53"}
{"level":"http","message":"GraphQL anonymous completed in 841ms","timestamp":"2025-05-31 00:21:53"}
{"level":"http","message":"POST / 200 - 845ms","timestamp":"2025-05-31 00:21:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:53"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:21:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:22:21","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:22:21"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:22:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:22:22","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:22:22"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-31 00:22:22"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:22:51","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:22:51"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:22:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:22:52","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:22:52"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:22:52"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:23:09"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:23:09"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:23:09"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:23:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-31 00:23:09","variables":{}}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:23:09"}
{"level":"info","message":"[MessageService] Found 3 conversations","timestamp":"2025-05-31 00:23:09"}
{"level":"http","message":"GraphQL anonymous completed in 811ms","timestamp":"2025-05-31 00:23:09"}
{"level":"http","message":"POST / 200 - 815ms","timestamp":"2025-05-31 00:23:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:23:11","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:23:11"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 43","timestamp":"2025-05-31 00:23:11"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:23:11"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:23:12"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:23:12"}
{"level":"http","message":"GraphQL anonymous completed in 597ms","timestamp":"2025-05-31 00:23:12"}
{"level":"http","message":"POST / 200 - 601ms","timestamp":"2025-05-31 00:23:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:23:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:23:12"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:23:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:23:21","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:23:21"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:23:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:23:22","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:23:22"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:23:22"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-31 00:23:51","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839bf2db3d00b25cf26af7e","senderId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839c1fcb3d00b25cf26afa7, receiverId=6839bf2db3d00b25cf26af7e, page=2, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=undefined","timestamp":"2025-05-31 00:23:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:23:51","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:23:51"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:23:51"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:23:51"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:23:51"}
{"level":"http","message":"GraphQL anonymous completed in 543ms","timestamp":"2025-05-31 00:23:51"}
{"level":"http","message":"POST / 200 - 547ms","timestamp":"2025-05-31 00:23:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:23:52","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:23:52"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 00:23:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-31 00:23:54","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839bf2db3d00b25cf26af7e","senderId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839c1fcb3d00b25cf26afa7, receiverId=6839bf2db3d00b25cf26af7e, page=3, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=undefined","timestamp":"2025-05-31 00:23:54"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:23:55"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:23:55"}
{"level":"http","message":"GraphQL anonymous completed in 532ms","timestamp":"2025-05-31 00:23:55"}
{"level":"http","message":"POST / 200 - 536ms","timestamp":"2025-05-31 00:23:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:24:21","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:24:21"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-31 00:24:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:24:22","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:24:22"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-31 00:24:22"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:24:51","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:24:51"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-31 00:24:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:25:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:25:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:25:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:25:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:25:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:25:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:26:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:26:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:26:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:26:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:26:28"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:26:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:27:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:27:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:27:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:27:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:27:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:27:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:28:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:28:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:28:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:28:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:28:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:28:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:28:52","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:28:52"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:28:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:29:22","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:29:22"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:29:22"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:29:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:29:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:29:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:29:52","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:29:52"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:29:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:30:22","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:30:22"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:30:22"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:30:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:30:28"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:30:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:30:52","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:30:52"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:30:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:30:54","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-31 00:30:54"}
{"level":"http","message":"POST / 200 - 21ms","timestamp":"2025-05-31 00:30:54"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:31:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:31:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:31:00"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-31 00:31:00"}
{"level":"info","message":"WebSocket connection authenticated for user 6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:31:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:31:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:31:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:31:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:31:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:31:00","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839c1fcb3d00b25cf26afa7","timestamp":"2025-05-31 00:31:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:31:00","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 65ms","timestamp":"2025-05-31 00:31:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:31:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:31:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:31:00"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:31:00"}
{"level":"http","message":"GraphQL anonymous completed in 57ms","timestamp":"2025-05-31 00:31:00"}
{"level":"http","message":"POST / 200 - 64ms","timestamp":"2025-05-31 00:31:00"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 53ms","timestamp":"2025-05-31 00:31:00"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 5, messages: 43","timestamp":"2025-05-31 00:31:01"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=0","timestamp":"2025-05-31 00:31:01"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:31:01"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:31:01"}
{"level":"http","message":"GraphQL anonymous completed in 821ms","timestamp":"2025-05-31 00:31:01"}
{"level":"http","message":"POST / 200 - 825ms","timestamp":"2025-05-31 00:31:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:31:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:31:01"}
{"level":"info","message":"WebSocket operation for user 6839c1fcb3d00b25cf26afa7, operation: subscribe","timestamp":"2025-05-31 00:31:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-31 00:31:06","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839bf2db3d00b25cf26af7e","senderId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839c1fcb3d00b25cf26afa7, receiverId=6839bf2db3d00b25cf26af7e, page=2, limit=10, userId=6839c1fcb3d00b25cf26afa7, offset=undefined","timestamp":"2025-05-31 00:31:06"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:31:07"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:31:07"}
{"level":"http","message":"GraphQL anonymous completed in 556ms","timestamp":"2025-05-31 00:31:07"}
{"level":"http","message":"POST / 200 - 559ms","timestamp":"2025-05-31 00:31:07"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-31 00:31:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:31:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:31:18"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:31:18"}
{"level":"info","message":"WebSocket connection authenticated for user 6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:31:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:31:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:31:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:31:18"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:31:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-31 00:31:18","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c3a5b3d00b25cf26afd1, userId=6839bf2db3d00b25cf26af7e","timestamp":"2025-05-31 00:31:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-31 00:31:19","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:31:19"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:31:19"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:31:19"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:31:19"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 90ms","timestamp":"2025-05-31 00:31:19"}
{"level":"http","message":"GraphQL anonymous completed in 62ms","timestamp":"2025-05-31 00:31:19"}
{"level":"http","message":"POST / 200 - 68ms","timestamp":"2025-05-31 00:31:19"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c3a5b3d00b25cf26afd1, unread: 1, messages: 43","timestamp":"2025-05-31 00:31:19"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=0","timestamp":"2025-05-31 00:31:19"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 249ms","timestamp":"2025-05-31 00:31:19"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:31:19"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:31:19"}
{"level":"http","message":"GraphQL anonymous completed in 438ms","timestamp":"2025-05-31 00:31:19"}
{"level":"http","message":"POST / 200 - 441ms","timestamp":"2025-05-31 00:31:19"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:31:19"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:31:19"}
{"level":"info","message":"WebSocket operation for user 6839bf2db3d00b25cf26af7e, operation: subscribe","timestamp":"2025-05-31 00:31:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-31 00:31:23","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":2,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=2, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-31 00:31:23"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:31:23"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:31:23"}
{"level":"http","message":"GraphQL anonymous completed in 319ms","timestamp":"2025-05-31 00:31:23"}
{"level":"http","message":"POST / 200 - 322ms","timestamp":"2025-05-31 00:31:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:31:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:31:30"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:31:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:31:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:31:49"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:31:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:32:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:32:00"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:32:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:32:19","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:32:19"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:32:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:32:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:32:30"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 00:32:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:32:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:32:49"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:32:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:33:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:33:00"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-31 00:33:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:33:19","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:33:19"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:33:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:33:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:33:30"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:33:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:33:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:33:49"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:33:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:34:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:34:18"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:34:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:34:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:34:28"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:34:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:34:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:34:48"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:34:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:35:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:35:09"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:35:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:35:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:35:18"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:35:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:35:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:35:30"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:35:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:35:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:35:48"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 00:35:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:36:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:36:00"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 00:36:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:36:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:36:18"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:36:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:36:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:36:30"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:36:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:36:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:36:48"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:36:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:37:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:37:00"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:37:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:37:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:37:18"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:37:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:37:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:37:30"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:37:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:37:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:37:48"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 00:37:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:38:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:38:00"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:38:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:38:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:38:18"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:38:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:38:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:38:30"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:38:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:38:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:38:48"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:38:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-31 00:38:51","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":3,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=3, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-31 00:38:51"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:38:51"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:38:51"}
{"level":"http","message":"GraphQL anonymous completed in 569ms","timestamp":"2025-05-31 00:38:51"}
{"level":"http","message":"POST / 200 - 574ms","timestamp":"2025-05-31 00:38:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetMessages($senderId: ID!, $receiverId: ID!, $conversationId: ID!, $page: Int, $limit: Int) {\n  getMessages(\n    senderId: $senderId\n    receiverId: $receiverId\n    conversationId: $conversationId\n    page: $page\n    limit: $limit\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    attachments {\n      url\n      type\n    }\n    replyTo {\n      id\n      content\n    }\n  }\n}","timestamp":"2025-05-31 00:38:57","variables":{"conversationId":"6839c3a5b3d00b25cf26afd1","limit":10,"page":4,"receiverId":"6839c1fcb3d00b25cf26afa7","senderId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c3a5b3d00b25cf26afd1, senderId=6839bf2db3d00b25cf26af7e, receiverId=6839c1fcb3d00b25cf26afa7, page=4, limit=10, userId=6839bf2db3d00b25cf26af7e, offset=undefined","timestamp":"2025-05-31 00:38:57"}
{"level":"info","message":"[MessageService] Retrieved 10 messages","timestamp":"2025-05-31 00:38:57"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-31 00:38:57"}
{"level":"http","message":"GraphQL anonymous completed in 459ms","timestamp":"2025-05-31 00:38:57"}
{"level":"http","message":"POST / 200 - 465ms","timestamp":"2025-05-31 00:38:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:39:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:39:18"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:39:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:39:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:39:28"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:39:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:39:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:39:32"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:39:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:39:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:39:48"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:39:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:40:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:40:00"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:40:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:40:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:40:18"}
{"level":"http","message":"POST / 200 - 26ms","timestamp":"2025-05-31 00:40:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:40:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:40:30"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:40:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:40:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:40:48"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:40:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:41:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:41:00"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:41:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:41:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:41:18"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-31 00:41:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:41:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:41:30"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:41:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:41:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:41:48"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:41:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:42:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:42:00"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:42:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:42:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:42:18"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:42:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:42:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:42:30"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:42:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:42:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:42:48"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:42:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:43:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:43:00"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:43:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:43:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:43:18"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:43:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:43:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:43:30"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:43:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:43:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:43:48"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:43:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:44:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:44:00"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:44:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:44:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:44:18"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-31 00:44:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:44:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:44:30"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:44:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:44:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:44:48"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:44:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:45:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:45:00"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-31 00:45:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:45:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:45:18"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:45:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:45:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:45:30"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:45:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:45:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:45:48"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-31 00:45:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:46:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:46:00"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:46:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:46:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:46:18"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:46:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:46:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:46:30"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-31 00:46:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:46:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:46:48"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-31 00:46:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:47:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-31 00:47:00"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-31 00:47:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-31 00:47:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-31 00:47:18"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-31 00:47:18"}
