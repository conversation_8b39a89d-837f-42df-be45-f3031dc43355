{"ast": null, "code": "import { CallType, CallStatus } from '../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/message.service\";\nimport * as i2 from \"../../services/call.service\";\nimport * as i3 from \"../../services/logger.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"localVideo\"];\nconst _c1 = [\"remoteVideo\"];\nfunction ActiveCallComponent_div_0_div_7_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28);\n    i0.ɵɵelement(3, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 30);\n    i0.ɵɵtext(5, \"Vous\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ActiveCallComponent_div_0_div_7_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 27);\n    i0.ɵɵelement(2, \"img\", 32);\n    i0.ɵɵelementStart(3, \"p\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r7.getOtherParticipantAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r7.getOtherParticipantName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r7.getOtherParticipantName());\n  }\n}\nfunction ActiveCallComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵelement(2, \"video\", 19, 20);\n    i0.ɵɵtemplate(4, ActiveCallComponent_div_0_div_7_div_4_Template, 6, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 22);\n    i0.ɵɵelement(6, \"video\", 23, 24);\n    i0.ɵɵtemplate(8, ActiveCallComponent_div_0_div_7_div_8_Template, 5, 3, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasLocalVideo);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasRemoteVideo);\n  }\n}\nfunction ActiveCallComponent_div_0_div_8_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 42);\n  }\n  if (rf & 2) {\n    const i_r9 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"height\", i_r9, \"px\");\n  }\n}\nfunction ActiveCallComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 34)(2, \"div\", 35);\n    i0.ɵɵelement(3, \"img\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 37)(5, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h2\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 40);\n    i0.ɵɵtemplate(9, ActiveCallComponent_div_0_div_8_div_9_Template, 1, 2, \"div\", 41);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r2.getOtherParticipantAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.getOtherParticipantName());\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getOtherParticipantName(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.audioLevels);\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"bg-blue-500 text-white shadow-lg shadow-blue-500/25\": a0,\n    \"bg-red-500 text-white shadow-lg shadow-red-500/25\": a1\n  };\n};\nfunction ActiveCallComponent_div_0_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.toggleCamera());\n    });\n    i0.ɵɵelement(1, \"i\", 12)(2, \"div\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c2, !ctx_r3.isVideoMuted, ctx_r3.isVideoMuted));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.isVideoMuted ? \"fa-video-slash\" : \"fa-video\");\n  }\n}\nconst _c3 = function (a0, a1) {\n  return {\n    \"bg-blue-500 text-white shadow-lg shadow-blue-500/25\": a0,\n    \"bg-gray-600 text-gray-300 shadow-lg shadow-gray-600/25\": a1\n  };\n};\nfunction ActiveCallComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n    i0.ɵɵelement(3, \"div\", 4);\n    i0.ɵɵelementStart(4, \"span\", 5);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 6);\n    i0.ɵɵtemplate(7, ActiveCallComponent_div_0_div_7_Template, 9, 2, \"div\", 7);\n    i0.ɵɵtemplate(8, ActiveCallComponent_div_0_div_8_Template, 10, 4, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 9)(10, \"div\", 10)(11, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.toggleMicrophone());\n    });\n    i0.ɵɵelement(12, \"i\", 12)(13, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, ActiveCallComponent_div_0_button_14_Template, 3, 5, \"button\", 14);\n    i0.ɵɵelementStart(15, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.toggleSpeaker());\n    });\n    i0.ɵɵelement(16, \"i\", 12)(17, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.endCall());\n    });\n    i0.ɵɵelement(19, \"i\", 16)(20, \"div\", 13);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.getCallStatusText());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoCall());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isVideoCall());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c2, !ctx_r0.isAudioMuted, ctx_r0.isAudioMuted));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isAudioMuted ? \"fa-microphone-slash\" : \"fa-microphone\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoCall());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c3, ctx_r0.isSpeakerOn, !ctx_r0.isSpeakerOn));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isSpeakerOn ? \"fa-volume-up\" : \"fa-volume-mute\");\n  }\n}\nexport class ActiveCallComponent {\n  constructor(messageService, callService, logger) {\n    this.messageService = messageService;\n    this.callService = callService;\n    this.logger = logger;\n    this.activeCall = null;\n    this.callDuration = '00:00';\n    this.isAudioMuted = false;\n    this.isVideoMuted = false;\n    this.isSpeakerOn = true;\n    // Propriétés pour l'interface\n    this.hasLocalVideo = false;\n    this.hasRemoteVideo = false;\n    this.audioLevels = [];\n    this.callStartTime = null;\n    this.subscriptions = [];\n    // Exposer les énums au template\n    this.CallType = CallType;\n    this.CallStatus = CallStatus;\n  }\n  ngOnInit() {\n    // S'abonner à l'appel actif\n    const activeCallSub = this.callService.activeCall$.subscribe(call => {\n      const previousCall = this.activeCall;\n      this.activeCall = call;\n      console.log('📞 [ActiveCall] Active call updated:', call);\n      if (call && call.status === CallStatus.CONNECTED) {\n        if (!previousCall || previousCall.id !== call.id) {\n          // Nouvel appel connecté\n          console.log('✅ [ActiveCall] Call connected, starting timer and setting up media');\n          this.startCallTimer();\n          this.setupMediaStreams();\n        }\n      } else if (!call || call.status !== CallStatus.CONNECTED) {\n        // Appel terminé ou non connecté\n        this.stopCallTimer();\n      }\n    });\n    this.subscriptions.push(activeCallSub);\n  }\n  /**\n   * Configure les flux média pour l'appel\n   */\n  setupMediaStreams() {\n    console.log('🎥 [ActiveCall] Setting up media streams...');\n    // Configurer le flux local\n    const localStream = this.callService.getLocalStream();\n    if (localStream && this.localVideo?.nativeElement) {\n      console.log('📹 [ActiveCall] Setting local video stream');\n      this.localVideo.nativeElement.srcObject = localStream;\n      this.hasLocalVideo = localStream.getVideoTracks().length > 0;\n    }\n    // Initialiser le visualiseur audio pour les appels audio\n    if (!this.isVideoCall()) {\n      this.startAudioVisualizer();\n    }\n    // Mettre à jour les états des contrôles\n    this.isAudioMuted = !this.callService.getAudioEnabled();\n    this.isVideoMuted = !this.callService.getVideoEnabled();\n  }\n  /**\n   * Démarre le visualiseur audio\n   */\n  startAudioVisualizer() {\n    // Générer des niveaux audio animés\n    this.audioLevels = Array.from({\n      length: 15\n    }, () => Math.random() * 30 + 10);\n    this.audioVisualizerInterval = setInterval(() => {\n      this.audioLevels = this.audioLevels.map(() => Math.random() * 40 + 5);\n    }, 150);\n  }\n  /**\n   * Arrête le visualiseur audio\n   */\n  stopAudioVisualizer() {\n    if (this.audioVisualizerInterval) {\n      clearInterval(this.audioVisualizerInterval);\n      this.audioVisualizerInterval = null;\n    }\n  }\n  ngAfterViewInit() {\n    // Configurer les éléments vidéo après le rendu du composant\n    if (this.activeCall && this.activeCall.status === CallStatus.CONNECTED) {\n      this.setupMediaStreams();\n    }\n  }\n  // Démarrer le minuteur d'appel\n  startCallTimer() {\n    this.callStartTime = new Date();\n    this.stopCallTimer(); // Arrêter tout minuteur existant\n    this.durationInterval = setInterval(() => {\n      if (!this.callStartTime) return;\n      const now = new Date();\n      const diff = Math.floor((now.getTime() - this.callStartTime.getTime()) / 1000);\n      const minutes = Math.floor(diff / 60).toString().padStart(2, '0');\n      const seconds = (diff % 60).toString().padStart(2, '0');\n      this.callDuration = `${minutes}:${seconds}`;\n    }, 1000);\n  }\n  // Arrêter le minuteur d'appel\n  stopCallTimer() {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n  }\n  // Terminer l'appel\n  endCall() {\n    if (!this.activeCall) {\n      return;\n    }\n    this.logger.debug('Ending call', {\n      callId: this.activeCall.id\n    });\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: result => {\n        this.logger.debug('Call ended successfully', {\n          callId: this.activeCall?.id,\n          success: result.success\n        });\n      },\n      error: error => {\n        this.logger.error('Error ending call', error);\n      }\n    });\n  }\n  // Basculer le micro\n  toggleMicrophone() {\n    if (!this.activeCall) {\n      return;\n    }\n    console.log('🎤 [ActiveCall] Toggling microphone...');\n    // Basculer localement d'abord\n    const newAudioState = this.callService.toggleAudio();\n    this.isAudioMuted = !newAudioState;\n    console.log('🎤 [ActiveCall] Audio toggled:', {\n      enabled: newAudioState,\n      muted: this.isAudioMuted\n    });\n    // Optionnel : Notifier le serveur\n    this.callService.toggleMedia(this.activeCall.id, undefined, newAudioState).subscribe({\n      next: result => {\n        console.log('✅ [ActiveCall] Microphone toggle sent to server');\n      },\n      error: error => {\n        console.error('❌ [ActiveCall] Error toggling microphone on server:', error);\n        // Continuer même si le serveur échoue\n      }\n    });\n  }\n  // Basculer la caméra\n  toggleCamera() {\n    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) {\n      return;\n    }\n    console.log('📹 [ActiveCall] Toggling camera...');\n    // Basculer localement d'abord\n    const newVideoState = this.callService.toggleVideo();\n    this.isVideoMuted = !newVideoState;\n    console.log('📹 [ActiveCall] Video toggled:', {\n      enabled: newVideoState,\n      muted: this.isVideoMuted\n    });\n    // Optionnel : Notifier le serveur\n    this.callService.toggleMedia(this.activeCall.id, newVideoState, undefined).subscribe({\n      next: result => {\n        console.log('✅ [ActiveCall] Camera toggle sent to server');\n      },\n      error: error => {\n        console.error('❌ [ActiveCall] Error toggling camera on server:', error);\n        // Continuer même si le serveur échoue\n      }\n    });\n  }\n  // Basculer le haut-parleur\n  toggleSpeaker() {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    if (this.remoteVideo?.nativeElement) {\n      this.remoteVideo.nativeElement.volume = this.isSpeakerOn ? 1 : 0;\n    }\n    this.logger.debug('Speaker toggled', {\n      on: this.isSpeakerOn\n    });\n  }\n  // Obtenir le nom de l'autre participant\n  getOtherParticipantName() {\n    if (!this.activeCall) {\n      return '';\n    }\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n    return isCurrentUserCaller ? this.activeCall.recipient.username : this.activeCall.caller.username;\n  }\n  // Obtenir l'avatar de l'autre participant\n  getOtherParticipantAvatar() {\n    if (!this.activeCall) {\n      return 'assets/images/default-avatar.png';\n    }\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n    const avatar = isCurrentUserCaller ? this.activeCall.recipient.image : this.activeCall.caller.image;\n    return avatar || 'assets/images/default-avatar.png';\n  }\n  // Obtenir le statut de l'appel sous forme de texte\n  getCallStatusText() {\n    if (!this.activeCall) {\n      return '';\n    }\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Appel en cours...';\n      case CallStatus.CONNECTED:\n        return this.callDuration;\n      case CallStatus.ENDED:\n        return 'Appel terminé';\n      case CallStatus.MISSED:\n        return 'Appel manqué';\n      case CallStatus.REJECTED:\n        return 'Appel rejeté';\n      case CallStatus.FAILED:\n        return \"Échec de l'appel\";\n      default:\n        return '';\n    }\n  }\n  // Vérifier si l'appel est connecté\n  isCallConnected() {\n    return this.activeCall?.status === CallStatus.CONNECTED;\n  }\n  // Vérifier si l'appel est en cours de sonnerie\n  isCallRinging() {\n    return this.activeCall?.status === CallStatus.RINGING;\n  }\n  // Vérifier si l'appel est un appel vidéo\n  isVideoCall() {\n    return this.activeCall?.type === CallType.VIDEO || this.activeCall?.type === CallType.VIDEO_ONLY;\n  }\n  ngOnDestroy() {\n    // Nettoyer les abonnements et les minuteurs\n    this.stopCallTimer();\n    this.stopAudioVisualizer();\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  static {\n    this.ɵfac = function ActiveCallComponent_Factory(t) {\n      return new (t || ActiveCallComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.CallService), i0.ɵɵdirectiveInject(i3.LoggerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActiveCallComponent,\n      selectors: [[\"app-active-call\"]],\n      viewQuery: function ActiveCallComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localVideo = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.remoteVideo = _t.first);\n        }\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"fixed inset-0 z-50 bg-gradient-to-br from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] flex flex-col\", 4, \"ngIf\"], [1, \"fixed\", \"inset-0\", \"z-50\", \"bg-gradient-to-br\", \"from-[#0a0a0a]\", \"via-[#1a1a2e]\", \"to-[#16213e]\", \"flex\", \"flex-col\"], [1, \"flex-shrink-0\", \"p-6\", \"text-center\"], [1, \"inline-flex\", \"items-center\", \"space-x-2\", \"px-4\", \"py-2\", \"rounded-full\", \"bg-black/30\", \"backdrop-blur-sm\", \"border\", \"border-blue-500/20\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"bg-blue-400\", \"animate-pulse\"], [1, \"text-white\", \"text-sm\", \"font-medium\"], [1, \"flex-1\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"px-6\"], [\"class\", \"w-full max-w-4xl mx-auto relative\", 4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"flex-shrink-0\", \"p-6\"], [1, \"flex\", \"justify-center\", \"space-x-6\"], [1, \"w-16\", \"h-16\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-200\", \"relative\", \"group\", 3, \"ngClass\", \"click\"], [1, \"fas\", \"text-xl\", 3, \"ngClass\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"bg-white/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [\"class\", \"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 relative group\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [1, \"w-16\", \"h-16\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"bg-red-500\", \"text-white\", \"transition-all\", \"duration-200\", \"relative\", \"group\", \"shadow-lg\", \"shadow-red-500/25\", 3, \"click\"], [1, \"fas\", \"fa-phone-slash\", \"text-xl\"], [1, \"w-full\", \"max-w-4xl\", \"mx-auto\", \"relative\"], [1, \"relative\", \"w-full\", \"aspect-video\", \"rounded-2xl\", \"overflow-hidden\", \"bg-gray-900\", \"border\", \"border-blue-500/20\"], [\"autoplay\", \"\", \"playsinline\", \"\", \"muted\", \"\", 1, \"w-full\", \"h-full\", \"object-cover\"], [\"localVideo\", \"\"], [\"class\", \"absolute inset-0 flex items-center justify-center bg-gray-900\", 4, \"ngIf\"], [1, \"absolute\", \"top-4\", \"right-4\", \"w-48\", \"h-36\", \"rounded-xl\", \"overflow-hidden\", \"bg-gray-800\", \"border\", \"border-blue-500/30\"], [\"autoplay\", \"\", \"playsinline\", \"\", 1, \"w-full\", \"h-full\", \"object-cover\"], [\"remoteVideo\", \"\"], [\"class\", \"absolute inset-0 flex items-center justify-center bg-gray-800\", 4, \"ngIf\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\", \"bg-gray-900\"], [1, \"text-center\"], [1, \"w-24\", \"h-24\", \"rounded-full\", \"bg-blue-500/20\", \"flex\", \"items-center\", \"justify-center\", \"mx-auto\", \"mb-4\"], [1, \"fas\", \"fa-user\", \"text-3xl\", \"text-blue-400\"], [1, \"text-white\", \"text-lg\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\", \"bg-gray-800\"], [1, \"w-16\", \"h-16\", \"rounded-full\", \"object-cover\", \"mx-auto\", \"mb-2\", \"border-2\", \"border-blue-400\", 3, \"src\", \"alt\"], [1, \"text-white\", \"text-sm\"], [1, \"relative\", \"mb-8\"], [1, \"w-40\", \"h-40\", \"rounded-full\", \"overflow-hidden\", \"border-4\", \"border-blue-500/50\", \"mx-auto\"], [1, \"w-full\", \"h-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border-4\", \"border-blue-400/30\", \"animate-ping\"], [1, \"absolute\", \"-inset-4\", \"rounded-full\", \"border-2\", \"border-blue-400/20\", \"animate-ping\", 2, \"animation-delay\", \"0.5s\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-1\", \"h-12\", \"mb-8\"], [\"class\", \"w-1 bg-gradient-to-t from-blue-600 to-blue-400 rounded-full transition-all duration-150\", 3, \"height\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-1\", \"bg-gradient-to-t\", \"from-blue-600\", \"to-blue-400\", \"rounded-full\", \"transition-all\", \"duration-150\"]],\n      template: function ActiveCallComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ActiveCallComponent_div_0_Template, 21, 14, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.activeCall);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.active-call-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  right: 0;\\n  z-index: 1000;\\n  background-color: var(--dark-bg);\\n  border-radius: var(--border-radius-lg) 0 0 0;\\n  box-shadow: 0 -4px 30px rgba(0, 0, 0, 0.3);\\n  overflow: hidden;\\n  transition: all var(--transition-medium);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n}\\n\\n.active-call-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(\\n    90deg,\\n    transparent,\\n    var(--accent-color),\\n    transparent\\n  );\\n  opacity: 0.5;\\n  z-index: 1;\\n}\\n\\n.active-call-container.video-call[_ngcontent-%COMP%] {\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 0;\\n  background-color: var(--dark-bg);\\n}\\n\\n\\n\\n.video-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.video-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    circle at center,\\n    transparent 50%,\\n    var(--dark-bg) 150%\\n  );\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n\\n.remote-video-wrapper[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.remote-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background: linear-gradient(135deg, var(--medium-bg), var(--dark-bg));\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  width: 300px;\\n  height: 300px;\\n  background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);\\n  opacity: 0.1;\\n  filter: blur(40px);\\n  animation: _ngcontent-%COMP%_pulse 4s infinite;\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  border-radius: 50%;\\n  border: 3px solid var(--accent-color);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n  z-index: 2;\\n  position: relative;\\n}\\n\\n.call-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 30px;\\n  left: 30px;\\n  color: var(--text-light);\\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 15px 20px;\\n  border-radius: var(--border-radius-md);\\n  border-left: 3px solid var(--accent-color);\\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.participant-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  color: transparent;\\n  text-shadow: none;\\n}\\n\\n.call-status[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  font-size: 16px;\\n  opacity: 0.9;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.call-status[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: inline-block;\\n  width: 8px;\\n  height: 8px;\\n  background-color: var(--accent-color);\\n  border-radius: 50%;\\n  margin-right: 8px;\\n  box-shadow: 0 0 8px var(--accent-color);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.local-video-wrapper[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  right: 30px;\\n  width: 200px;\\n  height: 130px;\\n  border-radius: var(--border-radius-md);\\n  overflow: hidden;\\n  border: 2px solid var(--accent-color);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n  z-index: 10;\\n  transition: all var(--transition-medium);\\n}\\n\\n.local-video-wrapper[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 0 30px rgba(0, 247, 255, 0.5);\\n}\\n\\n.local-video-wrapper[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border: 1px solid rgba(0, 247, 255, 0.5);\\n  border-radius: var(--border-radius-md);\\n  pointer-events: none;\\n}\\n\\n.local-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transform: scaleX(-1); \\n\\n}\\n\\n\\n\\n.audio-call-info[_ngcontent-%COMP%] {\\n  padding: 30px;\\n  text-align: center;\\n  width: 350px;\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.05),\\n    rgba(157, 78, 221, 0.05)\\n  );\\n}\\n\\n.participant-avatar[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n\\n.participant-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 100px;\\n  height: 100px;\\n  background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);\\n  opacity: 0.3;\\n  filter: blur(20px);\\n  animation: _ngcontent-%COMP%_pulse 3s infinite;\\n  z-index: -1;\\n}\\n\\n.participant-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  border: 3px solid var(--accent-color);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n}\\n\\n\\n\\n.call-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 20px;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-top: 1px solid rgba(0, 247, 255, 0.1);\\n}\\n\\n.video-call[_ngcontent-%COMP%]   .call-controls[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.control-btn[_ngcontent-%COMP%], .end-call-btn[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  border: none;\\n  margin: 0 12px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.control-btn[_ngcontent-%COMP%]::before, .end-call-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    circle at center,\\n    rgba(255, 255, 255, 0.1) 0%,\\n    transparent 70%\\n  );\\n  opacity: 0;\\n  transition: opacity var(--transition-fast);\\n}\\n\\n.control-btn[_ngcontent-%COMP%]:hover::before, .end-call-btn[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.control-btn[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--text-light);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.control-btn.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  border: none;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n.control-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);\\n}\\n\\n.control-btn.active[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.7);\\n}\\n\\n.end-call-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3547, #ff5252);\\n  color: white;\\n  box-shadow: 0 0 15px rgba(255, 53, 71, 0.3);\\n}\\n\\n.end-call-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 0 20px rgba(255, 53, 71, 0.5);\\n}\\n\\n.control-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .end-call-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 0.3;\\n    transform: scale(0.95);\\n  }\\n  50% {\\n    opacity: 0.6;\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    opacity: 0.3;\\n    transform: scale(0.95);\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9hY3RpdmUtY2FsbC9hY3RpdmUtY2FsbC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLGdCQUFnQjtBQUNoQjtFQUNFLGVBQWU7RUFDZixTQUFTO0VBQ1QsUUFBUTtFQUNSLGFBQWE7RUFDYixnQ0FBZ0M7RUFDaEMsNENBQTRDO0VBQzVDLDBDQUEwQztFQUMxQyxnQkFBZ0I7RUFDaEIsd0NBQXdDO0VBQ3hDLHdDQUF3QztBQUMxQzs7QUFFQTtFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsTUFBTTtFQUNOLE9BQU87RUFDUCxRQUFRO0VBQ1IsV0FBVztFQUNYOzs7OztHQUtDO0VBQ0QsWUFBWTtFQUNaLFVBQVU7QUFDWjs7QUFFQTtFQUNFLE1BQU07RUFDTixPQUFPO0VBQ1AsV0FBVztFQUNYLFlBQVk7RUFDWixnQkFBZ0I7RUFDaEIsZ0NBQWdDO0FBQ2xDOztBQUVBLGdCQUFnQjtBQUNoQjtFQUNFLGtCQUFrQjtFQUNsQixXQUFXO0VBQ1gsWUFBWTtBQUNkOztBQUVBO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixNQUFNO0VBQ04sT0FBTztFQUNQLFFBQVE7RUFDUixTQUFTO0VBQ1Q7Ozs7R0FJQztFQUNELG9CQUFvQjtFQUNwQixVQUFVO0FBQ1o7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsTUFBTTtFQUNOLE9BQU87RUFDUCxXQUFXO0VBQ1gsWUFBWTtBQUNkOztBQUVBO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWixpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsTUFBTTtFQUNOLE9BQU87RUFDUCxXQUFXO0VBQ1gsWUFBWTtFQUNaLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsbUJBQW1CO0VBQ25CLHFFQUFxRTtBQUN2RTs7QUFFQTtFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsWUFBWTtFQUNaLGFBQWE7RUFDYiw0RUFBNEU7RUFDNUUsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQiw0QkFBNEI7QUFDOUI7O0FBRUE7RUFDRSxZQUFZO0VBQ1osYUFBYTtFQUNiLGtCQUFrQjtFQUNsQixxQ0FBcUM7RUFDckMsMkNBQTJDO0VBQzNDLFVBQVU7RUFDVixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsU0FBUztFQUNULFVBQVU7RUFDVix3QkFBd0I7RUFDeEIsMENBQTBDO0VBQzFDLFdBQVc7RUFDWCw4QkFBOEI7RUFDOUIsbUNBQTJCO1VBQTNCLDJCQUEyQjtFQUMzQixrQkFBa0I7RUFDbEIsc0NBQXNDO0VBQ3RDLDBDQUEwQztFQUMxQywrQkFBK0I7QUFDakM7O0FBRUE7RUFDRTtJQUNFLFVBQVU7SUFDViw0QkFBNEI7RUFDOUI7RUFDQTtJQUNFLFVBQVU7SUFDVix3QkFBd0I7RUFDMUI7QUFDRjs7QUFFQTtFQUNFLFNBQVM7RUFDVCxlQUFlO0VBQ2YsZ0JBQWdCO0VBQ2hCOzs7O0dBSUM7RUFDRCw2QkFBNkI7RUFDN0IscUJBQXFCO0VBQ3JCLGtCQUFrQjtFQUNsQixpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsZUFBZTtFQUNmLFlBQVk7RUFDWixhQUFhO0VBQ2IsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsV0FBVztFQUNYLHFCQUFxQjtFQUNyQixVQUFVO0VBQ1YsV0FBVztFQUNYLHFDQUFxQztFQUNyQyxrQkFBa0I7RUFDbEIsaUJBQWlCO0VBQ2pCLHVDQUF1QztFQUN2Qyw0QkFBNEI7QUFDOUI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsYUFBYTtFQUNiLFdBQVc7RUFDWCxZQUFZO0VBQ1osYUFBYTtFQUNiLHNDQUFzQztFQUN0QyxnQkFBZ0I7RUFDaEIscUNBQXFDO0VBQ3JDLDJDQUEyQztFQUMzQyxXQUFXO0VBQ1gsd0NBQXdDO0FBQzFDOztBQUVBO0VBQ0Usc0JBQXNCO0VBQ3RCLDJDQUEyQztBQUM3Qzs7QUFFQTtFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsTUFBTTtFQUNOLE9BQU87RUFDUCxRQUFRO0VBQ1IsU0FBUztFQUNULHdDQUF3QztFQUN4QyxzQ0FBc0M7RUFDdEMsb0JBQW9CO0FBQ3RCOztBQUVBO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWixpQkFBaUI7RUFDakIscUJBQXFCLEVBQUUsaUJBQWlCO0FBQzFDOztBQUVBLGdCQUFnQjtBQUNoQjtFQUNFLGFBQWE7RUFDYixrQkFBa0I7RUFDbEIsWUFBWTtFQUNaOzs7O0dBSUM7QUFDSDs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLFFBQVE7RUFDUixTQUFTO0VBQ1QsZ0NBQWdDO0VBQ2hDLFlBQVk7RUFDWixhQUFhO0VBQ2IsNEVBQTRFO0VBQzVFLFlBQVk7RUFDWixrQkFBa0I7RUFDbEIsNEJBQTRCO0VBQzVCLFdBQVc7QUFDYjs7QUFFQTtFQUNFLFlBQVk7RUFDWixhQUFhO0VBQ2Isa0JBQWtCO0VBQ2xCLHFDQUFxQztFQUNyQywyQ0FBMkM7QUFDN0M7O0FBRUEsc0JBQXNCO0FBQ3RCO0VBQ0UsYUFBYTtFQUNiLHVCQUF1QjtFQUN2QixhQUFhO0VBQ2IsOEJBQThCO0VBQzlCLG1DQUEyQjtVQUEzQiwyQkFBMkI7RUFDM0IsNENBQTRDO0FBQzlDOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLFNBQVM7RUFDVCxPQUFPO0VBQ1AsUUFBUTtFQUNSLFdBQVc7QUFDYjs7QUFFQTs7RUFFRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQixZQUFZO0VBQ1osY0FBYztFQUNkLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsbUJBQW1CO0VBQ25CLGVBQWU7RUFDZixzQ0FBc0M7RUFDdEMsa0JBQWtCO0VBQ2xCLGdCQUFnQjtBQUNsQjs7QUFFQTs7RUFFRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLE1BQU07RUFDTixPQUFPO0VBQ1AsUUFBUTtFQUNSLFNBQVM7RUFDVDs7OztHQUlDO0VBQ0QsVUFBVTtFQUNWLDBDQUEwQztBQUM1Qzs7QUFFQTs7RUFFRSxVQUFVO0FBQ1o7O0FBRUE7RUFDRSx3Q0FBd0M7RUFDeEMsd0JBQXdCO0VBQ3hCLHdDQUF3QztBQUMxQzs7QUFFQTtFQUNFOzs7O0dBSUM7RUFDRCxZQUFZO0VBQ1osMkNBQTJDO0FBQzdDOztBQUVBO0VBQ0UsMkJBQTJCO0VBQzNCLHlDQUF5QztBQUMzQzs7QUFFQTtFQUNFLDJDQUEyQztBQUM3Qzs7QUFFQTtFQUNFLHFEQUFxRDtFQUNyRCxZQUFZO0VBQ1osMkNBQTJDO0FBQzdDOztBQUVBO0VBQ0UsMkJBQTJCO0VBQzNCLDJDQUEyQztBQUM3Qzs7QUFFQTs7RUFFRSxlQUFlO0VBQ2Ysa0JBQWtCO0VBQ2xCLFVBQVU7QUFDWjs7QUFFQTtFQUNFO0lBQ0UsWUFBWTtJQUNaLHNCQUFzQjtFQUN4QjtFQUNBO0lBQ0UsWUFBWTtJQUNaLHNCQUFzQjtFQUN4QjtFQUNBO0lBQ0UsWUFBWTtJQUNaLHNCQUFzQjtFQUN4QjtBQUNGOztBQUlBLHcwYUFBdzBhIiwic291cmNlc0NvbnRlbnQiOlsiQGNoYXJzZXQgXCJVVEYtOFwiO1xuLmFjdGl2ZS1jYWxsLWNvbnRhaW5lciB7XG4gIHBvc2l0aW9uOiBmaXhlZDtcbiAgYm90dG9tOiAwO1xuICByaWdodDogMDtcbiAgei1pbmRleDogMTAwMDtcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tZGFyay1iZyk7XG4gIGJvcmRlci1yYWRpdXM6IHZhcigtLWJvcmRlci1yYWRpdXMtbGcpIDAgMCAwO1xuICBib3gtc2hhZG93OiAwIC00cHggMzBweCByZ2JhKDAsIDAsIDAsIDAuMyk7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIHRyYW5zaXRpb246IGFsbCB2YXIoLS10cmFuc2l0aW9uLW1lZGl1bSk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjQ3LCAyNTUsIDAuMSk7XG59XG5cbi5hY3RpdmUtY2FsbC1jb250YWluZXI6OmJlZm9yZSB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgaGVpZ2h0OiAxcHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICA5MGRlZyxcbiAgICB0cmFuc3BhcmVudCxcbiAgICB2YXIoLS1hY2NlbnQtY29sb3IpLFxuICAgIHRyYW5zcGFyZW50XG4gICk7XG4gIG9wYWNpdHk6IDAuNTtcbiAgei1pbmRleDogMTtcbn1cblxuLmFjdGl2ZS1jYWxsLWNvbnRhaW5lci52aWRlby1jYWxsIHtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBib3JkZXItcmFkaXVzOiAwO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1kYXJrLWJnKTtcbn1cblxuLyogQXBwZWwgdmlkw4PCqW8gKi9cbi52aWRlby1jb250YWluZXIge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG59XG5cbi52aWRlby1jb250YWluZXI6OmFmdGVyIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICBib3R0b206IDA7XG4gIGJhY2tncm91bmQ6IHJhZGlhbC1ncmFkaWVudChcbiAgICBjaXJjbGUgYXQgY2VudGVyLFxuICAgIHRyYW5zcGFyZW50IDUwJSxcbiAgICB2YXIoLS1kYXJrLWJnKSAxNTAlXG4gICk7XG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xuICB6LWluZGV4OiAxO1xufVxuXG4ucmVtb3RlLXZpZGVvLXdyYXBwZXIge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbn1cblxuLnJlbW90ZS12aWRlbyB7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIG9iamVjdC1maXQ6IGNvdmVyO1xufVxuXG4udmlkZW8tcGxhY2Vob2xkZXIge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHZhcigtLW1lZGl1bS1iZyksIHZhcigtLWRhcmstYmcpKTtcbn1cblxuLnZpZGVvLXBsYWNlaG9sZGVyOjpiZWZvcmUge1xuICBjb250ZW50OiBcIlwiO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHdpZHRoOiAzMDBweDtcbiAgaGVpZ2h0OiAzMDBweDtcbiAgYmFja2dyb3VuZDogcmFkaWFsLWdyYWRpZW50KGNpcmNsZSwgdmFyKC0tYWNjZW50LWNvbG9yKSAwJSwgdHJhbnNwYXJlbnQgNzAlKTtcbiAgb3BhY2l0eTogMC4xO1xuICBmaWx0ZXI6IGJsdXIoNDBweCk7XG4gIGFuaW1hdGlvbjogcHVsc2UgNHMgaW5maW5pdGU7XG59XG5cbi52aWRlby1wbGFjZWhvbGRlciAuYXZhdGFyLWltZyB7XG4gIHdpZHRoOiAxNTBweDtcbiAgaGVpZ2h0OiAxNTBweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBib3JkZXI6IDNweCBzb2xpZCB2YXIoLS1hY2NlbnQtY29sb3IpO1xuICBib3gtc2hhZG93OiAwIDAgMjBweCByZ2JhKDAsIDI0NywgMjU1LCAwLjMpO1xuICB6LWluZGV4OiAyO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG59XG5cbi5jYWxsLWluZm8ge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMzBweDtcbiAgbGVmdDogMzBweDtcbiAgY29sb3I6IHZhcigtLXRleHQtbGlnaHQpO1xuICB0ZXh0LXNoYWRvdzogMCAycHggMTBweCByZ2JhKDAsIDAsIDAsIDAuNyk7XG4gIHotaW5kZXg6IDEwO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMyk7XG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcbiAgcGFkZGluZzogMTVweCAyMHB4O1xuICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzLW1kKTtcbiAgYm9yZGVyLWxlZnQ6IDNweCBzb2xpZCB2YXIoLS1hY2NlbnQtY29sb3IpO1xuICBhbmltYXRpb246IGZhZGVJbiAwLjVzIGVhc2Utb3V0O1xufVxuXG5Aa2V5ZnJhbWVzIGZhZGVJbiB7XG4gIGZyb20ge1xuICAgIG9wYWNpdHk6IDA7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xMHB4KTtcbiAgfVxuICB0byB7XG4gICAgb3BhY2l0eTogMTtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XG4gIH1cbn1cblxuLnBhcnRpY2lwYW50LW5hbWUge1xuICBtYXJnaW46IDA7XG4gIGZvbnQtc2l6ZTogMjRweDtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KFxuICAgIDEzNWRlZyxcbiAgICB2YXIoLS1hY2NlbnQtY29sb3IpLFxuICAgIHZhcigtLXNlY29uZGFyeS1jb2xvcilcbiAgKTtcbiAgLXdlYmtpdC1iYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XG4gIGJhY2tncm91bmQtY2xpcDogdGV4dDtcbiAgY29sb3I6IHRyYW5zcGFyZW50O1xuICB0ZXh0LXNoYWRvdzogbm9uZTtcbn1cblxuLmNhbGwtc3RhdHVzIHtcbiAgbWFyZ2luOiA1cHggMCAwIDA7XG4gIGZvbnQtc2l6ZTogMTZweDtcbiAgb3BhY2l0eTogMC45O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xufVxuXG4uY2FsbC1zdGF0dXM6OmJlZm9yZSB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgd2lkdGg6IDhweDtcbiAgaGVpZ2h0OiA4cHg7XG4gIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWFjY2VudC1jb2xvcik7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgbWFyZ2luLXJpZ2h0OiA4cHg7XG4gIGJveC1zaGFkb3c6IDAgMCA4cHggdmFyKC0tYWNjZW50LWNvbG9yKTtcbiAgYW5pbWF0aW9uOiBwdWxzZSAycyBpbmZpbml0ZTtcbn1cblxuLmxvY2FsLXZpZGVvLXdyYXBwZXIge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGJvdHRvbTogMTAwcHg7XG4gIHJpZ2h0OiAzMHB4O1xuICB3aWR0aDogMjAwcHg7XG4gIGhlaWdodDogMTMwcHg7XG4gIGJvcmRlci1yYWRpdXM6IHZhcigtLWJvcmRlci1yYWRpdXMtbWQpO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBib3JkZXI6IDJweCBzb2xpZCB2YXIoLS1hY2NlbnQtY29sb3IpO1xuICBib3gtc2hhZG93OiAwIDAgMjBweCByZ2JhKDAsIDI0NywgMjU1LCAwLjMpO1xuICB6LWluZGV4OiAxMDtcbiAgdHJhbnNpdGlvbjogYWxsIHZhcigtLXRyYW5zaXRpb24tbWVkaXVtKTtcbn1cblxuLmxvY2FsLXZpZGVvLXdyYXBwZXI6aG92ZXIge1xuICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xuICBib3gtc2hhZG93OiAwIDAgMzBweCByZ2JhKDAsIDI0NywgMjU1LCAwLjUpO1xufVxuXG4ubG9jYWwtdmlkZW8td3JhcHBlcjo6YWZ0ZXIge1xuICBjb250ZW50OiBcIlwiO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIGJvdHRvbTogMDtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC41KTtcbiAgYm9yZGVyLXJhZGl1czogdmFyKC0tYm9yZGVyLXJhZGl1cy1tZCk7XG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xufVxuXG4ubG9jYWwtdmlkZW8ge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgdHJhbnNmb3JtOiBzY2FsZVgoLTEpOyAvKiBFZmZldCBtaXJvaXIgKi9cbn1cblxuLyogQXBwZWwgYXVkaW8gKi9cbi5hdWRpby1jYWxsLWluZm8ge1xuICBwYWRkaW5nOiAzMHB4O1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIHdpZHRoOiAzNTBweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KFxuICAgIDEzNWRlZyxcbiAgICByZ2JhKDAsIDI0NywgMjU1LCAwLjA1KSxcbiAgICByZ2JhKDE1NywgNzgsIDIyMSwgMC4wNSlcbiAgKTtcbn1cblxuLnBhcnRpY2lwYW50LWF2YXRhciB7XG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cblxuLnBhcnRpY2lwYW50LWF2YXRhcjo6YmVmb3JlIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDUwJTtcbiAgbGVmdDogNTAlO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKTtcbiAgd2lkdGg6IDEwMHB4O1xuICBoZWlnaHQ6IDEwMHB4O1xuICBiYWNrZ3JvdW5kOiByYWRpYWwtZ3JhZGllbnQoY2lyY2xlLCB2YXIoLS1hY2NlbnQtY29sb3IpIDAlLCB0cmFuc3BhcmVudCA3MCUpO1xuICBvcGFjaXR5OiAwLjM7XG4gIGZpbHRlcjogYmx1cigyMHB4KTtcbiAgYW5pbWF0aW9uOiBwdWxzZSAzcyBpbmZpbml0ZTtcbiAgei1pbmRleDogLTE7XG59XG5cbi5wYXJ0aWNpcGFudC1hdmF0YXIgLmF2YXRhci1pbWcge1xuICB3aWR0aDogMTAwcHg7XG4gIGhlaWdodDogMTAwcHg7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgYm9yZGVyOiAzcHggc29saWQgdmFyKC0tYWNjZW50LWNvbG9yKTtcbiAgYm94LXNoYWRvdzogMCAwIDIwcHggcmdiYSgwLCAyNDcsIDI1NSwgMC4zKTtcbn1cblxuLyogQ29udHLDg8K0bGVzIGQnYXBwZWwgKi9cbi5jYWxsLWNvbnRyb2xzIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIHBhZGRpbmc6IDIwcHg7XG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4zKTtcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xuICBib3JkZXItdG9wOiAxcHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC4xKTtcbn1cblxuLnZpZGVvLWNhbGwgLmNhbGwtY29udHJvbHMge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGJvdHRvbTogMDtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIHotaW5kZXg6IDEwO1xufVxuXG4uY29udHJvbC1idG4sXG4uZW5kLWNhbGwtYnRuIHtcbiAgd2lkdGg6IDYwcHg7XG4gIGhlaWdodDogNjBweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBib3JkZXI6IG5vbmU7XG4gIG1hcmdpbjogMCAxMnB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgdmFyKC0tdHJhbnNpdGlvbi1mYXN0KTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuXG4uY29udHJvbC1idG46OmJlZm9yZSxcbi5lbmQtY2FsbC1idG46OmJlZm9yZSB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgYm90dG9tOiAwO1xuICBiYWNrZ3JvdW5kOiByYWRpYWwtZ3JhZGllbnQoXG4gICAgY2lyY2xlIGF0IGNlbnRlcixcbiAgICByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgMCUsXG4gICAgdHJhbnNwYXJlbnQgNzAlXG4gICk7XG4gIG9wYWNpdHk6IDA7XG4gIHRyYW5zaXRpb246IG9wYWNpdHkgdmFyKC0tdHJhbnNpdGlvbi1mYXN0KTtcbn1cblxuLmNvbnRyb2wtYnRuOmhvdmVyOjpiZWZvcmUsXG4uZW5kLWNhbGwtYnRuOmhvdmVyOjpiZWZvcmUge1xuICBvcGFjaXR5OiAxO1xufVxuXG4uY29udHJvbC1idG4ge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDI0NywgMjU1LCAwLjEpO1xuICBjb2xvcjogdmFyKC0tdGV4dC1saWdodCk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjQ3LCAyNTUsIDAuMyk7XG59XG5cbi5jb250cm9sLWJ0bi5hY3RpdmUge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoXG4gICAgMTM1ZGVnLFxuICAgIHZhcigtLWFjY2VudC1jb2xvciksXG4gICAgdmFyKC0tc2Vjb25kYXJ5LWNvbG9yKVxuICApO1xuICBib3JkZXI6IG5vbmU7XG4gIGJveC1zaGFkb3c6IDAgMCAxNXB4IHJnYmEoMCwgMjQ3LCAyNTUsIDAuNSk7XG59XG5cbi5jb250cm9sLWJ0bjpob3ZlciB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtM3B4KTtcbiAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMyk7XG59XG5cbi5jb250cm9sLWJ0bi5hY3RpdmU6aG92ZXIge1xuICBib3gtc2hhZG93OiAwIDAgMjBweCByZ2JhKDAsIDI0NywgMjU1LCAwLjcpO1xufVxuXG4uZW5kLWNhbGwtYnRuIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmMzU0NywgI2ZmNTI1Mik7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgYm94LXNoYWRvdzogMCAwIDE1cHggcmdiYSgyNTUsIDUzLCA3MSwgMC4zKTtcbn1cblxuLmVuZC1jYWxsLWJ0bjpob3ZlciB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtM3B4KTtcbiAgYm94LXNoYWRvdzogMCAwIDIwcHggcmdiYSgyNTUsIDUzLCA3MSwgMC41KTtcbn1cblxuLmNvbnRyb2wtYnRuIGksXG4uZW5kLWNhbGwtYnRuIGkge1xuICBmb250LXNpemU6IDIycHg7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgei1pbmRleDogMjtcbn1cblxuQGtleWZyYW1lcyBwdWxzZSB7XG4gIDAlIHtcbiAgICBvcGFjaXR5OiAwLjM7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgwLjk1KTtcbiAgfVxuICA1MCUge1xuICAgIG9wYWNpdHk6IDAuNjtcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xuICB9XG4gIDEwMCUge1xuICAgIG9wYWNpdHk6IDAuMztcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDAuOTUpO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CallType", "CallStatus", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ctx_r7", "getOtherParticipantAvatar", "ɵɵsanitizeUrl", "getOtherParticipantName", "ɵɵtextInterpolate", "ɵɵtemplate", "ActiveCallComponent_div_0_div_7_div_4_Template", "ActiveCallComponent_div_0_div_7_div_8_Template", "ctx_r1", "hasLocalVideo", "hasRemoteVideo", "ɵɵstyleProp", "i_r9", "ActiveCallComponent_div_0_div_8_div_9_Template", "ctx_r2", "ɵɵtextInterpolate1", "audioLevels", "ɵɵlistener", "ActiveCallComponent_div_0_button_14_Template_button_click_0_listener", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "toggleCamera", "ɵɵpureFunction2", "_c2", "ctx_r3", "isVideoMuted", "ActiveCallComponent_div_0_div_7_Template", "ActiveCallComponent_div_0_div_8_Template", "ActiveCallComponent_div_0_Template_button_click_11_listener", "_r13", "ctx_r12", "toggleMicrophone", "ActiveCallComponent_div_0_button_14_Template", "ActiveCallComponent_div_0_Template_button_click_15_listener", "ctx_r14", "toggleSpeaker", "ActiveCallComponent_div_0_Template_button_click_18_listener", "ctx_r15", "endCall", "ctx_r0", "getCallStatusText", "isVideoCall", "isAudioMuted", "_c3", "isSpeakerOn", "ActiveCallComponent", "constructor", "messageService", "callService", "logger", "activeCall", "callDuration", "callStartTime", "subscriptions", "ngOnInit", "activeCallSub", "activeCall$", "subscribe", "call", "previousCall", "console", "log", "status", "CONNECTED", "id", "startCallTimer", "setupMediaStreams", "stopCallTimer", "push", "localStream", "getLocalStream", "localVideo", "nativeElement", "srcObject", "getVideoTracks", "length", "startAudioVisualizer", "getAudioEnabled", "getVideoEnabled", "Array", "from", "Math", "random", "audioVisualizerInterval", "setInterval", "map", "stopAudioVisualizer", "clearInterval", "ngAfterViewInit", "Date", "durationInterval", "now", "diff", "floor", "getTime", "minutes", "toString", "padStart", "seconds", "debug", "callId", "next", "result", "success", "error", "newAudioState", "toggleAudio", "enabled", "muted", "toggleMedia", "undefined", "type", "AUDIO", "newVideoState", "toggleVideo", "remoteVideo", "volume", "on", "currentUserId", "localStorage", "getItem", "isCurrentUserCaller", "caller", "recipient", "username", "avatar", "image", "RINGING", "ENDED", "MISSED", "REJECTED", "FAILED", "isCallConnected", "isCallRinging", "VIDEO", "VIDEO_ONLY", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "CallService", "i3", "LoggerService", "selectors", "viewQuery", "ActiveCallComponent_Query", "rf", "ctx", "ActiveCallComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.html"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ViewChild,\n  ElementRef,\n  AfterViewInit,\n} from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { Call, CallType, CallStatus } from '../../models/message.model';\nimport { MessageService } from '../../services/message.service';\nimport { CallService } from '../../services/call.service';\nimport { LoggerService } from '../../services/logger.service';\n\n@Component({\n  selector: 'app-active-call',\n  templateUrl: './active-call.component.html',\n  styleUrls: ['./active-call.component.css'],\n})\nexport class ActiveCallComponent implements OnInit, OnDestroy, AfterViewInit {\n  @ViewChild('localVideo') localVideo!: ElementRef<HTMLVideoElement>;\n  @ViewChild('remoteVideo') remoteVideo!: ElementRef<HTMLVideoElement>;\n\n  activeCall: Call | null = null;\n  callDuration: string = '00:00';\n  isAudioMuted: boolean = false;\n  isVideoMuted: boolean = false;\n  isSpeakerOn: boolean = true;\n\n  // Propriétés pour l'interface\n  hasLocalVideo: boolean = false;\n  hasRemoteVideo: boolean = false;\n  audioLevels: number[] = [];\n\n  private durationInterval: any;\n  private callStartTime: Date | null = null;\n  private subscriptions: Subscription[] = [];\n  private audioVisualizerInterval: any;\n\n  // Exposer les énums au template\n  CallType = CallType;\n  CallStatus = CallStatus;\n\n  constructor(\n    private messageService: MessageService,\n    private callService: CallService,\n    private logger: LoggerService\n  ) {}\n\n  ngOnInit(): void {\n    // S'abonner à l'appel actif\n    const activeCallSub = this.callService.activeCall$.subscribe((call) => {\n      const previousCall = this.activeCall;\n      this.activeCall = call;\n\n      console.log('📞 [ActiveCall] Active call updated:', call);\n\n      if (call && call.status === CallStatus.CONNECTED) {\n        if (!previousCall || previousCall.id !== call.id) {\n          // Nouvel appel connecté\n          console.log(\n            '✅ [ActiveCall] Call connected, starting timer and setting up media'\n          );\n          this.startCallTimer();\n          this.setupMediaStreams();\n        }\n      } else if (!call || call.status !== CallStatus.CONNECTED) {\n        // Appel terminé ou non connecté\n        this.stopCallTimer();\n      }\n    });\n\n    this.subscriptions.push(activeCallSub);\n  }\n\n  /**\n   * Configure les flux média pour l'appel\n   */\n  private setupMediaStreams(): void {\n    console.log('🎥 [ActiveCall] Setting up media streams...');\n\n    // Configurer le flux local\n    const localStream = this.callService.getLocalStream();\n    if (localStream && this.localVideo?.nativeElement) {\n      console.log('📹 [ActiveCall] Setting local video stream');\n      this.localVideo.nativeElement.srcObject = localStream;\n      this.hasLocalVideo = localStream.getVideoTracks().length > 0;\n    }\n\n    // Initialiser le visualiseur audio pour les appels audio\n    if (!this.isVideoCall()) {\n      this.startAudioVisualizer();\n    }\n\n    // Mettre à jour les états des contrôles\n    this.isAudioMuted = !this.callService.getAudioEnabled();\n    this.isVideoMuted = !this.callService.getVideoEnabled();\n  }\n\n  /**\n   * Démarre le visualiseur audio\n   */\n  private startAudioVisualizer(): void {\n    // Générer des niveaux audio animés\n    this.audioLevels = Array.from(\n      { length: 15 },\n      () => Math.random() * 30 + 10\n    );\n\n    this.audioVisualizerInterval = setInterval(() => {\n      this.audioLevels = this.audioLevels.map(() => Math.random() * 40 + 5);\n    }, 150);\n  }\n\n  /**\n   * Arrête le visualiseur audio\n   */\n  private stopAudioVisualizer(): void {\n    if (this.audioVisualizerInterval) {\n      clearInterval(this.audioVisualizerInterval);\n      this.audioVisualizerInterval = null;\n    }\n  }\n\n  ngAfterViewInit(): void {\n    // Configurer les éléments vidéo après le rendu du composant\n    if (this.activeCall && this.activeCall.status === CallStatus.CONNECTED) {\n      this.setupMediaStreams();\n    }\n  }\n\n  // Démarrer le minuteur d'appel\n  private startCallTimer(): void {\n    this.callStartTime = new Date();\n    this.stopCallTimer(); // Arrêter tout minuteur existant\n\n    this.durationInterval = setInterval(() => {\n      if (!this.callStartTime) return;\n\n      const now = new Date();\n      const diff = Math.floor(\n        (now.getTime() - this.callStartTime.getTime()) / 1000\n      );\n\n      const minutes = Math.floor(diff / 60)\n        .toString()\n        .padStart(2, '0');\n      const seconds = (diff % 60).toString().padStart(2, '0');\n\n      this.callDuration = `${minutes}:${seconds}`;\n    }, 1000);\n  }\n\n  // Arrêter le minuteur d'appel\n  private stopCallTimer(): void {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n  }\n\n  // Terminer l'appel\n  endCall(): void {\n    if (!this.activeCall) {\n      return;\n    }\n\n    this.logger.debug('Ending call', { callId: this.activeCall.id });\n\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: (result) => {\n        this.logger.debug('Call ended successfully', {\n          callId: this.activeCall?.id,\n          success: result.success,\n        });\n      },\n      error: (error) => {\n        this.logger.error('Error ending call', error);\n      },\n    });\n  }\n\n  // Basculer le micro\n  toggleMicrophone(): void {\n    if (!this.activeCall) {\n      return;\n    }\n\n    console.log('🎤 [ActiveCall] Toggling microphone...');\n\n    // Basculer localement d'abord\n    const newAudioState = this.callService.toggleAudio();\n    this.isAudioMuted = !newAudioState;\n\n    console.log('🎤 [ActiveCall] Audio toggled:', {\n      enabled: newAudioState,\n      muted: this.isAudioMuted,\n    });\n\n    // Optionnel : Notifier le serveur\n    this.callService\n      .toggleMedia(this.activeCall.id, undefined, newAudioState)\n      .subscribe({\n        next: (result) => {\n          console.log('✅ [ActiveCall] Microphone toggle sent to server');\n        },\n        error: (error) => {\n          console.error(\n            '❌ [ActiveCall] Error toggling microphone on server:',\n            error\n          );\n          // Continuer même si le serveur échoue\n        },\n      });\n  }\n\n  // Basculer la caméra\n  toggleCamera(): void {\n    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) {\n      return;\n    }\n\n    console.log('📹 [ActiveCall] Toggling camera...');\n\n    // Basculer localement d'abord\n    const newVideoState = this.callService.toggleVideo();\n    this.isVideoMuted = !newVideoState;\n\n    console.log('📹 [ActiveCall] Video toggled:', {\n      enabled: newVideoState,\n      muted: this.isVideoMuted,\n    });\n\n    // Optionnel : Notifier le serveur\n    this.callService\n      .toggleMedia(this.activeCall.id, newVideoState, undefined)\n      .subscribe({\n        next: (result) => {\n          console.log('✅ [ActiveCall] Camera toggle sent to server');\n        },\n        error: (error) => {\n          console.error(\n            '❌ [ActiveCall] Error toggling camera on server:',\n            error\n          );\n          // Continuer même si le serveur échoue\n        },\n      });\n  }\n\n  // Basculer le haut-parleur\n  toggleSpeaker(): void {\n    this.isSpeakerOn = !this.isSpeakerOn;\n\n    if (this.remoteVideo?.nativeElement) {\n      this.remoteVideo.nativeElement.volume = this.isSpeakerOn ? 1 : 0;\n    }\n\n    this.logger.debug('Speaker toggled', { on: this.isSpeakerOn });\n  }\n\n  // Obtenir le nom de l'autre participant\n  getOtherParticipantName(): string {\n    if (!this.activeCall) {\n      return '';\n    }\n\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n\n    return isCurrentUserCaller\n      ? this.activeCall.recipient.username\n      : this.activeCall.caller.username;\n  }\n\n  // Obtenir l'avatar de l'autre participant\n  getOtherParticipantAvatar(): string {\n    if (!this.activeCall) {\n      return 'assets/images/default-avatar.png';\n    }\n\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n\n    const avatar = isCurrentUserCaller\n      ? this.activeCall.recipient.image\n      : this.activeCall.caller.image;\n\n    return avatar || 'assets/images/default-avatar.png';\n  }\n\n  // Obtenir le statut de l'appel sous forme de texte\n  getCallStatusText(): string {\n    if (!this.activeCall) {\n      return '';\n    }\n\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Appel en cours...';\n      case CallStatus.CONNECTED:\n        return this.callDuration;\n      case CallStatus.ENDED:\n        return 'Appel terminé';\n      case CallStatus.MISSED:\n        return 'Appel manqué';\n      case CallStatus.REJECTED:\n        return 'Appel rejeté';\n      case CallStatus.FAILED:\n        return \"Échec de l'appel\";\n      default:\n        return '';\n    }\n  }\n\n  // Vérifier si l'appel est connecté\n  isCallConnected(): boolean {\n    return this.activeCall?.status === CallStatus.CONNECTED;\n  }\n\n  // Vérifier si l'appel est en cours de sonnerie\n  isCallRinging(): boolean {\n    return this.activeCall?.status === CallStatus.RINGING;\n  }\n\n  // Vérifier si l'appel est un appel vidéo\n  isVideoCall(): boolean {\n    return (\n      this.activeCall?.type === CallType.VIDEO ||\n      this.activeCall?.type === CallType.VIDEO_ONLY\n    );\n  }\n\n  ngOnDestroy(): void {\n    // Nettoyer les abonnements et les minuteurs\n    this.stopCallTimer();\n    this.stopAudioVisualizer();\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n}\n", "<!-- Interface d'appel simplifiée et fonctionnelle -->\n<div\n  *ngIf=\"activeCall\"\n  class=\"fixed inset-0 z-50 bg-gradient-to-br from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] flex flex-col\"\n>\n  <!-- Header avec informations d'appel -->\n  <div class=\"flex-shrink-0 p-6 text-center\">\n    <div\n      class=\"inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-black/30 backdrop-blur-sm border border-blue-500/20\"\n    >\n      <div class=\"w-2 h-2 rounded-full bg-blue-400 animate-pulse\"></div>\n      <span class=\"text-white text-sm font-medium\">{{\n        getCallStatusText()\n      }}</span>\n    </div>\n  </div>\n\n  <!-- Zone principale d'appel -->\n  <div class=\"flex-1 flex flex-col items-center justify-center px-6\">\n    <!-- Appel vidéo -->\n    <div *ngIf=\"isVideoCall()\" class=\"w-full max-w-4xl mx-auto relative\">\n      <!-- Vidéo locale (grande) -->\n      <div\n        class=\"relative w-full aspect-video rounded-2xl overflow-hidden bg-gray-900 border border-blue-500/20\"\n      >\n        <video\n          #localVideo\n          autoplay\n          playsinline\n          muted\n          class=\"w-full h-full object-cover\"\n        ></video>\n\n        <!-- Overlay si pas de vidéo -->\n        <div\n          *ngIf=\"!hasLocalVideo\"\n          class=\"absolute inset-0 flex items-center justify-center bg-gray-900\"\n        >\n          <div class=\"text-center\">\n            <div\n              class=\"w-24 h-24 rounded-full bg-blue-500/20 flex items-center justify-center mx-auto mb-4\"\n            >\n              <i class=\"fas fa-user text-3xl text-blue-400\"></i>\n            </div>\n            <p class=\"text-white text-lg\">Vous</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Vidéo distante (petite, en overlay) -->\n      <div\n        class=\"absolute top-4 right-4 w-48 h-36 rounded-xl overflow-hidden bg-gray-800 border border-blue-500/30\"\n      >\n        <video\n          #remoteVideo\n          autoplay\n          playsinline\n          class=\"w-full h-full object-cover\"\n        ></video>\n\n        <!-- Avatar si pas de vidéo distante -->\n        <div\n          *ngIf=\"!hasRemoteVideo\"\n          class=\"absolute inset-0 flex items-center justify-center bg-gray-800\"\n        >\n          <div class=\"text-center\">\n            <img\n              [src]=\"getOtherParticipantAvatar()\"\n              [alt]=\"getOtherParticipantName()\"\n              class=\"w-16 h-16 rounded-full object-cover mx-auto mb-2 border-2 border-blue-400\"\n            />\n            <p class=\"text-white text-sm\">{{ getOtherParticipantName() }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Appel audio -->\n    <div *ngIf=\"!isVideoCall()\" class=\"text-center\">\n      <!-- Avatar de l'autre participant -->\n      <div class=\"relative mb-8\">\n        <div\n          class=\"w-40 h-40 rounded-full overflow-hidden border-4 border-blue-500/50 mx-auto\"\n        >\n          <img\n            [src]=\"getOtherParticipantAvatar()\"\n            [alt]=\"getOtherParticipantName()\"\n            class=\"w-full h-full object-cover\"\n          />\n        </div>\n\n        <!-- Effet de pulsation -->\n        <div\n          class=\"absolute inset-0 rounded-full border-4 border-blue-400/30 animate-ping\"\n        ></div>\n        <div\n          class=\"absolute -inset-4 rounded-full border-2 border-blue-400/20 animate-ping\"\n          style=\"animation-delay: 0.5s\"\n        ></div>\n      </div>\n\n      <!-- Nom du participant -->\n      <h2 class=\"text-3xl font-bold text-white mb-2\">\n        {{ getOtherParticipantName() }}\n      </h2>\n\n      <!-- Visualiseur audio -->\n      <div class=\"flex items-center justify-center space-x-1 h-12 mb-8\">\n        <div\n          *ngFor=\"let i of audioLevels\"\n          class=\"w-1 bg-gradient-to-t from-blue-600 to-blue-400 rounded-full transition-all duration-150\"\n          [style.height.px]=\"i\"\n        ></div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Contrôles d'appel simplifiés -->\n  <div class=\"flex-shrink-0 p-6\">\n    <div class=\"flex justify-center space-x-6\">\n      <!-- Bouton Microphone -->\n      <button\n        (click)=\"toggleMicrophone()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 relative group\"\n        [ngClass]=\"{\n          'bg-blue-500 text-white shadow-lg shadow-blue-500/25': !isAudioMuted,\n          'bg-red-500 text-white shadow-lg shadow-red-500/25': isAudioMuted\n        }\"\n      >\n        <i\n          class=\"fas text-xl\"\n          [ngClass]=\"isAudioMuted ? 'fa-microphone-slash' : 'fa-microphone'\"\n        ></i>\n        <div\n          class=\"absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity\"\n        ></div>\n      </button>\n\n      <!-- Bouton Caméra (seulement pour appels vidéo) -->\n      <button\n        *ngIf=\"isVideoCall()\"\n        (click)=\"toggleCamera()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 relative group\"\n        [ngClass]=\"{\n          'bg-blue-500 text-white shadow-lg shadow-blue-500/25': !isVideoMuted,\n          'bg-red-500 text-white shadow-lg shadow-red-500/25': isVideoMuted\n        }\"\n      >\n        <i\n          class=\"fas text-xl\"\n          [ngClass]=\"isVideoMuted ? 'fa-video-slash' : 'fa-video'\"\n        ></i>\n        <div\n          class=\"absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity\"\n        ></div>\n      </button>\n\n      <!-- Bouton Haut-parleur -->\n      <button\n        (click)=\"toggleSpeaker()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 relative group\"\n        [ngClass]=\"{\n          'bg-blue-500 text-white shadow-lg shadow-blue-500/25': isSpeakerOn,\n          'bg-gray-600 text-gray-300 shadow-lg shadow-gray-600/25': !isSpeakerOn\n        }\"\n      >\n        <i\n          class=\"fas text-xl\"\n          [ngClass]=\"isSpeakerOn ? 'fa-volume-up' : 'fa-volume-mute'\"\n        ></i>\n        <div\n          class=\"absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity\"\n        ></div>\n      </button>\n\n      <!-- Bouton Raccrocher -->\n      <button\n        (click)=\"endCall()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center bg-red-500 text-white transition-all duration-200 relative group shadow-lg shadow-red-500/25\"\n      >\n        <i class=\"fas fa-phone-slash text-xl\"></i>\n        <div\n          class=\"absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity\"\n        ></div>\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AASA,SAAeA,QAAQ,EAAEC,UAAU,QAAQ,4BAA4B;;;;;;;;;;ICyB/DC,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAAkD;IACpDF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAI,MAAA,WAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAiB1CH,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,SAAA,cAIE;IACFF,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAI,MAAA,GAA+B;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAJ/DH,EAAA,CAAAK,SAAA,GAAmC;IAAnCL,EAAA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,yBAAA,IAAAR,EAAA,CAAAS,aAAA,CAAmC,QAAAF,MAAA,CAAAG,uBAAA;IAIPV,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAW,iBAAA,CAAAJ,MAAA,CAAAG,uBAAA,GAA+B;;;;;IAnDrEV,EAAA,CAAAC,cAAA,cAAqE;IAKjED,EAAA,CAAAE,SAAA,oBAMS;IAGTF,EAAA,CAAAY,UAAA,IAAAC,8CAAA,kBAYM;IACRb,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAE,SAAA,oBAKS;IAGTF,EAAA,CAAAY,UAAA,IAAAE,8CAAA,kBAYM;IACRd,EAAA,CAAAG,YAAA,EAAM;;;;IAvCDH,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,UAAAS,MAAA,CAAAC,aAAA,CAAoB;IA2BpBhB,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAM,UAAA,UAAAS,MAAA,CAAAE,cAAA,CAAqB;;;;;IA8CxBjB,EAAA,CAAAE,SAAA,cAIO;;;;IADLF,EAAA,CAAAkB,WAAA,WAAAC,IAAA,OAAqB;;;;;IAjC3BnB,EAAA,CAAAC,cAAA,cAAgD;IAM1CD,EAAA,CAAAE,SAAA,cAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAE,SAAA,cAEO;IAKTF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAY,UAAA,IAAAQ,8CAAA,kBAIO;IACTpB,EAAA,CAAAG,YAAA,EAAM;;;;IA5BAH,EAAA,CAAAK,SAAA,GAAmC;IAAnCL,EAAA,CAAAM,UAAA,QAAAe,MAAA,CAAAb,yBAAA,IAAAR,EAAA,CAAAS,aAAA,CAAmC,QAAAY,MAAA,CAAAX,uBAAA;IAkBvCV,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAsB,kBAAA,MAAAD,MAAA,CAAAX,uBAAA,QACF;IAKkBV,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAM,UAAA,YAAAe,MAAA,CAAAE,WAAA,CAAc;;;;;;;;;;;;IA8BhCvB,EAAA,CAAAC,cAAA,iBAQC;IANCD,EAAA,CAAAwB,UAAA,mBAAAC,qEAAA;MAAAzB,EAAA,CAAA0B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAF,OAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAOxB/B,EAAA,CAAAE,SAAA,YAGK;IAIPF,EAAA,CAAAG,YAAA,EAAS;;;;IAZPH,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAgC,eAAA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,YAAA,EAAAD,MAAA,CAAAC,YAAA,EAGE;IAIAnC,EAAA,CAAAK,SAAA,GAAwD;IAAxDL,EAAA,CAAAM,UAAA,YAAA4B,MAAA,CAAAC,YAAA,iCAAwD;;;;;;;;;;;;IArJlEnC,EAAA,CAAAC,cAAA,aAGC;IAMKD,EAAA,CAAAE,SAAA,aAAkE;IAClEF,EAAA,CAAAC,cAAA,cAA6C;IAAAD,EAAA,CAAAI,MAAA,GAE3C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAKbH,EAAA,CAAAC,cAAA,aAAmE;IAEjED,EAAA,CAAAY,UAAA,IAAAwB,wCAAA,iBAuDM;IAGNpC,EAAA,CAAAY,UAAA,IAAAyB,wCAAA,kBAoCM;IACRrC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,aAA+B;IAIzBD,EAAA,CAAAwB,UAAA,mBAAAc,4DAAA;MAAAtC,EAAA,CAAA0B,aAAA,CAAAa,IAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAU,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAO5BzC,EAAA,CAAAE,SAAA,aAGK;IAIPF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAY,UAAA,KAAA8B,4CAAA,qBAgBS;IAGT1C,EAAA,CAAAC,cAAA,kBAOC;IANCD,EAAA,CAAAwB,UAAA,mBAAAmB,4DAAA;MAAA3C,EAAA,CAAA0B,aAAA,CAAAa,IAAA;MAAA,MAAAK,OAAA,GAAA5C,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAc,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAOzB7C,EAAA,CAAAE,SAAA,aAGK;IAIPF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAwB,UAAA,mBAAAsB,4DAAA;MAAA9C,EAAA,CAAA0B,aAAA,CAAAa,IAAA;MAAA,MAAAQ,OAAA,GAAA/C,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAiB,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAGnBhD,EAAA,CAAAE,SAAA,aAA0C;IAI5CF,EAAA,CAAAG,YAAA,EAAS;;;;IA7KoCH,EAAA,CAAAK,SAAA,GAE3C;IAF2CL,EAAA,CAAAW,iBAAA,CAAAsC,MAAA,CAAAC,iBAAA,GAE3C;IAOElD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,SAAA2C,MAAA,CAAAE,WAAA,GAAmB;IA0DnBnD,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,UAAA2C,MAAA,CAAAE,WAAA,GAAoB;IA8CtBnD,EAAA,CAAAK,SAAA,GAGE;IAHFL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAgC,eAAA,IAAAC,GAAA,GAAAgB,MAAA,CAAAG,YAAA,EAAAH,MAAA,CAAAG,YAAA,EAGE;IAIApD,EAAA,CAAAK,SAAA,GAAkE;IAAlEL,EAAA,CAAAM,UAAA,YAAA2C,MAAA,CAAAG,YAAA,2CAAkE;IASnEpD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,SAAA2C,MAAA,CAAAE,WAAA,GAAmB;IAqBpBnD,EAAA,CAAAK,SAAA,GAGE;IAHFL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAgC,eAAA,KAAAqB,GAAA,EAAAJ,MAAA,CAAAK,WAAA,GAAAL,MAAA,CAAAK,WAAA,EAGE;IAIAtD,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAM,UAAA,YAAA2C,MAAA,CAAAK,WAAA,qCAA2D;;;ADrJrE,OAAM,MAAOC,mBAAmB;EAwB9BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,MAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAvBhB,KAAAC,UAAU,GAAgB,IAAI;IAC9B,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAT,YAAY,GAAY,KAAK;IAC7B,KAAAjB,YAAY,GAAY,KAAK;IAC7B,KAAAmB,WAAW,GAAY,IAAI;IAE3B;IACA,KAAAtC,aAAa,GAAY,KAAK;IAC9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAM,WAAW,GAAa,EAAE;IAGlB,KAAAuC,aAAa,GAAgB,IAAI;IACjC,KAAAC,aAAa,GAAmB,EAAE;IAG1C;IACA,KAAAjE,QAAQ,GAAGA,QAAQ;IACnB,KAAAC,UAAU,GAAGA,UAAU;EAMpB;EAEHiE,QAAQA,CAAA;IACN;IACA,MAAMC,aAAa,GAAG,IAAI,CAACP,WAAW,CAACQ,WAAW,CAACC,SAAS,CAAEC,IAAI,IAAI;MACpE,MAAMC,YAAY,GAAG,IAAI,CAACT,UAAU;MACpC,IAAI,CAACA,UAAU,GAAGQ,IAAI;MAEtBE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEH,IAAI,CAAC;MAEzD,IAAIA,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKzE,UAAU,CAAC0E,SAAS,EAAE;QAChD,IAAI,CAACJ,YAAY,IAAIA,YAAY,CAACK,EAAE,KAAKN,IAAI,CAACM,EAAE,EAAE;UAChD;UACAJ,OAAO,CAACC,GAAG,CACT,oEAAoE,CACrE;UACD,IAAI,CAACI,cAAc,EAAE;UACrB,IAAI,CAACC,iBAAiB,EAAE;;OAE3B,MAAM,IAAI,CAACR,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKzE,UAAU,CAAC0E,SAAS,EAAE;QACxD;QACA,IAAI,CAACI,aAAa,EAAE;;IAExB,CAAC,CAAC;IAEF,IAAI,CAACd,aAAa,CAACe,IAAI,CAACb,aAAa,CAAC;EACxC;EAEA;;;EAGQW,iBAAiBA,CAAA;IACvBN,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAE1D;IACA,MAAMQ,WAAW,GAAG,IAAI,CAACrB,WAAW,CAACsB,cAAc,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,CAACE,UAAU,EAAEC,aAAa,EAAE;MACjDZ,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD,IAAI,CAACU,UAAU,CAACC,aAAa,CAACC,SAAS,GAAGJ,WAAW;MACrD,IAAI,CAAC/D,aAAa,GAAG+D,WAAW,CAACK,cAAc,EAAE,CAACC,MAAM,GAAG,CAAC;;IAG9D;IACA,IAAI,CAAC,IAAI,CAAClC,WAAW,EAAE,EAAE;MACvB,IAAI,CAACmC,oBAAoB,EAAE;;IAG7B;IACA,IAAI,CAAClC,YAAY,GAAG,CAAC,IAAI,CAACM,WAAW,CAAC6B,eAAe,EAAE;IACvD,IAAI,CAACpD,YAAY,GAAG,CAAC,IAAI,CAACuB,WAAW,CAAC8B,eAAe,EAAE;EACzD;EAEA;;;EAGQF,oBAAoBA,CAAA;IAC1B;IACA,IAAI,CAAC/D,WAAW,GAAGkE,KAAK,CAACC,IAAI,CAC3B;MAAEL,MAAM,EAAE;IAAE,CAAE,EACd,MAAMM,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAC9B;IAED,IAAI,CAACC,uBAAuB,GAAGC,WAAW,CAAC,MAAK;MAC9C,IAAI,CAACvE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACwE,GAAG,CAAC,MAAMJ,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACvE,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGQI,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACH,uBAAuB,EAAE;MAChCI,aAAa,CAAC,IAAI,CAACJ,uBAAuB,CAAC;MAC3C,IAAI,CAACA,uBAAuB,GAAG,IAAI;;EAEvC;EAEAK,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAACtC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACY,MAAM,KAAKzE,UAAU,CAAC0E,SAAS,EAAE;MACtE,IAAI,CAACG,iBAAiB,EAAE;;EAE5B;EAEA;EACQD,cAAcA,CAAA;IACpB,IAAI,CAACb,aAAa,GAAG,IAAIqC,IAAI,EAAE;IAC/B,IAAI,CAACtB,aAAa,EAAE,CAAC,CAAC;IAEtB,IAAI,CAACuB,gBAAgB,GAAGN,WAAW,CAAC,MAAK;MACvC,IAAI,CAAC,IAAI,CAAChC,aAAa,EAAE;MAEzB,MAAMuC,GAAG,GAAG,IAAIF,IAAI,EAAE;MACtB,MAAMG,IAAI,GAAGX,IAAI,CAACY,KAAK,CACrB,CAACF,GAAG,CAACG,OAAO,EAAE,GAAG,IAAI,CAAC1C,aAAa,CAAC0C,OAAO,EAAE,IAAI,IAAI,CACtD;MAED,MAAMC,OAAO,GAAGd,IAAI,CAACY,KAAK,CAACD,IAAI,GAAG,EAAE,CAAC,CAClCI,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnB,MAAMC,OAAO,GAAG,CAACN,IAAI,GAAG,EAAE,EAAEI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAEvD,IAAI,CAAC9C,YAAY,GAAG,GAAG4C,OAAO,IAAIG,OAAO,EAAE;IAC7C,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQ/B,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACuB,gBAAgB,EAAE;MACzBH,aAAa,CAAC,IAAI,CAACG,gBAAgB,CAAC;MACpC,IAAI,CAACA,gBAAgB,GAAG,IAAI;;EAEhC;EAEA;EACApD,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACY,UAAU,EAAE;MACpB;;IAGF,IAAI,CAACD,MAAM,CAACkD,KAAK,CAAC,aAAa,EAAE;MAAEC,MAAM,EAAE,IAAI,CAAClD,UAAU,CAACc;IAAE,CAAE,CAAC;IAEhE,IAAI,CAAChB,WAAW,CAACV,OAAO,CAAC,IAAI,CAACY,UAAU,CAACc,EAAE,CAAC,CAACP,SAAS,CAAC;MACrD4C,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAACrD,MAAM,CAACkD,KAAK,CAAC,yBAAyB,EAAE;UAC3CC,MAAM,EAAE,IAAI,CAAClD,UAAU,EAAEc,EAAE;UAC3BuC,OAAO,EAAED,MAAM,CAACC;SACjB,CAAC;MACJ,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACvD,MAAM,CAACuD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEA;EACAzE,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACmB,UAAU,EAAE;MACpB;;IAGFU,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IAErD;IACA,MAAM4C,aAAa,GAAG,IAAI,CAACzD,WAAW,CAAC0D,WAAW,EAAE;IACpD,IAAI,CAAChE,YAAY,GAAG,CAAC+D,aAAa;IAElC7C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5C8C,OAAO,EAAEF,aAAa;MACtBG,KAAK,EAAE,IAAI,CAAClE;KACb,CAAC;IAEF;IACA,IAAI,CAACM,WAAW,CACb6D,WAAW,CAAC,IAAI,CAAC3D,UAAU,CAACc,EAAE,EAAE8C,SAAS,EAAEL,aAAa,CAAC,CACzDhD,SAAS,CAAC;MACT4C,IAAI,EAAGC,MAAM,IAAI;QACf1C,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAChE,CAAC;MACD2C,KAAK,EAAGA,KAAK,IAAI;QACf5C,OAAO,CAAC4C,KAAK,CACX,qDAAqD,EACrDA,KAAK,CACN;QACD;MACF;KACD,CAAC;EACN;EAEA;EACAnF,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC6B,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC6D,IAAI,KAAK3H,QAAQ,CAAC4H,KAAK,EAAE;MAC/D;;IAGFpD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACA,MAAMoD,aAAa,GAAG,IAAI,CAACjE,WAAW,CAACkE,WAAW,EAAE;IACpD,IAAI,CAACzF,YAAY,GAAG,CAACwF,aAAa;IAElCrD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5C8C,OAAO,EAAEM,aAAa;MACtBL,KAAK,EAAE,IAAI,CAACnF;KACb,CAAC;IAEF;IACA,IAAI,CAACuB,WAAW,CACb6D,WAAW,CAAC,IAAI,CAAC3D,UAAU,CAACc,EAAE,EAAEiD,aAAa,EAAEH,SAAS,CAAC,CACzDrD,SAAS,CAAC;MACT4C,IAAI,EAAGC,MAAM,IAAI;QACf1C,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC5D,CAAC;MACD2C,KAAK,EAAGA,KAAK,IAAI;QACf5C,OAAO,CAAC4C,KAAK,CACX,iDAAiD,EACjDA,KAAK,CACN;QACD;MACF;KACD,CAAC;EACN;EAEA;EACArE,aAAaA,CAAA;IACX,IAAI,CAACS,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,IAAI,CAACuE,WAAW,EAAE3C,aAAa,EAAE;MACnC,IAAI,CAAC2C,WAAW,CAAC3C,aAAa,CAAC4C,MAAM,GAAG,IAAI,CAACxE,WAAW,GAAG,CAAC,GAAG,CAAC;;IAGlE,IAAI,CAACK,MAAM,CAACkD,KAAK,CAAC,iBAAiB,EAAE;MAAEkB,EAAE,EAAE,IAAI,CAACzE;IAAW,CAAE,CAAC;EAChE;EAEA;EACA5C,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACkD,UAAU,EAAE;MACpB,OAAO,EAAE;;IAGX;IACA,MAAMoE,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACpD,MAAMC,mBAAmB,GAAG,IAAI,CAACvE,UAAU,CAACwE,MAAM,CAAC1D,EAAE,KAAKsD,aAAa;IAEvE,OAAOG,mBAAmB,GACtB,IAAI,CAACvE,UAAU,CAACyE,SAAS,CAACC,QAAQ,GAClC,IAAI,CAAC1E,UAAU,CAACwE,MAAM,CAACE,QAAQ;EACrC;EAEA;EACA9H,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACoD,UAAU,EAAE;MACpB,OAAO,kCAAkC;;IAG3C;IACA,MAAMoE,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACpD,MAAMC,mBAAmB,GAAG,IAAI,CAACvE,UAAU,CAACwE,MAAM,CAAC1D,EAAE,KAAKsD,aAAa;IAEvE,MAAMO,MAAM,GAAGJ,mBAAmB,GAC9B,IAAI,CAACvE,UAAU,CAACyE,SAAS,CAACG,KAAK,GAC/B,IAAI,CAAC5E,UAAU,CAACwE,MAAM,CAACI,KAAK;IAEhC,OAAOD,MAAM,IAAI,kCAAkC;EACrD;EAEA;EACArF,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACU,UAAU,EAAE;MACpB,OAAO,EAAE;;IAGX,QAAQ,IAAI,CAACA,UAAU,CAACY,MAAM;MAC5B,KAAKzE,UAAU,CAAC0I,OAAO;QACrB,OAAO,mBAAmB;MAC5B,KAAK1I,UAAU,CAAC0E,SAAS;QACvB,OAAO,IAAI,CAACZ,YAAY;MAC1B,KAAK9D,UAAU,CAAC2I,KAAK;QACnB,OAAO,eAAe;MACxB,KAAK3I,UAAU,CAAC4I,MAAM;QACpB,OAAO,cAAc;MACvB,KAAK5I,UAAU,CAAC6I,QAAQ;QACtB,OAAO,cAAc;MACvB,KAAK7I,UAAU,CAAC8I,MAAM;QACpB,OAAO,kBAAkB;MAC3B;QACE,OAAO,EAAE;;EAEf;EAEA;EACAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAAClF,UAAU,EAAEY,MAAM,KAAKzE,UAAU,CAAC0E,SAAS;EACzD;EAEA;EACAsE,aAAaA,CAAA;IACX,OAAO,IAAI,CAACnF,UAAU,EAAEY,MAAM,KAAKzE,UAAU,CAAC0I,OAAO;EACvD;EAEA;EACAtF,WAAWA,CAAA;IACT,OACE,IAAI,CAACS,UAAU,EAAE6D,IAAI,KAAK3H,QAAQ,CAACkJ,KAAK,IACxC,IAAI,CAACpF,UAAU,EAAE6D,IAAI,KAAK3H,QAAQ,CAACmJ,UAAU;EAEjD;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAACrE,aAAa,EAAE;IACpB,IAAI,CAACmB,mBAAmB,EAAE;IAC1B,IAAI,CAACjC,aAAa,CAACoF,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;;;uBAjUW9F,mBAAmB,EAAAvD,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxJ,EAAA,CAAAsJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1J,EAAA,CAAAsJ,iBAAA,CAAAK,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAnBrG,mBAAmB;MAAAsG,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UClBhChK,EAAA,CAAAY,UAAA,IAAAsJ,kCAAA,mBA0LM;;;UAzLHlK,EAAA,CAAAM,UAAA,SAAA2J,GAAA,CAAArG,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}