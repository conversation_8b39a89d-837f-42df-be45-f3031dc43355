{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ActiveCallComponent } from '../active-call/active-call.component';\nimport { IncomingCallComponent } from '../incoming-call/incoming-call.component';\nimport * as i0 from \"@angular/core\";\nexport class CallModule {\n  static {\n    this.ɵfac = function CallModule_Factory(t) {\n      return new (t || CallModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CallModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CallModule, {\n    declarations: [ActiveCallComponent, IncomingCallComponent],\n    imports: [CommonModule],\n    exports: [ActiveCallComponent, IncomingCallComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ActiveCallComponent", "IncomingCallComponent", "CallModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\call\\call.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ActiveCallComponent } from '../active-call/active-call.component';\nimport { IncomingCallComponent } from '../incoming-call/incoming-call.component';\n\n@NgModule({\n  declarations: [\n    ActiveCallComponent,\n    IncomingCallComponent\n  ],\n  imports: [\n    CommonModule\n  ],\n  exports: [\n    ActiveCallComponent,\n    IncomingCallComponent\n  ]\n})\nexport class CallModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,qBAAqB,QAAQ,0CAA0C;;AAehF,OAAM,MAAOC,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAPnBH,YAAY;IAAA;EAAA;;;2EAOHG,UAAU;IAAAC,YAAA,GAXnBH,mBAAmB,EACnBC,qBAAqB;IAAAG,OAAA,GAGrBL,YAAY;IAAAM,OAAA,GAGZL,mBAAmB,EACnBC,qBAAqB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}