{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, catchError, tap, throwError, Subject } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@auth0/angular-jwt\";\nexport class AuthuserService {\n  constructor(http, router, jwtHelper) {\n    this.http = http;\n    this.router = router;\n    this.jwtHelper = jwtHelper;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isInitialized = false;\n    // Subject pour notifier les changements d'authentification\n    this.authChangeSubject = new Subject();\n    this.authChange$ = this.authChangeSubject.asObservable();\n    this.initializeCurrentUser();\n  }\n  // Authentification\n  getUserHeaders() {\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      throw new Error('Token invalide ou expiré');\n    }\n    return new HttpHeaders({\n      Authorization: `Bearer ${token || ''}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  getCommonParams() {\n    return new HttpParams().set('secret', environment.secret).set('client', environment.client);\n  }\n  initializeCurrentUser() {\n    if (this.isInitialized) return;\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      this.isInitialized = true;\n      return;\n    }\n    const decodedToken = this.jwtHelper.decodeToken(token);\n    // Déterminer l'image de profil à utiliser\n    let profileImage = 'assets/images/default-profile.png';\n    // Vérifier d'abord profileImage\n    if (decodedToken.profileImage && decodedToken.profileImage !== 'null' && decodedToken.profileImage !== 'undefined' && decodedToken.profileImage.trim() !== '') {\n      profileImage = decodedToken.profileImage;\n    }\n    // Ensuite vérifier image si profileImage n'est pas valide\n    else if (decodedToken.image && decodedToken.image !== 'null' && decodedToken.image !== 'undefined' && decodedToken.image.trim() !== '') {\n      profileImage = decodedToken.image;\n    }\n    console.log('AuthuserService - Using profile image:', profileImage);\n    const fallbackUser = {\n      _id: decodedToken.id,\n      username: decodedToken.username,\n      fullName: decodedToken.fullName,\n      email: decodedToken.email,\n      role: decodedToken.role,\n      image: profileImage,\n      profileImage: profileImage,\n      isActive: true\n    };\n    this.currentUserSubject.next(fallbackUser);\n    this.isInitialized = true;\n  }\n  // Typage plus strict pour les réponses\n  register(userData) {\n    return this.http.post(`${environment.urlBackend}users/register`, userData).pipe(tap(response => {\n      this.saveToken(response.token);\n      this.setCurrentUser(response.user);\n    }), catchError(this.handleError));\n  }\n  login(credentials) {\n    return this.http.post(`${environment.urlBackend}users/login`, credentials).pipe(tap(response => {\n      this.saveToken(response.token);\n      this.setCurrentUser(response.user);\n    }), catchError(this.handleError));\n  }\n  setCurrentUser(user) {\n    this.currentUserSubject.next(user);\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  getCurrentUserRole() {\n    const currentUser = this.getCurrentUser();\n    if (currentUser && currentUser.role) {\n      return currentUser.role;\n    }\n    // Fallback: try to get role from JWT token\n    const token = this.getToken();\n    if (token) {\n      try {\n        const decodedToken = this.jwtHelper.decodeToken(token);\n        return decodedToken?.role || null;\n      } catch (error) {\n        console.error('Error decoding token for role:', error);\n        return null;\n      }\n    }\n    return null;\n  }\n  saveToken(token) {\n    localStorage.setItem('token', token);\n    this.initializeCurrentUser();\n    // Notifier du changement d'authentification\n    this.authChangeSubject.next({\n      type: 'login',\n      token\n    });\n  }\n  getToken() {\n    return localStorage.getItem('token');\n  }\n  userLoggedIn() {\n    const token = localStorage.getItem('token');\n    if (!token) return false;\n    const decodedToken = this.jwtHelper.decodeToken(token);\n    return !!decodedToken?.role && !this.jwtHelper.isTokenExpired(token);\n  }\n  getCurrentUserId() {\n    const token = localStorage.getItem('token');\n    if (!token) return null;\n    return this.jwtHelper.decodeToken(token)?.id || null;\n  }\n  // Déconnexion plus robuste\n  // Modifiez la méthode logout\n  logout() {\n    return this.http.put(`${environment.urlBackend}users/logout`, {}, {\n      headers: this.getUserHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(() => {\n      this.clearAuthData();\n      this.router.navigate(['/loginuser'], {\n        queryParams: {\n          message: 'Vous avez été déconnecté avec succès'\n        },\n        replaceUrl: true\n      });\n    }), catchError(error => {\n      this.clearAuthData();\n      return throwError(() => error);\n    }));\n  }\n  deactivateSelf() {\n    if (!this.userLoggedIn()) {\n      return throwError(() => new Error('User not logged in'));\n    }\n    return this.http.put(`${environment.urlBackend}users/deactivateself`, {}, {\n      headers: this.getUserHeaders(),\n      params: this.getCommonParams()\n    }).pipe(tap(() => this.clearAuthData()));\n  }\n  clearAuthData() {\n    localStorage.removeItem('token');\n    this.currentUserSubject.next(null);\n    this.isInitialized = false;\n    // Notifier du changement d'authentification\n    this.authChangeSubject.next({\n      type: 'logout',\n      token: null\n    });\n  }\n  handleError(error) {\n    let errorMessage = 'Authentication error';\n    if (error.error instanceof ErrorEvent) {\n      errorMessage = `Client error: ${error.error.message}`;\n    } else {\n      errorMessage = error.error?.message || error.message || 'Unknown authentication error';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function AuthuserService_Factory(t) {\n      return new (t || AuthuserService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.JwtHelperService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthuserService,\n      factory: AuthuserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "BehaviorSubject", "catchError", "tap", "throwError", "Subject", "environment", "AuthuserService", "constructor", "http", "router", "jwtHelper", "currentUserSubject", "currentUser$", "asObservable", "isInitialized", "authChangeSubject", "authChange$", "initializeCurrentUser", "getUserHeaders", "token", "localStorage", "getItem", "isTokenExpired", "Error", "Authorization", "getCommonParams", "set", "secret", "client", "decodedToken", "decodeToken", "profileImage", "trim", "image", "console", "log", "fallbackUser", "_id", "id", "username", "fullName", "email", "role", "isActive", "next", "register", "userData", "post", "urlBackend", "pipe", "response", "saveToken", "setCurrentUser", "user", "handleError", "login", "credentials", "getCurrentUser", "value", "getCurrentUserRole", "currentUser", "getToken", "error", "setItem", "type", "userLoggedIn", "getCurrentUserId", "logout", "put", "headers", "params", "clearAuthData", "navigate", "queryParams", "message", "replaceUrl", "deactivateSelf", "removeItem", "errorMessage", "ErrorEvent", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "i3", "JwtHelperService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\authuser.service.ts"], "sourcesContent": ["import {\n  HttpClient,\n  HttpErrorResponse,\n  HttpHeaders,\n  HttpParams,\n} from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { JwtHelperService } from '@auth0/angular-jwt';\nimport {\n  BehaviorSubject,\n  catchError,\n  Observable,\n  of,\n  shareReplay,\n  tap,\n  throwError,\n  Subject,\n} from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { User } from '../models/user.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class AuthuserService {\n  private currentUserSubject = new BehaviorSubject<any>(null);\n  public currentUser$ = this.currentUserSubject.asObservable();\n  private isInitialized = false;\n\n  // Subject pour notifier les changements d'authentification\n  private authChangeSubject = new Subject<{\n    type: 'login' | 'logout' | 'token_refresh';\n    token: string | null;\n  }>();\n  public authChange$ = this.authChangeSubject.asObservable();\n  constructor(\n    private http: HttpClient,\n    private router: Router,\n    private jwtHelper: JwtHelperService\n  ) {\n    this.initializeCurrentUser();\n  }\n  // Authentification\n  private getUserHeaders(): HttpHeaders {\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      throw new Error('Token invalide ou expiré');\n    }\n    return new HttpHeaders({\n      Authorization: `Bearer ${token || ''}`,\n      'Content-Type': 'application/json',\n    });\n  }\n  private getCommonParams(): HttpParams {\n    return new HttpParams()\n      .set('secret', environment.secret)\n      .set('client', environment.client);\n  }\n\n  initializeCurrentUser(): void {\n    if (this.isInitialized) return;\n\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      this.isInitialized = true;\n      return;\n    }\n\n    const decodedToken = this.jwtHelper.decodeToken(token);\n\n    // Déterminer l'image de profil à utiliser\n    let profileImage = 'assets/images/default-profile.png';\n\n    // Vérifier d'abord profileImage\n    if (\n      decodedToken.profileImage &&\n      decodedToken.profileImage !== 'null' &&\n      decodedToken.profileImage !== 'undefined' &&\n      decodedToken.profileImage.trim() !== ''\n    ) {\n      profileImage = decodedToken.profileImage;\n    }\n    // Ensuite vérifier image si profileImage n'est pas valide\n    else if (\n      decodedToken.image &&\n      decodedToken.image !== 'null' &&\n      decodedToken.image !== 'undefined' &&\n      decodedToken.image.trim() !== ''\n    ) {\n      profileImage = decodedToken.image;\n    }\n\n    console.log('AuthuserService - Using profile image:', profileImage);\n\n    const fallbackUser: User = {\n      _id: decodedToken.id,\n      username: decodedToken.username,\n      fullName: decodedToken.fullName,\n      email: decodedToken.email,\n      role: decodedToken.role,\n      image: profileImage,\n      profileImage: profileImage,\n      isActive: true,\n    };\n\n    this.currentUserSubject.next(fallbackUser);\n    this.isInitialized = true;\n  }\n  // Typage plus strict pour les réponses\n  register(userData: User): Observable<{ user: User; token: string }> {\n    return this.http\n      .post<{ user: User; token: string }>(\n        `${environment.urlBackend}users/register`,\n        userData\n      )\n      .pipe(\n        tap((response) => {\n          this.saveToken(response.token);\n          this.setCurrentUser(response.user);\n        }),\n        catchError(this.handleError)\n      );\n  }\n  login(credentials: {\n    email: string;\n    password: string;\n  }): Observable<{ user: User; token: string }> {\n    return this.http\n      .post<{ user: User; token: string }>(\n        `${environment.urlBackend}users/login`,\n        credentials\n      )\n      .pipe(\n        tap((response) => {\n          this.saveToken(response.token);\n          this.setCurrentUser(response.user);\n        }),\n        catchError(this.handleError)\n      );\n  }\n  setCurrentUser(user: any): void {\n    this.currentUserSubject.next(user);\n  }\n  getCurrentUser(): any {\n    return this.currentUserSubject.value;\n  }\n\n  getCurrentUserRole(): string | null {\n    const currentUser = this.getCurrentUser();\n    if (currentUser && currentUser.role) {\n      return currentUser.role;\n    }\n\n    // Fallback: try to get role from JWT token\n    const token = this.getToken();\n    if (token) {\n      try {\n        const decodedToken = this.jwtHelper.decodeToken(token);\n        return decodedToken?.role || null;\n      } catch (error) {\n        console.error('Error decoding token for role:', error);\n        return null;\n      }\n    }\n\n    return null;\n  }\n  saveToken(token: string): void {\n    localStorage.setItem('token', token);\n    this.initializeCurrentUser();\n\n    // Notifier du changement d'authentification\n    this.authChangeSubject.next({ type: 'login', token });\n  }\n  getToken(): string | null {\n    return localStorage.getItem('token');\n  }\n  userLoggedIn(): boolean {\n    const token = localStorage.getItem('token');\n    if (!token) return false;\n\n    const decodedToken = this.jwtHelper.decodeToken(token);\n    return !!decodedToken?.role && !this.jwtHelper.isTokenExpired(token);\n  }\n  getCurrentUserId(): string | null {\n    const token = localStorage.getItem('token');\n    if (!token) return null;\n    return this.jwtHelper.decodeToken(token)?.id || null;\n  }\n  // Déconnexion plus robuste\n  // Modifiez la méthode logout\n  logout(): Observable<void> {\n    return this.http\n      .put<void>(\n        `${environment.urlBackend}users/logout`,\n        {},\n        {\n          headers: this.getUserHeaders(),\n          params: this.getCommonParams(),\n        }\n      )\n      .pipe(\n        tap(() => {\n          this.clearAuthData();\n          this.router.navigate(['/loginuser'], {\n            queryParams: { message: 'Vous avez été déconnecté avec succès' },\n            replaceUrl: true,\n          });\n        }),\n        catchError((error) => {\n          this.clearAuthData();\n          return throwError(() => error);\n        })\n      );\n  }\n  deactivateSelf(): Observable<void> {\n    if (!this.userLoggedIn()) {\n      return throwError(() => new Error('User not logged in'));\n    }\n\n    return this.http\n      .put<void>(\n        `${environment.urlBackend}users/deactivateself`,\n        {},\n        {\n          headers: this.getUserHeaders(),\n          params: this.getCommonParams(),\n        }\n      )\n      .pipe(tap(() => this.clearAuthData()));\n  }\n  clearAuthData(): void {\n    localStorage.removeItem('token');\n    this.currentUserSubject.next(null);\n    this.isInitialized = false;\n\n    // Notifier du changement d'authentification\n    this.authChangeSubject.next({ type: 'logout', token: null });\n  }\n\n  private handleError(error: HttpErrorResponse): Observable<never> {\n    let errorMessage = 'Authentication error';\n\n    if (error.error instanceof ErrorEvent) {\n      errorMessage = `Client error: ${error.error.message}`;\n    } else {\n      errorMessage =\n        error.error?.message || error.message || 'Unknown authentication error';\n    }\n\n    return throwError(() => new Error(errorMessage));\n  }\n}\n"], "mappings": "AAAA,SAGEA,WAAW,EACXC,UAAU,QACL,sBAAsB;AAI7B,SACEC,eAAe,EACfC,UAAU,EAIVC,GAAG,EACHC,UAAU,EACVC,OAAO,QACF,MAAM;AACb,SAASC,WAAW,QAAQ,8BAA8B;;;;;AAM1D,OAAM,MAAOC,eAAe;EAW1BC,YACUC,IAAgB,EAChBC,MAAc,EACdC,SAA2B;IAF3B,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,SAAS,GAATA,SAAS;IAbX,KAAAC,kBAAkB,GAAG,IAAIX,eAAe,CAAM,IAAI,CAAC;IACpD,KAAAY,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IACpD,KAAAC,aAAa,GAAG,KAAK;IAE7B;IACQ,KAAAC,iBAAiB,GAAG,IAAIX,OAAO,EAGnC;IACG,KAAAY,WAAW,GAAG,IAAI,CAACD,iBAAiB,CAACF,YAAY,EAAE;IAMxD,IAAI,CAACI,qBAAqB,EAAE;EAC9B;EACA;EACQC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACT,SAAS,CAACY,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;;IAE7C,OAAO,IAAIzB,WAAW,CAAC;MACrB0B,aAAa,EAAE,UAAUL,KAAK,IAAI,EAAE,EAAE;MACtC,cAAc,EAAE;KACjB,CAAC;EACJ;EACQM,eAAeA,CAAA;IACrB,OAAO,IAAI1B,UAAU,EAAE,CACpB2B,GAAG,CAAC,QAAQ,EAAErB,WAAW,CAACsB,MAAM,CAAC,CACjCD,GAAG,CAAC,QAAQ,EAAErB,WAAW,CAACuB,MAAM,CAAC;EACtC;EAEAX,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACH,aAAa,EAAE;IAExB,MAAMK,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACT,SAAS,CAACY,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,IAAI,CAACL,aAAa,GAAG,IAAI;MACzB;;IAGF,MAAMe,YAAY,GAAG,IAAI,CAACnB,SAAS,CAACoB,WAAW,CAACX,KAAK,CAAC;IAEtD;IACA,IAAIY,YAAY,GAAG,mCAAmC;IAEtD;IACA,IACEF,YAAY,CAACE,YAAY,IACzBF,YAAY,CAACE,YAAY,KAAK,MAAM,IACpCF,YAAY,CAACE,YAAY,KAAK,WAAW,IACzCF,YAAY,CAACE,YAAY,CAACC,IAAI,EAAE,KAAK,EAAE,EACvC;MACAD,YAAY,GAAGF,YAAY,CAACE,YAAY;;IAE1C;IAAA,KACK,IACHF,YAAY,CAACI,KAAK,IAClBJ,YAAY,CAACI,KAAK,KAAK,MAAM,IAC7BJ,YAAY,CAACI,KAAK,KAAK,WAAW,IAClCJ,YAAY,CAACI,KAAK,CAACD,IAAI,EAAE,KAAK,EAAE,EAChC;MACAD,YAAY,GAAGF,YAAY,CAACI,KAAK;;IAGnCC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEJ,YAAY,CAAC;IAEnE,MAAMK,YAAY,GAAS;MACzBC,GAAG,EAAER,YAAY,CAACS,EAAE;MACpBC,QAAQ,EAAEV,YAAY,CAACU,QAAQ;MAC/BC,QAAQ,EAAEX,YAAY,CAACW,QAAQ;MAC/BC,KAAK,EAAEZ,YAAY,CAACY,KAAK;MACzBC,IAAI,EAAEb,YAAY,CAACa,IAAI;MACvBT,KAAK,EAAEF,YAAY;MACnBA,YAAY,EAAEA,YAAY;MAC1BY,QAAQ,EAAE;KACX;IAED,IAAI,CAAChC,kBAAkB,CAACiC,IAAI,CAACR,YAAY,CAAC;IAC1C,IAAI,CAACtB,aAAa,GAAG,IAAI;EAC3B;EACA;EACA+B,QAAQA,CAACC,QAAc;IACrB,OAAO,IAAI,CAACtC,IAAI,CACbuC,IAAI,CACH,GAAG1C,WAAW,CAAC2C,UAAU,gBAAgB,EACzCF,QAAQ,CACT,CACAG,IAAI,CACH/C,GAAG,CAAEgD,QAAQ,IAAI;MACf,IAAI,CAACC,SAAS,CAACD,QAAQ,CAAC/B,KAAK,CAAC;MAC9B,IAAI,CAACiC,cAAc,CAACF,QAAQ,CAACG,IAAI,CAAC;IACpC,CAAC,CAAC,EACFpD,UAAU,CAAC,IAAI,CAACqD,WAAW,CAAC,CAC7B;EACL;EACAC,KAAKA,CAACC,WAGL;IACC,OAAO,IAAI,CAAChD,IAAI,CACbuC,IAAI,CACH,GAAG1C,WAAW,CAAC2C,UAAU,aAAa,EACtCQ,WAAW,CACZ,CACAP,IAAI,CACH/C,GAAG,CAAEgD,QAAQ,IAAI;MACf,IAAI,CAACC,SAAS,CAACD,QAAQ,CAAC/B,KAAK,CAAC;MAC9B,IAAI,CAACiC,cAAc,CAACF,QAAQ,CAACG,IAAI,CAAC;IACpC,CAAC,CAAC,EACFpD,UAAU,CAAC,IAAI,CAACqD,WAAW,CAAC,CAC7B;EACL;EACAF,cAAcA,CAACC,IAAS;IACtB,IAAI,CAAC1C,kBAAkB,CAACiC,IAAI,CAACS,IAAI,CAAC;EACpC;EACAI,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC9C,kBAAkB,CAAC+C,KAAK;EACtC;EAEAC,kBAAkBA,CAAA;IAChB,MAAMC,WAAW,GAAG,IAAI,CAACH,cAAc,EAAE;IACzC,IAAIG,WAAW,IAAIA,WAAW,CAAClB,IAAI,EAAE;MACnC,OAAOkB,WAAW,CAAClB,IAAI;;IAGzB;IACA,MAAMvB,KAAK,GAAG,IAAI,CAAC0C,QAAQ,EAAE;IAC7B,IAAI1C,KAAK,EAAE;MACT,IAAI;QACF,MAAMU,YAAY,GAAG,IAAI,CAACnB,SAAS,CAACoB,WAAW,CAACX,KAAK,CAAC;QACtD,OAAOU,YAAY,EAAEa,IAAI,IAAI,IAAI;OAClC,CAAC,OAAOoB,KAAK,EAAE;QACd5B,OAAO,CAAC4B,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,OAAO,IAAI;;;IAIf,OAAO,IAAI;EACb;EACAX,SAASA,CAAChC,KAAa;IACrBC,YAAY,CAAC2C,OAAO,CAAC,OAAO,EAAE5C,KAAK,CAAC;IACpC,IAAI,CAACF,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACF,iBAAiB,CAAC6B,IAAI,CAAC;MAAEoB,IAAI,EAAE,OAAO;MAAE7C;IAAK,CAAE,CAAC;EACvD;EACA0C,QAAQA,CAAA;IACN,OAAOzC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACtC;EACA4C,YAAYA,CAAA;IACV,MAAM9C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE,OAAO,KAAK;IAExB,MAAMU,YAAY,GAAG,IAAI,CAACnB,SAAS,CAACoB,WAAW,CAACX,KAAK,CAAC;IACtD,OAAO,CAAC,CAACU,YAAY,EAAEa,IAAI,IAAI,CAAC,IAAI,CAAChC,SAAS,CAACY,cAAc,CAACH,KAAK,CAAC;EACtE;EACA+C,gBAAgBA,CAAA;IACd,MAAM/C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE,OAAO,IAAI;IACvB,OAAO,IAAI,CAACT,SAAS,CAACoB,WAAW,CAACX,KAAK,CAAC,EAAEmB,EAAE,IAAI,IAAI;EACtD;EACA;EACA;EACA6B,MAAMA,CAAA;IACJ,OAAO,IAAI,CAAC3D,IAAI,CACb4D,GAAG,CACF,GAAG/D,WAAW,CAAC2C,UAAU,cAAc,EACvC,EAAE,EACF;MACEqB,OAAO,EAAE,IAAI,CAACnD,cAAc,EAAE;MAC9BoD,MAAM,EAAE,IAAI,CAAC7C,eAAe;KAC7B,CACF,CACAwB,IAAI,CACH/C,GAAG,CAAC,MAAK;MACP,IAAI,CAACqE,aAAa,EAAE;MACpB,IAAI,CAAC9D,MAAM,CAAC+D,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;QACnCC,WAAW,EAAE;UAAEC,OAAO,EAAE;QAAsC,CAAE;QAChEC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC,EACF1E,UAAU,CAAE6D,KAAK,IAAI;MACnB,IAAI,CAACS,aAAa,EAAE;MACpB,OAAOpE,UAAU,CAAC,MAAM2D,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EACAc,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACX,YAAY,EAAE,EAAE;MACxB,OAAO9D,UAAU,CAAC,MAAM,IAAIoB,KAAK,CAAC,oBAAoB,CAAC,CAAC;;IAG1D,OAAO,IAAI,CAACf,IAAI,CACb4D,GAAG,CACF,GAAG/D,WAAW,CAAC2C,UAAU,sBAAsB,EAC/C,EAAE,EACF;MACEqB,OAAO,EAAE,IAAI,CAACnD,cAAc,EAAE;MAC9BoD,MAAM,EAAE,IAAI,CAAC7C,eAAe;KAC7B,CACF,CACAwB,IAAI,CAAC/C,GAAG,CAAC,MAAM,IAAI,CAACqE,aAAa,EAAE,CAAC,CAAC;EAC1C;EACAA,aAAaA,CAAA;IACXnD,YAAY,CAACyD,UAAU,CAAC,OAAO,CAAC;IAChC,IAAI,CAAClE,kBAAkB,CAACiC,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAAC9B,aAAa,GAAG,KAAK;IAE1B;IACA,IAAI,CAACC,iBAAiB,CAAC6B,IAAI,CAAC;MAAEoB,IAAI,EAAE,QAAQ;MAAE7C,KAAK,EAAE;IAAI,CAAE,CAAC;EAC9D;EAEQmC,WAAWA,CAACQ,KAAwB;IAC1C,IAAIgB,YAAY,GAAG,sBAAsB;IAEzC,IAAIhB,KAAK,CAACA,KAAK,YAAYiB,UAAU,EAAE;MACrCD,YAAY,GAAG,iBAAiBhB,KAAK,CAACA,KAAK,CAACY,OAAO,EAAE;KACtD,MAAM;MACLI,YAAY,GACVhB,KAAK,CAACA,KAAK,EAAEY,OAAO,IAAIZ,KAAK,CAACY,OAAO,IAAI,8BAA8B;;IAG3E,OAAOvE,UAAU,CAAC,MAAM,IAAIoB,KAAK,CAACuD,YAAY,CAAC,CAAC;EAClD;;;uBAnOWxE,eAAe,EAAA0E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;aAAfjF,eAAe;MAAAkF,OAAA,EAAflF,eAAe,CAAAmF,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}