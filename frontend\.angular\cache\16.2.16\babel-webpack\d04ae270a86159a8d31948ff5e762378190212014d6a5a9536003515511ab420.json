{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, throwError, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport { CallType } from '../models/message.model';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // État des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // États simples pour les médias\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n    this.preloadSounds();\n    this.initializeSubscriptions();\n  }\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  initializeSubscriptions() {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n    }, 2000);\n  }\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n  }\n  /**\n   * Charge un fichier audio\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      console.error(`Error loading sound ${name}:`, error);\n    }\n  }\n  /**\n   * Joue un son\n   */\n  play(name, loop = false) {\n    if (this.muted) return;\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n      sound.loop = loop;\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(console.error);\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      console.error(`Error playing sound ${name}:`, error);\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      console.error(`Error stopping sound ${name}:`, error);\n    }\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    console.log('🔔 [CallService] Setting up incoming call subscription...');\n    this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION\n    }).subscribe({\n      next: ({\n        data\n      }) => {\n        if (data?.incomingCall) {\n          console.log('📞 [CallService] Incoming call received:', {\n            callId: data.incomingCall.id,\n            callType: data.incomingCall.type,\n            caller: data.incomingCall.caller?.username,\n            conversationId: data.incomingCall.conversationId\n          });\n          this.handleIncomingCall(data.incomingCall);\n        }\n      },\n      error: error => {\n        console.error('❌ [CallService] Error in incoming call subscription:', error);\n        // Réessayer après 5 secondes en cas d'erreur\n        setTimeout(() => {\n          console.log('🔄 [CallService] Retrying incoming call subscription...');\n          this.subscribeToIncomingCalls();\n        }, 5000);\n      }\n    });\n  }\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  reinitializeSubscription() {\n    console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId\n    });\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n    console.log('🔊 [CallService] Ringtone started, call notification sent to UI');\n  }\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    // Pour l'instant, utiliser une offre WebRTC factice\n    const offer = JSON.stringify({\n      type: 'offer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    const variables = {\n      recipientId,\n      callType: callType,\n      callId,\n      offer,\n      conversationId\n    };\n    console.log('📤 [CallService] Sending initiate call mutation:', variables);\n    return this.apollo.mutate({\n      mutation: INITIATE_CALL_MUTATION,\n      variables\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call initiated successfully:', result);\n      if (!result.data?.initiateCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.initiateCall;\n      console.log('📞 [CallService] Call details:', {\n        id: call.id,\n        type: call.type,\n        status: call.status,\n        caller: call.caller?.username,\n        recipient: call.recipient?.username\n      });\n      // Mettre à jour l'état local\n      this.activeCall.next(call);\n      return call;\n    }), catchError(error => {\n      console.error('❌ [CallService] initiateCall error:', error);\n      this.logger.error('Error initiating call:', error);\n      let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n      if (error.networkError) {\n        errorMessage = 'Erreur de connexion réseau';\n      } else if (error.graphQLErrors?.length > 0) {\n        errorMessage = error.graphQLErrors[0].message || errorMessage;\n      }\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall) {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n'\n    });\n    return this.apollo.mutate({\n      mutation: ACCEPT_CALL_MUTATION,\n      variables: {\n        callId: incomingCall.id,\n        answer\n      }\n    }).pipe(switchMap(result => {\n      console.log('✅ [CallService] Call accepted successfully:', result);\n      if (!result.data?.acceptCall) {\n        throw new Error('No call data received from server');\n      }\n      const call = result.data.acceptCall;\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      this.play('call-connected');\n      // Démarrer les médias pour l'appel de manière asynchrone\n      return from(this.startMediaForCall(incomingCall, call));\n    }), catchError(error => {\n      console.error('❌ [CallService] acceptCall error:', error);\n      this.logger.error('Error accepting call:', error);\n      return throwError(() => new Error(\"Erreur lors de l'acceptation de l'appel\"));\n    }));\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId, reason) {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call rejected successfully:', result);\n      if (!result.data?.rejectCall) {\n        throw new Error('No response received from server');\n      }\n      // Mettre à jour l'état local\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      return result.data.rejectCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] rejectCall error:', error);\n      this.logger.error('Error rejecting call:', error);\n      return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId) {\n    console.log('🔄 [CallService] Ending call:', callId);\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback: null // Pas de feedback pour l'instant\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Call ended successfully:', result);\n      if (!result.data?.endCall) {\n        throw new Error('No response received from server');\n      }\n      // Nettoyer les ressources WebRTC\n      this.cleanupWebRTC();\n      // Mettre à jour l'état local\n      this.activeCall.next(null);\n      this.incomingCall.next(null);\n      // Arrêter tous les sons\n      this.stopAllSounds();\n      this.play('call-end');\n      return result.data.endCall;\n    }), catchError(error => {\n      console.error('❌ [CallService] endCall error:', error);\n      this.logger.error('Error ending call:', error);\n      return throwError(() => new Error(\"Erreur lors de la fin de l'appel\"));\n    }));\n  }\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio\n    });\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      console.log('✅ [CallService] Media toggled successfully:', result);\n      if (!result.data?.toggleCallMedia) {\n        throw new Error('No response received from server');\n      }\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      console.error('❌ [CallService] toggleMedia error:', error);\n      this.logger.error('Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  startMediaForCall(incomingCall, call) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('🎥 [CallService] Starting media for call...');\n        const isVideoCall = incomingCall.type === CallType.VIDEO;\n        yield _this.getUserMedia(isVideoCall, true);\n        console.log('✅ [CallService] Media started successfully');\n      } catch (error) {\n        console.error('❌ [CallService] Error starting media:', error);\n        // Continuer même si les médias échouent\n      }\n      // Mettre à jour l'état local\n      _this.activeCall.next(call);\n      _this.incomingCall.next(null); // Supprimer l'appel entrant\n      return call;\n    })();\n  }\n  /**\n   * Obtient les médias utilisateur (caméra/microphone)\n   */\n  getUserMedia() {\n    var _this2 = this;\n    return _asyncToGenerator(function* (video = true, audio = true) {\n      console.log('🎥 [CallService] Getting user media:', {\n        video,\n        audio\n      });\n      try {\n        const constraints = {\n          video: video ? {\n            width: 640,\n            height: 480\n          } : false,\n          audio: audio\n        };\n        _this2.localStream = yield navigator.mediaDevices.getUserMedia(constraints);\n        _this2.isVideoEnabled = video;\n        _this2.isAudioEnabled = audio;\n        console.log('✅ [CallService] User media obtained successfully');\n        return _this2.localStream;\n      } catch (error) {\n        console.error('❌ [CallService] Error getting user media:', error);\n        throw new Error(\"Impossible d'accéder à la caméra/microphone\");\n      }\n    }).apply(this, arguments);\n  }\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo() {\n    if (this.localStream) {\n      const videoTracks = this.localStream.getVideoTracks();\n      if (videoTracks.length > 0) {\n        this.isVideoEnabled = !this.isVideoEnabled;\n        videoTracks[0].enabled = this.isVideoEnabled;\n        console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n      }\n    }\n    return this.isVideoEnabled;\n  }\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio() {\n    if (this.localStream) {\n      const audioTracks = this.localStream.getAudioTracks();\n      if (audioTracks.length > 0) {\n        this.isAudioEnabled = !this.isAudioEnabled;\n        audioTracks[0].enabled = this.isAudioEnabled;\n        console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n      }\n    }\n    return this.isAudioEnabled;\n  }\n  /**\n   * Obtient le stream local\n   */\n  getLocalStream() {\n    return this.localStream;\n  }\n  /**\n   * Obtient le stream distant\n   */\n  getRemoteStream() {\n    return this.remoteStream;\n  }\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled() {\n    return this.isVideoEnabled;\n  }\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled() {\n    return this.isAudioEnabled;\n  }\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  cleanupWebRTC() {\n    console.log('🧹 [CallService] Cleaning up WebRTC resources');\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => {\n        track.stop();\n      });\n      this.localStream = null;\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  ngOnDestroy() {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "from", "map", "catchError", "switchMap", "CallType", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "activeCall$", "asObservable", "incomingCall$", "sounds", "isPlaying", "muted", "isVideoEnabled", "isAudioEnabled", "preloadSounds", "initializeSubscriptions", "setTimeout", "subscribeToIncomingCalls", "loadSound", "name", "path", "audio", "Audio", "load", "addEventListener", "error", "console", "play", "loop", "sound", "currentTime", "catch", "stop", "pause", "log", "subscribe", "query", "next", "data", "callId", "id", "callType", "type", "caller", "username", "conversationId", "handleIncomingCall", "reinitializeSubscription", "call", "initiateCall", "recipientId", "Error", "Date", "now", "Math", "random", "toString", "substr", "offer", "JSON", "stringify", "sdp", "variables", "mutate", "mutation", "pipe", "result", "status", "recipient", "errorMessage", "networkError", "graphQLErrors", "length", "message", "acceptCall", "answer", "startMediaForCall", "rejectCall", "reason", "endCall", "feedback", "cleanupWebRTC", "stopAllSounds", "toggleMedia", "enableVideo", "enableAudio", "video", "toggleCallMedia", "_this", "_asyncToGenerator", "isVideoCall", "VIDEO", "getUserMedia", "_this2", "constraints", "width", "height", "localStream", "navigator", "mediaDevices", "apply", "arguments", "toggleVideo", "videoTracks", "getVideoTracks", "enabled", "toggleAudio", "audioTracks", "getAudioTracks", "getLocalStream", "getRemoteStream", "remoteStream", "getVideoEnabled", "getAudioEnabled", "getTracks", "for<PERSON>ach", "track", "peerConnection", "close", "Object", "keys", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of, from } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnDestroy {\n  // État des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  // États simples pour les médias\n  private isVideoEnabled = true;\n  private isAudioEnabled = true;\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.preloadSounds();\n    this.initializeSubscriptions();\n  }\n\n  /**\n   * Initialise les subscriptions avec un délai pour s'assurer que l'authentification est prête\n   */\n  private initializeSubscriptions(): void {\n    // Attendre un peu pour s'assurer que l'authentification est prête\n    setTimeout(() => {\n      this.subscribeToIncomingCalls();\n    }, 2000);\n  }\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n  }\n\n  /**\n   * Charge un fichier audio\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      console.error(`Error loading sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * Joue un son\n   */\n  private play(name: string, loop: boolean = false): void {\n    if (this.muted) return;\n\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n\n      sound.loop = loop;\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(console.error);\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      console.error(`Error playing sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  private stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      console.error(`Error stopping sound ${name}:`, error);\n    }\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    console.log('🔔 [CallService] Setting up incoming call subscription...');\n\n    this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n      })\n      .subscribe({\n        next: ({ data }) => {\n          if (data?.incomingCall) {\n            console.log('📞 [CallService] Incoming call received:', {\n              callId: data.incomingCall.id,\n              callType: data.incomingCall.type,\n              caller: data.incomingCall.caller?.username,\n              conversationId: data.incomingCall.conversationId,\n            });\n            this.handleIncomingCall(data.incomingCall);\n          }\n        },\n        error: (error) => {\n          console.error(\n            '❌ [CallService] Error in incoming call subscription:',\n            error\n          );\n\n          // Réessayer après 5 secondes en cas d'erreur\n          setTimeout(() => {\n            console.log(\n              '🔄 [CallService] Retrying incoming call subscription...'\n            );\n            this.subscribeToIncomingCalls();\n          }, 5000);\n        },\n      });\n  }\n\n  /**\n   * Méthode publique pour forcer la réinitialisation de la subscription\n   */\n  public reinitializeSubscription(): void {\n    console.log('🔄 [CallService] Manually reinitializing subscription...');\n    this.subscribeToIncomingCalls();\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    console.log('🔔 [CallService] Handling incoming call:', {\n      callId: call.id,\n      callType: call.type,\n      caller: call.caller?.username,\n      conversationId: call.conversationId,\n    });\n\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n\n    console.log(\n      '🔊 [CallService] Ringtone started, call notification sent to UI'\n    );\n  }\n\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    console.log('🔄 [CallService] Initiating call:', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      console.error('❌ [CallService] initiateCall error:', error);\n      return throwError(() => error);\n    }\n\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n\n    // Pour l'instant, utiliser une offre WebRTC factice\n    const offer = JSON.stringify({\n      type: 'offer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    const variables = {\n      recipientId,\n      callType: callType,\n      callId,\n      offer,\n      conversationId,\n    };\n\n    console.log('📤 [CallService] Sending initiate call mutation:', variables);\n\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables,\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call initiated successfully:', result);\n\n          if (!result.data?.initiateCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.initiateCall;\n          console.log('📞 [CallService] Call details:', {\n            id: call.id,\n            type: call.type,\n            status: call.status,\n            caller: call.caller?.username,\n            recipient: call.recipient?.username,\n          });\n\n          // Mettre à jour l'état local\n          this.activeCall.next(call);\n\n          return call;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] initiateCall error:', error);\n          this.logger.error('Error initiating call:', error);\n\n          let errorMessage = \"Erreur lors de l'initiation de l'appel\";\n          if (error.networkError) {\n            errorMessage = 'Erreur de connexion réseau';\n          } else if (error.graphQLErrors?.length > 0) {\n            errorMessage = error.graphQLErrors[0].message || errorMessage;\n          }\n\n          return throwError(() => new Error(errorMessage));\n        })\n      );\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    console.log('🔄 [CallService] Accepting call:', incomingCall.id);\n\n    // Générer une réponse WebRTC factice\n    const answer = JSON.stringify({\n      type: 'answer',\n      sdp: 'v=0\\r\\no=- 0 0 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\n',\n    });\n\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer,\n        },\n      })\n      .pipe(\n        switchMap((result) => {\n          console.log('✅ [CallService] Call accepted successfully:', result);\n\n          if (!result.data?.acceptCall) {\n            throw new Error('No call data received from server');\n          }\n\n          const call = result.data.acceptCall;\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n          this.play('call-connected');\n\n          // Démarrer les médias pour l'appel de manière asynchrone\n          return from(this.startMediaForCall(incomingCall, call));\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] acceptCall error:', error);\n          this.logger.error('Error accepting call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de l'acceptation de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Rejecting call:', callId, reason);\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason: reason || 'User rejected',\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call rejected successfully:', result);\n\n          if (!result.data?.rejectCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Mettre à jour l'état local\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n\n          return result.data.rejectCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] rejectCall error:', error);\n          this.logger.error('Error rejecting call:', error);\n          return throwError(() => new Error(\"Erreur lors du rejet de l'appel\"));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Ending call:', callId);\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback: null, // Pas de feedback pour l'instant\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Call ended successfully:', result);\n\n          if (!result.data?.endCall) {\n            throw new Error('No response received from server');\n          }\n\n          // Nettoyer les ressources WebRTC\n          this.cleanupWebRTC();\n\n          // Mettre à jour l'état local\n          this.activeCall.next(null);\n          this.incomingCall.next(null);\n\n          // Arrêter tous les sons\n          this.stopAllSounds();\n          this.play('call-end');\n\n          return result.data.endCall;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] endCall error:', error);\n          this.logger.error('Error ending call:', error);\n          return throwError(\n            () => new Error(\"Erreur lors de la fin de l'appel\")\n          );\n        })\n      );\n  }\n\n  /**\n   * Bascule l'état des médias (audio/vidéo) pendant un appel\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    console.log('🔄 [CallService] Toggling media:', {\n      callId,\n      enableVideo,\n      enableAudio,\n    });\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video: enableVideo,\n          audio: enableAudio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log('✅ [CallService] Media toggled successfully:', result);\n\n          if (!result.data?.toggleCallMedia) {\n            throw new Error('No response received from server');\n          }\n\n          return result.data.toggleCallMedia;\n        }),\n        catchError((error) => {\n          console.error('❌ [CallService] toggleMedia error:', error);\n          this.logger.error('Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  /**\n   * Démarre les médias pour un appel accepté\n   */\n  private async startMediaForCall(\n    incomingCall: IncomingCall,\n    call: Call\n  ): Promise<Call> {\n    try {\n      console.log('🎥 [CallService] Starting media for call...');\n      const isVideoCall = incomingCall.type === CallType.VIDEO;\n      await this.getUserMedia(isVideoCall, true);\n      console.log('✅ [CallService] Media started successfully');\n    } catch (error) {\n      console.error('❌ [CallService] Error starting media:', error);\n      // Continuer même si les médias échouent\n    }\n\n    // Mettre à jour l'état local\n    this.activeCall.next(call);\n    this.incomingCall.next(null); // Supprimer l'appel entrant\n\n    return call;\n  }\n\n  /**\n   * Obtient les médias utilisateur (caméra/microphone)\n   */\n  async getUserMedia(\n    video: boolean = true,\n    audio: boolean = true\n  ): Promise<MediaStream> {\n    console.log('🎥 [CallService] Getting user media:', { video, audio });\n\n    try {\n      const constraints: MediaStreamConstraints = {\n        video: video ? { width: 640, height: 480 } : false,\n        audio: audio,\n      };\n\n      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);\n      this.isVideoEnabled = video;\n      this.isAudioEnabled = audio;\n\n      console.log('✅ [CallService] User media obtained successfully');\n      return this.localStream;\n    } catch (error) {\n      console.error('❌ [CallService] Error getting user media:', error);\n      throw new Error(\"Impossible d'accéder à la caméra/microphone\");\n    }\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    if (this.localStream) {\n      const videoTracks = this.localStream.getVideoTracks();\n      if (videoTracks.length > 0) {\n        this.isVideoEnabled = !this.isVideoEnabled;\n        videoTracks[0].enabled = this.isVideoEnabled;\n        console.log('📹 [CallService] Video toggled:', this.isVideoEnabled);\n      }\n    }\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    if (this.localStream) {\n      const audioTracks = this.localStream.getAudioTracks();\n      if (audioTracks.length > 0) {\n        this.isAudioEnabled = !this.isAudioEnabled;\n        audioTracks[0].enabled = this.isAudioEnabled;\n        console.log('🎤 [CallService] Audio toggled:', this.isAudioEnabled);\n      }\n    }\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient le stream local\n   */\n  getLocalStream(): MediaStream | null {\n    return this.localStream;\n  }\n\n  /**\n   * Obtient le stream distant\n   */\n  getRemoteStream(): MediaStream | null {\n    return this.remoteStream;\n  }\n\n  /**\n   * Obtient l'état de la vidéo\n   */\n  getVideoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient l'état de l'audio\n   */\n  getAudioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  private cleanupWebRTC(): void {\n    console.log('🧹 [CallService] Cleaning up WebRTC resources');\n\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => {\n        track.stop();\n      });\n      this.localStream = null;\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n    this.isVideoEnabled = true;\n    this.isAudioEnabled = true;\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  private stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAcC,UAAU,EAAMC,IAAI,QAAQ,MAAM;AACxE,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC3D,SAEEC,QAAQ,QAIH,yBAAyB;AAChC,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,QACrB,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EAkBtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAjBlD;IACQ,KAAAC,UAAU,GAAG,IAAIjB,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAkB,YAAY,GAAG,IAAIlB,eAAe,CAAsB,IAAI,CAAC;IAErE;IACO,KAAAmB,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAEvD;IACQ,KAAAE,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAErB;IACQ,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAG3B,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEA;;;EAGQA,uBAAuBA,CAAA;IAC7B;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,wBAAwB,EAAE;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQH,aAAaA,CAAA;IACnB,IAAI,CAACI,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;EACtE;EAEA;;;EAGQA,SAASA,CAACC,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC;MAC7BC,KAAK,CAACE,IAAI,EAAE;MACZ,IAAI,CAACd,MAAM,CAACU,IAAI,CAAC,GAAGE,KAAK;MACzB,IAAI,CAACX,SAAS,CAACS,IAAI,CAAC,GAAG,KAAK;MAE5BE,KAAK,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAACd,SAAS,CAACS,IAAI,CAAC,GAAG,KAAK;MAC9B,CAAC,CAAC;KACH,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuBN,IAAI,GAAG,EAAEM,KAAK,CAAC;;EAExD;EAEA;;;EAGQE,IAAIA,CAACR,IAAY,EAAES,IAAA,GAAgB,KAAK;IAC9C,IAAI,IAAI,CAACjB,KAAK,EAAE;IAEhB,IAAI;MACF,MAAMkB,KAAK,GAAG,IAAI,CAACpB,MAAM,CAACU,IAAI,CAAC;MAC/B,IAAI,CAACU,KAAK,EAAE;MAEZA,KAAK,CAACD,IAAI,GAAGA,IAAI;MACjB,IAAI,CAAC,IAAI,CAAClB,SAAS,CAACS,IAAI,CAAC,EAAE;QACzBU,KAAK,CAACC,WAAW,GAAG,CAAC;QACrBD,KAAK,CAACF,IAAI,EAAE,CAACI,KAAK,CAACL,OAAO,CAACD,KAAK,CAAC;QACjC,IAAI,CAACf,SAAS,CAACS,IAAI,CAAC,GAAG,IAAI;;KAE9B,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuBN,IAAI,GAAG,EAAEM,KAAK,CAAC;;EAExD;EAEA;;;EAGQO,IAAIA,CAACb,IAAY;IACvB,IAAI;MACF,MAAMU,KAAK,GAAG,IAAI,CAACpB,MAAM,CAACU,IAAI,CAAC;MAC/B,IAAI,CAACU,KAAK,EAAE;MAEZ,IAAI,IAAI,CAACnB,SAAS,CAACS,IAAI,CAAC,EAAE;QACxBU,KAAK,CAACI,KAAK,EAAE;QACbJ,KAAK,CAACC,WAAW,GAAG,CAAC;QACrB,IAAI,CAACpB,SAAS,CAACS,IAAI,CAAC,GAAG,KAAK;;KAE/B,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwBN,IAAI,GAAG,EAAEM,KAAK,CAAC;;EAEzD;EAEA;;;EAGQR,wBAAwBA,CAAA;IAC9BS,OAAO,CAACQ,GAAG,CAAC,2DAA2D,CAAC;IAExE,IAAI,CAAChC,MAAM,CACRiC,SAAS,CAAiC;MACzCC,KAAK,EAAErC;KACR,CAAC,CACDoC,SAAS,CAAC;MACTE,IAAI,EAAEA,CAAC;QAAEC;MAAI,CAAE,KAAI;QACjB,IAAIA,IAAI,EAAEjC,YAAY,EAAE;UACtBqB,OAAO,CAACQ,GAAG,CAAC,0CAA0C,EAAE;YACtDK,MAAM,EAAED,IAAI,CAACjC,YAAY,CAACmC,EAAE;YAC5BC,QAAQ,EAAEH,IAAI,CAACjC,YAAY,CAACqC,IAAI;YAChCC,MAAM,EAAEL,IAAI,CAACjC,YAAY,CAACsC,MAAM,EAAEC,QAAQ;YAC1CC,cAAc,EAAEP,IAAI,CAACjC,YAAY,CAACwC;WACnC,CAAC;UACF,IAAI,CAACC,kBAAkB,CAACR,IAAI,CAACjC,YAAY,CAAC;;MAE9C,CAAC;MACDoB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;QAED;QACAT,UAAU,CAAC,MAAK;UACdU,OAAO,CAACQ,GAAG,CACT,yDAAyD,CAC1D;UACD,IAAI,CAACjB,wBAAwB,EAAE;QACjC,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACN;EAEA;;;EAGO8B,wBAAwBA,CAAA;IAC7BrB,OAAO,CAACQ,GAAG,CAAC,0DAA0D,CAAC;IACvE,IAAI,CAACjB,wBAAwB,EAAE;EACjC;EAEA;;;EAGQ6B,kBAAkBA,CAACE,IAAkB;IAC3CtB,OAAO,CAACQ,GAAG,CAAC,0CAA0C,EAAE;MACtDK,MAAM,EAAES,IAAI,CAACR,EAAE;MACfC,QAAQ,EAAEO,IAAI,CAACN,IAAI;MACnBC,MAAM,EAAEK,IAAI,CAACL,MAAM,EAAEC,QAAQ;MAC7BC,cAAc,EAAEG,IAAI,CAACH;KACtB,CAAC;IAEF,IAAI,CAACxC,YAAY,CAACgC,IAAI,CAACW,IAAI,CAAC;IAC5B,IAAI,CAACrB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3BD,OAAO,CAACQ,GAAG,CACT,iEAAiE,CAClE;EACH;EAEA;;;EAGAe,YAAYA,CACVC,WAAmB,EACnBT,QAAkB,EAClBI,cAAuB;IAEvBnB,OAAO,CAACQ,GAAG,CAAC,mCAAmC,EAAE;MAC/CgB,WAAW;MACXT,QAAQ;MACRI;KACD,CAAC;IAEF,IAAI,CAACK,WAAW,EAAE;MAChB,MAAMzB,KAAK,GAAG,IAAI0B,KAAK,CAAC,0BAA0B,CAAC;MACnDzB,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAOrC,UAAU,CAAC,MAAMqC,KAAK,CAAC;;IAGhC;IACA,MAAMc,MAAM,GAAG,QAAQa,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAC/CC,QAAQ,CAAC,EAAE,CAAC,CACZC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAEjB;IACA,MAAMC,KAAK,GAAGC,IAAI,CAACC,SAAS,CAAC;MAC3BlB,IAAI,EAAE,OAAO;MACbmB,GAAG,EAAE;KACN,CAAC;IAEF,MAAMC,SAAS,GAAG;MAChBZ,WAAW;MACXT,QAAQ,EAAEA,QAAQ;MAClBF,MAAM;MACNmB,KAAK;MACLb;KACD;IAEDnB,OAAO,CAACQ,GAAG,CAAC,kDAAkD,EAAE4B,SAAS,CAAC;IAE1E,OAAO,IAAI,CAAC5D,MAAM,CACf6D,MAAM,CAAyB;MAC9BC,QAAQ,EAAEtE,sBAAsB;MAChCoE;KACD,CAAC,CACDG,IAAI,CACH3E,GAAG,CAAE4E,MAAM,IAAI;MACbxC,OAAO,CAACQ,GAAG,CAAC,8CAA8C,EAAEgC,MAAM,CAAC;MAEnE,IAAI,CAACA,MAAM,CAAC5B,IAAI,EAAEW,YAAY,EAAE;QAC9B,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMH,IAAI,GAAGkB,MAAM,CAAC5B,IAAI,CAACW,YAAY;MACrCvB,OAAO,CAACQ,GAAG,CAAC,gCAAgC,EAAE;QAC5CM,EAAE,EAAEQ,IAAI,CAACR,EAAE;QACXE,IAAI,EAAEM,IAAI,CAACN,IAAI;QACfyB,MAAM,EAAEnB,IAAI,CAACmB,MAAM;QACnBxB,MAAM,EAAEK,IAAI,CAACL,MAAM,EAAEC,QAAQ;QAC7BwB,SAAS,EAAEpB,IAAI,CAACoB,SAAS,EAAExB;OAC5B,CAAC;MAEF;MACA,IAAI,CAACxC,UAAU,CAACiC,IAAI,CAACW,IAAI,CAAC;MAE1B,OAAOA,IAAI;IACb,CAAC,CAAC,EACFzD,UAAU,CAAEkC,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAACtB,MAAM,CAACsB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAElD,IAAI4C,YAAY,GAAG,wCAAwC;MAC3D,IAAI5C,KAAK,CAAC6C,YAAY,EAAE;QACtBD,YAAY,GAAG,4BAA4B;OAC5C,MAAM,IAAI5C,KAAK,CAAC8C,aAAa,EAAEC,MAAM,GAAG,CAAC,EAAE;QAC1CH,YAAY,GAAG5C,KAAK,CAAC8C,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO,IAAIJ,YAAY;;MAG/D,OAAOjF,UAAU,CAAC,MAAM,IAAI+D,KAAK,CAACkB,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAK,UAAUA,CAACrE,YAA0B;IACnCqB,OAAO,CAACQ,GAAG,CAAC,kCAAkC,EAAE7B,YAAY,CAACmC,EAAE,CAAC;IAEhE;IACA,MAAMmC,MAAM,GAAGhB,IAAI,CAACC,SAAS,CAAC;MAC5BlB,IAAI,EAAE,QAAQ;MACdmB,GAAG,EAAE;KACN,CAAC;IAEF,OAAO,IAAI,CAAC3D,MAAM,CACf6D,MAAM,CAAuB;MAC5BC,QAAQ,EAAErE,oBAAoB;MAC9BmE,SAAS,EAAE;QACTvB,MAAM,EAAElC,YAAY,CAACmC,EAAE;QACvBmC;;KAEH,CAAC,CACDV,IAAI,CACHzE,SAAS,CAAE0E,MAAM,IAAI;MACnBxC,OAAO,CAACQ,GAAG,CAAC,6CAA6C,EAAEgC,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC5B,IAAI,EAAEoC,UAAU,EAAE;QAC5B,MAAM,IAAIvB,KAAK,CAAC,mCAAmC,CAAC;;MAGtD,MAAMH,IAAI,GAAGkB,MAAM,CAAC5B,IAAI,CAACoC,UAAU;MAEnC;MACA,IAAI,CAAC1C,IAAI,CAAC,UAAU,CAAC;MACrB,IAAI,CAACL,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACA,OAAOtC,IAAI,CAAC,IAAI,CAACuF,iBAAiB,CAACvE,YAAY,EAAE2C,IAAI,CAAC,CAAC;IACzD,CAAC,CAAC,EACFzD,UAAU,CAAEkC,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACtB,MAAM,CAACsB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOrC,UAAU,CACf,MAAM,IAAI+D,KAAK,CAAC,yCAAyC,CAAC,CAC3D;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA0B,UAAUA,CAACtC,MAAc,EAAEuC,MAAe;IACxCpD,OAAO,CAACQ,GAAG,CAAC,kCAAkC,EAAEK,MAAM,EAAEuC,MAAM,CAAC;IAE/D,OAAO,IAAI,CAAC5E,MAAM,CACf6D,MAAM,CAA8B;MACnCC,QAAQ,EAAEpE,oBAAoB;MAC9BkE,SAAS,EAAE;QACTvB,MAAM;QACNuC,MAAM,EAAEA,MAAM,IAAI;;KAErB,CAAC,CACDb,IAAI,CACH3E,GAAG,CAAE4E,MAAM,IAAI;MACbxC,OAAO,CAACQ,GAAG,CAAC,6CAA6C,EAAEgC,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC5B,IAAI,EAAEuC,UAAU,EAAE;QAC5B,MAAM,IAAI1B,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAAC9C,YAAY,CAACgC,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACjC,UAAU,CAACiC,IAAI,CAAC,IAAI,CAAC;MAE1B;MACA,IAAI,CAACL,IAAI,CAAC,UAAU,CAAC;MAErB,OAAOkC,MAAM,CAAC5B,IAAI,CAACuC,UAAU;IAC/B,CAAC,CAAC,EACFtF,UAAU,CAAEkC,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACtB,MAAM,CAACsB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOrC,UAAU,CAAC,MAAM,IAAI+D,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA4B,OAAOA,CAACxC,MAAc;IACpBb,OAAO,CAACQ,GAAG,CAAC,+BAA+B,EAAEK,MAAM,CAAC;IAEpD,OAAO,IAAI,CAACrC,MAAM,CACf6D,MAAM,CAA2B;MAChCC,QAAQ,EAAEnE,iBAAiB;MAC3BiE,SAAS,EAAE;QACTvB,MAAM;QACNyC,QAAQ,EAAE,IAAI,CAAE;;KAEnB,CAAC,CACDf,IAAI,CACH3E,GAAG,CAAE4E,MAAM,IAAI;MACbxC,OAAO,CAACQ,GAAG,CAAC,0CAA0C,EAAEgC,MAAM,CAAC;MAE/D,IAAI,CAACA,MAAM,CAAC5B,IAAI,EAAEyC,OAAO,EAAE;QACzB,MAAM,IAAI5B,KAAK,CAAC,kCAAkC,CAAC;;MAGrD;MACA,IAAI,CAAC8B,aAAa,EAAE;MAEpB;MACA,IAAI,CAAC7E,UAAU,CAACiC,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAAChC,YAAY,CAACgC,IAAI,CAAC,IAAI,CAAC;MAE5B;MACA,IAAI,CAAC6C,aAAa,EAAE;MACpB,IAAI,CAACvD,IAAI,CAAC,UAAU,CAAC;MAErB,OAAOuC,MAAM,CAAC5B,IAAI,CAACyC,OAAO;IAC5B,CAAC,CAAC,EACFxF,UAAU,CAAEkC,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAACtB,MAAM,CAACsB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC9C,OAAOrC,UAAU,CACf,MAAM,IAAI+D,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAgC,WAAWA,CACT5C,MAAc,EACd6C,WAAqB,EACrBC,WAAqB;IAErB3D,OAAO,CAACQ,GAAG,CAAC,kCAAkC,EAAE;MAC9CK,MAAM;MACN6C,WAAW;MACXC;KACD,CAAC;IAEF,OAAO,IAAI,CAACnF,MAAM,CACf6D,MAAM,CAAmC;MACxCC,QAAQ,EAAElE,0BAA0B;MACpCgE,SAAS,EAAE;QACTvB,MAAM;QACN+C,KAAK,EAAEF,WAAW;QAClB/D,KAAK,EAAEgE;;KAEV,CAAC,CACDpB,IAAI,CACH3E,GAAG,CAAE4E,MAAM,IAAI;MACbxC,OAAO,CAACQ,GAAG,CAAC,6CAA6C,EAAEgC,MAAM,CAAC;MAElE,IAAI,CAACA,MAAM,CAAC5B,IAAI,EAAEiD,eAAe,EAAE;QACjC,MAAM,IAAIpC,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,OAAOe,MAAM,CAAC5B,IAAI,CAACiD,eAAe;IACpC,CAAC,CAAC,EACFhG,UAAU,CAAEkC,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,IAAI,CAACtB,MAAM,CAACsB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOrC,UAAU,CACf,MAAM,IAAI+D,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGcyB,iBAAiBA,CAC7BvE,YAA0B,EAC1B2C,IAAU;IAAA,IAAAwC,KAAA;IAAA,OAAAC,iBAAA;MAEV,IAAI;QACF/D,OAAO,CAACQ,GAAG,CAAC,6CAA6C,CAAC;QAC1D,MAAMwD,WAAW,GAAGrF,YAAY,CAACqC,IAAI,KAAKjD,QAAQ,CAACkG,KAAK;QACxD,MAAMH,KAAI,CAACI,YAAY,CAACF,WAAW,EAAE,IAAI,CAAC;QAC1ChE,OAAO,CAACQ,GAAG,CAAC,4CAA4C,CAAC;OAC1D,CAAC,OAAOT,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D;;MAGF;MACA+D,KAAI,CAACpF,UAAU,CAACiC,IAAI,CAACW,IAAI,CAAC;MAC1BwC,KAAI,CAACnF,YAAY,CAACgC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MAE9B,OAAOW,IAAI;IAAC;EACd;EAEA;;;EAGM4C,YAAYA,CAAA,EAEK;IAAA,IAAAC,MAAA;IAAA,OAAAJ,iBAAA,YADrBH,KAAA,GAAiB,IAAI,EACrBjE,KAAA,GAAiB,IAAI;MAErBK,OAAO,CAACQ,GAAG,CAAC,sCAAsC,EAAE;QAAEoD,KAAK;QAAEjE;MAAK,CAAE,CAAC;MAErE,IAAI;QACF,MAAMyE,WAAW,GAA2B;UAC1CR,KAAK,EAAEA,KAAK,GAAG;YAAES,KAAK,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAG,CAAE,GAAG,KAAK;UAClD3E,KAAK,EAAEA;SACR;QAEDwE,MAAI,CAACI,WAAW,SAASC,SAAS,CAACC,YAAY,CAACP,YAAY,CAACE,WAAW,CAAC;QACzED,MAAI,CAACjF,cAAc,GAAG0E,KAAK;QAC3BO,MAAI,CAAChF,cAAc,GAAGQ,KAAK;QAE3BK,OAAO,CAACQ,GAAG,CAAC,kDAAkD,CAAC;QAC/D,OAAO2D,MAAI,CAACI,WAAW;OACxB,CAAC,OAAOxE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE,MAAM,IAAI0B,KAAK,CAAC,6CAA6C,CAAC;;IAC/D,GAAAiD,KAAA,OAAAC,SAAA;EACH;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACL,WAAW,EAAE;MACpB,MAAMM,WAAW,GAAG,IAAI,CAACN,WAAW,CAACO,cAAc,EAAE;MACrD,IAAID,WAAW,CAAC/B,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,CAAC5D,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C2F,WAAW,CAAC,CAAC,CAAC,CAACE,OAAO,GAAG,IAAI,CAAC7F,cAAc;QAC5Cc,OAAO,CAACQ,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACtB,cAAc,CAAC;;;IAGvE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGA8F,WAAWA,CAAA;IACT,IAAI,IAAI,CAACT,WAAW,EAAE;MACpB,MAAMU,WAAW,GAAG,IAAI,CAACV,WAAW,CAACW,cAAc,EAAE;MACrD,IAAID,WAAW,CAACnC,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,CAAC3D,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C8F,WAAW,CAAC,CAAC,CAAC,CAACF,OAAO,GAAG,IAAI,CAAC5F,cAAc;QAC5Ca,OAAO,CAACQ,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACrB,cAAc,CAAC;;;IAGvE,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAgG,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACZ,WAAW;EACzB;EAEA;;;EAGAa,eAAeA,CAAA;IACb,OAAO,IAAI,CAACC,YAAY;EAC1B;EAEA;;;EAGAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACpG,cAAc;EAC5B;EAEA;;;EAGAqG,eAAeA,CAAA;IACb,OAAO,IAAI,CAACpG,cAAc;EAC5B;EAEA;;;EAGQoE,aAAaA,CAAA;IACnBvD,OAAO,CAACQ,GAAG,CAAC,+CAA+C,CAAC;IAE5D,IAAI,IAAI,CAAC+D,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACiB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAI;QAC7CA,KAAK,CAACpF,IAAI,EAAE;MACd,CAAC,CAAC;MACF,IAAI,CAACiE,WAAW,GAAG,IAAI;;IAGzB,IAAI,IAAI,CAACoB,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACC,KAAK,EAAE;MAC3B,IAAI,CAACD,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACN,YAAY,GAAG,IAAI;IACxB,IAAI,CAACnG,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EAEA;;;EAGQqE,aAAaA,CAAA;IACnBqC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/G,MAAM,CAAC,CAAC0G,OAAO,CAAEhG,IAAI,IAAI;MACxC,IAAI,CAACa,IAAI,CAACb,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEAsG,WAAWA,CAAA;IACT,IAAI,CAACvC,aAAa,EAAE;IACpB,IAAI,CAACD,aAAa,EAAE;EACtB;;;uBAvjBWjF,WAAW,EAAA0H,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAX/H,WAAW;MAAAgI,OAAA,EAAXhI,WAAW,CAAAiI,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}