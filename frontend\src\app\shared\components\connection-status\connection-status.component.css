/* Styles pour le composant de statut de connexion */

.connection-status {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-online {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.status-offline {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.status-indicator {
  position: relative;
}

.status-indicator::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: inherit;
  background: inherit;
  opacity: 0.3;
  animation: ping 2s infinite;
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.offline-banner {
  background: linear-gradient(90deg, #dc2626, #ef4444, #dc2626);
  background-size: 200% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Transitions fluides */
.connection-status {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.connection-status:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Responsive design */
@media (max-width: 640px) {
  .connection-status {
    left: 1rem;
    right: 1rem;
    transform: translateX(0);
    max-width: calc(100vw - 2rem);
  }
  
  .connection-status span {
    font-size: 0.75rem;
  }
}

/* Animation d'apparition */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translateX(-50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}

.connection-status {
  animation: fadeInScale 0.3s ease-out;
}

/* Effet de glow pour l'état hors ligne */
.status-offline {
  box-shadow: 
    0 4px 12px rgba(239, 68, 68, 0.3),
    0 0 20px rgba(239, 68, 68, 0.2);
}

.status-online {
  box-shadow: 
    0 4px 12px rgba(16, 185, 129, 0.3),
    0 0 20px rgba(16, 185, 129, 0.2);
}
