import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { CallService } from '../../services/call.service';
import { LoggerService } from '../../services/logger.service';
import { Call, CallType, CallStatus } from '../../models/message.model';

@Component({
  selector: 'app-active-call',
  templateUrl: './active-call.component.html',
  styleUrls: ['./active-call.component.css'],
})
export class ActiveCallComponent implements OnInit, OnDestroy {
  activeCall: Call | null = null;
  callDuration: string = '00:00';
  isAudioMuted: boolean = false;
  isVideoMuted: boolean = false;
  isSpeakerOn: boolean = true;

  private durationInterval: any;
  private callStartTime: Date | null = null;
  private subscriptions: Subscription[] = [];

  // Exposer les énums au template
  CallType = CallType;
  CallStatus = CallStatus;

  constructor(
    private callService: CallService,
    private logger: LoggerService
  ) {}

  ngOnInit(): void {
    // S'abonner à l'appel actif
    const activeCallSub = this.callService.activeCall$.subscribe((call) => {
      const previousCall = this.activeCall;
      this.activeCall = call;

      console.log('📞 [ActiveCall] Active call updated:', call);

      if (call && call.status === CallStatus.CONNECTED) {
        if (!previousCall || previousCall.id !== call.id) {
          console.log('✅ [ActiveCall] Call connected, starting timer');
          this.startCallTimer();
        }
      } else if (!call || call.status !== CallStatus.CONNECTED) {
        this.stopCallTimer();
      }
    });

    this.subscriptions.push(activeCallSub);
  }

  ngOnDestroy(): void {
    this.stopCallTimer();
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  // Démarrer le minuteur d'appel
  private startCallTimer(): void {
    this.callStartTime = new Date();
    this.stopCallTimer(); // Arrêter tout minuteur existant

    this.durationInterval = setInterval(() => {
      if (this.callStartTime) {
        const now = new Date();
        const duration = Math.floor(
          (now.getTime() - this.callStartTime.getTime()) / 1000
        );
        this.callDuration = this.formatDuration(duration);
      }
    }, 1000);
  }

  // Arrêter le minuteur d'appel
  private stopCallTimer(): void {
    if (this.durationInterval) {
      clearInterval(this.durationInterval);
      this.durationInterval = null;
    }
    this.callStartTime = null;
    this.callDuration = '00:00';
  }

  // Formater la durée en MM:SS
  private formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
      .toString()
      .padStart(2, '0')}`;
  }

  // Basculer le micro
  toggleMicrophone(): void {
    if (!this.activeCall) return;

    this.isAudioMuted = !this.isAudioMuted;
    console.log('🎤 [ActiveCall] Audio muted:', this.isAudioMuted);
  }

  // Basculer la caméra
  toggleCamera(): void {
    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) return;

    this.isVideoMuted = !this.isVideoMuted;
    console.log('📹 [ActiveCall] Video muted:', this.isVideoMuted);
  }

  // Basculer le haut-parleur
  toggleSpeaker(): void {
    this.isSpeakerOn = !this.isSpeakerOn;
    console.log('🔊 [ActiveCall] Speaker on:', this.isSpeakerOn);
  }

  // Terminer l'appel
  endCall(): void {
    if (!this.activeCall) return;

    console.log('📞 [ActiveCall] Ending call:', this.activeCall.id);

    this.callService.endCall(this.activeCall.id).subscribe({
      next: () => {
        console.log('✅ [ActiveCall] Call ended successfully');
      },
      error: (error) => {
        console.error('❌ [ActiveCall] Error ending call:', error);
      },
    });
  }

  // Méthodes utilitaires pour le template
  isVideoCall(): boolean {
    return this.activeCall?.type === CallType.VIDEO;
  }

  getCallStatusText(): string {
    if (!this.activeCall) return '';

    switch (this.activeCall.status) {
      case CallStatus.RINGING:
        return 'Sonnerie...';
      case CallStatus.CONNECTED:
        return 'Connecté';
      case CallStatus.ENDED:
        return 'Terminé';
      default:
        return 'En cours...';
    }
  }

  getOtherParticipantName(): string {
    if (!this.activeCall) return '';

    // Logique pour obtenir le nom de l'autre participant
    // Si on est l'appelant, on affiche le destinataire, sinon l'appelant
    return (
      this.activeCall.recipient?.username ||
      this.activeCall.caller?.username ||
      'Utilisateur'
    );
  }

  getOtherParticipantAvatar(): string {
    if (!this.activeCall) return '/assets/images/default-avatar.png';

    // Logique pour obtenir l'avatar de l'autre participant
    return (
      this.activeCall.recipient?.image ||
      this.activeCall.caller?.image ||
      '/assets/images/default-avatar.png'
    );
  }
}
