import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { CallService } from '../../services/call.service';
import { LoggerService } from '../../services/logger.service';
import { IncomingCall, CallType } from '../../models/message.model';

@Component({
  selector: 'app-incoming-call',
  templateUrl: './incoming-call.component.html',
  styleUrls: ['./incoming-call.component.css']
})
export class IncomingCallComponent implements OnInit, OnDestroy {
  
  incomingCall: IncomingCall | null = null;
  isProcessing: boolean = false;

  private subscriptions: Subscription[] = [];

  // Exposer les énums au template
  CallType = CallType;

  constructor(
    private callService: CallService,
    private logger: LoggerService
  ) {}

  ngOnInit(): void {
    // S'abonner aux appels entrants
    const incomingCallSub = this.callService.incomingCall$.subscribe((call) => {
      this.incomingCall = call;
      this.isProcessing = false;

      if (call) {
        console.log('📞 [IncomingCall] Incoming call received:', {
          callId: call.id,
          caller: call.caller?.username,
          type: call.type
        });
      }
    });

    this.subscriptions.push(incomingCallSub);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  /**
   * Accepte l'appel entrant
   */
  acceptCall(): void {
    if (!this.incomingCall || this.isProcessing) return;

    console.log('✅ [IncomingCall] Accepting call:', this.incomingCall.id);
    this.isProcessing = true;

    this.callService.acceptCall(this.incomingCall).subscribe({
      next: (call) => {
        console.log('✅ [IncomingCall] Call accepted successfully:', call);
        this.isProcessing = false;
        // L'interface d'appel actif va automatiquement s'afficher
      },
      error: (error) => {
        console.error('❌ [IncomingCall] Error accepting call:', error);
        this.logger.error('Error accepting call:', error);
        this.isProcessing = false;
        
        // Optionnel : Afficher un message d'erreur à l'utilisateur
        alert('Erreur lors de l\'acceptation de l\'appel. Veuillez réessayer.');
      }
    });
  }

  /**
   * Rejette l'appel entrant
   */
  rejectCall(): void {
    if (!this.incomingCall || this.isProcessing) return;

    console.log('❌ [IncomingCall] Rejecting call:', this.incomingCall.id);
    this.isProcessing = true;

    this.callService.rejectCall(this.incomingCall.id, 'User rejected').subscribe({
      next: (result) => {
        console.log('✅ [IncomingCall] Call rejected successfully:', result);
        this.isProcessing = false;
      },
      error: (error) => {
        console.error('❌ [IncomingCall] Error rejecting call:', error);
        this.logger.error('Error rejecting call:', error);
        this.isProcessing = false;
      }
    });
  }

  /**
   * Obtient le nom de l'appelant
   */
  getCallerName(): string {
    return this.incomingCall?.caller?.username || 'Utilisateur inconnu';
  }

  /**
   * Obtient l'avatar de l'appelant
   */
  getCallerAvatar(): string {
    return this.incomingCall?.caller?.image || '/assets/images/default-avatar.png';
  }

  /**
   * Obtient le type d'appel (audio/vidéo)
   */
  getCallTypeText(): string {
    if (!this.incomingCall) return '';
    
    return this.incomingCall.type === CallType.VIDEO ? 'Appel vidéo' : 'Appel audio';
  }

  /**
   * Obtient l'icône du type d'appel
   */
  getCallTypeIcon(): string {
    if (!this.incomingCall) return 'fa-phone';
    
    return this.incomingCall.type === CallType.VIDEO ? 'fa-video' : 'fa-phone';
  }

  /**
   * Vérifie si c'est un appel vidéo
   */
  isVideoCall(): boolean {
    return this.incomingCall?.type === CallType.VIDEO;
  }
}
