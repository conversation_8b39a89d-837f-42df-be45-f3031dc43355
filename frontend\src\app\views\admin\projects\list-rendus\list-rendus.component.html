<div class="min-h-screen bg-[#edf1f4] dark:bg-dark-bg-primary transition-colors duration-300">
  <div class="container mx-auto px-4 py-8">
    <!-- Header avec gradient -->
    <div class="bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-2xl p-8 mb-8 shadow-xl">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="bg-white/20 dark:bg-black/20 p-3 rounded-xl backdrop-blur-sm">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-white mb-2">Liste des Rendus</h1>
            <p class="text-white/80">Gestion et évaluation des projets étudiants</p>
          </div>
        </div>
        <div class="hidden md:flex items-center space-x-4 text-white/80">
          <div class="text-center">
            <div class="text-2xl font-bold">{{ rendus.length }}</div>
            <div class="text-sm">Total</div>
          </div>
          <div class="w-px h-12 bg-white/20"></div>
          <div class="text-center">
            <div class="text-2xl font-bold">{{ getEvaluatedCount() }}</div>
            <div class="text-sm">Évalués</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filtres modernes -->
    <div class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 mb-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50">
      <div class="flex items-center space-x-3 mb-6">
        <div class="bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg">
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
          </svg>
        </div>
        <h2 class="text-xl font-bold text-text-dark dark:text-dark-text-primary">Filtres et recherche</h2>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Filtre par groupe -->
        <div class="space-y-2">
          <label class="block text-sm font-semibold text-text-dark dark:text-dark-text-primary">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
              <span>Groupe</span>
            </div>
          </label>
          <select
            [(ngModel)]="filtreGroupe"
            (ngModelChange)="applyFilters()"
            class="w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary"
          >
            <option value="">Tous les groupes</option>
            <option *ngFor="let groupe of groupes" [value]="groupe">{{ groupe }}</option>
          </select>
        </div>

        <!-- Filtre par projet -->
        <div class="space-y-2">
          <label class="block text-sm font-semibold text-text-dark dark:text-dark-text-primary">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
              </svg>
              <span>Projet</span>
            </div>
          </label>
          <select
            [(ngModel)]="filtreProjet"
            (ngModelChange)="applyFilters()"
            class="w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary"
          >
            <option value="">Tous les projets</option>
            <option *ngFor="let projet of projets" [value]="projet._id">{{ projet.titre }}</option>
          </select>
        </div>

        <!-- Filtre par statut -->
        <div class="space-y-2">
          <label class="block text-sm font-semibold text-text-dark dark:text-dark-text-primary">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>Statut</span>
            </div>
          </label>
          <select
            [(ngModel)]="filterStatus"
            (ngModelChange)="applyFilters()"
            class="w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary"
          >
            <option value="all">Tous les statuts</option>
            <option value="evaluated">Évalués</option>
            <option value="pending">En attente</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex flex-col items-center justify-center py-16">
      <div class="relative">
        <div class="animate-spin rounded-full h-16 w-16 border-4 border-primary/30 dark:border-dark-accent-primary/30"></div>
        <div class="animate-spin rounded-full h-16 w-16 border-4 border-transparent border-t-primary dark:border-t-dark-accent-primary absolute top-0 left-0"></div>
      </div>
      <p class="mt-4 text-text dark:text-dark-text-secondary animate-pulse">Chargement des rendus...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error" class="bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-4 mb-6 backdrop-blur-sm">
      <div class="flex items-center space-x-3">
        <svg class="w-5 h-5 text-danger dark:text-danger-dark flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <p class="font-medium">{{ error }}</p>
      </div>
    </div>

    <!-- Liste des rendus en cartes modernes -->
    <div *ngIf="!isLoading && filteredRendus.length > 0" class="space-y-6">
      <div *ngFor="let rendu of filteredRendus" class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">

          <!-- Informations étudiant -->
          <div class="flex items-center space-x-4">
            <div class="relative">
              <div class="h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center text-white text-lg font-bold shadow-lg">
                {{ getInitials(rendu.etudiant) }}
              </div>
              <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-r from-success to-success-dark dark:from-dark-accent-secondary dark:to-dark-accent-primary rounded-full flex items-center justify-center">
                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-bold text-text-dark dark:text-dark-text-primary">
                {{ getStudentName(rendu.etudiant) }}
              </h3>
              <p class="text-sm text-text dark:text-dark-text-secondary">{{ rendu.etudiant?.email || 'Email non disponible' }}</p>
              <div class="flex items-center space-x-4 mt-2">
                <div class="flex items-center space-x-1">
                  <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                  <span class="text-sm font-medium text-text-dark dark:text-dark-text-primary">
                    {{ getGroupName(rendu.etudiant) }}
                  </span>
                </div>
                <div class="flex items-center space-x-1">
                  <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L16 7"></path>
                  </svg>
                  <span class="text-sm font-medium text-text-dark dark:text-dark-text-primary">{{ rendu.projet?.titre }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Informations du rendu -->
          <div class="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-6">
            <!-- Date de soumission -->
            <div class="flex items-center space-x-2">
              <div class="bg-info/10 dark:bg-dark-accent-primary/10 p-2 rounded-lg">
                <svg class="w-4 h-4 text-info dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L16 7"></path>
                </svg>
              </div>
              <div>
                <p class="text-xs text-text dark:text-dark-text-secondary">Soumis le</p>
                <p class="text-sm font-semibold text-text-dark dark:text-dark-text-primary">{{ formatDate(rendu.dateSoumission) }}</p>
              </div>
            </div>

            <!-- Statut -->
            <div class="flex items-center space-x-2">
              <div [ngClass]="getStatusIconClass(rendu)" class="p-2 rounded-lg">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path *ngIf="rendu.evaluation" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  <path *ngIf="!rendu.evaluation" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div>
                <p class="text-xs text-text dark:text-dark-text-secondary">Statut</p>
                <span [ngClass]="getStatusBadgeClass(rendu)" class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold">
                  {{ getStatutEvaluation(rendu) }}
                </span>
              </div>
            </div>

            <!-- Score (si évalué) -->
            <div *ngIf="rendu.evaluation" class="flex items-center space-x-2">
              <div class="bg-success/10 dark:bg-dark-accent-secondary/10 p-2 rounded-lg">
                <svg class="w-4 h-4 text-success dark:text-dark-accent-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <div>
                <p class="text-xs text-text dark:text-dark-text-secondary">Score</p>
                <p class="text-sm font-bold" [ngClass]="getScoreColorClass(getScoreTotal(rendu))">
                  {{ getScoreTotal(rendu) }}/20
                </p>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex flex-col sm:flex-row gap-2">
            <!-- Si déjà évalué -->
            <div *ngIf="rendu.evaluation" class="flex flex-col sm:flex-row gap-2">
              <a [routerLink]="['/admin/projects/evaluation-details', rendu._id]"
                 class="group/btn px-4 py-2 bg-gradient-to-r from-info to-primary dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium text-center">
                <div class="flex items-center justify-center space-x-2">
                  <svg class="w-4 h-4 group-hover/btn:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  <span>Voir l'évaluation</span>
                </div>
              </a>
              <button (click)="navigateToEditEvaluation(rendu._id)"
                      class="group/btn px-4 py-2 bg-gradient-to-r from-secondary to-primary-dark dark:from-dark-accent-secondary dark:to-dark-accent-primary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
                <div class="flex items-center justify-center space-x-2">
                  <svg class="w-4 h-4 group-hover/btn:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                  <span>Modifier</span>
                </div>
              </button>
            </div>

            <!-- Si non évalué -->
            <div *ngIf="!rendu.evaluation" class="flex flex-col sm:flex-row gap-2">
              <button (click)="evaluerRendu(rendu._id, 'manual')"
                      class="group/btn px-4 py-2 bg-gradient-to-r from-success to-success-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
                <div class="flex items-center justify-center space-x-2">
                  <svg class="w-4 h-4 group-hover/btn:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                  <span>Évaluer manuellement</span>
                </div>
              </button>
              <button (click)="evaluerRendu(rendu._id, 'ai')"
                      class="group/btn px-4 py-2 bg-gradient-to-r from-secondary to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
                <div class="flex items-center justify-center space-x-2">
                  <svg class="w-4 h-4 group-hover/btn:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                  </svg>
                  <span>Évaluer par IA</span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State moderne -->
    <div *ngIf="!isLoading && filteredRendus.length === 0" class="text-center py-16">
      <div class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-12 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 max-w-md mx-auto">
        <div class="bg-gradient-to-br from-primary/10 to-secondary/10 dark:from-dark-accent-primary/20 dark:to-dark-accent-secondary/20 rounded-2xl p-6 mb-6">
          <svg class="h-16 w-16 mx-auto text-primary dark:text-dark-accent-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-text-dark dark:text-dark-text-primary mb-2">Aucun rendu trouvé</h3>
        <p class="text-text dark:text-dark-text-secondary mb-4">Aucun rendu ne correspond à vos critères de filtrage actuels.</p>
        <button (click)="resetFilters()" class="px-6 py-2 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
          <div class="flex items-center justify-center space-x-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span>Réinitialiser les filtres</span>
          </div>
        </button>
      </div>
    </div>
  </div>
</div>

