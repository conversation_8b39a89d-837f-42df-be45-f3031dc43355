{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LayoutsModule } from './layouts/layouts.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { JwtModule, JWT_OPTIONS } from '@auth0/angular-jwt';\nimport { environment } from 'src/environments/environment';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { GraphQLModule } from './graphql.module';\nimport { ApolloModule } from 'apollo-angular';\nimport { CallModule } from './components/call/call.module';\nimport { ConnectionStatusModule } from './components/connection-status/connection-status.module';\nimport { GraphqlStatusModule } from './components/graphql-status/graphql-status.module';\nimport { VoiceMessageModule } from './components/voice-message/voice-message.module';\nimport { SharedModule } from './shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@auth0/angular-jwt\";\n// Factory simplifiée sans injection de JwtHelperService\nexport function jwtOptionsFactory() {\n  return {\n    tokenGetter: () => {\n      if (!environment.production) {\n        console.debug('JWT token retrieved from storage');\n      }\n      return localStorage.getItem('token');\n    },\n    allowedDomains: [new URL(environment.urlBackend).hostname],\n    disallowedRoutes: [`${new URL(environment.urlBackend).origin}/users/login`]\n  };\n}\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, AppRoutingModule, LayoutsModule, FormsModule, ReactiveFormsModule, HttpClientModule, BrowserAnimationsModule, JwtModule.forRoot({\n        jwtOptionsProvider: {\n          provide: JWT_OPTIONS,\n          useFactory: jwtOptionsFactory\n        }\n      }), GraphQLModule, ApolloModule, CallModule, ConnectionStatusModule, GraphqlStatusModule, VoiceMessageModule, SharedModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [BrowserModule, AppRoutingModule, LayoutsModule, FormsModule, ReactiveFormsModule, HttpClientModule, BrowserAnimationsModule, i1.JwtModule, GraphQLModule, ApolloModule, CallModule, ConnectionStatusModule, GraphqlStatusModule, VoiceMessageModule, SharedModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "AppRoutingModule", "AppComponent", "LayoutsModule", "FormsModule", "ReactiveFormsModule", "HttpClientModule", "JwtModule", "JWT_OPTIONS", "environment", "BrowserAnimationsModule", "GraphQLModule", "ApolloModule", "CallModule", "ConnectionStatusModule", "GraphqlStatusModule", "VoiceMessageModule", "SharedModule", "jwtOptionsFactory", "tokenGetter", "production", "console", "debug", "localStorage", "getItem", "allowedDomains", "URL", "urlBackend", "hostname", "disallowedRoutes", "origin", "AppModule", "bootstrap", "forRoot", "jwtOptionsProvider", "provide", "useFactory", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LayoutsModule } from './layouts/layouts.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { JwtModule, JWT_OPTIONS } from '@auth0/angular-jwt';\nimport { environment } from 'src/environments/environment';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { GraphQLModule } from './graphql.module';\nimport { ApolloModule } from 'apollo-angular';\nimport { CallModule } from './components/call/call.module';\nimport { ConnectionStatusModule } from './components/connection-status/connection-status.module';\nimport { GraphqlStatusModule } from './components/graphql-status/graphql-status.module';\nimport { VoiceMessageModule } from './components/voice-message/voice-message.module';\nimport { SharedModule } from './shared/shared.module';\n// Factory simplifiée sans injection de JwtHelperService\nexport function jwtOptionsFactory() {\n  return {\n    tokenGetter: () => {\n      if (!environment.production) {\n        console.debug('JWT token retrieved from storage');\n      }\n      return localStorage.getItem('token');\n    },\n    allowedDomains: [new URL(environment.urlBackend).hostname],\n    disallowedRoutes: [`${new URL(environment.urlBackend).origin}/users/login`],\n  };\n}\n\n@NgModule({\n  declarations: [AppComponent],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    LayoutsModule,\n    FormsModule,\n    ReactiveFormsModule,\n    HttpClientModule,\n    BrowserAnimationsModule,\n    JwtModule.forRoot({\n      jwtOptionsProvider: {\n        provide: JWT_OPTIONS,\n        useFactory: jwtOptionsFactory,\n      },\n    }),\n    GraphQLModule,\n    ApolloModule,\n    CallModule,\n    ConnectionStatusModule,\n    GraphqlStatusModule,\n    VoiceMessageModule,\n    SharedModule,\n  ],\n  providers: [],\n  bootstrap: [AppComponent],\n})\nexport class AppModule {}\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,SAAS,EAAEC,WAAW,QAAQ,oBAAoB;AAC3D,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,sBAAsB,QAAQ,yDAAyD;AAChG,SAASC,mBAAmB,QAAQ,mDAAmD;AACvF,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,YAAY,QAAQ,wBAAwB;;;AACrD;AACA,OAAM,SAAUC,iBAAiBA,CAAA;EAC/B,OAAO;IACLC,WAAW,EAAEA,CAAA,KAAK;MAChB,IAAI,CAACV,WAAW,CAACW,UAAU,EAAE;QAC3BC,OAAO,CAACC,KAAK,CAAC,kCAAkC,CAAC;;MAEnD,OAAOC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IACtC,CAAC;IACDC,cAAc,EAAE,CAAC,IAAIC,GAAG,CAACjB,WAAW,CAACkB,UAAU,CAAC,CAACC,QAAQ,CAAC;IAC1DC,gBAAgB,EAAE,CAAC,GAAG,IAAIH,GAAG,CAACjB,WAAW,CAACkB,UAAU,CAAC,CAACG,MAAM,cAAc;GAC3E;AACH;AA6BA,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFR9B,YAAY;IAAA;EAAA;;;gBAtBtBF,aAAa,EACbC,gBAAgB,EAChBE,aAAa,EACbC,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChBI,uBAAuB,EACvBH,SAAS,CAAC0B,OAAO,CAAC;QAChBC,kBAAkB,EAAE;UAClBC,OAAO,EAAE3B,WAAW;UACpB4B,UAAU,EAAElB;;OAEf,CAAC,EACFP,aAAa,EACbC,YAAY,EACZC,UAAU,EACVC,sBAAsB,EACtBC,mBAAmB,EACnBC,kBAAkB,EAClBC,YAAY;IAAA;EAAA;;;2EAKHc,SAAS;IAAAM,YAAA,GA1BLnC,YAAY;IAAAoC,OAAA,GAEzBtC,aAAa,EACbC,gBAAgB,EAChBE,aAAa,EACbC,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChBI,uBAAuB,EAAA6B,EAAA,CAAAhC,SAAA,EAOvBI,aAAa,EACbC,YAAY,EACZC,UAAU,EACVC,sBAAsB,EACtBC,mBAAmB,EACnBC,kBAAkB,EAClBC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}