<!-- Indicateur de statut de connexion -->
<div
  *ngIf="showStatus || !isOnline"
  class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-300"
  [ngClass]="{
    'animate-slideInDown': showStatus,
    'animate-slideOutUp': !showStatus && isOnline
  }"
>
  <div
    [ngClass]="getStatusColor()"
    class="px-4 py-2 rounded-full shadow-lg text-white text-sm font-medium flex items-center space-x-2 backdrop-blur-sm"
  >
    <i [class]="getStatusIcon()" [ngClass]="{ 'animate-pulse': !isOnline }"></i>
    <span>{{ getStatusText() }}</span>
    
    <!-- Bouton fermer (seulement si en ligne) -->
    <button
      *ngIf="isOnline"
      (click)="hideStatus()"
      class="ml-2 p-1 hover:bg-white/20 rounded-full transition-colors"
      title="Fermer"
    >
      <i class="fas fa-times text-xs"></i>
    </button>
  </div>
</div>

<!-- Indicateur permanent en bas si hors ligne -->
<div
  *ngIf="!isOnline"
  class="fixed bottom-0 left-0 right-0 bg-red-600 text-white text-center py-2 z-40"
>
  <div class="flex items-center justify-center space-x-2">
    <i class="fas fa-exclamation-triangle animate-pulse"></i>
    <span class="text-sm font-medium">Vous êtes hors ligne. Certaines fonctionnalités peuvent être limitées.</span>
  </div>
</div>

<!-- Styles CSS intégrés -->
<style>
  @keyframes slideInDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @keyframes slideOutUp {
    from {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
    to {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
  }

  .animate-slideInDown {
    animation: slideInDown 0.3s ease-out;
  }

  .animate-slideOutUp {
    animation: slideOutUp 0.3s ease-in;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .animate-pulse {
    animation: pulse 2s infinite;
  }
</style>
