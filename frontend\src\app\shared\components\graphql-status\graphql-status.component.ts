import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { Apollo } from 'apollo-angular';

@Component({
  selector: 'app-graphql-status',
  templateUrl: './graphql-status.component.html',
  styleUrls: ['./graphql-status.component.css']
})
export class GraphQLStatusComponent implements OnInit, OnDestroy {
  isConnected = true;
  showStatus = false;
  lastError: string | null = null;
  private subscriptions = new Subscription();

  constructor(private apollo: Apollo) {}

  ngOnInit(): void {
    // Surveiller les erreurs GraphQL
    this.subscriptions.add(
      this.apollo.client.onResetStore(() => {
        console.log('🔄 GraphQL store reset');
        this.isConnected = true;
        this.showStatus = false;
      })
    );

    // Surveiller les erreurs de réseau
    this.subscriptions.add(
      this.apollo.client.onClearStore(() => {
        console.log('🗑️ GraphQL store cleared');
      })
    );

    // Simuler la surveillance des erreurs GraphQL
    // Dans un vrai projet, vous pourriez utiliser apollo-link-error
    this.checkGraphQLConnection();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private checkGraphQLConnection(): void {
    // Vérifier périodiquement la connexion GraphQL
    setInterval(() => {
      this.performHealthCheck();
    }, 30000); // Vérifier toutes les 30 secondes
  }

  private performHealthCheck(): void {
    // Ici vous pourriez faire une requête GraphQL simple pour tester la connexion
    // Pour l'instant, on simule juste
    try {
      // Simuler une vérification de santé
      const wasConnected = this.isConnected;
      this.isConnected = navigator.onLine; // Simplification
      
      if (wasConnected !== this.isConnected) {
        this.showStatus = true;
        this.lastError = this.isConnected ? null : 'Connexion GraphQL perdue';
        
        if (this.isConnected) {
          setTimeout(() => {
            this.showStatus = false;
          }, 3000);
        }
      }
    } catch (error) {
      console.error('❌ GraphQL health check failed:', error);
      this.isConnected = false;
      this.lastError = 'Erreur de connexion GraphQL';
      this.showStatus = true;
    }
  }

  hideStatus(): void {
    this.showStatus = false;
  }

  retry(): void {
    console.log('🔄 Retrying GraphQL connection...');
    this.performHealthCheck();
  }

  getStatusText(): string {
    if (this.isConnected) {
      return 'GraphQL connecté';
    }
    return this.lastError || 'GraphQL déconnecté';
  }

  getStatusIcon(): string {
    return this.isConnected ? 'fas fa-database' : 'fas fa-exclamation-triangle';
  }

  getStatusColor(): string {
    return this.isConnected ? 'bg-blue-500' : 'bg-orange-500';
  }
}
