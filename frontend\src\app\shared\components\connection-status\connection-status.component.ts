import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subscription, fromEvent, merge, of } from 'rxjs';
import { map, startWith } from 'rxjs/operators';

@Component({
  selector: 'app-connection-status',
  templateUrl: './connection-status.component.html',
  styleUrls: ['./connection-status.component.css']
})
export class ConnectionStatusComponent implements OnInit, OnDestroy {
  isOnline = true;
  showStatus = false;
  private subscriptions = new Subscription();

  ngOnInit(): void {
    // Surveiller le statut de connexion
    const online$ = fromEvent(window, 'online').pipe(map(() => true));
    const offline$ = fromEvent(window, 'offline').pipe(map(() => false));
    
    this.subscriptions.add(
      merge(online$, offline$)
        .pipe(startWith(navigator.onLine))
        .subscribe(isOnline => {
          const wasOnline = this.isOnline;
          this.isOnline = isOnline;
          
          // Afficher le statut seulement lors des changements
          if (wasOnline !== isOnline) {
            this.showStatus = true;
            
            // Masquer automatiquement après 3 secondes si en ligne
            if (isOnline) {
              setTimeout(() => {
                this.showStatus = false;
              }, 3000);
            }
          }
        })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  hideStatus(): void {
    this.showStatus = false;
  }

  getStatusText(): string {
    return this.isOnline ? 'Connexion rétablie' : 'Connexion perdue';
  }

  getStatusIcon(): string {
    return this.isOnline ? 'fas fa-wifi' : 'fas fa-wifi-slash';
  }

  getStatusColor(): string {
    return this.isOnline ? 'bg-green-500' : 'bg-red-500';
  }
}
