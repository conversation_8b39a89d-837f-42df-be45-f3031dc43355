/* CSS pour le composant d'interface d'appel */

/* Animations personnalisées */
@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
  }
}

@keyframes bounce-subtle {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translateY(0);
  }
  40%,
  43% {
    transform: translateY(-4px);
  }
  70% {
    transform: translateY(-2px);
  }
}

@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Styles pour les boutons d'appel */
.call-button {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.call-button::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.call-button:hover::before {
  width: 100%;
  height: 100%;
}

/* Styles pour les boutons actifs */
.call-button-active {
  animation: pulse-glow 2s infinite;
}

/* Styles pour les tooltips */
.tooltip {
  pointer-events: none;
  white-space: nowrap;
  z-index: 1000;
}

/* Styles pour les indicateurs de statut */
.status-indicator {
  animation: bounce-subtle 2s infinite;
}

/* Styles pour les ondes sonores */
.sound-wave {
  animation: pulse 1.5s ease-in-out infinite;
}

.sound-wave:nth-child(2) {
  animation-delay: 0.2s;
}

.sound-wave:nth-child(3) {
  animation-delay: 0.4s;
}

/* Styles pour l'avatar en rotation */
.rotating-border {
  animation: rotate-slow 3s linear infinite;
}

/* Styles pour les effets de glassmorphism */
.glass-effect {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Styles pour les boutons avec effet de glow */
.glow-red {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
}

.glow-green {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
}

.glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.glow-gray {
  box-shadow: 0 0 20px rgba(107, 114, 128, 0.3);
}

/* Responsive design */
@media (max-width: 640px) {
  .call-button {
    width: 4rem;
    height: 4rem;
  }

  .call-button i {
    font-size: 1.25rem;
  }

  .tooltip {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

/* Mode sombre amélioré */
@media (prefers-color-scheme: dark) {
  .call-button {
    border-color: rgba(255, 255, 255, 0.2);
  }

  .glass-effect {
    background: rgba(17, 24, 39, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
  }
}

/* Animations d'entrée */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Styles pour les indicateurs de connexion */
.connection-indicator {
  position: relative;
}

.connection-indicator::after {
  content: "";
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, #10b981, #059669);
  animation: rotate-slow 2s linear infinite;
  z-index: -1;
}

/* Styles pour les effets de particules */
.particle-effect {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(59, 130, 246, 0.6);
  border-radius: 50%;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.5;
  }
}
