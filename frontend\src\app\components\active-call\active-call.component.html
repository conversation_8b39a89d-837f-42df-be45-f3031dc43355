<!-- Interface d'appel ultra-simple -->
<div *ngIf="activeCall" class="fixed inset-0 z-50 bg-black/90 flex flex-col">
  
  <!-- Header simple -->
  <div class="p-4 text-center">
    <div class="text-white">
      <span class="text-sm">{{ getCallStatusText() }}</span>
      <span class="ml-4 text-sm">{{ callDuration }}</span>
    </div>
  </div>

  <!-- Zone principale -->
  <div class="flex-1 flex items-center justify-center">
    <div class="text-center text-white">
      
      <!-- Avatar -->
      <div class="w-32 h-32 rounded-full overflow-hidden mx-auto mb-4 border-2 border-blue-500">
        <img 
          [src]="getOtherParticipantAvatar()" 
          [alt]="getOtherParticipantName()"
          class="w-full h-full object-cover"
        />
      </div>
      
      <!-- Nom -->
      <h2 class="text-2xl font-bold mb-2">{{ getOtherParticipantName() }}</h2>
      
      <!-- Type d'appel -->
      <p class="text-gray-400 mb-8">
        {{ isVideoCall() ? 'Appel vidéo' : 'Appel audio' }}
      </p>
      
    </div>
  </div>

  <!-- Contrôles simples -->
  <div class="p-6">
    <div class="flex justify-center space-x-8">
      
      <!-- Micro -->
      <button 
        (click)="toggleMicrophone()"
        class="w-14 h-14 rounded-full flex items-center justify-center"
        [class]="isAudioMuted ? 'bg-red-500' : 'bg-blue-500'"
      >
        <i class="fas text-white" [class]="isAudioMuted ? 'fa-microphone-slash' : 'fa-microphone'"></i>
      </button>

      <!-- Caméra (si vidéo) -->
      <button 
        *ngIf="isVideoCall()"
        (click)="toggleCamera()"
        class="w-14 h-14 rounded-full flex items-center justify-center"
        [class]="isVideoMuted ? 'bg-red-500' : 'bg-blue-500'"
      >
        <i class="fas text-white" [class]="isVideoMuted ? 'fa-video-slash' : 'fa-video'"></i>
      </button>

      <!-- Raccrocher -->
      <button 
        (click)="endCall()"
        class="w-14 h-14 rounded-full bg-red-600 flex items-center justify-center"
      >
        <i class="fas fa-phone-slash text-white"></i>
      </button>
      
    </div>
  </div>
  
</div>
